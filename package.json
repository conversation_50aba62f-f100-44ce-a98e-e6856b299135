{"name": "kbc-chatbot-web", "version": "1.0.0", "private": true, "scripts": {"local-test": "vite --mode test", "test": "vite --mode test", "dev": "vite --mode dev", "sta": "vite build --mode sta", "prod": "vite build --mode prod", "build": "run-p type-check build-only --mode prod", "preview": "vite preview", "build-only": "vite build --mode prod", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "update-packages": "npm install @dt/dt-monitor@*"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vue/compiler-core": "^3.5.20", "autoprefixer": "^10.4.14", "axios": "^1.4.0", "copy-text-to-clipboard": "^3.2.0", "element-plus": "^2.3.10", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "micromark": "^4.0.2", "micromark-extension-gfm-table": "^2.1.1", "nprogress": "^0.2.0", "pinia": "^2.0.36", "pinia-plugin-persistedstate": "^3.1.0", "quill": "^1.3.7", "vue": "^3.3.2", "vue-json-pretty": "^2.4.0", "vue-router": "^4.2.0", "vuedraggable": "^2.24.3"}, "devDependencies": {"@dt/dt-monitor": "^2.4.24", "@rushstack/eslint-patch": "^1.2.0", "@tsconfig/node18": "^2.0.1", "@types/js-cookie": "^3.0.3", "@types/node": "^18.16.8", "@types/nprogress": "^0.2.0", "@types/quill": "^2.0.10", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "eslint": "^8.39.0", "eslint-plugin-vue": "^9.11.0", "https-proxy-agent": "^7.0.5", "less": "^4.1.3", "npm-run-all": "^4.1.5", "postcss": "^8.4.23", "prettier": "^2.8.8", "tailwindcss": "^3.3.2", "typescript": "~5.0.4", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.24.1", "unplugin-vue-define-options": "^1.3.5", "vite": "^4.3.5", "vue-tsc": "^1.6.4"}}