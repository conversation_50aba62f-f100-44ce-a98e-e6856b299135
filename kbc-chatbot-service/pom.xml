<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>kbc-chatbot</artifactId>
        <groupId>com.kbao</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.kbao</groupId>
    <artifactId>kbc-chatbot-service</artifactId>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <!-- 公共实体配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>common-spring-boot-starter</artifactId>
        </dependency>

        <!-- API文档配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
        </dependency>

        <!-- db配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>db-spring-boot-starter</artifactId>
        </dependency>

        <!-- redis 配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>redis-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>

        <!-- elasticsearch -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.microsoft.spring.data.gremlin</groupId>-->
<!--            <artifactId>spring-data-gremlin</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.microsoft.azure</groupId>-->
<!--            <artifactId>spring-data-gremlin-boot-starter</artifactId>-->
<!--        </dependency>-->

        <!-- neo4j -->
<!--        <dependency>-->
<!--            <groupId>org.neo4j.driver</groupId>-->
<!--            <artifactId>neo4j-java-driver-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-data-neo4j</artifactId>-->
<!--        </dependency>-->

        <!-- 日志中心 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>log-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-chatbot-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-chatbot-common</artifactId>
        </dependency>

        <!-- 基础平台web 接口服务包 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bsc-web-client</artifactId>
        </dependency>

        <!-- 基础平台api 接口服务包 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bsc-api-client</artifactId>
        </dependency>
        <!--云知识库web-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-km-web-client</artifactId>
        </dependency>
        <!--云知识库api-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-km-api-client</artifactId>
        </dependency>
        <!--UCS-web-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ucs-web-client</artifactId>
        </dependency>
        <!--ospt api-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-tsc-ospt-api-client</artifactId>
        </dependency>
        <!--olpt api-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-tsc-olpt-api-client</artifactId>
        </dependency>


        <!-- 文件平台 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ufs-web-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ufs-api-client</artifactId>
        </dependency>

        <!-- 根据客户信息和顾问信息查询托管/服务关系 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-pm-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-pm-web-client</artifactId>
        </dependency>

        <!-- 好赔 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-claim-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-claim-web-client </artifactId>
        </dependency>

        <!-- UOC -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-uoc-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-uoc-web-client</artifactId>
        </dependency>

        <!--CCS 童管家-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ccs-dc-api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ums-api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ums-web-client</artifactId>
        </dependency>

        <!--调度平台-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-job-client-starter</artifactId>
        </dependency>

        <!-- spring 模板引擎 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.threeten</groupId>
            <artifactId>threetenbp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.gsonfire</groupId>
            <artifactId>gson-fire</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
