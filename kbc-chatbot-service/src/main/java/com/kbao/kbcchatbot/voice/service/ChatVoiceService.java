package com.kbao.kbcchatbot.voice.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.externalapi.model.MaasToVoiceReq;
import com.kbao.kbcchatbot.kbc.ufs.KbcUfsService;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcchatbot.voice.bean.VoiceVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 语音处理
 * @date 2025/4/3 11:02
 */
@Service
@Slf4j
public class ChatVoiceService {

    @Autowired
    private MaasHttpService maasHttpService;

    @Autowired
    private KbcUfsService kbcUfsService;
    /**
    * @Description: 语音文件转文本
    * @Param: [url]
    * @return: void
    * @Author: husw
    * @Date: 2025/4/3 11:27
    */
    public String speakToText(String fileId) {
        String userId = ChatUserUtil.getUserId();
        //文件ID转链接
        String url = kbcUfsService.generatePresignedUrl(ChatUserUtil.getUser().getTenantId(), fileId, userId);
        //生成文本
        return maasHttpService.speakToText(userId,url);
    }

    /**
     * @Description: 获取文本语音
     * @Param: [id]
     * @return: com.kbao.kbcchatbot.voice.entity.Voice
     * @Author: husw
     * @Date: 2025/4/17 16:41
     */
    public String getVoice(String id, String text) {
        MaasToVoiceReq req = new MaasToVoiceReq();
        try {
            req.setAnswerId(id);
            req.setText(text);
            req.setUserId(ChatUserUtil.getUserId());
            return maasHttpService.getTextToVoice(req);
        }catch (Exception e){
            log.error("文本转语音失败！",e);
            throw new BusinessException("播报失败");
        }
    }

}
