package com.kbao.kbcchatbot.discard.robotmanualservicekeywords.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.discard.robotmanualservicekeywords.entity.RobotManualServiceKeywords;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_robot_manual_service_keywords(聊天机器人转人工关键词表)】的数据库操作Mapper
* @createDate 2023-05-17 14:31:53
*/
public interface RobotManualServiceKeywordsMapper extends BaseMapper<RobotManualServiceKeywords,Long> {

    List<RobotManualServiceKeywords> selectByRobotId(Long robotId);

    void deleteByRobotIdAndType(Map<String,Object> map);

}
