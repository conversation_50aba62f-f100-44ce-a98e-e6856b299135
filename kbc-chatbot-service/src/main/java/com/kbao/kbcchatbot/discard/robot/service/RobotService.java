/*
package com.kbao.kbcchatbot.robot.service;

import com.google.common.collect.Lists;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.commons.util.TokenUtil;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscApiContext;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.constants.Constant;
import com.kbao.kbcchatbot.externalapi.model.LargeModelService;
import com.kbao.kbcchatbot.rasa3x.bean.TrainModelReqVO;
import com.kbao.kbcchatbot.rasa3x.service.Rasa3xService;
import com.kbao.kbcchatbot.robot.bean.TrainRasaModelFailureReqVO;
import com.kbao.kbcchatbot.robot.dao.RobotMapper;
import com.kbao.kbcchatbot.robot.entity.Robot;
import com.kbao.kbcchatbot.robot.enums.RobotReleaseTypeEnum;
import com.kbao.kbcchatbot.robot.enums.RobotStatusEnum;
import com.kbao.kbcchatbot.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.robot.vo.RobotAddReqVO;
import com.kbao.kbcchatbot.robot.vo.RobotUpdateStatusVO;
import com.kbao.kbcchatbot.robotbasicconfig.entity.RobotBasicConfig;
import com.kbao.kbcchatbot.robotbasicconfig.service.RobotBasicConfigService;
import com.kbao.kbcchatbot.robotknowledgeconfig.entity.RobotKnowledgeConfig;
import com.kbao.kbcchatbot.robotknowledgeconfig.enums.KnowledgeOperationEnum;
import com.kbao.kbcchatbot.robotknowledgeconfig.service.RobotKnowledgeAsyncService;
import com.kbao.kbcchatbot.robotknowledgeconfig.service.RobotKnowledgeConfigService;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeEmbeddingVO;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeTrainVO;
import com.kbao.kbcchatbot.robotmanualserviceconfig.entity.RobotManualServiceConfig;
import com.kbao.kbcchatbot.robotmanualserviceconfig.enums.PlatAccessTypeEnum;
import com.kbao.kbcchatbot.robotmanualserviceconfig.service.RobotManualServiceConfigService;
import com.kbao.kbcchatbot.robotmanualservicekeywords.entity.RobotManualServiceKeywords;
import com.kbao.kbcchatbot.robotmanualservicekeywords.service.RobotManualServiceKeywordsService;
import com.kbao.kbcchatbot.robotreleaserecord.entity.RobotReleaseRecord;
import com.kbao.kbcchatbot.robotreleaserecord.enums.RobotReleaseStatusEnum;
import com.kbao.kbcchatbot.robotreleaserecord.service.RobotReleaseRecordService;
import com.kbao.kbcchatbot.series.enums.SeriesNameEnum;
import com.kbao.kbcchatbot.series.service.SeriesDataService;
import com.kbao.kbcchatbot.upload.UploadService;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.kbao.kbcchatbot.robot.enums.RobotStatusEnum.NOT_PUBlish;
import static com.kbao.kbcchatbot.robot.enums.RobotStatusEnum.PUBLISHED;

*/
/**
 * @program: kbc-chatbot
 * @description: 机器人
 * @author: husw
 * @create: 2023-05-16 11:39
 **//*

//@Service
//@Slf4j
public class RobotService extends BaseSQLServiceImpl<Robot,Long, RobotMapper> {

    @Autowired
    private SeriesDataService seriesDataService;

    @Lazy
    @Autowired
    private RobotBasicConfigService robotBasicConfigService;

    @Lazy
    @Autowired
    private RobotKnowledgeConfigService robotKnowledgeConfigService;

    @Lazy
    @Autowired
    private RobotManualServiceConfigService robotManualServiceConfigService;

    @Autowired
    private RobotReleaseRecordService robotReleaseRecordService;

    @Autowired
    private RobotManualServiceKeywordsService robotManualServiceKeywordsService;

    @Autowired
    private RobotKnowledgeAsyncService robotKnowledgeAsyncService;

//    @Lazy
//    @Autowired
//    private ChannelService channelService;

    @Autowired
    private Rasa3xService rasa3xService;

    @Autowired
    private UploadService uploadService;

    @Autowired
    private LargeModelService largeModelService;

    @Autowired
    ThreadPoolTaskExecutor asyncThreadPool;

    */
/**
    * @Description: 新增机器人
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/16 14:23
    *//*

    */
/*public void add(RobotAddReqVO param) {
        checkParam(param);
        Robot robot = new Robot();
        String nextId = seriesDataService.getNextId(SeriesNameEnum.ROBOT_ID.getSeriesName(),
                SeriesNameEnum.ROBOT_ID.getPrefix(), SeriesNameEnum.ROBOT_ID.getNum());
        robot.setCode(nextId);
        robot.setReleaseType(EmptyUtils.isEmpty(param.getReleaseType())?2:param.getReleaseType());
        robot.setModelType(1);
        robot.setTenantId(BscApiContext.TenantId.get());
        robot.setName(param.getName());
        robot.setStatus(PUBLISHED.getCode());
        robot.setCreateId(BscUserUtils.getUserId());
        robot.setCreateTime(DateUtils.getCurrentDate());
        robot.setUpadteId(BscUserUtils.getUserId());
        robot.setUpdateTime(DateUtils.getCurrentDate());
        robot.setIsDeleted(YesNoEnum.NO.getValue());
        this.mapper.insert(robot);
    }*//*

    */
/**
    * @Description: 编辑
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/16 14:26
    *//*

    */
/*public void update(RobotAddReqVO param) {
        Robot robot = this.mapper.selectByPrimaryKey(param.getId());
        if(EmptyUtils.isEmpty(robot)){
            throw new BusinessException("机器人ID不存在！");
        }
        checkParam(param);
        //防止状态卡在发布中
        if (EmptyUtils.isNotEmpty(param.getStatus())){
            robot.setStatus(param.getStatus());
        }
        robot.setName(param.getName());
        this.mapper.updateByPrimaryKey(robot);
    }*//*

    */
/**
    * @Description: 参数检验
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/6/14 18:30
    *//*

    */
/*private void checkParam(RobotAddReqVO param) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("nameEq", param.getName());
        paramMap.put("tenantId",BscUserUtils.getUser().getUser().getTenantId());
        List<Robot> robots = this.mapper.selectAll(paramMap);
        if (EmptyUtils.isNotEmpty(robots)){
            throw new BusinessException("机器人名称不能重复！");
        }
    }

    *//*
*/
/**
    * @Description: 发布
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/23 10:32
    *//*
*/
/*
    @Transactional(rollbackFor = Exception.class)
    public void release(RobotUpdateStatusVO param) {
        //校验机器人基础配置和知识配置
        List<RobotBasicConfig> robotBasicConfigs = robotBasicConfigService.getMapper().selectByRobotId(param.getId());
        if (EmptyUtils.isEmpty(robotBasicConfigs)){
            throw new BusinessException("机器人基础配置为空，不可发布！");
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("robotId",param.getId());
        paramMap.put("operationNotEq",KnowledgeOperationEnum.UNBIND.getCode());
        paramMap.put("type",YesNoEnum.NO.getValue());
        List<RobotKnowledgeConfig> robotKnowledgeConfigList = robotKnowledgeConfigService.getMapper().selectByCondition(paramMap);
        if (EmptyUtils.isEmpty(robotKnowledgeConfigList)){
            throw new BusinessException("机器人知识配置为空，不可发布！");
        }
        param.setStatus(RobotStatusEnum.PUBLISHING.getCode());
        //变更状态
        updateStatus(param);
    }*//*

    */
/**
    * @Description: 上线
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/26 9:20
    *//*

    */
/*@Transactional(rollbackFor = Exception.class)
    public void up(RobotUpdateStatusVO param) {
        param.setStatus(PUBLISHED.getCode());
        updateStatus(param);
    }*//*

    */
/**
    * @Description: 下线
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/23 10:32
    *//*

//    @Transactional(rollbackFor = Exception.class)
//    public void down(RobotUpdateStatusVO param) {
//        //查询当前机器人是否绑定了渠道，如果绑定了渠道需渠道属于未发布状态才可下线机器人
//        int num = channelService.getEnabledRobotNumByRobotId(param.getId());
//        if (num > 0){
//            throw new BusinessException("机器人正在被渠道使用，无法下线!");
//        }
//        param.setStatus(RobotStatusEnum.OFFLINE.getCode());
//        updateStatus(param);
//    }

    */
/**
    * @Description: 变更状态
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/16 14:39
    *//*

    */
/*public void updateStatus(RobotUpdateStatusVO param) {
        Robot robot = this.mapper.selectByPrimaryKey(param.getId());
        if (EmptyUtils.isEmpty(robot)){
            throw new BusinessException("机器人ID不存在!");
        }
        if (robot.getStatus().equals(param.getStatus())){
            return;
        }
        //校验机器人状态
        if (RobotStatusEnum.OFFLINE.getCode().equals(robot.getStatus())
                && RobotStatusEnum.EDITING.getCode().equals(param.getStatus())){
            throw new BusinessException("已下线机器人不支持编辑！");
        }
        //未发布状态不需要变更编辑中状态
        if (NOT_PUBlish.getCode().equals(robot.getStatus())
                && RobotStatusEnum.EDITING.getCode().equals(param.getStatus())){
            return;
        }
        robot.setPreStatus(robot.getStatus());
        robot.setStatus(param.getStatus());
        if (RobotStatusEnum.PUBLISHING.getCode().equals(param.getStatus())){
            robot.setPublisher(BscUserUtils.getUserId());
            //校验知识配置是否存在同步中的数据
            robotKnowledgeConfigService.checkStatus(robot.getId(),null);
            String accessToken = TokenUtil.getToken();
            String tenantId = BscUserUtils.getUser().getUser().getTenantId();
            String funcId = BscUserUtils.getUser().getFunction().getFuncId();
            //发布
            asyncThreadPool.execute(()->release(robot, accessToken, tenantId, funcId));
        }
        String dateTime = DateUtils.thisDateTime();
        robot.setUpdateTime(DateUtils.str2DateTime(dateTime));
        robot.setPublisher(BscUserUtils.getUserId());
        this.mapper.updateByPrimaryKey(robot);
    }*//*



    */
/*protected void release(Robot robot, String accessToken, String tenantId, String funcId){
        //发布
        RobotReleaseRecord releaseRecord = new RobotReleaseRecord();
        releaseRecord.setId(IdWorker.get32UUID());
        releaseRecord.setStatus(RobotReleaseStatusEnum.PUBLISHING.getCode());
        releaseRecord.setRobotId(robot.getId());
        releaseRecord.setPublisher(robot.getPublisher());
        releaseRecord.setVersion(robot.getVersion());
        //暂存记录
        robotReleaseRecordService.save(releaseRecord);
        releaseRecord.setStatus(RobotReleaseStatusEnum.SUCCESS.getCode());
        //同步数据ES并返回数据包
        RobotKnowledgeTrainVO trainVO;
        try {
            trainVO = syncKnowledgeToEs(robot, releaseRecord);
        }catch (Exception e){
            releaseRecord.setStatus(RobotReleaseStatusEnum.FAIL.getCode());
            releaseRecord.setErrorMsg(e.getMessage());
            completeRelease(robot, releaseRecord);
            log.error("同步ES数据异常---》",e);
            throw new BusinessException("同步ES数据异常");
        }
        //训练数据包
        try {
            trainModel(robot, accessToken, tenantId, funcId, releaseRecord, trainVO);
        }catch (Exception e){
            releaseRecord.setStatus(RobotReleaseStatusEnum.FAIL.getCode());
            releaseRecord.setErrorMsg(e.getMessage());
            completeRelease(robot, releaseRecord);
            log.error("训练数据异常---》",e);
            throw new BusinessException("训练数据异常");
        }
        completeRelease(robot, releaseRecord);
    }*//*

    */
/**
    * @Description: 调用模型平台传入数据包
    * @Param: [robot, accessToken, tenantId, funcId, releaseRecord, trainVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/6/6 15:05
    *//*

    */
/*private void trainModel(Robot robot, String accessToken, String tenantId, String funcId, RobotReleaseRecord releaseRecord, RobotKnowledgeTrainVO trainVO) {
        if (RobotReleaseTypeEnum.RASA.getCode().equals(robot.getReleaseType())){
            //RASA
            TrainModelReqVO reqVO = new TrainModelReqVO();
            reqVO.setRobotId(robot.getId());
            reqVO.setReleaseRecordId(releaseRecord.getId());
            reqVO.setEnvironment(EnvTypeEnum.TEST_ENV.getCode());
            reqVO.setAccessToken(accessToken);
            reqVO.setTenantId(tenantId);
            reqVO.setFuncId(funcId);
            rasa3xService.tranModel(reqVO);
        }else if (RobotReleaseTypeEnum.BIG_MODEL.getCode().equals(robot.getReleaseType())){
            //大模型
            releaseRecord.setTrainVO(trainVO);
            if (EmptyUtils.isNotEmpty(trainVO)){
                //删除内容
                if (EmptyUtils.isNotEmpty(trainVO.getDelVO())){
                    largeModelService.embeddingDel(trainVO.getDelVO());
                }
                //新增内容
                if (EmptyUtils.isNotEmpty(trainVO.getEmbeddingVO())){
                    StringBuilder errorMsg = new StringBuilder();
                    List<List<RobotKnowledgeEmbeddingVO.Embedding>> embeddingLists = Lists.partition(trainVO.getEmbeddingVO().getDatas(), 10);
                    embeddingLists.forEach(list->{
                        RobotKnowledgeEmbeddingVO embeddingVO = new RobotKnowledgeEmbeddingVO();
                        embeddingVO.setDatas(list);
                        try {
                            largeModelService.embeddingModel(embeddingVO);
                        }catch (Exception e){
                            errorMsg.append(e.getMessage()).append(",");
                        }
                    });
                    if (EmptyUtils.isNotEmpty(errorMsg.toString())){
                        throw new BusinessException("部分数据向量化失败:"+ errorMsg);
                    }
                }
            }
        }
    }*//*


    */
/*private RobotKnowledgeTrainVO syncKnowledgeToEs(Robot robot, RobotReleaseRecord releaseRecord) {
        Map<String,Object> map = new HashMap<>();
        map.put("robotId",robot.getId());
        //同步基础配置
        syncBasicConfig(releaseRecord, map);

        //同步转人工配置
        syncManualServiceConfig(releaseRecord, map);

        //同步转客服关键词
        syncManualServiceKeywords(releaseRecord, map);

        //同步生产知识配置
        return syncKnowledgeConfig(robot, releaseRecord, map);
    }*//*


    */
/*private void completeRelease(Robot robot, RobotReleaseRecord releaseRecord) {
        Map<String,Object> map = new HashMap<>();
        map.put("robotId",robot.getId());

        robot.setVersion(EmptyUtils.isEmpty(robot.getVersion())?Constant.VERSION:"v"+(Integer.parseInt(robot.getVersion().split("v")[1])+1));
        robot.setPublisher(releaseRecord.getPublisher());
        String dateTime = DateUtils.thisDateTime();
        robot.setPublishTime(DateUtils.str2DateTime(dateTime));
        robot.setStatus(PUBLISHED.getCode());
        this.mapper.updateByPrimaryKey(robot);

        //保存发布记录
        releaseRecord.setPublishTime(DateUtils.str2DateTime(dateTime));
        robotReleaseRecordService.saveOrUpdate(releaseRecord);
    }*//*


    */
/*private void releaseFailure(Robot robot, RobotReleaseRecord releaseRecord) {
        robot.setVersion(EmptyUtils.isEmpty(robot.getVersion())?Constant.VERSION:"v"+(Integer.parseInt(robot.getVersion().split("v")[1])+1));
        robot.setPublisher(releaseRecord.getPublisher());
        String dateTime = DateUtils.thisDateTime();
        robot.setPublishTime(DateUtils.str2DateTime(dateTime));
        robot.setStatus(robot.getPreStatus());
        this.mapper.updateByPrimaryKey(robot);

        //保存发布记录
        releaseRecord.setPublishTime(DateUtils.str2DateTime(dateTime));
        releaseRecord.setStatus(RobotReleaseStatusEnum.FAIL.getCode());
        robotReleaseRecordService.saveOrUpdate(releaseRecord);
    }*//*



    */
/**
    * @Description: 同步知识配置
    * @Param: [robot, releaseRecord, map]
    * @return: void
    * @Author: husw
    * @Date: 2023/6/14 17:04
    *//*

    */
/*private RobotKnowledgeTrainVO syncKnowledgeConfig(Robot robot, RobotReleaseRecord releaseRecord, Map<String, Object> map) {
        Map<String,Object> knowledgeMap = new HashMap<>();
        knowledgeMap.put("robotId", robot.getId());
        knowledgeMap.put("type",YesNoEnum.YES.getValue());
        List<RobotKnowledgeConfig> robotKnowledgeConfigs = robotKnowledgeConfigService.getMapper().selectByCondition(knowledgeMap);
        releaseRecord.setKnowledgeConfig(robotKnowledgeConfigs);
        List<RobotKnowledgeConfig> robotKnowledgeConfigList = robotKnowledgeConfigService.getMapper().selectByRobotId(robot.getId());
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigList)){
            Map<Integer, List<RobotKnowledgeConfig>> operationMap = robotKnowledgeConfigList.stream().collect(Collectors.groupingBy(RobotKnowledgeConfig::getOperation));
            //更新es配置数据
            saveKnowledgeConfig(map, operationMap);
            //同步操作ES并返回变更数据包
            RobotKnowledgeTrainVO trainVO = robotKnowledgeAsyncService.releaseKnowledge(robot, operationMap);
            //数据同步完成后，清理测试版的操作
            robotKnowledgeConfigList.forEach(x->x.setOperation(KnowledgeOperationEnum.INIT.getCode()));
            robotKnowledgeConfigService.batchUpdate(robotKnowledgeConfigList,"updateByPrimaryKey",30);
            return trainVO;
        }
        return null;
    }*//*

    */
/**
    * @Description: 查询并组装数据
    * @Param: [map, robotKnowledgeConfigList]
    * @return: java.util.Map<java.lang.Integer,java.util.List<com.kbao.kbcchatbot.robotknowledgeconfig.entity.RobotKnowledgeConfig>>
    * @Author: husw
    * @Date: 2024/6/6 11:17
    *//*

    */
/*private void saveKnowledgeConfig(Map<String, Object> map, Map<Integer, List<RobotKnowledgeConfig>> operationMap) {
        //测试版解绑
        List<RobotKnowledgeConfig> robotKnowledgeConfigUnbindList = operationMap.get(KnowledgeOperationEnum.UNBIND.getCode());
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigUnbindList)){
            List<String> directoryIds = robotKnowledgeConfigUnbindList.stream().map(RobotKnowledgeConfig::getDirectoryId).collect(Collectors.toList());
            map.put("directoryIdList",directoryIds);
//                    map.put("type",YesNoEnum.NO.getValue());
            robotKnowledgeConfigService.getMapper().deleteByRobotIdAndDirect(map);
        }
        //测试版新增绑定
        List<RobotKnowledgeConfig> robotKnowledgeConfigAddList = operationMap.get(KnowledgeOperationEnum.BIND.getCode());
        List<RobotKnowledgeConfig> robotKnowledgeConfigProdList = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigAddList)){
            robotKnowledgeConfigAddList.forEach(x->{
                RobotKnowledgeConfig robotKnowledgeConfig = new RobotKnowledgeConfig();
                BeanUtils.copyProperties(x,robotKnowledgeConfig,"id");
                robotKnowledgeConfig.setType(1);
                robotKnowledgeConfig.setOperation(KnowledgeOperationEnum.INIT.getCode());
                robotKnowledgeConfigProdList.add(robotKnowledgeConfig);
            });
            robotKnowledgeConfigService.batchInsert(robotKnowledgeConfigProdList);
        }
        //测试版刷新绑定
        List<RobotKnowledgeConfig> robotKnowledgeConfigRefreshList = operationMap.get(KnowledgeOperationEnum.REFRESH.getCode());
        List<RobotKnowledgeConfig> robotKnowledgeConfigRefreshProdList = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigRefreshList)){
            List<String> directoryIds = robotKnowledgeConfigRefreshList.stream().map(RobotKnowledgeConfig::getDirectoryId).collect(Collectors.toList());
            map.put("directoryIdList",directoryIds);
            map.put("type",YesNoEnum.YES.getValue());
            robotKnowledgeConfigService.getMapper().deleteByRobotIdAndDirect(map);
            robotKnowledgeConfigRefreshList.forEach(x->{
                RobotKnowledgeConfig robotKnowledgeConfig = new RobotKnowledgeConfig();
                BeanUtils.copyProperties(x,robotKnowledgeConfig,"id");
                robotKnowledgeConfig.setType(1);
                robotKnowledgeConfig.setOperation(KnowledgeOperationEnum.INIT.getCode());
                robotKnowledgeConfigRefreshProdList.add(robotKnowledgeConfig);
            });
            robotKnowledgeConfigService.batchInsert(robotKnowledgeConfigRefreshProdList);
        }
    }*//*


    */
/**
    * @Description: 同步关键词
    * @Param: [releaseRecord, map]
    * @return: void
    * @Author: husw
    * @Date: 2023/6/14 17:05
    *//*

    */
/*private void syncManualServiceKeywords(RobotReleaseRecord releaseRecord, Map<String, Object> map) {
        List<RobotManualServiceKeywords> robotManualServiceKeywords = robotManualServiceKeywordsService.getMapper().selectAll(map);
        if (EmptyUtils.isNotEmpty(robotManualServiceKeywords)){
            Map<Integer, List<RobotManualServiceKeywords>> manualServiceKeywords = robotManualServiceKeywords.stream().collect(Collectors.groupingBy(RobotManualServiceKeywords::getType));
            List<RobotManualServiceKeywords> testManualServiceKeywords = manualServiceKeywords.get(EnvTypeEnum.TEST_ENV.getCode());
            List<RobotManualServiceKeywords> proManualServiceKeywords = manualServiceKeywords.get(EnvTypeEnum.PROD_ENV.getCode());
            releaseRecord.setManualServiceKeywords(proManualServiceKeywords);
            if (EmptyUtils.isNotEmpty(proManualServiceKeywords)){
                //删除之前的敏感词
                Map<String,Object> keywordsMap = new HashMap<>();
                keywordsMap.put("robotId",proManualServiceKeywords.get(0).getRobotId());
                keywordsMap.put("type", EnvTypeEnum.PROD_ENV.getCode());
                robotManualServiceKeywordsService.getMapper().deleteByRobotIdAndType(keywordsMap);
            }
            if(EmptyUtils.isNotEmpty(testManualServiceKeywords)) {
                //插入新的敏感词
                testManualServiceKeywords.forEach(x->{
                    x.setId(null);
                    x.setType(EnvTypeEnum.PROD_ENV.getCode());
                    x.setCreateTime(DateUtils.getCurrentDate());
                });
                robotManualServiceKeywordsService.batchInsert(testManualServiceKeywords);
            }
        }
    }*//*

    */
/**
    * @Description: 同步转人工
    * @Param: [releaseRecord, map]
    * @return: void
    * @Author: husw
    * @Date: 2023/6/14 17:05
    *//*

    */
/*private void syncManualServiceConfig(RobotReleaseRecord releaseRecord, Map<String, Object> map) {
        List<RobotManualServiceConfig> robotManualServiceConfigs = robotManualServiceConfigService.getMapper().selectAll(map);
        if (EmptyUtils.isEmpty(robotManualServiceConfigs) || robotManualServiceConfigs.size() == 0){
            //未配置转人工  则生成默认配置
            RobotManualServiceConfig saveReqVO = new RobotManualServiceConfig();
            saveReqVO.setRobotId((Long)map.get("robotId"));
            initManualConfig(saveReqVO);
            robotManualServiceConfigService.insert(saveReqVO);
            robotManualServiceConfigs.add(saveReqVO);
        }
        RobotManualServiceConfig robotManualServiceConfigTest = robotManualServiceConfigs.stream().filter(x -> (EnvTypeEnum.TEST_ENV.getCode().equals(x.getType()))).findFirst().orElse(new RobotManualServiceConfig());
        RobotManualServiceConfig robotManualServiceConfigProd = robotManualServiceConfigs.stream().filter(x -> (EnvTypeEnum.PROD_ENV.getCode().equals(x.getType()))).findFirst().orElse(null);
        checkManualServiceConfig(robotManualServiceConfigTest);
        releaseRecord.setManualServiceConfig(robotManualServiceConfigProd);
        if (EmptyUtils.isEmpty(robotManualServiceConfigProd)){
            robotManualServiceConfigTest.setId(null);
            robotManualServiceConfigTest.setType(EnvTypeEnum.PROD_ENV.getCode());
            robotManualServiceConfigService.insert(robotManualServiceConfigTest);
        }else {
            BeanUtils.copyProperties(robotManualServiceConfigTest,robotManualServiceConfigProd,"id");
            robotManualServiceConfigProd.setType(EnvTypeEnum.PROD_ENV.getCode());
            robotManualServiceConfigService.updateByPrimaryKey(robotManualServiceConfigProd);
        }
    }*//*

    */
/**
    * @Description: 校验转人工配置数据
    * @Param: [robotManualServiceConfig]
    * @return: void
    * @Author: husw
    * @Date: 2023/6/28 15:05
    *//*

    */
/*private void checkManualServiceConfig(RobotManualServiceConfig robotManualServiceConfig){
        boolean flag = YesNoEnum.YES.getValue().equals(robotManualServiceConfig.getNoReplyTransferStatus());
        if (YesNoEnum.YES.getValue().equals(robotManualServiceConfig.getClearTransferStatus())){
            flag = YesNoEnum.YES.getValue().equals(robotManualServiceConfig.getClearTransferStatus());
        }
        if (YesNoEnum.YES.getValue().equals(robotManualServiceConfig.getSameQuestionTransferStatus())){
            flag = true;
        }
        if (YesNoEnum.YES.getValue().equals(robotManualServiceConfig.getKeywordsTransferStatus())){
            flag = true;
        }
        if (flag && PlatAccessTypeEnum.WEB.getCode().equals(robotManualServiceConfig.getCustServPlatAccessType())
                && EmptyUtils.isEmpty(robotManualServiceConfig.getCustServPlatAddr())){
            throw new BusinessException("人工客服平台跳转地址不能为空！");
        }
    }*//*

    */
/**
    * @Description: 同步基础配置
    * @Param: [releaseRecord, map]
    * @return: void
    * @Author: husw
    * @Date: 2023/6/14 17:05
    *//*

    */
/*private void syncBasicConfig(RobotReleaseRecord releaseRecord, Map<String, Object> map) {
        List<RobotBasicConfig> robotBasicConfigs = robotBasicConfigService.getMapper().selectAll(map);
        if (EmptyUtils.isEmpty(robotBasicConfigs) || robotBasicConfigs.size()<= 0){
            throw new BusinessException("当前其机器人基础配置为空！");
        }
        RobotBasicConfig robotBasicConfigTest = robotBasicConfigs.stream().filter(x -> (EnvTypeEnum.TEST_ENV.getCode().equals(x.getType()))).findFirst().orElse(new RobotBasicConfig());
        RobotBasicConfig robotBasicConfigProd = robotBasicConfigs.stream().filter(x -> (EnvTypeEnum.PROD_ENV.getCode().equals(x.getType()))).findFirst().orElse(null);
        releaseRecord.setBasicConfig(robotBasicConfigProd);
        if (EmptyUtils.isEmpty(robotBasicConfigProd)){
            robotBasicConfigTest.setId(null);
            robotBasicConfigTest.setType(EnvTypeEnum.PROD_ENV.getCode());
            robotBasicConfigService.insert(robotBasicConfigTest);
        }else {
            BeanUtils.copyProperties(robotBasicConfigTest,robotBasicConfigProd,"id");
            robotBasicConfigProd.setType(EnvTypeEnum.PROD_ENV.getCode());
            robotBasicConfigService.updateByPrimaryKey(robotBasicConfigProd);
        }
    }*//*


    */
/*private void initManualConfig(RobotManualServiceConfig saveReqVO){
        saveReqVO.setClearTransferStatus(YesNoEnum.NO.getValue());
        saveReqVO.setKeywordsTransferStatus(YesNoEnum.NO.getValue());
        saveReqVO.setClearTransferStatus(YesNoEnum.NO.getValue());
        saveReqVO.setNoReplyTransferStatus(YesNoEnum.NO.getValue());
        saveReqVO.setCustServPlatAccessType(PlatAccessTypeEnum.SYSTEM.getCode());
        saveReqVO.setSameQuestionTransferStatus(YesNoEnum.NO.getValue());
        saveReqVO.setType(EnvTypeEnum.TEST_ENV.getCode());
    }*//*


    */
/*@Transactional(rollbackFor = Exception.class)
    public void trainModelCallback(MultipartFile file, Long robotId, String releaseRecordId) {
        log.info("rasa模型训练成功回调！");
        //上传RASA模型到OSS
        Result<FileUploadResponse> uploadResult = uploadService.uploadFileWeb(file, "rasaModel");
        //替换RASA模型
        //rasa3xService.replaceModel(uploadResult.getDatas().getAbsolutePath());
        //发布成功改变机器人状态
        Robot robot = selectByPrimaryKey(robotId);
        RobotReleaseRecord releaseRecord = robotReleaseRecordService.findById(releaseRecordId);
        releaseRecord.setRasaModelOSSFileId(uploadResult.getDatas().getFileId());
        completeRelease(robot, releaseRecord);
    }*//*


    */
/*@Transactional(rollbackFor = Exception.class)
    public void trainModelCallbackFailure(TrainRasaModelFailureReqVO reqVO, Long robotId, String releaseRecordId) {
        log.info("rasa模型训练失败回调！");
        //发布失败改变机器人状态
        Robot robot = selectByPrimaryKey(robotId);
        RobotReleaseRecord releaseRecord = robotReleaseRecordService.findById(releaseRecordId);
        releaseRecord.setErrorMsg(reqVO.getReason());
        releaseRecord.setTrainRasaModelFailure(reqVO);
        releaseFailure(robot, releaseRecord);
    }*//*

}
*/
