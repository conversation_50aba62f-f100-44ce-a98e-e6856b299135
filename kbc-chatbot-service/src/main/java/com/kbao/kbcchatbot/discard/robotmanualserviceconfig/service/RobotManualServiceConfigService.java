package com.kbao.kbcchatbot.discard.robotmanualserviceconfig.service;

import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.discard.robot.enums.RobotStatusEnum;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.discard.robot.vo.RobotUpdateStatusVO;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.dao.RobotManualServiceConfigMapper;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.entity.RobotManualServiceConfig;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.vo.RobotManualServiceConfigDetailVO;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.vo.RobotManualServiceConfigGetReqVO;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.vo.RobotManualServiceConfigSaveReqVO;
import com.kbao.kbcchatbot.discard.robotmanualservicekeywords.entity.RobotManualServiceKeywords;
import com.kbao.kbcchatbot.discard.robotmanualservicekeywords.service.RobotManualServiceKeywordsService;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: kbc-chatbot
 * @description: 转人工配置
 * @author: husw
 * @create: 2023-05-17 14:09
 **/
@Service
@Slf4j
public class RobotManualServiceConfigService extends BaseSQLServiceImpl<RobotManualServiceConfig,Long, RobotManualServiceConfigMapper> {

    @Autowired
    private RobotManualServiceKeywordsService robotManualServiceKeywordsService;

/*    @Autowired
    @Lazy
    private RobotService robotService;*/
    /**
    * @Description: 保存转人工配置
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/17 15:12
    */
    @Transactional(rollbackFor = Exception.class)
    public void save(RobotManualServiceConfigSaveReqVO param) {
        Map<String,Object> map = new HashMap<>();
        map.put("robotId",param.getRobotId());
//        map.put("type", EnvTypeEnum.TEST_ENV.getCode());
        map.put("type", EnvTypeEnum.PROD_ENV.getCode());
        List<RobotManualServiceConfig> robotManualServiceConfigs = this.mapper.selectAll(map);
        RobotManualServiceConfig robotManualServiceConfig = new RobotManualServiceConfig();
        BeanUtils.copyProperties(param,robotManualServiceConfig);
//        robotManualServiceConfig.setType(EnvTypeEnum.TEST_ENV.getCode());
        robotManualServiceConfig.setType(EnvTypeEnum.PROD_ENV.getCode());
        List<String> keywords = param.getKeywords();
        robotManualServiceKeywordsService.getMapper().deleteByRobotIdAndType(map);
        if (YesNoEnum.YES.getValue().equals(param.getKeywordsTransferStatus())){
            List<RobotManualServiceKeywords> list = new ArrayList<>();
            if (EmptyUtils.isEmpty(param.getKeywords())){
                throw new BusinessException("关键词不能为为空!");
            }
            keywords.forEach(x->{
                RobotManualServiceKeywords manualServiceKeywords = new RobotManualServiceKeywords();
                manualServiceKeywords.setRobotId(param.getRobotId());
//                manualServiceKeywords.setType(EnvTypeEnum.TEST_ENV.getCode());
                manualServiceKeywords.setType(EnvTypeEnum.PROD_ENV.getCode());
                manualServiceKeywords.setKeywords(x);
                manualServiceKeywords.setCreateId(BscUserUtils.getUserId());
                manualServiceKeywords.setCreateTime(DateUtils.getCurrentDate());
                list.add(manualServiceKeywords);
            });
            robotManualServiceKeywordsService.batchInsert(list);
        }
        //将机器人状态变更为编辑中
        RobotUpdateStatusVO vo = new RobotUpdateStatusVO();
        vo.setId(param.getRobotId());
//        vo.setStatus(RobotStatusEnum.EDITING.getCode());
        vo.setStatus(RobotStatusEnum.PUBLISHED.getCode());
        //robotService.updateStatus(vo);

        if (EmptyUtils.isNotEmpty(robotManualServiceConfigs)){
            robotManualServiceConfig.setId(robotManualServiceConfigs.get(0).getId());
            this.mapper.updateByPrimaryKey(robotManualServiceConfig);
        } else {
            this.mapper.insert(robotManualServiceConfig);
        }
    }

    /**
    * @Description: 查询转人工配置
    * @Param: [param]
    * @return: com.kbao.kbcchatbot.robotmanualserviceconfig.entity.RobotManualServiceConfig
    * @Author: husw
    * @Date: 2023/5/17 15:12
    */
    public RobotManualServiceConfigDetailVO get(RobotManualServiceConfigGetReqVO param) {
        param.setType(EnvTypeEnum.PROD_ENV.getCode());
        Map<String, Object> paramMap = MapUtils.objectToMap(param);
        List<RobotManualServiceConfig> robotManualServiceConfigs = this.mapper.selectAll(paramMap);
        if (EmptyUtils.isEmpty(robotManualServiceConfigs)){
            return null;
        }
        RobotManualServiceConfigDetailVO detailVo = new RobotManualServiceConfigDetailVO();
        RobotManualServiceConfig robotManualServiceConfig = new RobotManualServiceConfig();
        if (robotManualServiceConfigs.size()>1){
            robotManualServiceConfig = robotManualServiceConfigs.stream().filter(x -> x.getType().equals(param.getType())).findFirst().orElse(robotManualServiceConfig);
        }else {
            robotManualServiceConfig = robotManualServiceConfigs.get(0);
        }
        BeanUtils.copyProperties(robotManualServiceConfig,detailVo);
        List<RobotManualServiceKeywords> robotManualServiceKeywords = robotManualServiceKeywordsService.getMapper().selectAll(paramMap);
        if (EmptyUtils.isNotEmpty(robotManualServiceKeywords)){
            List<String> keywords = robotManualServiceKeywords.stream().map(RobotManualServiceKeywords::getKeywords).collect(Collectors.toList());
            detailVo.setKeywords(keywords);
        }
        return detailVo;
    }
}
