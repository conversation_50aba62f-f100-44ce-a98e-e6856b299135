package com.kbao.kbcchatbot.discard.insurancecompanyrule.service;

import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.discard.insurancecompanyrule.dao.InsuranceCompanyRuleMapper;
import com.kbao.tool.util.StringUtil;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import com.kbao.tool.util.DateUtils;
import com.kbao.kbcchatbot.discard.insurancecompanyrule.entity.InsuranceCompanyRule;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 保险公司回访规则Service类
 * @Date 2023-11-07
 */
@Service
public class InsuranceCompanyRuleService extends BaseSQLServiceImpl<InsuranceCompanyRule, Long, InsuranceCompanyRuleMapper> {

    @Transactional(rollbackFor = Exception.class)
    public int insert(InsuranceCompanyRule insuranceCompanyRule) {
        String currentUserId = BscUserUtils.getUserId();
        insuranceCompanyRule.setUpdateTime(DateUtils.getCurrentDate());
        insuranceCompanyRule.setUpdateId(currentUserId);
        insuranceCompanyRule.setCreateTime(DateUtils.getCurrentDate());
        insuranceCompanyRule.setCreateId(currentUserId);
        return this.mapper.insert(insuranceCompanyRule);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(InsuranceCompanyRule insuranceCompanyRule) {
        insuranceCompanyRule.setUpdateTime(DateUtils.getCurrentDate());
        insuranceCompanyRule.setUpdateId(BscUserUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(insuranceCompanyRule);
    }


    /**
     * 根据公司名称查询 公司回访规则
     *
     * @param companyName 公司名称
     * @return List<InsuranceCompanyRule>
     */
    public List<InsuranceCompanyRule> getInsuranceCompanyRuleByName(String companyName) {

        if (StringUtil.isBlank(companyName)) {
            return null;
        }

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("companyName", companyName);
        return selectByParam(queryMap);
    }
}
