package com.kbao.kbcchatbot.discard.insurancecompany.service;

import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import com.kbao.tool.util.DateUtils;
import com.kbao.kbcchatbot.discard.insurancecompany.entity.InsuranceCompany;
import com.kbao.kbcchatbot.discard.insurancecompany.dao.InsuranceCompanyMapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 保险公司Service类
 * @Date 2023-11-07
 */
@Service
public class InsuranceCompanyService extends BaseSQLServiceImpl<InsuranceCompany, Long, InsuranceCompanyMapper> {

    @Transactional(rollbackFor = Exception.class)
    public int insert(InsuranceCompany insuranceCompany) {
        String currentUserId = BscUserUtils.getUserId();
        insuranceCompany.setUpdateTime(DateUtils.getCurrentDate());
        insuranceCompany.setUpdateId(currentUserId);
        insuranceCompany.setCreateTime(DateUtils.getCurrentDate());
        insuranceCompany.setCreateId(currentUserId);
        return this.mapper.insert(insuranceCompany);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(InsuranceCompany insuranceCompany) {
        insuranceCompany.setUpdateTime(DateUtils.getCurrentDate());
        insuranceCompany.setUpdateId(BscUserUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(insuranceCompany);
    }

    /**
     * 根据公司名称查询公司信息
     *
     * @param companyName 公司名称
     * @return InsuranceCompany
     */
    public InsuranceCompany getInsuranceCompanyByName(String companyName) {

        if (StringUtil.isBlank(companyName)) {
            return null;
        }

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("companyName", companyName);
        List<InsuranceCompany> insuranceCompanies = selectByParam(queryMap);
        if (CollectionUtils.isNotEmpty(insuranceCompanies)) {
            return insuranceCompanies.get(0);
        }
        return null;
    }
}
