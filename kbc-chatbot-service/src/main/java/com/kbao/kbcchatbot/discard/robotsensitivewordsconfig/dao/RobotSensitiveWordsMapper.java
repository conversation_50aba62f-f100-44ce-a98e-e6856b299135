package com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.entity.RobotSensitiveWords;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【t_robot_sensitive_words(聊天机器人转人工敏感词表)】的数据库操作Mapper
* @createDate 2023-07-03 18:04:51
* @Entity generator.domain.RobotSensitiveWords
*/
public interface RobotSensitiveWordsMapper extends BaseMapper<RobotSensitiveWords, Long> {


    int selectCountByWords(@Param("words") String words, @Param("robotId") Long robotId);

}
