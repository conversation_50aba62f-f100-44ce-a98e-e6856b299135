package com.kbao.kbcchatbot.discard.knowledgepackage.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @Description 知识包Dao类
* @Date 2023-05-23
*/
public interface KnowledgePackageMapper  extends BaseMapper<KnowledgePackage, Long>{

    int count(Map<String, Object> param);

    List<String> getPackageCodeListByRobotId(@Param("robotId") Long robotId);

}