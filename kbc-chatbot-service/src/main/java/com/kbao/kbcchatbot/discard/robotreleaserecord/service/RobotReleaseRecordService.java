package com.kbao.kbcchatbot.discard.robotreleaserecord.service;

import com.github.pagehelper.PageInfo;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcchatbot.discard.robotreleaserecord.dao.RobotReleaseRecordDao;
import com.kbao.kbcchatbot.discard.robotreleaserecord.entity.RobotReleaseRecord;
import com.kbao.kbcchatbot.discard.robotreleaserecord.enums.RobotReleaseStatusEnum;
import com.kbao.kbcchatbot.discard.robotreleaserecord.vo.RobotReleaseRecordIdReqVO;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

/**
 * @program: kbc-chatbot
 * @description: 发布记录
 * @author: husw
 * @create: 2023-05-18 09:44
 **/
@Service
@Slf4j
public class RobotReleaseRecordService extends BaseMongoServiceImpl<RobotReleaseRecord,String, RobotReleaseRecordDao> {

    /**
    * @Description: 分页查询
    * @Param: [pageRequest]
    * @return: com.github.pagehelper.PageInfo<com.kbao.kbcchatbot.robotreleaserecord.entity.RobotReleaseRecord>
    * @Author: husw
    * @Date: 2023/5/30 17:44
    */
    public PageInfo<RobotReleaseRecord> page(RequestObjectPage<RobotReleaseRecordIdReqVO> pageRequest) {
        Query query = new Query();
        query.addCriteria(Criteria.where("robotId").is(pageRequest.getParam().getRobotId()));
        query.fields().include("id","robotId","publishTime","publisher","version","status","errorMsg");
        Pagination<RobotReleaseRecord> pagination = new Pagination<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        pagination.setSort(EmptyUtils.isEmpty(pageRequest.getSort()) ? "publishTime desc" : pageRequest.getSort());
        return this.page(query, pagination);
    }

    public RobotReleaseRecord findModelsByRobotId(Long robotId, String releaseRecordId) {
        Query query = new Query();
        if(StringUtil.isNotEmpty(releaseRecordId)) {
            RobotReleaseRecord currentReleaseRecord = findById(releaseRecordId);
            if(currentReleaseRecord != null) {
                query.addCriteria(Criteria.where("id").ne(currentReleaseRecord.getId()));
                if(currentReleaseRecord.getPublishTime() != null) {
                    query.addCriteria(Criteria.where("publishTime").gt(currentReleaseRecord.getPublishTime()));
                }
            }
        }
        query.addCriteria(Criteria.where("robotId").is(robotId));
        query.addCriteria(Criteria.where("status").is(RobotReleaseStatusEnum.SUCCESS.getCode()));
        query.addCriteria(Criteria.where("rasaModelOSSFileId").exists(true));
        query.limit(1);
        query.with(Sort.by(Sort.Direction.DESC, "publishTime"));
        return findOne(query);
    }

}
