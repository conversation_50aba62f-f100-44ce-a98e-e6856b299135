package com.kbao.kbcchatbot.discard.chatauth.service;

import com.kbao.kbcbsc.redis.util.RedisUtil;
//import com.kbao.kbcchatbot.channel.service.ChannelService;
import com.kbao.kbcchatbot.maas.chatsession.service.ChatSessionApiService;
import com.kbao.kbcchatbot.discard.robotbasicconfig.service.RobotBasicConfigService;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.service.RobotManualServiceConfigService;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.service.RobotSensitiveWordsConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: kbc-chatbot
 * @description: 机器人配置
 * @author: husw
 * @create: 2023-06-09 17:11
 **/
@Service
@Slf4j
public class ChatAuthApiService {

//    @Autowired
//    private ChannelService channelService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RobotBasicConfigService robotBasicConfigService;

    @Autowired
    private ChatSessionApiService chatSessionApiService;

    @Autowired
    private RobotManualServiceConfigService robotManualServiceConfigService;

    @Autowired
    private RobotSensitiveWordsConfigService robotSensitiveWordsConfigService;


    /**
     * @Description: 获取渠道key
     * @Param: [req]
     * @return: com.kbao.kbcchatbot.utils.cripto.bean.CryptoRSAKeyPair
     * @Author: husw
     * @Date: 2023/9/4 11:05
     */
/*    public CryptoRSAKeyPair getKey(ChatAuthKeyReqVO req) {
        //查询渠道信息
        Channel channel = channelService.getMapper().selectKeyByCode(req.getChannelCode());
        if (EmptyUtils.isEmpty(channel)){
            throw new BusinessException("暂无渠道信息,请先申请授权!");
        }
        CryptoRSAKeyPair keyPair = new CryptoRSAKeyPair();
        //AES加密内容
        AES aes = SecureUtil.aes(req.getKey().getBytes());
        String encryptContent = aes.encryptBase64(channel.getPublicKey());
        keyPair.setPublicKey(encryptContent);
        return keyPair;
    }*/

    /**
     * @Description: 创建授权码
     * @Param: [req]
     * @return: com.kbao.kbcchatbot.robotbasicconfig.vo.RobotAuthCodeVO
     * @Author: husw
     * @Date: 2023/6/12 9:37
     */
    /*public RobotAuthCodeVO createInitToken(ChatAuthTokenReqVO req) {
        //查询渠道信息
        Channel channel = channelService.getMapper().selectKeyByCode(req.getChannelCode());
        if (EmptyUtils.isEmpty(channel)){
            throw new BusinessException("渠道编码错误，授权失败！");
        }
        CryptoData cryptoData = new CryptoData();
        cryptoData.setKey(req.getKey());
        cryptoData.setContent(req.getContent());
        String decrypt;
        try{
            decrypt = CryptoUtil.decrypt(cryptoData, channel.getPrivateKey());
        }catch (Exception e){
            log.error("授权失败原因：{}",e.getMessage(),e);
            throw new BusinessException("授权失败！");
        }
        if (decrypt.split(",").length !=3){
            throw new BusinessException("请求参数数量错误!");
        }
        if (!YesNoEnum.YES.getValue().toString().equals(channel.getStatus())){
            throw new BusinessException("渠道未启用!");
        }

        Robot robot = robotService.getMapper().selectByPrimaryKey(channel.getRobotId());
        if (EmptyUtils.isEmpty(robot)){
            throw new BusinessException("渠道不存在可服务机器人!");
        }
        String userId = decrypt.split(",")[0];
        String userName = decrypt.split(",")[1];
        String timestamp = decrypt.split(",")[2];
        if(EmptyUtils.isEmpty(userId)){
            throw new BusinessException("未获取到用户ID");
        }
//        if(EmptyUtils.isEmpty(userName)){
//            throw new BusinessException("未获取到用户姓名");
//        }
        log.info("授权用户信息：{}-{}",userId,userName);

        //请求时间戳校验，时间戳和系统时间不能大于5分钟
        if (System.currentTimeMillis() - Long.parseLong(timestamp) > 5 * 60 * 1000) {
            throw new BusinessException("请求时间戳与系统时间相差过大，请重新请求");
        }
        //生成渠道专属授权码
        ConfigCacheVO configCacheVO = new ConfigCacheVO();
        BeanUtils.copyProperties(req, configCacheVO);
        configCacheVO.setUserId(userId);
        configCacheVO.setUserName(userName);
        configCacheVO.setChannelId(channel.getId());
        configCacheVO.setVersion(robot.getVersion());
        configCacheVO.setRobotId(channel.getRobotId());
        configCacheVO.setRobotCode(robot.getCode());
        configCacheVO.setTenantId(channel.getTenantId());

        ChatSessionCache chatSessionCache = initChatConfig(configCacheVO.getChannelId());
        configCacheVO.setChatSessionCache(chatSessionCache);

        //创建session会话
        ChatSession session = chatSessionApiService.createSession(configCacheVO);
        configCacheVO.setSessionId(session.getSessionId());

        String redisKey = redisUtil.generateKey(MessageFormat.format(CHAT_TOKEN, session.getSessionId()));
        boolean set = redisUtil.set(redisKey, configCacheVO, SESSION_MAX_TIME);
        if (set) {
            return new RobotAuthCodeVO(session.getSessionId());
        }
        throw new BusinessException("获取授权码失败，请重试！");
    }*/


    /**
     * @Description: 缓存会话配置实体初始化
     * @Param: [robotId]
     * @return: com.kbao.kbcchatbot.chatsession.bean.ChatSessionCache
     * @Author: husw
     * @Date: 2023/6/13 9:41
     */
    /*public ChatSessionCache initChatConfig(Long channelId){
        //查询渠道
        Channel channel = channelService.selectByPrimaryKey(channelId);
        if (EmptyUtils.isEmpty(channel)){
            throw new BusinessException("当前渠道不存在！");
        }
        //查询渠道关联机器人
        RobotBasicConfig detailCache = robotBasicConfigService.getDetail(channel.getRobotId());
        if (EmptyUtils.isEmpty(detailCache)){
            throw new BusinessException("机器人基础配置为空！");
        }
        ChatSessionCache chatSessionCache = new ChatSessionCache();
        chatSessionCache.setDirectReplyThreshold(detailCache.getDirectReplyThreshold());
        if (YesNoEnum.NO.getValue().equals(detailCache.getClearStatus())){
            chatSessionCache.setClearReplyThreshold(detailCache.getDirectReplyThreshold());
            chatSessionCache.setClearMaxCount(1);
        }else {
            chatSessionCache.setClearReplyThreshold(detailCache.getClearThreshold());
            chatSessionCache.setClearMaxCount(detailCache.getClearMaxNumber());
        }

        if (EmptyUtils.isNotEmpty(detailCache.getNoAnswerReply())){
            chatSessionCache.setNoAnswerContent(new String(detailCache.getNoAnswerReply(), StandardCharsets.UTF_8));
        }
        if (EmptyUtils.isNotEmpty(detailCache.getClearReply())){
            chatSessionCache.setClearContent(new String(detailCache.getClearReply(),StandardCharsets.UTF_8));
        }
        if (EmptyUtils.isNotEmpty(detailCache.getSensitiveWordsReply())){
            chatSessionCache.setSensitiveWordsReply(new String(detailCache.getSensitiveWordsReply(),StandardCharsets.UTF_8));
        }
        //查询机器人转人工配置
        RobotManualServiceConfigGetReqVO param = new RobotManualServiceConfigGetReqVO();
        param.setRobotId(channel.getRobotId());
        param.setType(EnvTypeEnum.PROD_ENV.getCode());
        RobotManualServiceConfigDetailVO robotManualServiceConfigDetailVO = robotManualServiceConfigService.get(param);
        if (EmptyUtils.isEmpty(robotManualServiceConfigDetailVO)){
            return chatSessionCache;
        }
        //关键词转人工
        if (YesNoEnum.YES.getValue().equals(robotManualServiceConfigDetailVO.getKeywordsTransferStatus())){
            chatSessionCache.setKeywords(robotManualServiceConfigDetailVO.getKeywords());
        }
        //无回复转人工
        if (YesNoEnum.YES.getValue().equals(robotManualServiceConfigDetailVO.getNoReplyTransferStatus())){
            chatSessionCache.setNoReplyTransferTimes(robotManualServiceConfigDetailVO.getNoReplyTransferTimes());
        }
        //澄清重复转人工
        if (YesNoEnum.YES.getValue().equals(robotManualServiceConfigDetailVO.getClearTransferStatus())){
            chatSessionCache.setClearTransferTimes(robotManualServiceConfigDetailVO.getClearTransferTimes());
        }
        //用户重复提问转人工
        if (YesNoEnum.YES.getValue().equals(robotManualServiceConfigDetailVO.getSameQuestionTransferStatus())){
            chatSessionCache.setSameQuestionTransferTimes(robotManualServiceConfigDetailVO.getSameQuestionTransferTimes());
        }
        chatSessionCache.setCustServPlatAccessType(robotManualServiceConfigDetailVO.getCustServPlatAccessType());
        chatSessionCache.setCustServPlatAddr(robotManualServiceConfigDetailVO.getCustServPlatAddr());
        //获取敏感词
        Map<String,Object> map = new HashMap<>();
        map.put("robotId",channel.getRobotId());
        List<RobotSensitiveWords> robotSensitiveWords = robotSensitiveWordsConfigService.getMapper().selectAll(map);
        List<String> sensitiveWords = robotSensitiveWords.stream().map(RobotSensitiveWords::getWords).collect(Collectors.toList());
        chatSessionCache.setSensitiveWords(sensitiveWords);
        return chatSessionCache;
    }*/
}
