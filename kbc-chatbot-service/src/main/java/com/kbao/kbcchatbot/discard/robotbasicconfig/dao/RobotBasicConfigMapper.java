package com.kbao.kbcchatbot.discard.robotbasicconfig.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.discard.robotbasicconfig.entity.RobotBasicConfig;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_robot_basic_config(聊天机器人基础配置表)】的数据库操作Mapper
* @createDate 2023-05-16 15:08:26
*/
public interface RobotBasicConfigMapper extends BaseMapper<RobotBasicConfig,Long> {

    List<RobotBasicConfig> selectByRobotId(Long robotId);
}
