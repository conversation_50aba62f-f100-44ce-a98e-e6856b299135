package com.kbao.kbcchatbot.discard.chatorder.service;

import com.kbao.kbcchatbot.kbc.olpt.KbcOlptService;
import com.kbao.kbcchatbot.kbc.uoc.KbcUocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: kbc-chatbot
 * @description: 订单
 * @author: husw
 * @create: 2023-12-08 14:09
 **/
@Service
public class ChatOrderService {

    @Autowired
    private KbcUocService kbcUocService;

    @Autowired
    private KbcOlptService kbcOlptService;

//    public void page(@RequestBody RequestObjectPage<UocPolicyInfoReq> req) {

//        PageInfo<UocPolicyInfoResp> uocPolicyInfo = kbcUocService.getUocPolicyInfo(req);
//        //短险判断是否有回访（长期医疗险才有）
//        if (EmptyUtils.isNotEmpty(uocPolicyInfo.getList()) && EmptyUtils.isNotEmpty(req.getParam().getOrderTypeList()) &&
//                req.getParam().getOrderTypeList().contains(Integer.valueOf(OrderTypeEnum.SHORT.getType()))){
//            List<String> orderIds = uocPolicyInfo.getList().stream().map(UocPolicyInfoResp::getOrderId).collect(Collectors.toList());
//            List<ChitChatOrderVo> chitchatOrderList = kbcOlptService.findChitchatOrderList(orderIds, ChatUserUtil.getUser().getTenantId());
//            if (EmptyUtils.isEmpty(chitchatOrderList)){
//                return uocPolicyInfo;
//            }
//            uocPolicyInfo.getList().forEach(x -> {
//                for (ChitChatOrderVo orderVo : chitchatOrderList) {
//                    if (x.getOrderId().equals(orderVo.getOrderCode())) {
//                        if (!YesNoEnum.YES.getCodeString().equals(orderVo.getLongMedical())) {
//                            x.setVisitStatus("");
//                        }
//                    }
//                }
//            });
//        }
//        return uocPolicyInfo;
//    }
}
