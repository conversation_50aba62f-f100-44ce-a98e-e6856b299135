package com.kbao.kbcchatbot.discard.robotpackagerel.service;

import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.discard.robotpackagerel.dao.RobotPackageRelMapper;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import com.kbao.tool.util.DateUtils;
import com.kbao.kbcchatbot.discard.robotpackagerel.entity.RobotPackageRel;

/**
* <AUTHOR>
* @Description 机器人包关联Service类
* @Date 2023-06-02
*/
@Service
public class RobotPackageRelService extends BaseSQLServiceImpl<RobotPackageRel, Long, RobotPackageRelMapper> {

    @Transactional(rollbackFor = Exception.class)
    public int insert(RobotPackageRel robotPackageRel) {
        String currentUserId = BscUserUtils.getUserId();
        String nowStr = DateUtils.thisDateTime();
        robotPackageRel.setCreateTime(nowStr);
        robotPackageRel.setCreateId(currentUserId);
        return this.mapper.insert(robotPackageRel);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(RobotPackageRel robotPackageRel) {
        return this.mapper.updateByPrimaryKeySelective(robotPackageRel);
    }

}
