package com.kbao.kbcchatbot.discard.chatauth.job;

import com.alibaba.fastjson.JSON;
import com.kbao.job.core.biz.model.ReturnT;
import com.kbao.job.core.handler.annotation.XxlJob;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcchatbot.maas.chatsession.entity.ChatSession;
import com.kbao.kbcchatbot.maas.chatsession.service.ChatSessionService;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: kbc-chatbot
 * @description: 会话定时任务
 * @author: husw
 * @create: 2023-06-14 13:55
 **/
@Slf4j
@Service
public class ChatAuthJob {

    @Autowired
    private ChatSessionRecordService chatSessionRecordService;

    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private RedisUtil redisUtil;


    private static final int addMinute = -1;
    /**
    * @Description: 关闭会话并统计接待明细
    * @Param: [param]
    * @return: com.kbao.job.core.biz.model.ReturnT<java.lang.String>
    * @Author: husw
    * @Date: 2023/6/28 16:39
    */
    @XxlJob("statisticsChatSession")
    public ReturnT<String> statisticsChatSession(String param){
        //查询全部未关闭的会话
//        Map<String,Object> map = new HashMap<>();
//        String date = EmptyUtils.isEmpty(param) ? DateUtils.thisDate() : param;
//        map.put("startDate", date);
        List<ChatSession> chatSessions = chatSessionService.getMapper().selectUpdateSession();
        if(EmptyUtils.isEmpty(chatSessions)){
            return new ReturnT<>(ReturnT.SUCCESS_CODE,"当天暂无会话");
        }
        //查询30分钟内没有会话记录的数据
        for (ChatSession chatSession:chatSessions){
            //更新统计接待明细数据
            chatSessionService.updateChatSession(chatSession,param);
        }
        String sessionIds = chatSessions.stream().map(ChatSession::getSessionId).collect(Collectors.joining(","));
        Map<String, Object> xxlmap = new HashMap<>(2);
        xxlmap.put("关闭会话sessionId：",sessionIds);
        return new ReturnT<>(ReturnT.SUCCESS_CODE, JSON.toJSONString(xxlmap));
    }

}
