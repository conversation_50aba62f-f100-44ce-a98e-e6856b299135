package com.kbao.kbcchatbot.discard.knowledgepackage.dao;

import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackageQA;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 知识包内容Repository类
 * @Date 2023-05-25
 */
@Repository
public interface KnowledgePackageQARepository extends ElasticsearchRepository<KnowledgePackageQA, String> {

    List<KnowledgePackageQA> findByRobotIdAndEnvironmentAndSecondDirect(Long robotId, Integer environment, String secondDirect);
    void deleteAllByRobotIdAndEnvironmentAndSecondDirect(Long robotId,Integer environment,String secondDirect);

    KnowledgePackageQA findByRobotIdAndEnvironmentAndKnowledgeId(Long robotId, Integer environment, String knowledgeId);
}
