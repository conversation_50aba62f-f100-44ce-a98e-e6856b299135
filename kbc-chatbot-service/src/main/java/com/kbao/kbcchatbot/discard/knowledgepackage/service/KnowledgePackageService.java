package com.kbao.kbcchatbot.discard.knowledgepackage.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.discard.knowledgepackage.dao.KnowledgePackageMapper;
import com.kbao.kbcchatbot.discard.knowledgepackage.dao.KnowledgePackageQARepository;
import com.kbao.kbcchatbot.elasticsearch.search.BaseSearchService;
import com.kbao.kbcchatbot.elasticsearch.search.bean.SearchQuery;
import com.kbao.kbcchatbot.discard.knowledgepackage.bean.KnowledgePackageQAImportVO;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackage;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackageQA;
import com.kbao.kbcchatbot.utils.easyexcel.EasyExcelUtil;
import com.kbao.kbcchatbot.utils.easyexcel.callback.EasyExcelCallBack;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.StringUtil;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @Description 知识包Service类
* @Date 2023-05-23
*/
@Service
@Slf4j
public class KnowledgePackageService extends BaseSQLServiceImpl<KnowledgePackage, Long, KnowledgePackageMapper> {

    @Autowired
    private KnowledgePackageQARepository knowledgePackageQARepository;

    @Autowired
    private ElasticsearchOperations elasticsearchOperations;

    @Autowired
    private BaseSearchService baseSearchService;

    @Transactional(rollbackFor = Exception.class)
    public int insert(KnowledgePackage knowledgePackage) {
        String currentUserId = BscUserUtils.getUserId();
        String nowStr = DateUtils.thisDateTime();
        knowledgePackage.setCreateTime(nowStr);
        knowledgePackage.setCreateId(currentUserId);
        knowledgePackage.setUpdateTime(nowStr);
        knowledgePackage.setUpdateId(currentUserId);
        knowledgePackage.setIsDeleted(0);
        return this.mapper.insertSelective(knowledgePackage);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(KnowledgePackage knowledgePackage) {
        knowledgePackage.setUpdateTime(DateUtils.thisDateTime());
        knowledgePackage.setUpdateId(BscUserUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(knowledgePackage);
    }

    @Transactional(rollbackFor = Exception.class)
    public void uploadPackage(MultipartFile file, String packageDTOJson) {
        KnowledgePackage knowledgePackage = JSONObject.toJavaObject(JSONObject.parseObject(packageDTOJson), KnowledgePackage.class);
        validateKnowledgePackage(knowledgePackage);
        if(knowledgePackage.getId() == null) {
            insert(knowledgePackage);
        }else {
            Query query = new NativeSearchQueryBuilder().withQuery(QueryBuilders.termsQuery("packageCode", knowledgePackage.getCode())).build();
            elasticsearchOperations.delete(query, KnowledgePackageQA.class);
            update(knowledgePackage);
        }
        String nowStr = DateUtils.thisDateTime();
        EasyExcelUtil.read(file, KnowledgePackageQAImportVO.class, new EasyExcelCallBack() {
            @Override
            public void doAnalysedValidate(Object o, Integer rowIndex) {
                KnowledgePackageQAImportVO importVO = new KnowledgePackageQAImportVO();
                BeanUtils.copyProperties(o, importVO);
                if (StringUtils.isBlank(importVO.getQuestion())) {
                    throw new BusinessException("第" + (rowIndex + 1) + "问题不能为空");
                }
                if (StringUtils.isBlank(importVO.getAnswer())) {
                    throw new BusinessException("第" + (rowIndex + 1) + "答案不能为空");
                }
                KnowledgePackageQA packageQA = new KnowledgePackageQA();
                BeanUtils.copyProperties(importVO, packageQA);
                if(StringUtil.isNotEmpty(importVO.getSimilarQuestionsStr())) {
                    String[] similarQuestionArr = importVO.getSimilarQuestionsStr().split("###");
                    packageQA.setSimilarQuestions(Arrays.asList(similarQuestionArr));
                }
                packageQA.setCreateTime(nowStr);
                packageQA.setTenantId(SysLoginUtils.getUser().getTenantId());
                packageQA.setPackageCode(knowledgePackage.getCode());
                packageQA.setQaId(IdWorker.get32UUID());
                knowledgePackageQARepository.save(packageQA);
            }
            @Override
            public void doAfterAllAnalysed(List<Object> list) {
                log.info("累计导入：" + list.size() + "条数据");
            }
        });
    }

    private void validateKnowledgePackage(KnowledgePackage knowledgePackage) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", knowledgePackage.getCode());
        param.put("idNotEq", knowledgePackage.getId());
        int count = this.mapper.count(param);
        if(count > 0) {
            throw new BusinessException("知识包编码已存在");
        }
    }

    public SearchHits search(SearchQuery searchQuery) {
        SearchHits searchHits = baseSearchService.search(KnowledgePackageQA.class, searchQuery, 100,null, Operator.OR.name());
        return searchHits;
    }

    public List<String> getPackageCodeListByRobotId(Long robotId) {
        return this.mapper.getPackageCodeListByRobotId(robotId);
    }
}
