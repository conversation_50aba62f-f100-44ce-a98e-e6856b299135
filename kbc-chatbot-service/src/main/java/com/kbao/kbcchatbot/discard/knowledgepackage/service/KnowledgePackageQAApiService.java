package com.kbao.kbcchatbot.discard.knowledgepackage.service;

import com.kbao.kbcchatbot.discard.knowledgepackage.dao.KnowledgePackageQARepository;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatSearchResultDTO;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerSourceEnum;
import com.kbao.kbcchatbot.elasticsearch.search.BaseSearchService;
import com.kbao.kbcchatbot.elasticsearch.search.bean.BusinessQueryDTO;
import com.kbao.kbcchatbot.elasticsearch.search.bean.SearchQuery;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackageQA;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums.KnowledgeStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.kbao.kbcchatbot.constants.ElasticsearchConstants.BUSINESS_QUERY_OPERATOR_EQUALS;

/**
 * <AUTHOR>
 * @Description 知识包Api Service类
 * @Date 2023-05-23
 */
@Service
@Slf4j
public class KnowledgePackageQAApiService {

    /*@Autowired
    private KnowledgePackageService knowledgePackageService;*/

    @Autowired
    private BaseSearchService baseSearchService;

    @Autowired
    private KnowledgePackageQARepository knowledgePackageQARepository;

    public List<ChatSearchResultDTO> queryAnswer(String question, Long robotId, String indId, int maxCount, String conditionConcat,List<String> visibleSecondDirectIds) {
        /*//过滤知识包业务条件
        List<String> packageCodeList = knowledgePackageService.getPackageCodeListByRobotId(robotId);
        List<BusinessQueryDTO> businessQueryDTOList = new ArrayList<>();
        BusinessQueryDTO businessQueryDTO = new BusinessQueryDTO();
        businessQueryDTO.setColumnName("packageCode");
        businessQueryDTO.setOperator(ElasticsearchConstants.BUSINESS_QUERY_OPERATOR_EQUALS);
        businessQueryDTO.setValue(packageCodeList);
        businessQueryDTOList.add(businessQueryDTO);*/

        //检索知识包
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(question);
        searchQuery.setIndId(indId);
        searchQuery.setRobotId(robotId);
        SearchHits<KnowledgePackageQA> searchHits = baseSearchService.search(KnowledgePackageQA.class, searchQuery, maxCount, getBusinessQuery(robotId,visibleSecondDirectIds), conditionConcat);
        List<ChatSearchResultDTO> searchResultDTOList = new ArrayList<>();
        if(searchHits.getTotalHits() > 0) {
            searchHits.getSearchHits().forEach(searchHit -> {
                ChatSearchResultDTO searchResultDTO = new ChatSearchResultDTO();
                searchResultDTO.setQaId(searchHit.getContent().getQaId());
                searchResultDTO.setAnswer(searchHit.getContent().getAnswer());
                searchResultDTO.setSimilarQuestions(searchHit.getContent().getSimilarQuestions());
                searchResultDTO.setQuestion(searchHit.getContent().getQuestion());
                searchResultDTO.setQaSource(ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_QA.getKey());
                searchResultDTO.setKnowledgeId(searchHit.getContent().getKnowledgeId());
                searchResultDTO.setRelatedArticles(searchHit.getContent().getRelatedArticles());
                searchResultDTO.setSecondDirectId(searchHit.getContent().getSecondDirect());
                searchResultDTOList.add(searchResultDTO);
            });
        }
        return searchResultDTOList;
    }

    private List<BusinessQueryDTO> getBusinessQuery(Long robotId, List<String> secondDirects) {
        List<BusinessQueryDTO> businessQueryDTOList = new ArrayList<>();
        BusinessQueryDTO businessQueryDTO1 = new BusinessQueryDTO();
        businessQueryDTO1.setColumnName("robotId");
        businessQueryDTO1.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
        businessQueryDTO1.setValue(robotId);
        businessQueryDTOList.add(businessQueryDTO1);
        BusinessQueryDTO businessQueryDTO2 = new BusinessQueryDTO();
        businessQueryDTO2.setColumnName("environment");
        businessQueryDTO2.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
        businessQueryDTO2.setValue(EnvTypeEnum.PROD_ENV.getCode());
        businessQueryDTOList.add(businessQueryDTO2);
        BusinessQueryDTO businessQueryDTO3 = new BusinessQueryDTO();
        businessQueryDTO3.setColumnName("isDeleted");
        businessQueryDTO3.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
        businessQueryDTO3.setValue("0");
        businessQueryDTOList.add(businessQueryDTO3);
        BusinessQueryDTO businessQueryDTO4 = new BusinessQueryDTO();
        businessQueryDTO4.setColumnName("state");
        businessQueryDTO4.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
        businessQueryDTO4.setValue(KnowledgeStateEnum.YES.getCode());
        businessQueryDTOList.add(businessQueryDTO4);
//        BusinessQueryDTO businessQueryDTO5 = new BusinessQueryDTO();
//        businessQueryDTO5.setColumnName("secondDirect");
//        businessQueryDTO5.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
//        businessQueryDTO5.setValue(secondDirects);
//        businessQueryDTOList.add(businessQueryDTO5);
        return businessQueryDTOList;
    }

    public KnowledgePackageQA selectByQAId(String bizId) {
        Optional<KnowledgePackageQA> knowledgePackageQAOptional = knowledgePackageQARepository.findById(bizId);
        if(knowledgePackageQAOptional.isPresent()) {
            return knowledgePackageQAOptional.get();
        }
        return null;
    }

    public KnowledgePackageQA selectByKnowledgeId(String knowledgeId, Long robotId, Integer environment) {
        return knowledgePackageQARepository.findByRobotIdAndEnvironmentAndKnowledgeId(robotId, environment, knowledgeId);
    }
}
