/*
package com.kbao.kbcchatbot.channel.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcchatbot.channel.bean.ChannelFullVO;
import com.kbao.kbcchatbot.channel.bean.ChannelVO;
import com.kbao.kbcchatbot.channel.dao.ChannelMapper;
import com.kbao.kbcchatbot.channel.entity.Channel;
import com.kbao.kbcchatbot.channelbasicconfig.entity.ChannelBasicConfig;
import com.kbao.kbcchatbot.channelbasicconfig.service.ChannelBasicConfigService;
import com.kbao.kbcchatbot.channelcommoncard.bean.ChannelCommonCardVO;
import com.kbao.kbcchatbot.channelcommoncard.service.ChannelCommonCardService;
import com.kbao.kbcchatbot.channelcommonphrase.bean.ChannelCommonPhraseVO;
import com.kbao.kbcchatbot.channelcommonphrase.entity.ChannelCommonPhrase;
import com.kbao.kbcchatbot.channelcommonphrase.service.ChannelCommonPhraseService;
import com.kbao.kbcchatbot.channelguessquestion.bean.ChannelGuessQuestionVO;
import com.kbao.kbcchatbot.channelguessquestion.entity.ChannelGuessQuestion;
import com.kbao.kbcchatbot.channelguessquestion.service.ChannelGuessQuestionService;
import com.kbao.kbcchatbot.robot.entity.Robot;
import com.kbao.kbcchatbot.robot.service.RobotService;
import com.kbao.kbcchatbot.series.service.SeriesDataService;
import com.kbao.kbcchatbot.utils.BeanCopyUtil;
import com.kbao.kbcchatbot.utils.cripto.CryptoUtil;
import com.kbao.kbcchatbot.utils.cripto.bean.CryptoRSAKeyPair;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.kbao.kbcchatbot.constants.Constant.STATUS_DISABLE;
import static com.kbao.kbcchatbot.constants.Constant.STATUS_ENABLE;

*/
/**
 * <AUTHOR>
 * @Description 渠道表Service类
 * @Date 2023-05-18
 *//*

public class ChannelService extends BaseSQLServiceImpl<Channel, Long, ChannelMapper> {

    @Autowired
    private SeriesDataService seriesDataService;

    @Autowired
    private ChannelBasicConfigService channelBasicConfigService;

    @Autowired
    private ChannelCommonCardService channelCommonCardService;

    @Autowired
    private ChannelCommonPhraseService channelCommonPhraseService;

    @Autowired
    private ChannelGuessQuestionService channelGuessQuestionService;

    @Autowired
    @Lazy
    private RobotService robotService;

    @Transactional(rollbackFor = Exception.class)
    public int insert(Channel channel) {
        String currentUserId = SysLoginUtils.getUserId();
        String nowStr = DateUtils.thisDateTime();
        channel.setCreateTime(nowStr);
        channel.setCreateId(currentUserId);
        channel.setUpdateTime(nowStr);
        channel.setUpdateId(currentUserId);
        channel.setIsDeleted(0);
        channel.setCode(getNextId());
        channel.setTenantId(SysLoginUtils.getUser().getTenantId());
        CryptoRSAKeyPair cryptoRSAKeyPair = CryptoUtil.generateKeyPair();
        channel.setPrivateKey(cryptoRSAKeyPair.getPrivateKey());
        channel.setPublicKey(cryptoRSAKeyPair.getPublicKey());
        return this.mapper.insert(channel);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(Channel channel) {
        channel.setUpdateTime(DateUtils.thisDateTime());
        channel.setUpdateId(SysLoginUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(channel);
    }

    public String getNextId() {
        return seriesDataService.getNextId("ChannelService", "CN", 8);
    }


    */
/**
     * 生成一个逻辑删除方法
     *
     * @param id 主键
     *//*

    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        String currentUserId = SysLoginUtils.getUserId();
        String nowStr = DateUtils.thisDateTime();
        Channel channel = new Channel();
        channel.setId(id);
        channel.setIsDeleted(1);
        channel.setUpdateTime(nowStr);
        channel.setUpdateId(currentUserId);
        return this.mapper.updateByPrimaryKeySelective(channel);
    }

    */
/**
     * 分页查询
     *
     * @param page 分页参数
     * @return 分页结果
     *//*

    public PageInfo<Channel> getChannelPage(RequestObjectPage<Channel> page) {
        page.getParam().setTenantId(SysLoginUtils.getUser().getTenantId());
        PageInfo<Channel> channelPageInfo = page(page);
        List<Channel> channelList = channelPageInfo.getList();

        // 构建 robotId、channelId 列表
        List<Long> robotIdList = channelList.stream().map(Channel::getRobotId).collect(Collectors.toList());
        List<Long> channelIdList = channelList.stream().map(Channel::getId).collect(Collectors.toList());

        // 异步查询所有的 Robot 和 ChannelBasicConfig 信息
        CompletableFuture<Map<Long, Robot>> robotFuture = CompletableFuture.supplyAsync(() -> getRobotMap(robotIdList));
        CompletableFuture<Map<Long, ChannelBasicConfig>> channelBasicConfigFuture = CompletableFuture.supplyAsync(() -> getChannelBasicConfigMap(channelIdList));

        // 等待异步查询完成
        Map<Long, Robot> robotMap = robotFuture.join();
        Map<Long, ChannelBasicConfig> channelBasicConfigMap = channelBasicConfigFuture.join();

        // 更新 Channel 对象
        channelList.forEach(channel -> {
            // 设置 robotName
            Robot robot = robotMap.get(channel.getRobotId());
            if (Objects.nonNull(robot)) {
                channel.setRobotName(robot.getName());
            }
            // 设置 channelBasicConfigId
            ChannelBasicConfig channelBasicConfig = channelBasicConfigMap.get(channel.getId());
            if (Objects.nonNull(channelBasicConfig)) {
                channel.setChannelBasicConfigId(channelBasicConfig.getId());
            }
        });

        // 设置更新后的 channelList
        channelPageInfo.setList(channelList);

        return channelPageInfo;
    }


    */
/**
     * 获取所有 Robot 信息的 map
     *
     * @param robotIdList robotId 列表
     * @return robotId -> Robot 的 map
     *//*

    private Map<Long, Robot> getRobotMap(List<Long> robotIdList) {
        if (CollectionUtils.isEmpty(robotIdList)) {
            return Collections.emptyMap();
        }
        List<Robot> robotList = robotService.getMapper().selectBatchByPrimaryKey(robotIdList);
        return robotList.stream().collect(Collectors.toMap(Robot::getId, Function.identity(), (existingValue, newValue) -> existingValue));

    }


    */
/**
     * 获取所有 ChannelBasicConfig 信息的 map
     *
     * @param channelIdList channelId 列表
     * @return channelId -> ChannelBasicConfig 的 map
     *//*

    private Map<Long, ChannelBasicConfig> getChannelBasicConfigMap(List<Long> channelIdList) {
        if (CollectionUtils.isEmpty(channelIdList)) {
            return Collections.emptyMap();
        }
        List<ChannelBasicConfig> channelBasicConfigList = channelBasicConfigService.getMapper().selectBatchByPrimaryKey(channelIdList);
        return channelBasicConfigList.stream().collect(Collectors.toMap(ChannelBasicConfig::getChannelId, Function.identity(), (existingValue, newValue) -> existingValue));
    }

    */
/**
     * 获取渠道全量数据
     *
     * @param channelId 渠道id
     * @return 渠道全量数据
     *//*

    public ChannelFullVO getChannelFullData(Long channelId) {
        Channel channel = selectByPrimaryKey(channelId);
        if (Objects.isNull(channel)) {
            logger.info("渠道信息为空 channelId:{}", channelId);
            return null;
        }

        // 获取渠道基础信息
        ChannelFullVO channelFullVO = new ChannelFullVO();
        ChannelVO channelVO = BeanCopyUtil.copy(channel, ChannelVO.class);
        channelFullVO.setChannelVO(channelVO);

        List<ChannelBasicConfig> channelBasicConfigListByChannelId = channelBasicConfigService.getChannelBasicConfigListByChannelId(channelId);
        if (CollectionUtils.isEmpty(channelBasicConfigListByChannelId)) {
            logger.info("渠道基础配置为空 channelId:{}", channelId);
            return null;
        }

        // 获取渠道基础配置
        channelFullVO.setChannelBasicConfig(channelBasicConfigListByChannelId.get(0));

        // 获取渠道常用卡片
        List<ChannelCommonCardVO> cardVOList = channelCommonCardService.getChannelCommonCardListByChannelId(channelId,null);
        channelFullVO.setChannelCommonCardList(cardVOList);

        // 渠道猜你想问列表
        List<ChannelGuessQuestionVO> channelGuessQuestionVOList = channelGuessQuestionService.getChannelGuessQuestionListByChannelId(channelId,null,null);
        channelFullVO.setChannelGuessQuestionList(channelGuessQuestionVOList);

        // 获取常用短语配置
        List<ChannelCommonPhraseVO> channelCommonPhraseListByChannelId = channelCommonPhraseService.getChannelCommonPhraseListByChannelId(channelId,null);
        channelFullVO.setChannelCommonPhraseList(channelCommonPhraseListByChannelId);

        return channelFullVO;

    }


    */
/**
     * 根据机器人ID查询绑定已启用渠道的数量
     *
     * @param robotId 机器人ID
     * @return 绑定已启用渠道的数量
     *//*

    public int getEnabledRobotNumByRobotId(Long robotId) {
        if (Objects.isNull(robotId)) {
            return 0;
        }
        return this.mapper.getEnabledRobotNumByRobotId(robotId);
    }



}
*/
