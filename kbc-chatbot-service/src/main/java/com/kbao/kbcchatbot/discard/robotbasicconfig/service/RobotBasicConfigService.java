package com.kbao.kbcchatbot.discard.robotbasicconfig.service;

import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.discard.robot.enums.RobotStatusEnum;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.discard.robot.vo.RobotUpdateStatusVO;
import com.kbao.kbcchatbot.discard.robotbasicconfig.dao.RobotBasicConfigMapper;
import com.kbao.kbcchatbot.discard.robotbasicconfig.entity.RobotBasicConfig;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.RobotBasicConfigDetailVO;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.RobotBasicConfigGetVO;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.RobotBasicConfigSaveVO;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description: 基础配置
 * @author: husw
 * @create: 2023-05-16 15:19
 **/
@Service
@Slf4j
public class RobotBasicConfigService extends BaseSQLServiceImpl<RobotBasicConfig,Long, RobotBasicConfigMapper> {

/*    @Autowired
    @Lazy
    private RobotService robotService;*/

    @Autowired
    private RedisUtil redisUtil;
    /**
    * @Description: 保存基础配置
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/17 9:07
    */
    @Transactional(rollbackFor = Exception.class)
    public void save(RobotBasicConfigSaveVO param) {
        if (EmptyUtils.isNotEmpty(param.getClearThreshold())){
            int compare = param.getClearThreshold().compareTo(param.getDirectReplyThreshold());
            if (compare >= 0){
                throw new BusinessException("澄清阈值需小于直接回复阈值！");
            }
        }
        RobotBasicConfig robotBasicConfig = new RobotBasicConfig();
        BeanUtils.copyProperties(param,robotBasicConfig);
        robotBasicConfig.setUpdateId(BscUserUtils.getUserId());
        robotBasicConfig.setUpdateTime(DateUtils.getCurrentDate());
//        robotBasicConfig.setType(EnvTypeEnum.TEST_ENV.getCode());
        robotBasicConfig.setType(EnvTypeEnum.PROD_ENV.getCode());
        if (EmptyUtils.isNotEmpty(param.getClearReply())){
            robotBasicConfig.setClearReply(param.getClearReply().getBytes(StandardCharsets.UTF_8));
        }
        robotBasicConfig.setNoAnswerReply(param.getNoAnswerReply().getBytes(StandardCharsets.UTF_8));
        robotBasicConfig.setSensitiveWordsReply(param.getSensitiveWordsReply().getBytes(StandardCharsets.UTF_8));
        //将机器人状态变更为编辑中
        RobotUpdateStatusVO vo = new RobotUpdateStatusVO();
        vo.setId(robotBasicConfig.getRobotId());
//        vo.setStatus(RobotStatusEnum.EDITING.getCode());
        vo.setStatus(RobotStatusEnum.PUBLISHED.getCode());
        //robotService.updateStatus(vo);
        Map<String,Object> reqMap = new HashMap<>();
        reqMap.put("robotId",param.getRobotId());
//        reqMap.put("type", EnvTypeEnum.TEST_ENV.getCode());
        reqMap.put("type", EnvTypeEnum.PROD_ENV.getCode());
        List<RobotBasicConfig> robotBasicConfigs = this.mapper.selectAll(reqMap);
        if (EmptyUtils.isNotEmpty(robotBasicConfigs)){
            robotBasicConfig.setId(robotBasicConfigs.get(0).getId());
            this.mapper.updateByPrimaryKey(robotBasicConfig);
        }else {
            this.mapper.insert(robotBasicConfig);
        }
    }
    /**
    * @Description: 查询基础配置
    * @Param: [param]
    * @return: com.kbao.kbcchatbot.robotbasicconfig.vo.RobotBasicConfigDetailVo
    * @Author: husw
    * @Date: 2023/5/17 9:41
    */
    public RobotBasicConfigDetailVO get(RobotBasicConfigGetVO param) {
        param.setType(EnvTypeEnum.PROD_ENV.getCode());
        List<RobotBasicConfig> robotBasicConfigs = this.mapper.selectByRobotId(param.getRobotId());
        if (EmptyUtils.isEmpty(robotBasicConfigs)){
            return null;
        }
        RobotBasicConfigDetailVO vo = new RobotBasicConfigDetailVO();
        RobotBasicConfig robotBasicConfig = new RobotBasicConfig();
        if (robotBasicConfigs.size()>1){
            robotBasicConfig = robotBasicConfigs.stream().filter(x -> x.getType().equals(param.getType())).findFirst().orElse(robotBasicConfig);
        }else {
            robotBasicConfig = robotBasicConfigs.get(0);
        }
        BeanUtils.copyProperties(robotBasicConfig,vo);
        if (EmptyUtils.isNotEmpty(robotBasicConfig.getClearReply())){
            vo.setClearReply(new String(robotBasicConfig.getClearReply(),StandardCharsets.UTF_8));
        }
        vo.setNoAnswerReply(new String(robotBasicConfig.getNoAnswerReply(),StandardCharsets.UTF_8));
        vo.setSensitiveWordsReply(new String(robotBasicConfig.getSensitiveWordsReply(),StandardCharsets.UTF_8));
        return vo;
    }

    /**
    * @Description: 基本配置数据
    * @Param: [robotId]
    * @return: com.kbao.kbcchatbot.robotbasicconfig.entity.RobotBasicConfig
    * @Author: husw
    * @Date: 2023/6/13 9:41
    */
    public RobotBasicConfig getDetail(Long robotId) {
        Map<String,Object> map = new HashMap<>();
        map.put("robotId",robotId);
        map.put("type",YesNoEnum.YES.getValue());
        List<RobotBasicConfig> robotBasicConfigs = this.mapper.selectAll(map);
        if (EmptyUtils.isEmpty(robotBasicConfigs)){
            return null;
        }
        return robotBasicConfigs.get(0);
    }
}
