package com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.dao.RobotSensitiveWordsMapper;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.entity.RobotSensitiveWords;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.vo.RobotSensitiveWordsSaveVO;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @program: kbc-chatbot
 * @description: 敏感词配置
 * @author: husw
 * @create: 2023-07-03 17:59
 **/
@Service
@Slf4j
public class RobotSensitiveWordsConfigService extends BaseSQLServiceImpl<RobotSensitiveWords,Long, RobotSensitiveWordsMapper> {
    /**
    * @Description: 保存敏感词
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/7/4 9:04
    */
    public void save(RobotSensitiveWordsSaveVO param) {
        int count = this.mapper.selectCountByWords(param.getWords(), param.getRobotId());
        if (count > 0){
            throw new BusinessException("当前敏感词已存在！");
        }
        RobotSensitiveWords robotSensitiveWords = new RobotSensitiveWords();
        robotSensitiveWords.setRobotId(param.getRobotId());
        robotSensitiveWords.setWords(param.getWords());
        robotSensitiveWords.setCreateId(SysLoginUtils.getUserId());
        robotSensitiveWords.setCreateTime(DateUtils.getCurrentDate());
        this.mapper.insert(robotSensitiveWords);
    }
}
