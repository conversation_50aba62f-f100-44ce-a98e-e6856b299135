package com.kbao.kbcchatbot.discard.robotmanualserviceconfig.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.entity.RobotManualServiceConfig;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_robot_manual_service_config(聊天机器人转人工配置表)】的数据库操作Mapper
* @createDate 2023-05-17 13:54:23
*/
public interface RobotManualServiceConfigMapper extends BaseMapper<RobotManualServiceConfig,Long> {

    List<RobotManualServiceConfig> selectByRobotId(Long robotId);

    RobotManualServiceConfig selectProdByRobotId(Long robotId);

}
