package com.kbao.kbcchatbot.discard.robot.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.discard.robot.entity.Robot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_robot(聊天机器人表)】的数据库操作Mapper
* @createDate 2023-05-16 10:08:57
* @Entity generator.domain.Robot
*/
public interface RobotMapper extends BaseMapper<Robot,Long> {

    /**
     * 根据机器人Ids查询机器人信息
     * @param robotIds 机器人Ids
     * @return 机器人信息
     */
    List<Robot> selectBatchByPrimaryKey(@Param("robotIds") List<Long> robotIds);

    Robot selectByCode(@Param("code") String code);

}
