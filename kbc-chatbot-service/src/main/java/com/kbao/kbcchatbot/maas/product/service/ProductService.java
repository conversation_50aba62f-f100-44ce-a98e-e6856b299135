package com.kbao.kbcchatbot.maas.product.service;

import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.product.bean.ProductLabelVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 产品服务
 * @date 2025/4/1 14:09
 */
@Service
public class ProductService {

    @Autowired
    private MaasHttpService maasHttpService;

    public void getProductLabel(ProductLabelVO productLabelVO){
        maasHttpService.getProductLabel(productLabelVO);
    }
}
