package com.kbao.kbcchatbot.maas.flow.dao;


import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.flow.entity.Flow;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_flow(流程配置表)】的数据库操作Mapper
* @createDate 2025-05-28 09:30:07
* @Entity generator.domain.Flow
*/
public interface FlowMapper extends BaseMapper<Flow, Integer> {

    List<Flow> selectList(Map<String, Object> params);

}
