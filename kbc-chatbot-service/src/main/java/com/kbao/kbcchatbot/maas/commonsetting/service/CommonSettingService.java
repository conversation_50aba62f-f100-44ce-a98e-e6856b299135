package com.kbao.kbcchatbot.maas.commonsetting.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.kbcchatbot.maas.commonsetting.dao.CommonSettingMapper;
import com.kbao.kbcchatbot.maas.commonsetting.entity.CommonSetting;
import com.kbao.tool.util.StringUtil;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-01-09
*/
@Service
public class CommonSettingService extends BaseSQLServiceImpl<CommonSetting, Integer, CommonSettingMapper> {

    public void saveSetting(String code, String value) {
        CommonSetting settings = CommonSetting.builder().code(code).value(value)
                .updateBy(SysLoginUtils.getUserId())
                .tenantId(SysLoginUtils.getUser().getTenantId())
                .build();
        mapper.saveSetting(settings);
    }

    public String getValue(String code) {
        return mapper.getSettingValue(code);
    }

    public JSONObject getJSONValue(String code) {
        String value = mapper.getSettingValue(code);
        if(StringUtil.isNotEmpty(value)){
            return JSONObject.parseObject(value);
        }
        return null;
    }
}
