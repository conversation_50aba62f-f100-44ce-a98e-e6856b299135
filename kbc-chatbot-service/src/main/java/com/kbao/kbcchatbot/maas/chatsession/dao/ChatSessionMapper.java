package com.kbao.kbcchatbot.maas.chatsession.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.chatsession.entity.ChatSession;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotReceptionRespVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @Description 聊天会话Dao类
* @Date 2023-06-06
*/
public interface ChatSessionMapper  extends BaseMapper<ChatSession, Long>{

    List<RobotReceptionRespVO> selectDataBoard(Map<String,Object> param);

    ChatSession selectByUserIdCurrentDay(@Param("sessionId") String sessionId,
                                         @Param("channelCode") String channelCode,
                                         @Param("robotCode") String robotCode);
    List<ChatSession> selectUpdateSession();

    void updateStatusCurrentDay(@Param("sessionId") String sessionId,
                             @Param("channelCode") String channelCode,
                             @Param("robotCode") String robotCode);
	
}