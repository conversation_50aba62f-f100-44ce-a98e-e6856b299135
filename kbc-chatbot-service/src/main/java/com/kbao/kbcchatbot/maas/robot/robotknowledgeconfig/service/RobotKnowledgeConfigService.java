package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.param.entity.Param;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscApiContext;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerSourceEnum;
import com.kbao.kbcchatbot.elasticsearch.search.BaseSearchService;
import com.kbao.kbcchatbot.elasticsearch.search.bean.BusinessQueryDTO;
import com.kbao.kbcchatbot.elasticsearch.search.bean.SearchQuery;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.kbc.bsc.KbcBscService;
import com.kbao.kbcchatbot.kbc.km.KbcKmService;
import com.kbao.kbcchatbot.discard.knowledgepackage.dao.KnowledgePackageQARepository;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackageQA;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums.*;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.*;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.service.RobotKnowledgeDataService;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeRecord.service.RobotKnowledgeRecordService;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.dao.KnowledgePackageCloudRepository;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.dao.RobotKnowledgeConfigMapper;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgePackageCloud;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig;
import com.kbao.kbcchatbot.maas.robot.robotknowledgerecord.entity.RobotKnowledgeRecord;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeRemoveReqVO;
import com.kbao.kbckm.directory.bean.DirectTreeVo;
import com.kbao.kbckm.knowledge.entity.KnowledgeBase;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.MapUtils;
import com.kbao.tool.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: kbc-chatbot
 * @description: 知识配置
 * @author: husw
 * @create: 2023-05-17 10:32
 **/
@Service
@Slf4j
public class RobotKnowledgeConfigService extends BaseSQLServiceImpl<RobotKnowledgeConfig,Long, RobotKnowledgeConfigMapper> {

    @Autowired
    private RobotKnowledgeRecordService robotKnowledgeRecordService;

    @Autowired
    @Lazy
    private KbcKmService kbcKmService;

    @Autowired
    private BaseSearchService baseSearchService;

    @Autowired
    private RobotKnowledgeAsyncService robotKnowledgeAsyncService;

    @Autowired
    private ElasticsearchRestTemplate elasticSearchRestTemplate;

    @Autowired
    private KnowledgePackageCloudRepository knowledgePackageCloudRepository;

    @Autowired
    private KnowledgePackageQARepository knowledgePackageQARepository;

    @Autowired
    private KbcBscService kbcBscService;

    @Autowired
    private MaasHttpService maasHttpService;

    @Autowired
    private ThreadPoolTaskExecutor asyncThreadPool;

    @Autowired
    private RobotKnowledgeDataService robotKnowledgeDataService;

    private final static String PARAM_CODE = "km.knowledgeDetail.funcId";

    /**
    * @Description: 绑定
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/17 11:48
    */
    @Transactional(rollbackFor = Exception.class)
    public void bind(RobotKnowledgeConfigBindReqVO param) {
        Map<String, Object> paramMap = MapUtils.objectToMap(param);
        paramMap.put("type",EnvTypeEnum.PROD_ENV.getCode());
        paramMap.put("operationNoEq",KnowledgeOperationEnum.UNBIND.getCode());
        paramMap.put("directoryIdList",param.getDirectVOS().stream().map(RobotKnowledgeDirectVO::getDirectoryId).collect(Collectors.toList()));
        List<RobotKnowledgeConfig> robotKnowledgeConfigList = this.mapper.selectByCondition(paramMap);
        List<RobotKnowledgeConfig> robotKnowledgeConfigs = new ArrayList<>();
        List<String> ignoreList = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigList)){
            ignoreList = robotKnowledgeConfigList.stream().map(RobotKnowledgeConfig::getDirectoryId).collect(Collectors.toList());
        }
        //删除已存在的相同知识配置
        this.mapper.deleteByRobotIdAndDirect(paramMap);
        //处理绑定知识
        final String tenantId = BscApiContext.TenantId.get();
        for (RobotKnowledgeDirectVO x:param.getDirectVOS()){
            if (EmptyUtils.isNotEmpty(ignoreList) && ignoreList.contains(x.getDirectoryId())){
                continue;
            }
            //封装配置参数
            RobotKnowledgeConfig robotKnowledgeConfig = convertKnowledgeConfig(param, x);
            robotKnowledgeConfigs.add(robotKnowledgeConfig);
        }
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigs)){
            //保存知识配置和记录操作
            String operateUserId = BscUserUtils.getUserId();
            this.batchInsert(robotKnowledgeConfigs);
            asyncThreadPool.execute(() -> robotKnowledgeConfigs.forEach(robotKnowledgeConfig -> {
                RobotKnowledgeRecord robotKnowledgeRecord = initKnowledgeRecord(robotKnowledgeConfig, KnowledgeOperationEnum.BIND, robotKnowledgeConfig.getDirectoryId(), robotKnowledgeConfig.getDirectoryName(), operateUserId);
                boolean operateStatus = robotKnowledgeAsyncService.syncKnowledge(robotKnowledgeConfig, tenantId, null);
                if (!operateStatus){
                    robotKnowledgeRecord.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                }
                robotKnowledgeRecordService.save(robotKnowledgeRecord);
            }));
        }
    }
    /**
    * @Description: 保存知识操作记录
    * @Param: [robotId, firstProjectId, firstProjectName, projectId, projectName, folderId, folderName, bind, directoryId, directoryName]
    * @return: void
    * @Author: husw
    * @Date: 2023/6/15 11:02
    */
    private RobotKnowledgeRecord initKnowledgeRecord(RobotKnowledgeConfig config, KnowledgeOperationEnum bind, String directoryId, String directoryName, String operateUserId) {
        RobotKnowledgeRecord robotKnowledgeRecord = new RobotKnowledgeRecord();
        robotKnowledgeRecord.setRobotId(config.getRobotId());
        robotKnowledgeRecord.setRobotCode(config.getRobotCode());
        robotKnowledgeRecord.setFirstDirectId(config.getFirstDirectId());
        robotKnowledgeRecord.setFirstDirectName(config.getFirstDirectName());
        robotKnowledgeRecord.setProjectId(config.getProjectId());
        robotKnowledgeRecord.setProjectName(config.getProjectName());
        robotKnowledgeRecord.setFolderId(config.getFolderId());
        robotKnowledgeRecord.setFolderName(config.getFolderName());
        robotKnowledgeRecord.setType(bind.getCode());
        robotKnowledgeRecord.setDirectoryId(directoryId);
        robotKnowledgeRecord.setDirectoryName(directoryName);
        robotKnowledgeRecord.setBindId(operateUserId);
        robotKnowledgeRecord.setBindTime(DateUtils.getCurrentDate());
        robotKnowledgeRecord.setOperateStatus(OperateStatusEnum.SUCCESS.getCode());
        return robotKnowledgeRecord;

    }

    /**
    * @Description: 初始化知识配置
    * @Param: [param, knowledgeDirectVO]
    * @return: com.kbao.kbcchatbot.robotknowledgeconfig.entity.RobotKnowledgeConfig
    * @Author: husw
    * @Date: 2023/6/15 10:56
    */
    private RobotKnowledgeConfig convertKnowledgeConfig(RobotKnowledgeConfigBindReqVO param, RobotKnowledgeDirectVO knowledgeDirectVO) {
        RobotKnowledgeConfig robotKnowledgeConfig = new RobotKnowledgeConfig();
        robotKnowledgeConfig.setDirectoryCode(IdWorker.getIdStr());
        robotKnowledgeConfig.setRobotId(param.getRobotId());
        robotKnowledgeConfig.setRobotCode(param.getRobotCode());
        robotKnowledgeConfig.setOperation(KnowledgeOperationEnum.BIND.getCode());
        robotKnowledgeConfig.setFirstDirectId(param.getFirstDirectId());
        robotKnowledgeConfig.setFirstDirectName(param.getFirstDirectName());
        robotKnowledgeConfig.setDirectoryId(knowledgeDirectVO.getDirectoryId());
        robotKnowledgeConfig.setDirectoryName(knowledgeDirectVO.getDirectoryName());
        robotKnowledgeConfig.setBindId(BscUserUtils.getUserId());
        robotKnowledgeConfig.setBindTime(DateUtils.getCurrentDate());
        robotKnowledgeConfig.setProjectId(param.getProjectId());
        robotKnowledgeConfig.setProjectName(param.getProjectName());
        robotKnowledgeConfig.setFolderId(param.getFolderId());
        robotKnowledgeConfig.setFolderName(param.getFolderName());
        robotKnowledgeConfig.setType(EnvTypeEnum.PROD_ENV.getCode());
        robotKnowledgeConfig.setRefreshId(BscUserUtils.getUserId());
        robotKnowledgeConfig.setRefreshTime(DateUtils.getCurrentDate());
        robotKnowledgeConfig.setSyncStatus(SyncStatusEnum.SYNCING.getCode());
        robotKnowledgeConfig.setUpdateId(BscUserUtils.getUserId());
        robotKnowledgeConfig.setUpdateTime(DateUtils.getCurrentDate());
        return robotKnowledgeConfig;
    }

    public List<RobotKnowledgeDirectRespVO> directoryList(RobotKnowledgeDirectoryReqVO param){
        Map<String, Object> paramMap = MapUtils.objectToMap(param);
        if (EmptyUtils.isNotEmpty(param.getFirstDirectId())){
            return this.mapper.selectDirectoryByRobot(paramMap);
        }
        return this.mapper.selectFirstDirectoryByRobot(paramMap);
    }


    /**
    * @Description:解绑
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/17 11:47
    */
    @Transactional(rollbackFor = Exception.class)
    public void unbind(RobotKnowledgeConfigBindReqVO param) {
        List<String> directoryIdList = param.getDirectVOS().stream().map(RobotKnowledgeDirectVO::getDirectoryId).collect(Collectors.toList());
        checkStatusByCode(param.getRobotCode(),directoryIdList);
        Map<String,Object> map = new HashMap<>();
        map.put("robotCode",param.getRobotCode());
        map.put("directoryIdList",directoryIdList);
        map.put("type",EnvTypeEnum.PROD_ENV.getCode());
        this.mapper.deleteByRobotIdAndDirect(map);
        RobotKnowledgeConfig robotKnowledgeConfig = new RobotKnowledgeConfig();
        BeanUtils.copyProperties(param,robotKnowledgeConfig);
        String operateUserId = BscUserUtils.getUserId();
        param.getDirectVOS().forEach(x->{
            RobotKnowledgeRecord robotKnowledgeRecord = initKnowledgeRecord(robotKnowledgeConfig, KnowledgeOperationEnum.UNBIND, x.getDirectoryId(), x.getDirectoryName(), operateUserId);
            try {
                //删除物料库数据
                robotKnowledgeAsyncService.deleteKnowledgeByCode(x.getDirectoryCode(), param.getRobotCode());
            } catch (Exception e) {
                robotKnowledgeRecord.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                log.error("删除知识失败, 目录：{}", x.getDirectoryName(), e);
            }
            robotKnowledgeRecordService.save(robotKnowledgeRecord);
        });
    }
    /**
    * @Description: 刷新
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/29 11:47
    */
    @Transactional(rollbackFor = Exception.class)
    public void refresh(RobotKnowledgeConfigRefreshReqVO param) {
        checkStatusByCode(param.getRobotCode(), Collections.singletonList(param.getDirectoryId()));
        Map<String, Object> paramMap = MapUtils.objectToMap(param);
        paramMap.put("type",EnvTypeEnum.PROD_ENV.getCode());
        paramMap.put("operationNotEq",KnowledgeOperationEnum.UNBIND.getCode());
        List<RobotKnowledgeConfig> robotKnowledgeConfigList = this.mapper.selectByCondition(paramMap);
        if (EmptyUtils.isEmpty(robotKnowledgeConfigList)){
            log.info("配置信息为空！");
            return;
        }
        //更新配置
        RobotKnowledgeConfig robotKnowledgeConfig = robotKnowledgeConfigList.get(0);
        //新绑定的目录刷新不变更操作状态（绑定后再刷新会覆盖绑定操作）
        if (!KnowledgeOperationEnum.BIND.getCode().equals(robotKnowledgeConfig.getOperation())){
            robotKnowledgeConfig.setOperation(KnowledgeOperationEnum.REFRESH.getCode());
        }
        String userId = BscUserUtils.getUserId();
        robotKnowledgeConfig.setSyncStatus(SyncStatusEnum.SYNCING.getCode());
        robotKnowledgeConfig.setRefreshId(userId);
        robotKnowledgeConfig.setRefreshTime(DateUtils.getCurrentDate());
        robotKnowledgeConfig.setUpdateId(userId);
        robotKnowledgeConfig.setUpdateTime(DateUtils.getCurrentDate());
        this.mapper.updateByPrimaryKey(robotKnowledgeConfig);
        //同步知识
        final String tenantId = BscApiContext.TenantId.get();
        asyncThreadPool.execute(() -> {
            RobotKnowledgeRecord robotKnowledgeRecord = initKnowledgeRecord(robotKnowledgeConfig, KnowledgeOperationEnum.REFRESH,
                    robotKnowledgeConfig.getDirectoryId(), robotKnowledgeConfig.getDirectoryName(), userId);
            boolean operateStatus = robotKnowledgeAsyncService.syncKnowledge(robotKnowledgeConfig, tenantId, null);
            if (!operateStatus){
                robotKnowledgeRecord.setOperateStatus(OperateStatusEnum.FAIL.getCode());
            }
            robotKnowledgeRecordService.save(robotKnowledgeRecord);
        });
    }


    /**
    * @Description: 更新单个知识
    * @Param: [param]
    * @return: void
    * @Author: husw
    * @Date: 2024/9/5 16:55
    */
    public void update(RobotKnowledgeUpdateReqVO param) {
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        knowledgeBase.setKnowledgeId(param.getKnowledgeId());
        robotKnowledgeAsyncService.embeddingDataByCode(Collections.singletonList(knowledgeBase),param.getRobotCode(),param.getDirectoryCode(), null);
    }

    public void checkStatus(Long robotId,List<String> directoryIds){
        Map<String,Object> map = new HashMap<>();
        map.put("robotId",robotId);
        map.put("type", EnvTypeEnum.PROD_ENV.getCode());
        map.put("syncStatus",SyncStatusEnum.SYNCING.getCode());
        map.put("directoryIdList",directoryIds);
        List<RobotKnowledgeConfig> robotKnowledgeConfigs = this.mapper.selectByCondition(map);
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigs)){
            throw new BusinessException("机器人存在正在同步中的知识，请等待同步完成后再操作！");
        }
    }
    /**
    * @Description: 更新目录删除状态
    * @Param: [directoryId]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/19 14:08
    */
    public void removeDirectory(RobotKnowledgeRemoveReqVO reqVO) {
        this.mapper.updateHasRemoved(reqVO.getDirectoryId());
    }
    /**
    * @Description: 查询目录树
    * @Param: []
    * @return: java.util.List<com.kbao.kbckm.directory.bean.DirectTreeVo>
    * @Author: husw
    * @Date: 2023/5/19 15:29
    */
    public List<RobotKnowledgeConfigDirectVO> list(RobotKnowledgeConfigIdReqVO reqVO){
        List<DirectTreeVo> list = kbcKmService.list();
        if (EmptyUtils.isEmpty(list)){
            return null;
        }
        reqVO.setType(EnvTypeEnum.PROD_ENV.getCode());
        Map<String, Object> paramMap = MapUtils.objectToMap(reqVO);
        paramMap.put("operationNoEq",KnowledgeOperationEnum.UNBIND.getCode());
        List<String> directIds = this.mapper.selectDirectId(paramMap);
        Map<String,String> directType = new HashMap<>();
        List<RobotKnowledgeConfigDirectVO> directVOS = JSONObject.parseArray(JSON.toJSONString(list), RobotKnowledgeConfigDirectVO.class);
        //处理数据
        setConfigDirect(directVOS,directIds,directType);
        return directVOS;
    }
    /**
    * @Description:  初始化部分数据
    * @Param: [configDirectVOs, directIds, directType]
    * @return: void
    * @Author: husw
    * @Date: 2023/5/30 10:45
    */
    private void setConfigDirect(List<RobotKnowledgeConfigDirectVO> configDirectVOs,List<String> directIds,Map<String,String> directType){
        configDirectVOs.forEach(x->{
            //处理上级数据
            if (RobotDirectTypeEnum.PROJECT.getCode().equals(x.getDirectType())){
                directType.put("projectId",x.getDirectId());
                directType.put("projectName",x.getDirectName());
            }
            if (RobotDirectTypeEnum.FOLDER.getCode().equals(x.getDirectType())){
                directType.put("folderId",x.getDirectId());
                directType.put("folderName",x.getDirectName());
            }
            //一级目录初始化值
            if (RobotDirectTypeEnum.FIRST.getCode().equals(x.getDirectType())){
                x.setProjectId(directType.get("projectId"));
                x.setFolderId(directType.get("folderId"));
                x.setProjectName(directType.get("projectName"));
                x.setFolderName(directType.get("folderName"));
                x.setFirstDirectId(x.getDirectId());
                x.setFirstDirectName(x.getDirectName());
                directType.put("firstDirectoryId",x.getDirectId());
                directType.put("firstDirectoryName",x.getDirectName());
            }
            //二级目录初始化值
            if (EmptyUtils.isEmpty(x.getChilds())){
                x.setProjectId(directType.get("projectId"));
                x.setFolderId(directType.get("folderId"));
                x.setProjectName(directType.get("projectName"));
                x.setFolderName(directType.get("folderName"));
                x.setFirstDirectId(directType.get("firstDirectoryId"));
                x.setFirstDirectName(directType.get("firstDirectoryName"));
                //筛选是否绑定目录
                if (directIds.contains(x.getDirectId())){
                    x.setIsBind(YesNoEnum.YES.getValue());
                }else {
                    x.setIsBind(YesNoEnum.NO.getValue());
                }
            }else {
                setConfigDirect(x.getChilds(),directIds,directType);
            }
        });
    }


    public SearchHits search(SearchQuery searchQuery) {
        List<BusinessQueryDTO> businessQuery = new ArrayList<>();
        BusinessQueryDTO dto = new BusinessQueryDTO();
        dto.setColumnName("robotId");
        dto.setOperator("eq");
        dto.setValue(searchQuery.getRobotId());
        businessQuery.add(dto);
        SearchHits searchHits = baseSearchService.search(KnowledgePackageCloud.class, searchQuery, 100, businessQuery, Operator.OR.name());
        return searchHits;
    }

    /**
    * @Description: 分页查询
    * @Param: [pageRequest]
    * @return: org.springframework.data.elasticsearch.core.SearchPage<com.kbao.kbcchatbot.robotknowledgeconfig.entity.KnowledgePackageCloud>
    * @Author: husw
    * @Date: 2023/6/2 17:26
    */
    public PageInfo<RobotKnowledgeSubDetailVO> pageSearch(RequestObjectPage<RobotKnowledgeSearchReqVO> pageRequest) {
        pageRequest.getParam().setType(EnvTypeEnum.PROD_ENV.getCode());
        Pageable pageable = PageRequest.of(pageRequest.getPageNum()-1,pageRequest.getPageSize());
        BoolQueryBuilder baseQueryBuilder = new BoolQueryBuilder();
        baseQueryBuilder.must(QueryBuilders.termQuery("robotId", pageRequest.getParam().getRobotId()));
        if (EmptyUtils.isNotEmpty(pageRequest.getParam().getDirectoryId())){
            baseQueryBuilder.must(QueryBuilders.termQuery("secondDirect", pageRequest.getParam().getDirectoryId()));
        }
        if (YesNoEnum.YES.getValue().toString().equals(pageRequest.getParam().getKnowledgeType())){
            if (EmptyUtils.isNotEmpty(pageRequest.getParam().getKnowledgeId())){
                baseQueryBuilder.must(QueryBuilders.termQuery("knowledgeId.keyword", pageRequest.getParam().getKnowledgeId()));
            }
            if (EmptyUtils.isNotEmpty(pageRequest.getParam().getTitle())){
                baseQueryBuilder.must(QueryBuilders.matchPhraseQuery("title", StringUtil.join("*",pageRequest.getParam().getTitle(),"*")));
            }
        }else {
            if (EmptyUtils.isNotEmpty(pageRequest.getParam().getKnowledgeId())){
                baseQueryBuilder.must(QueryBuilders.termQuery("knowledgeId", pageRequest.getParam().getKnowledgeId()));
            }
            if (EmptyUtils.isNotEmpty(pageRequest.getParam().getTitle())){
                baseQueryBuilder.must(QueryBuilders.matchPhraseQuery("title",pageRequest.getParam().getTitle()));
            }
        }
        baseQueryBuilder.must(QueryBuilders.termQuery("environment", pageRequest.getParam().getType()));
        baseQueryBuilder.must(QueryBuilders.termQuery("state", KnowledgeStateEnum.YES.getCode()));
        baseQueryBuilder.must(QueryBuilders.termQuery("isDeleted", "0"));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withQuery(baseQueryBuilder)
                .withFields("knowledgeId","robotId","environment","tenantId","title","type","projectId","projectName",
                        "folderId","folderName","firstDirect","firstDirectName","secondDirect","secondDirectName","mutexArticleName",
                        "state","belongType","mutexArticle","belongName","updateId","updateName","updateTime","isDeleted")
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .build();
        nativeSearchQuery.setPageable(pageable);

        List<RobotKnowledgeSubDetailVO> robotKnowledgeSubDetailVOS = new ArrayList<>();
        PageInfo<RobotKnowledgeSubDetailVO> pageInfo = new PageInfo<>();
        if(YesNoEnum.YES.getValue().toString().equals(pageRequest.getParam().getKnowledgeType())){
            //QA
            SearchHits<KnowledgePackageQA> search = elasticSearchRestTemplate.search(nativeSearchQuery, KnowledgePackageQA.class);
            SearchPage<KnowledgePackageQA> searchHits = SearchHitSupport.searchPageFor(search, nativeSearchQuery.getPageable());

            searchHits.getContent().forEach(x-> {
                RobotKnowledgeSubDetailVO detailVO = new RobotKnowledgeSubDetailVO();
                BeanUtils.copyProperties(x.getContent(),detailVO);
                detailVO.setKnowledgeType(pageRequest.getParam().getKnowledgeType());
                robotKnowledgeSubDetailVOS.add(detailVO);
            });
            pageInfo.setList(robotKnowledgeSubDetailVOS);
            pageInfo.setPages(searchHits.getTotalPages());
            pageInfo.setIsFirstPage(searchHits.isFirst());
            pageInfo.setIsLastPage(searchHits.isLast());
            pageInfo.setTotal(searchHits.getTotalElements());
        }else {
            SearchHits<KnowledgePackageCloud> search = elasticSearchRestTemplate.search(nativeSearchQuery, KnowledgePackageCloud.class);
            SearchPage<KnowledgePackageCloud> searchHits = SearchHitSupport.searchPageFor(search, nativeSearchQuery.getPageable());

            searchHits.getContent().forEach(x-> {
                RobotKnowledgeSubDetailVO detailVO = new RobotKnowledgeSubDetailVO();
                BeanUtils.copyProperties(x.getContent(),detailVO);
                detailVO.setKnowledgeType(pageRequest.getParam().getKnowledgeType());
                robotKnowledgeSubDetailVOS.add(detailVO);
            });
            pageInfo.setList(robotKnowledgeSubDetailVOS);
            pageInfo.setPages(searchHits.getTotalPages());
            pageInfo.setIsFirstPage(searchHits.isFirst());
            pageInfo.setIsLastPage(searchHits.isLast());
            pageInfo.setTotal(searchHits.getTotalElements());
        }
        return pageInfo;
    }

    public void deleteByRobot(Long robotId,Integer type) {
        knowledgePackageCloudRepository.deleteAllByRobotIdAndEnvironment(robotId,type);
    }

    /**
    * @Description: 查询机器人知识详情
    * @Param: [robot, knowledgeId]
    * @return: com.kbao.kbcchatbot.robotknowledgeconfig.entity.KnowledgePackageCloud
    * @Author: husw
    * @Date: 2023/6/2 17:35
    */
    public RobotKnowledgeDetailRespVO getDetail(Long robot, String knowledgeId) {
        RobotKnowledgeDetailRespVO robotKnowledgeDetailRespVO = new RobotKnowledgeDetailRespVO();
        KnowledgePackageQA knowledgePackageQA = knowledgePackageQARepository.findByRobotIdAndEnvironmentAndKnowledgeId(robot, EnvTypeEnum.PROD_ENV.getCode(), knowledgeId);
        if (EmptyUtils.isEmpty(knowledgePackageQA)){
            KnowledgePackageCloud knowledgePackageCloud = knowledgePackageCloudRepository.findByRobotIdAndEnvironmentAndKnowledgeId(robot, EnvTypeEnum.PROD_ENV.getCode(), knowledgeId);
            if (EmptyUtils.isNotEmpty(knowledgePackageCloud)){
                BeanUtils.copyProperties(knowledgePackageCloud, robotKnowledgeDetailRespVO);
                return robotKnowledgeDetailRespVO;
            }
        }else {
            BeanUtils.copyProperties(knowledgePackageQA, robotKnowledgeDetailRespVO);
            return robotKnowledgeDetailRespVO;
        }
        return null;
    }

    public SearchPage<KnowledgePackageCloud> pageAll(RequestObjectPage<RobotKnowledgeConfigIdReqVO> pageRequest) {
        pageRequest.getParam().setType(EnvTypeEnum.PROD_ENV.getCode());
        Pageable pageable = PageRequest.of(pageRequest.getPageNum()-1,pageRequest.getPageSize());
        BoolQueryBuilder baseQueryBuilder = new BoolQueryBuilder();
        baseQueryBuilder.must(QueryBuilders.termQuery("robotId", pageRequest.getParam().getRobotId()));
        baseQueryBuilder.must(QueryBuilders.termQuery("secondDirect", pageRequest.getParam().getDirectoryId()));
        baseQueryBuilder.must(QueryBuilders.termQuery("environment", pageRequest.getParam().getType()));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withQuery(baseQueryBuilder)
                .withSort(SortBuilders.fieldSort("updateTime").order(SortOrder.DESC))
                .build();
        nativeSearchQuery.setPageable(pageable);

        SearchHits<KnowledgePackageCloud> search = elasticSearchRestTemplate.search(nativeSearchQuery, KnowledgePackageCloud.class);
        return SearchHitSupport.searchPageFor(search, nativeSearchQuery.getPageable());
    }

    /**
    * @Description: 查询知识库菜单funcId
    * @Param: []
    * @return: com.kbao.kbcbsc.param.entity.Param
    * @Author: husw
    * @Date: 2023/6/5 14:35
    */
    public Param getParam() {
        return kbcBscService.getParam(PARAM_CODE);
    }

    public List<RobotKnowledgeSubDetailVO> getFAQKnowledgeList(Long robotId, Integer environment) {
        BoolQueryBuilder baseQueryBuilder = new BoolQueryBuilder();
        baseQueryBuilder.must(QueryBuilders.termQuery("robotId", robotId));
        baseQueryBuilder.must(QueryBuilders.termQuery("environment", environment));
        baseQueryBuilder.must(QueryBuilders.termQuery("state", KnowledgeStateEnum.YES.getCode()));
        baseQueryBuilder.must(QueryBuilders.termQuery("isDeleted", "0"));
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withQuery(baseQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .build();

        List<RobotKnowledgeSubDetailVO> robotKnowledgeSubDetailVOS = new ArrayList<>();
        //QA
        SearchHits<KnowledgePackageQA> qaSearch = elasticSearchRestTemplate.search(nativeSearchQuery, KnowledgePackageQA.class);
        qaSearch.getSearchHits().forEach(x-> {
            RobotKnowledgeSubDetailVO detailVO = new RobotKnowledgeSubDetailVO();
            BeanUtils.copyProperties(x.getContent(), detailVO);
            detailVO.setKnowledgeType(ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_QA.getKey());
            robotKnowledgeSubDetailVOS.add(detailVO);
        });

        //CLOUD
        SearchHits<KnowledgePackageCloud> cloudSearch = elasticSearchRestTemplate.search(nativeSearchQuery, KnowledgePackageCloud.class);
        cloudSearch.getSearchHits().forEach(x-> {
            RobotKnowledgeSubDetailVO detailVO = new RobotKnowledgeSubDetailVO();
            BeanUtils.copyProperties(x.getContent(),detailVO);
            detailVO.setKnowledgeType(ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_CLOUD.getKey());
            robotKnowledgeSubDetailVOS.add(detailVO);
        });
        return robotKnowledgeSubDetailVOS;
    }

    public void refreshAll(RobotKnowledgeConfigRefreshAllReqVO param){
        Map<String, Object> paramMap = MapUtils.objectToMap(param);
        paramMap.put("type",EnvTypeEnum.PROD_ENV.getCode());
        paramMap.put("operationNotEq",KnowledgeOperationEnum.UNBIND.getCode());
        List<RobotKnowledgeConfig> robotKnowledgeConfigList = this.mapper.selectByCondition(paramMap);
        if (EmptyUtils.isEmpty(robotKnowledgeConfigList)){
            log.info("配置信息为空！");
            return;
        }
        log.info("待同步目录数量：{}",robotKnowledgeConfigList.size());
        String operateUserId = BscUserUtils.getUserId();
        for (RobotKnowledgeConfig robotKnowledgeConfig:robotKnowledgeConfigList){
            //新绑定的目录刷新不变更操作状态（绑定后再刷新会覆盖绑定操作）
            if (!KnowledgeOperationEnum.BIND.getCode().equals(robotKnowledgeConfig.getOperation())){
                robotKnowledgeConfig.setOperation(KnowledgeOperationEnum.REFRESH.getCode());
            }
            robotKnowledgeConfig.setSyncStatus(SyncStatusEnum.SYNCING.getCode());
            this.mapper.updateByPrimaryKey(robotKnowledgeConfig);
            //同步知识
            RobotKnowledgeRecord robotKnowledgeRecord = initKnowledgeRecord(robotKnowledgeConfig, KnowledgeOperationEnum.BIND, robotKnowledgeConfig.getDirectoryId(), robotKnowledgeConfig.getDirectoryName(), operateUserId);
            boolean operateStatus = robotKnowledgeAsyncService.syncKnowledge(robotKnowledgeConfig, BscApiContext.TenantId.get(), param.getOnlyFile());
            if (!operateStatus){
                robotKnowledgeRecord.setOperateStatus(OperateStatusEnum.FAIL.getCode());
            }
            robotKnowledgeRecordService.save(robotKnowledgeRecord);
        }
    }

    public void updateDirectoryCode(RobotKnowledgeConfigRefreshAllReqVO param){
        Map<String, Object> paramMap = MapUtils.objectToMap(param);
        paramMap.put("type",EnvTypeEnum.PROD_ENV.getCode());
        List<RobotKnowledgeConfig> robotKnowledgeConfigs = this.mapper.selectByCondition(paramMap);
        for (RobotKnowledgeConfig config:robotKnowledgeConfigs){
            int totalPage;
            int pageNum = 1;
            try {
                do {
                    RequestObjectPage<RobotKnowledgeSearchReqVO> pageRequest = new RequestObjectPage<>();
                    RobotKnowledgeSearchReqVO reqVO = new RobotKnowledgeSearchReqVO();
                    reqVO.setDirectoryId(config.getDirectoryId());
                    reqVO.setRobotId(config.getRobotId());
                    pageRequest.setPageNum(pageNum);
                    pageRequest.setPageSize(1000);
                    pageRequest.setParam(reqVO);
                    PageInfo<RobotKnowledgeSubDetailVO> detailVOPageInfo = pageSearch(pageRequest);
                    List<String> knowledgeIds = detailVOPageInfo.getList().stream().map(RobotKnowledgeSubDetailVO::getKnowledgeId).collect(Collectors.toList());
                    if (EmptyUtils.isNotEmpty(knowledgeIds)){
                        Map<String,Object> map = new HashMap<>();
                        map.put("directoryCode",config.getDirectoryCode());
                        map.put("knowledgeIds",knowledgeIds);
                        int i = robotKnowledgeDataService.getMapper().updateDirectoryCodeByKnowledgeIds(map);
                        log.info("更新目录编码成功，影响行数：{}",i);
                    }
                    totalPage = detailVOPageInfo.getPages();
                    //向量化数据
                    pageNum++;
                } while (totalPage >= pageNum);
            }catch (Exception e){
                log.error("更新数据异常：{}",e.getMessage(),e);
            }
        }



    }

    public void checkStatusByCode(String robotCode,List<String> directoryIds){
        Map<String,Object> map = new HashMap<>();
        map.put("robotCode",robotCode);
        map.put("type", EnvTypeEnum.PROD_ENV.getCode());
        map.put("syncStatus",SyncStatusEnum.SYNCING.getCode());
        map.put("directoryIdList",directoryIds);
        List<RobotKnowledgeConfig> robotKnowledgeConfigs = this.mapper.selectByCondition(map);
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigs)){
            throw new BusinessException("机器人存在正在同步中的知识，请等待同步完成后再操作！");
        }
    }
}
