package com.kbao.kbcchatbot.maas.project.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.kbcchatbot.maas.project.bean.ProjectPageReqVO;
import com.kbao.kbcchatbot.maas.project.bean.ProjectSimpleVO;
import com.kbao.kbcchatbot.maas.project.dao.ProjectMapper;
import com.kbao.kbcchatbot.maas.project.entity.Project;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @Description maas项目配置Service类
* @Date 2024-12-19
*/
@Service
public class ProjectService extends BaseSQLServiceImpl<Project, Integer, ProjectMapper> {

    @Autowired
    private ChannelService channelService;
    @Transactional(rollbackFor = Exception.class)
    public void save(Project project) {
        if(EmptyUtils.isEmpty(project.getName()) || EmptyUtils.isEmpty(project.getCode()) || EmptyUtils.isEmpty(project.getStatus())){
            throw new BusinessException("参数缺失！");
        }
        int count = this.mapper.isExistProject(project.getCode(), project.getId());
        if(count > 0){
            throw new BusinessException("项目编码已存在！");
        }
        String secretKey = project.getSecretKey();
        String currentUserId = BscUserUtils.getUserId();
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        project.setCreateBy(currentUserId);
        project.setCreateTime(DateUtils.getCurrentDate());
        project.setUpdateBy(currentUserId);
        project.setUpdateTime(DateUtils.getCurrentDate());
        project.setTenantId(tenantId);
        if(EmptyUtils.isEmpty(project.getId())){
            if(EmptyUtils.isEmpty(secretKey)){
                String uuidSecretKey = UUID.randomUUID().toString().replace("-", "");
                project.setSecretKey(uuidSecretKey);
            }
            this.mapper.insertSelective(project);
        } else {
            Project projectQuery = this.mapper.selectByPrimaryKey(project.getId());
            if(EmptyUtils.isNotEmpty(secretKey) && !secretKey.contains("****")){
                project.setSecretKey(secretKey);
            } else {
                project.setSecretKey(projectQuery.getSecretKey());
            }
            this.mapper.updateByPrimaryKeySelective(project);
        }
    }

    public PageInfo<Project> getProjectPage(PageRequest<ProjectPageReqVO> reqVo){
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize(), "t.create_time desc");
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        reqVo.getParam().setTenantId(tenantId);
        List<Project> projectList = this.mapper.getProjectList(reqVo.getParam());
        for(Project project : projectList){
            if(project.getSecretKey().length() > 4){
                //将密钥处理一下，只显示前4位和后4位，中间用****替换
                project.setSecretKey(project.getSecretKey().substring(0, 4) + "****" + project.getSecretKey().substring(project.getSecretKey().length() - 4));
            }
        }
        return new PageInfo<>(projectList);
    }
    @Transactional(rollbackFor = Exception.class)
    public void deleteProject(Integer projectId){
        Map<String, Object> map = new HashMap<>();
        map.put("projectId", projectId);
        int count = channelService.getMapper().count(map);
        if(count > 0){
            throw new BusinessException("该项目下有渠道，无法删除！");
        }
        this.mapper.deleteByPrimaryKey(projectId);
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Integer projectId, String status){
        Project project = new Project();
        String currentUserId = BscUserUtils.getUserId();
        project.setId(projectId);
        project.setStatus(status);
        project.setUpdateBy(currentUserId);
        project.setUpdateTime(DateUtils.getCurrentDate());
        this.mapper.updateByPrimaryKeySelective(project);
    }


    public List<ProjectSimpleVO> getSimpleList(){
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        return this.mapper.getSimpleProjectList(tenantId);
    }
}
