package com.kbao.kbcchatbot.maas.channel.channelguessquestion.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.entity.ChannelGuessQuestion;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @Description 渠道猜你想问Dao类
* @Date 2023-05-24
*/
public interface ChannelGuessQuestionMapper  extends BaseMapper<ChannelGuessQuestion, Long>{

    /**
     * 根据渠道id获取猜你想问列表
     * @param map<String,Object>
     * @return 猜你想问列表
     */
    List<ChannelGuessQuestion> getChannelGuessQuestionListByChannelId(Map<String,Object> map);

    /**
     * 根据渠道code获取猜你想问列表
     * @param map<String,Object>
     * @return 猜你想问列表
     */
    List<ChannelGuessQuestion> getChannelGuessQuestionListByChannelCode(Map<String,Object> map);

    /**
     * 根据渠道ID获取猜你想问条数
     */
    int getChannelGuessQuestionCountByChannelId(Long channelId);

    void updateSourceType();
}