package com.kbao.kbcchatbot.maas.chatsession.service;

import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerRespVO;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerTypeEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgePackageCloud;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.KnowledgePackageCloudApiService;
import com.kbao.kbcchatbot.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description QA问题澄清实现Service类
 * @Date 2023-6-13
 */
@Service
public class KnowledgeCloudClearSearchService extends ChatSearchAbstractService {

    @Autowired
    private KnowledgePackageCloudApiService knowledgePackageCloudApiService;


    @Override
    protected ChatQueryAnswerRespVO query(String question, Map<String, Object> slots,Integer sceneShowType, String sessionRecordId,boolean isAsync) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        KnowledgePackageCloud knowledgePackageCloud = knowledgePackageCloudApiService.selectByQAId(StringUtil.escape(question));
        respVO.setType(ChatAnswerTypeEnum.QUESTION_CLEAR_ANSWER.getKey());
        respVO.setTitle(knowledgePackageCloud.getTitle());
        respVO.setRelatedArticles(knowledgePackageCloud.getKnowledgeId());
        respVO.setRelatedArticlesName(knowledgePackageCloud.getTitle());
        respVO.setRecordId(sessionRecordId);
        return respVO;
    }

}
