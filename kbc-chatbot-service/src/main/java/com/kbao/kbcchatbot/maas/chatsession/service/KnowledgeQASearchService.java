package com.kbao.kbcchatbot.maas.chatsession.service;

import com.kbao.commons.enums.YesNoEnum;
import com.kbao.kbcchatbot.maas.chatsession.bean.ChatSessionMatchQuestionDTO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatKnowledgeClearRespVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerRespVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatSearchResultDTO;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerSourceEnum;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerTypeEnum;
import com.kbao.kbcchatbot.constants.ElasticsearchConstants;
import com.kbao.kbcchatbot.elasticsearch.index.service.IndexService;
import com.kbao.kbcchatbot.kbc.km.KbcKmService;
import com.kbao.kbcchatbot.discard.knowledgepackage.service.KnowledgePackageQAApiService;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgePackageCloud;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.KnowledgePackageCloudApiService;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.service.RobotSensitiveWordsConfigService;
import com.kbao.kbcchatbot.utils.StringUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.tool.util.EmptyUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.indices.AnalyzeResponse;
import org.elasticsearch.index.query.Operator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description QA搜索实现Service类
 * @Date 2023-6-13
 */
@Service
@Slf4j
public class KnowledgeQASearchService extends ChatSearchAbstractService {

    @Autowired
    private KnowledgePackageQAApiService knowledgePackageQAApiService;

    @Autowired
    private KnowledgePackageCloudApiService knowledgePackageCloudApiService;

    @Autowired
    private IndexService indexService;

    @Autowired
    private KbcKmService kbcKmService;

    @Autowired
    private RobotSensitiveWordsConfigService robotSensitiveWordsConfigService;

    @Override
    protected ChatQueryAnswerRespVO query(String question, Map<String, Object> slots, Integer sceneShowType, String sessionRecordId,boolean isAsync) {
        question = StringUtil.escape(question);
        Long robotId = ChatUserUtil.getUser().getRobotId();
        //直接回复阈值
        BigDecimal directReplyThreshold = ChatUserUtil.getUser().getChatSessionCache().getDirectReplyThreshold();
        //澄清阈值
        BigDecimal clearReplyThreshold = ChatUserUtil.getUser().getChatSessionCache().getClearReplyThreshold();
        //澄清问题最大条数
        int clearMaxCount = ChatUserUtil.getUser().getChatSessionCache().getClearMaxCount();
        //敏感词
        List<String> sensitiveWordsList = ChatUserUtil.getUser().getChatSessionCache().getSensitiveWords();
        //敏感词回复文案
        String sensitiveWords = ChatUserUtil.getUser().getChatSessionCache().getSensitiveWordsReply();
        //客服跳转链接
        String custServPlatAddr = ChatUserUtil.getUser().getChatSessionCache().getCustServPlatAddr();

        //敏感词校验
        boolean isSensitiveWords = checkSensitiveWords(question, sensitiveWordsList);
        if (isSensitiveWords) {
            return getCustomResp(ChatAnswerTypeEnum.SENSITIVE_WORD_ANSWER, sensitiveWords, sessionRecordId,null);
        }
        //转人工关键字校验
        if (checkTransferLabor(ChatUserUtil.getUser().getChatSessionCache(), question, YesNoEnum.YES.getValue(), null)) {
            return getCustomResp(ChatAnswerTypeEnum.TRANSFER_LABOR, custServPlatAddr, sessionRecordId,null);
        }
        //查询可见目录
//        List<String> visibleSecondDirectIds = kbcKmService.getVisibleSecondDirectIds();
        List<String> visibleSecondDirectIds = null;
        //检索云知识库
        List<ChatSearchResultDTO> packageCloudSearchResultDTOList = knowledgePackageCloudApiService.queryAnswer(question, robotId, ElasticsearchConstants.MATERIALS_KNOWLEDGE_PACKAGE_CLOUD_INDEX_ID, clearMaxCount, Operator.OR.name(), visibleSecondDirectIds);
        //检索知识包
        List<ChatSearchResultDTO> packageQASearchResultDTOList = knowledgePackageQAApiService.queryAnswer(question, robotId, ElasticsearchConstants.MATERIALS_KNOWLEDGE_PACKAGE_QA_INDEX_ID, clearMaxCount, Operator.OR.name(), visibleSecondDirectIds);
        List<ChatSearchResultDTO> searchResultDTOList = new ArrayList<>(packageCloudSearchResultDTOList.size() + packageQASearchResultDTOList.size());
        //按照知识问题去重，优先保留QA
        Map<String,ChatSearchResultDTO> searchResultDTOMap = new HashMap<>();
        packageCloudSearchResultDTOList.forEach(x->searchResultDTOMap.put(x.getQuestion(),x));
        packageQASearchResultDTOList.forEach(x->searchResultDTOMap.put(x.getQuestion(),x));
        searchResultDTOList.addAll(searchResultDTOMap.values());
        if (searchResultDTOList.size() == 0) {
            return   getNoAnswerResp(ChatUserUtil.getUser().getChatSessionCache().getNoAnswerContent(), sessionRecordId,null);
        } else {
            List<AnalyzeResponse.AnalyzeToken> questionTokenList = indexService.getIkAnalysisList(question, ElasticsearchConstants.ANALYZER_IK_MAX_WORD_WITH_SYNONYM);
            questionTokenList = filterTokenList(questionTokenList);
            List<ChatKnowledgeClearRespVO> knowledgeClearVOList = new ArrayList<>();
            for (ChatSearchResultDTO searchResultDTO : searchResultDTOList) {
                //计算匹配度
                ChatSessionMatchQuestionDTO matchQuestionDTO = getMatchPercent(questionTokenList, searchResultDTO);
                searchResultDTO.setMatchPercent(matchQuestionDTO.getMatchPercent());
            }
            ChatSearchResultDTO chatSearchResultDTO = searchResultDTOList.stream().max(Comparator.comparing(ChatSearchResultDTO::getMatchPercent)).get();
            //大于直接回复阈值
            if (chatSearchResultDTO.getMatchPercent().compareTo(directReplyThreshold) == 1) {
                return getDirectAnswerResp(chatSearchResultDTO);
            }

            for (ChatSearchResultDTO searchResultDTO : searchResultDTOList) {
                //澄清阈值区间
                if (searchResultDTO.getMatchPercent().compareTo(clearReplyThreshold) == 1 && searchResultDTO.getMatchPercent().compareTo(directReplyThreshold) != 1) {
                    ChatKnowledgeClearRespVO chatKnowledgeClearRespVO = new ChatKnowledgeClearRespVO();
                    chatKnowledgeClearRespVO.setMatchPercent(searchResultDTO.getMatchPercent());
                    chatKnowledgeClearRespVO.setQaSource(searchResultDTO.getQaSource());
                    chatKnowledgeClearRespVO.setQaId(searchResultDTO.getQaId());
                    chatKnowledgeClearRespVO.setKnowledgeId(searchResultDTO.getKnowledgeId());
                    //使用匹配上的question
                    chatKnowledgeClearRespVO.setQuestion(searchResultDTO.getQuestion());
                    knowledgeClearVOList.add(chatKnowledgeClearRespVO);
                }
            }
            //澄清回复
            if (knowledgeClearVOList.size() > 0) {
                //处理排序
                List<ChatKnowledgeClearRespVO> knowledgeClearVOSortList = knowledgeClearVOList.stream()
                        .sorted(Comparator.comparing(ChatKnowledgeClearRespVO::getMatchPercent).reversed()).collect(Collectors.toList());
                return getClearAnswerResp(knowledgeClearVOSortList, clearMaxCount);
            }
            //无回复
            else {
                return getNoAnswerResp(ChatUserUtil.getUser().getChatSessionCache().getNoAnswerContent(), sessionRecordId,null);
            }
        }
    }

//    private boolean checkSensitiveWords(String question, List<String> sensitiveWords) {
//        if (EmptyUtils.isNotEmpty(sensitiveWords)){
//            for (String words: sensitiveWords){
//                if (question.contains(words)){
//                    return true;
//                }
//            }
//        }
//        return false;
//    }

    public ChatSessionMatchQuestionDTO getMatchPercent(List<AnalyzeResponse.AnalyzeToken> questionTokenList, ChatSearchResultDTO searchResultDTO) {
        ChatSessionMatchQuestionDTO dto = new ChatSessionMatchQuestionDTO();
        String question = searchResultDTO.getQuestion();
        dto.setQuestion(question);
        BigDecimal questionMatchPercent = computerMatchPercent(questionTokenList, question);
        if (!CollectionUtils.isEmpty(searchResultDTO.getSimilarQuestions())) {
            for (String similarQuestion : searchResultDTO.getSimilarQuestions()) {
                BigDecimal similarQuestionMatchPercent = computerMatchPercent(questionTokenList, similarQuestion);
                if (similarQuestionMatchPercent.compareTo(questionMatchPercent) == 1) {
                    questionMatchPercent = similarQuestionMatchPercent;
                    dto.setQuestion(similarQuestion);
                }
            }
        }
        dto.setMatchPercent(questionMatchPercent);
        return dto;
    }

    private BigDecimal computerMatchPercent(List<AnalyzeResponse.AnalyzeToken> questionTokenList, String searchHitsQuestion) {
        int matchCount = 0;
        List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionTokenList = indexService.getIkAnalysisList(searchHitsQuestion, ElasticsearchConstants.ANALYZER_IK_MAX_WORD_WITH_SYNONYM);
        if (CollectionUtils.isEmpty(searchHitsQuestionTokenList)) {
            return BigDecimal.ZERO;
        }
        searchHitsQuestionTokenList = filterTokenList(searchHitsQuestionTokenList);

        long searchHitQuestionTokenSize = getSearchHitsQuestionTokenListSize(searchHitsQuestionTokenList);
        if (searchHitQuestionTokenSize == 0) {
            return BigDecimal.ZERO;
        }
        int synonymWordMatchCount = 0;
        for (AnalyzeResponse.AnalyzeToken questionToken : questionTokenList) {
            Optional<AnalyzeResponse.AnalyzeToken> searchHitQuestionTokenOptional = searchHitsQuestionTokenList.stream().filter(searchHitQuestionToken -> searchHitQuestionToken.getTerm().equals(questionToken.getTerm())).findFirst();
            if (searchHitQuestionTokenOptional.isPresent()) {
                if (ElasticsearchConstants.IK_TOKEN_TYPE_SYNONYM.equals(searchHitQuestionTokenOptional.get().getType())) {
                    synonymWordMatchCount++;
                }
                matchCount++;
            }
        }
        BigDecimal matchPercent;
        if (synonymWordMatchCount > 0) {
            matchPercent = new BigDecimal(matchCount - synonymWordMatchCount).divide(new BigDecimal(searchHitQuestionTokenSize), 2, BigDecimal.ROUND_UP);
        } else {
            matchPercent = new BigDecimal(matchCount).divide(new BigDecimal(searchHitQuestionTokenSize), 2, BigDecimal.ROUND_UP);
//            matchPercent = new BigDecimal(matchCount).divide(new BigDecimal(questionTokenList.size()), 2, BigDecimal.ROUND_UP);
        }
        return matchPercent;
    }

    private long getSearchHitsQuestionTokenListSize(List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionTokenList) {
        return searchHitsQuestionTokenList.stream().filter(item -> !ElasticsearchConstants.IK_TOKEN_TYPE_SYNONYM.equals(item.getTerm())).count();
    }


    @SneakyThrows
    public List<AnalyzeResponse.AnalyzeToken> filterTokenList(List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionTokenList) {
        List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionList = searchHitsQuestionTokenList;
        searchHitsQuestionTokenList = searchHitsQuestionTokenList.stream().filter(searchHitQuestionToken -> searchHitQuestionToken.getTerm().length() > 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(searchHitsQuestionTokenList)) {
            searchHitsQuestionTokenList = searchHitsQuestionList;

        }
        List<AnalyzeResponse.AnalyzeToken> tokenList = new ArrayList<>();
        List<String> wordList = new ArrayList<>();
        searchHitsQuestionTokenList.forEach(item -> {
            if (!wordList.contains(item.getTerm())) {
                tokenList.add(item);
                wordList.add(item.getTerm());
            }
        });
        return tokenList;
    }

    @SneakyThrows
    public void updateTokenList(List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionTokenList, String query) {
        List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionList = searchHitsQuestionTokenList;
        searchHitsQuestionTokenList = searchHitsQuestionTokenList.stream().filter(searchHitQuestionToken -> searchHitQuestionToken.getTerm().length() > 1).collect(Collectors.toList());
        // 在if条件前加上一个判断，检查变量的值是否为false

        if (CollectionUtils.isEmpty(searchHitsQuestionTokenList)) {

            AnalyzeResponse.AnalyzeToken analyzeToken = searchHitsQuestionList.get(0);
            // 获取AnalyzeResponse.AnalyzeToken类的Class对象
            Class<?> clazz = analyzeToken.getClass();
            // 获取setTerm方法的Method对象
            Method setTermMethod = clazz.getDeclaredMethod("setTerm", String.class);
            // 设置setTerm方法的访问权限为true，以便可以调用它
            setTermMethod.setAccessible(true);
            // 调用setTerm方法，修改term的值
            setTermMethod.invoke(analyzeToken, query);

            searchHitsQuestionTokenList.add(analyzeToken);
        }


    }

    private ChatQueryAnswerRespVO getClearAnswerResp(List<ChatKnowledgeClearRespVO> knowledgeClearVOList, int clearMaxCount) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        respVO.setType(ChatAnswerTypeEnum.QUESTION_CLEAR.getKey());
        if (knowledgeClearVOList.size() > clearMaxCount) {
            respVO.setAnswer(knowledgeClearVOList.subList(0, clearMaxCount - 1));
        } else {
            respVO.setAnswer(knowledgeClearVOList);
        }
        return respVO;
    }

    private ChatQueryAnswerRespVO getDirectAnswerResp(ChatSearchResultDTO searchResultDTO) {
        /*ChatKnowledgeAnswerRespVO knowledgeAnswerVO = new ChatKnowledgeAnswerRespVO();
        knowledgeAnswerVO.setMatchPercent(matchPercent);
        knowledgeAnswerVO.setContent(answer);*/

        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        respVO.setType(ChatAnswerTypeEnum.KNOWLEDGE_ANSWER.getKey());
        respVO.setQaSource(searchResultDTO.getQaSource());
        respVO.setTitle(searchResultDTO.getQuestion());
        if (ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_QA.getKey().equals(searchResultDTO.getQaSource())) {
            respVO.setAnswer(searchResultDTO.getAnswer());
            //查询关联知识
            String relatedArticlesName = getCloudTitle(searchResultDTO.getRelatedArticles(), ChatUserUtil.getUser().getRobotId());
            if (EmptyUtils.isNotEmpty(relatedArticlesName)) {
                respVO.setRelatedArticles(searchResultDTO.getRelatedArticles());
                respVO.setRelatedArticlesName(relatedArticlesName);
            }
        } else {
            respVO.setRelatedArticles(searchResultDTO.getKnowledgeId());
            respVO.setRelatedArticlesName(searchResultDTO.getQuestion());
        }
        log.info("答复知识ID：{}", searchResultDTO.getKnowledgeId());
        return respVO;
    }


    public String getCloudTitle(String knowledgeId, Long robotId) {
        if (EmptyUtils.isEmpty(knowledgeId)) {
            return null;
        }
        KnowledgePackageCloud knowledgePackageCloud = knowledgePackageCloudApiService.selectByKnowledgeId(knowledgeId, robotId, EnvTypeEnum.PROD_ENV.getCode());
        if (EmptyUtils.isNotEmpty(knowledgePackageCloud)) {
            return knowledgePackageCloud.getTitle();
        }
        return null;
    }
}
