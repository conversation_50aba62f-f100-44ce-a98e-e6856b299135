package com.kbao.kbcchatbot.maas.product.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.web.PageRequest;
import com.kbao.job.core.util.DateUtil;
import com.kbao.kbcchatbot.maas.product.bean.ProductSyncVo;
import com.kbao.kbcchatbot.maas.product.entity.ProductSyncLog;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.service.RobotKnowledgeHttpService;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class ProductSyncService {
    @Autowired
    private ProductSyncLogService productSyncLogService;
    @Autowired
    private RobotKnowledgeHttpService robotKnowledgeHttpService;

    public void syncProducts() {
        Integer pageNum = 1, pageSize = 20;
        ProductSyncVo productSyncVo = new ProductSyncVo();
        ProductSyncLog lastSyncLog = productSyncLogService.getLastSyncLog();
        if (lastSyncLog != null) {
            productSyncVo.setLastSyncTime(DateUtil.formatDateTime(lastSyncLog.getCreateTime()));
        }
        PageRequest<ProductSyncVo> pageRequest = new PageRequest<>();
        pageRequest.setParam(productSyncVo);
        pageRequest.setPageSize(pageSize);
        Integer productNum = 0;
        while (true) {
            log.info("同步产品数据，当前页码：{}", pageNum);
            pageRequest.setPageNum(pageNum++);
            Integer num = robotKnowledgeHttpService.syncProductData(pageRequest);
            if (num == 0) {
                break;
            }
            productNum += num;
        }
        Date createTime = DateTime.now().minusMinutes(-1).toDate();
        ProductSyncLog productSyncLog = ProductSyncLog.builder()
                .syncType("0").productNum(productNum).createTime(createTime).build();
        productSyncLogService.insert(productSyncLog);
    }


    public void syncProductFileData() {
        log.info("同步产品文件数据...");
        robotKnowledgeHttpService.syncProductFileData();
    }

    public void syncCompanyFileData() {
        log.info("同步保司文件数据...");
        robotKnowledgeHttpService.syncCompanyFileData();
    }
}
