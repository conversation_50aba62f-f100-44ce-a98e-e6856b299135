package com.kbao.kbcchatbot.maas.commonsetting.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.commonsetting.entity.CommonSetting;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2025-01-09
*/
public interface CommonSettingMapper extends BaseMapper<CommonSetting, Integer>{

    void saveSetting(CommonSetting setting);

    String getSettingValue(@Param("code") String code);
}