package com.kbao.kbcchatbot.maas.base;

import com.kbao.kbcbsc.wechat.request.AppParam;
import com.kbao.kbcbsc.wechat.request.WechatParam;
import com.kbao.kbcbsc.wechat.response.SignatureVO;
import com.kbao.kbcccs.dc.commons.context.HeaderContext;
import com.kbao.kbcchatbot.kbc.bsc.KbcBscService;
import com.kbao.kbcchatbot.kbc.bsc.config.KbcBscConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WxAuthService {
    @Autowired
    private KbcBscService kbcBscService;
    @Autowired
    private KbcBscConfig kbcBscConfig;


    public SignatureVO getSignature(String url) {
        //获取头信息
        WechatParam wechatParam = new WechatParam();
        wechatParam.setAuthUrl(url);
        AppParam appParam = kbcBscService.getAppParam(wechatParam, HeaderContext.getTenantId(), kbcBscConfig.getAppCode(), "2");
        return kbcBscService.getSignature(appParam);
    }


}
