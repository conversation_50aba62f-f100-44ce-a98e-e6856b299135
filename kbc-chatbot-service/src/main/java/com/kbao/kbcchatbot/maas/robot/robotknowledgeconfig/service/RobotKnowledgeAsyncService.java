package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.kbao.common.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcchatbot.discard.knowledgepackage.dao.KnowledgePackageQARepository;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackageQA;
import com.kbao.kbcchatbot.discard.robot.entity.Robot;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.discard.robot.enums.RobotReleaseTypeEnum;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.kbc.km.KbcKmService;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatKnowledgeTypeEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.service.RobotKnowledgeDataService;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.dao.KnowledgePackageCloudRepository;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.dao.RobotKnowledgeConfigMapper;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgePackageCloud;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums.KnowledgeOperationEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums.SyncStatusEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeDelVO;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeEmbeddingVO;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeTrainVO;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeClaimSyncVO;
import com.kbao.kbckm.directory.bean.DirectTreeReqVo;
import com.kbao.kbckm.knowledge.bean.KnowledgeChapter;
import com.kbao.kbckm.knowledge.entity.KnowledgeBase;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.kbao.kbcchatbot.constants.CacheConstant.SYNC_DEFAULT_ROBOT_CODE;

/**
 * @program: kbc-chatbot
 * @description: 知识配置异步处理
 * @author: husw
 * @create: 2023-05-31 15:15
 **/
@Service
@Slf4j
public class RobotKnowledgeAsyncService extends BaseSQLServiceImpl<RobotKnowledgeConfig,Long, RobotKnowledgeConfigMapper> {
    @Autowired
    private ElasticsearchOperations elasticsearchOperations;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private KnowledgePackageCloudRepository knowledgePackageCloudRepository;

    @Autowired
    private KnowledgePackageQARepository knowledgePackageQARepository;
    @Autowired
    @Lazy
    private KbcKmService kbcKmService;

    @Autowired
    private MaasHttpService maasHttpService;

    @Autowired
    private RobotKnowledgeDataService robotKnowledgeDataService;

/*    @Autowired
    @Lazy
    private RobotService robotService;*/


    private static final String DIRECT_TYPE = "4";
    private static final int PAGE_SIZE = 100;
    /**
     * @Description: 同步知识
     * @Param: [directId]
     * @return: void
     * @Author: husw
     * @Date: 2023/5/30 10:55
     */
    public boolean syncKnowledge(RobotKnowledgeConfig knowledgeConfig,String tenantId, String onlyFile) {
        int totalPage;
        int pageNum = 1;
        boolean operateStatus = true;
        try {
            //向量化数据清理
            knowledgeConfig.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
            //只同步文件的情况下，不删除原有数据
            if (!"1".equals(onlyFile)) {
                deleteKnowledgeByCode(knowledgeConfig.getDirectoryCode(),knowledgeConfig.getRobotCode());
            }
            do {
                //分页查询知识
                PageInfo<KnowledgeBase> knowledgePageList = getKmKnowledgeList(knowledgeConfig, tenantId, pageNum);
                log.info("知识信息：{}", JSON.toJSONString(knowledgePageList));
                if (EmptyUtils.isEmpty(knowledgePageList) || EmptyUtils.isEmpty(knowledgePageList.getList())) {
                    break;
                }
                totalPage = knowledgePageList.getPages();
                //向量化数据
                embeddingDataByCode(knowledgePageList.getList(),knowledgeConfig.getRobotCode(),knowledgeConfig.getDirectoryCode(), onlyFile);
                pageNum++;
                //知识配置信息
                setKnowledgeConfig(knowledgeConfig, knowledgePageList.getList().get(0));
            } while (totalPage >= pageNum);
        }catch (Exception e){
            operateStatus = false;
            log.error("同步知识数据异常：{}",e.getMessage(),e);
            knowledgeConfig.setSyncStatus(SyncStatusEnum.FAIL.getCode());
            if (e.getMessage().length()>=499){
                knowledgeConfig.setFailMsg(e.getMessage().substring(0,499));
            }else {
                knowledgeConfig.setFailMsg(e.getMessage());
            }
        }
        this.mapper.updateByPrimaryKeySelective(knowledgeConfig);
        return operateStatus;
    }

    private void setKnowledgeConfig(RobotKnowledgeConfig knowledgeConfig, KnowledgeBase knowledgeBase) {
        knowledgeConfig.setProjectId(knowledgeBase.getProjectId());
        knowledgeConfig.setProjectName(knowledgeBase.getProjectName());
        knowledgeConfig.setFolderId(knowledgeBase.getFolderId());
        knowledgeConfig.setFolderName(knowledgeBase.getFolderName());
        knowledgeConfig.setDirectoryId(knowledgeBase.getFirstDirect());
        knowledgeConfig.setDirectoryName(knowledgeBase.getFirstDirectName());
        knowledgeConfig.setDirectoryId(knowledgeBase.getSecondDirect());
        knowledgeConfig.setDirectoryName(knowledgeBase.getSecondDirectName());
        knowledgeConfig.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
    }
    /**
    * @Description: 查询知识库
    * @Param: [knowledgeConfig, tenantId, pageNum]
    * @return: com.github.pagehelper.PageInfo<com.kbao.kbckm.knowledge.entity.KnowledgeBase>
    * @Author: husw
    * @Date: 2024/9/3 13:53
    */
    private PageInfo<KnowledgeBase> getKmKnowledgeList(RobotKnowledgeConfig knowledgeConfig, String tenantId, int pageNum) {
        PageRequest<DirectTreeReqVo> pageRequest = new PageRequest<>();
        DirectTreeReqVo reqVo = new DirectTreeReqVo();
        reqVo.setTenantId(tenantId);
        reqVo.setDirectId(knowledgeConfig.getDirectoryId());
        //根据二级目录查询
        reqVo.setDirectType(DIRECT_TYPE);
        pageRequest.setPageNum(pageNum);
        pageRequest.setPageSize(PAGE_SIZE);
        pageRequest.setParam(reqVo);
        return kbcKmService.getKnowledgePageList(pageRequest);
    }

    /**
     * @Description: 同步云知识库知识
     * @Param: [knowledgePackage, knowledgeBase]
     * @return: void
     * @Author: husw
     * @Date: 2023/5/26 10:59
     */
    /*public void embeddingData(List<KnowledgeBase> knowledgeBases,Long robotId, String directoryCode) {
        List<RobotKnowledgeEmbeddingVO.Embedding> embeddings = new ArrayList<>();
        //获取机器人编码
        Robot robot = robotService.selectByPrimaryKey(robotId);
        if (EmptyUtils.isEmpty(robot)){
            throw new BusinessException("机器人不存在!");
        }
        for (KnowledgeBase bases:knowledgeBases){
            //查询完整的知识数据
            KnowledgeBase knowledgeBase = kbcKmService.findById(bases.getKnowledgeId());
            if (!"2".equals(knowledgeBase.getState())){
                //未发布知识不同步
                continue;
            }
            embeddings.add(initEmbeddingData(knowledgeBase,bases.getFirstDirectName(), robot.getCode(),directoryCode));
        }
        //推送数据
        sendLargeEmbedding(embeddings);
    }*/
    /**
    * @Description: 向量化数据
    * @Param: [qaList, robot]
    * @return: void
    * @Author: husw
    * @Date: 2024/9/3 15:41
    */
    private void sendLargeEmbedding(RobotKnowledgeEmbeddingVO.Embedding embedding) {
        RobotKnowledgeEmbeddingVO embeddingVO = new RobotKnowledgeEmbeddingVO();
        embeddingVO.setDatas(Collections.singletonList(embedding));
        try {
            maasHttpService.embeddingModel(embeddingVO);
        }catch (Exception e){
            log.error("知识ID：{}，向量化失败。",embedding.getKnowledgeId());
            boolean hasUnfinished = robotKnowledgeDataService.hasUnfinishedKnowledgeData(embedding.getRobotCode(), embedding.getKnowledgeId());
            if (!hasUnfinished){
                throw new BusinessException(e.getMessage());
            } else {
                for (int i = 0;  hasUnfinished && i <= 10; i++) {
                    try {
                        Thread.sleep(60 * 1000);
                    } catch (InterruptedException ex) {
                        throw new RuntimeException(ex);
                    }
                    hasUnfinished = robotKnowledgeDataService.hasUnfinishedKnowledgeData(embedding.getRobotCode(), embedding.getKnowledgeId());
                }
                if (hasUnfinished){
                    throw new BusinessException(e.getMessage());
                }
            }
        }
    }
    /**
     * @Description: 删除物料库知识
     * @Param: [secondDirect]
     * @return: void
     * @Author: husw
     * @Date: 2023/5/29 11:54
     */
    /*public void deleteKnowledge(String directoryCode,Long robotId){
        Robot robot = robotService.getMapper().selectByPrimaryKey(robotId);
        RobotKnowledgeDelVO delVo = new RobotKnowledgeDelVO();
        delVo.setDirectoryCode(directoryCode);
        delVo.setRobotCode(robot.getCode());
        largeModelService.embeddingDel(delVo);
    }*/
    /**
    * @Description: 机器人发布更新ES知识数据
    * @Param: [robotId, operationMap]
    * @return: void
    * @Author: husw
    * @Date: 2023/6/2 15:45
    */
    public RobotKnowledgeTrainVO releaseKnowledge(Robot robot, Map<Integer, List<RobotKnowledgeConfig>> operationMap) {
        RobotKnowledgeTrainVO trainVO = new RobotKnowledgeTrainVO();
        RobotKnowledgeDelVO robotKnowledgeDelVO = new RobotKnowledgeDelVO();
        RobotKnowledgeEmbeddingVO embeddingVO = new RobotKnowledgeEmbeddingVO();
        Long robotId = robot.getId();
        String robotCode = robot.getCode();
        Set<String> knowledgeIdSet = new HashSet<>();
        //解绑
        List<RobotKnowledgeConfig> robotKnowledgeConfigUnBindList = operationMap.get(KnowledgeOperationEnum.UNBIND.getCode());
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigUnBindList)){
            //知识
            robotKnowledgeConfigUnBindList.forEach(item-> {
                if (RobotReleaseTypeEnum.BIG_MODEL.getCode().equals(robot.getReleaseType())){
                    List<KnowledgePackageCloud> knowledgePackageCloudList = knowledgePackageCloudRepository.findByRobotIdAndEnvironmentAndSecondDirect(robotId, EnvTypeEnum.PROD_ENV.getCode(), item.getDirectoryId());
                    knowledgePackageCloudList.forEach(x->{
                        x.setId(IdWorker.getIdStr());
                        x.setEnvironment(EnvTypeEnum.PROD_ENV.getCode());
                        knowledgeIdSet.add(x.getKnowledgeId());
                    });
                }
                knowledgePackageCloudRepository.deleteAllByRobotIdAndEnvironmentAndSecondDirect(robotId, EnvTypeEnum.PROD_ENV.getCode(),item.getDirectoryId());
            });
            //QA
            robotKnowledgeConfigUnBindList.forEach(item-> {
                if (RobotReleaseTypeEnum.BIG_MODEL.getCode().equals(robot.getReleaseType())){
                    List<KnowledgePackageQA> knowledgePackageQaList = knowledgePackageQARepository.findByRobotIdAndEnvironmentAndSecondDirect(robotId, EnvTypeEnum.PROD_ENV.getCode(), item.getDirectoryId());
                    knowledgePackageQaList.forEach(x->{
                        x.setQaId(IdWorker.getIdStr());
                        x.setEnvironment(EnvTypeEnum.PROD_ENV.getCode());
                        knowledgeIdSet.add(x.getKnowledgeId());
                    });
                }
                knowledgePackageQARepository.deleteAllByRobotIdAndEnvironmentAndSecondDirect(robotId, EnvTypeEnum.PROD_ENV.getCode(),item.getDirectoryId());
            });
        }
        //新增和刷新
        List<RobotKnowledgeEmbeddingVO.Embedding> embeddingList = new ArrayList<>();
        List<RobotKnowledgeConfig> robotKnowledgeConfigAddList = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(operationMap.get(KnowledgeOperationEnum.REFRESH.getCode()))){
            robotKnowledgeConfigAddList.addAll(operationMap.get(KnowledgeOperationEnum.REFRESH.getCode()));
            //向量化数据重新同步可能存在删除了的数据，此处先将数据都过滤出来删除
            getDelKnowledge(robotKnowledgeConfigAddList, robotId, knowledgeIdSet);
        }
        if (EmptyUtils.isNotEmpty(operationMap.get(KnowledgeOperationEnum.BIND.getCode()))){
            robotKnowledgeConfigAddList.addAll(operationMap.get(KnowledgeOperationEnum.BIND.getCode()));
        }
        if (EmptyUtils.isNotEmpty(robotKnowledgeConfigAddList)){
            robotKnowledgeConfigAddList.forEach(item->{
                //知识
                knowledgePackageCloudRepository.deleteAllByRobotIdAndEnvironmentAndSecondDirect(robotId,1,item.getDirectoryId());
                List<KnowledgePackageCloud> knowledgePackageCloudList = knowledgePackageCloudRepository.findByRobotIdAndEnvironmentAndSecondDirect(robotId, EnvTypeEnum.TEST_ENV.getCode(), item.getDirectoryId());
                knowledgePackageCloudList.forEach(x->{
                    x.setId(IdWorker.getIdStr());
                    x.setEnvironment(EnvTypeEnum.PROD_ENV.getCode());
//                    initEmbeddingCloud(x, embeddingList, robotCode);
                });
                if (EmptyUtils.isNotEmpty(knowledgePackageCloudList)){
                    knowledgePackageCloudRepository.saveAll(knowledgePackageCloudList);
                }
                //QA
                knowledgePackageQARepository.deleteAllByRobotIdAndEnvironmentAndSecondDirect(robotId,1,item.getDirectoryId());
                List<KnowledgePackageQA> knowledgePackageQaList = knowledgePackageQARepository.findByRobotIdAndEnvironmentAndSecondDirect(robotId, EnvTypeEnum.TEST_ENV.getCode(), item.getDirectoryId());
                knowledgePackageQaList.forEach(x->{
                    x.setQaId(IdWorker.getIdStr());
                    x.setEnvironment(EnvTypeEnum.PROD_ENV.getCode());
//                    initEmbeddingQA(x,embeddingList,robotCode);
                });
                if (EmptyUtils.isNotEmpty(knowledgePackageQaList)){
                    knowledgePackageQARepository.saveAll(knowledgePackageQaList);
                }
            });
        }
//        embeddingVO.setDatas(embeddingList);
//        robotKnowledgeDelVO.setIds(new ArrayList<>(knowledgeIdSet));
//        //封装数据包对象
//        trainVO.setEmbeddingVO(embeddingVO);
//        trainVO.setDelVO(robotKnowledgeDelVO);
        return trainVO;
    }
    /**
    * @Description: 获取删除数据
    * @Param: [robotKnowledgeConfigAddList, robotId, knowledgeIdSet]
    * @return: void
    * @Author: husw
    * @Date: 2024/9/4 11:15
    */
    private void getDelKnowledge(List<RobotKnowledgeConfig> robotKnowledgeConfigAddList, Long robotId, Set<String> knowledgeIdSet) {
        if (EmptyUtils.isEmpty(robotKnowledgeConfigAddList)){
            return;
        }
        robotKnowledgeConfigAddList.forEach(item->{
            List<KnowledgePackageCloud> knowledgePackageCloudList = knowledgePackageCloudRepository.
                    findByRobotIdAndEnvironmentAndSecondDirect(robotId, EnvTypeEnum.TEST_ENV.getCode(), item.getDirectoryId());
            List<KnowledgePackageQA> knowledgePackageQaList = knowledgePackageQARepository.
                    findByRobotIdAndEnvironmentAndSecondDirect(robotId, EnvTypeEnum.TEST_ENV.getCode(), item.getDirectoryId());
            if (EmptyUtils.isNotEmpty(knowledgePackageCloudList)){
                knowledgeIdSet.addAll(knowledgePackageCloudList.stream().map(KnowledgePackageCloud::getKnowledgeId).collect(Collectors.toSet()));
            }
            if (EmptyUtils.isNotEmpty(knowledgePackageQaList)){
                knowledgeIdSet.addAll(knowledgePackageQaList.stream().map(KnowledgePackageQA::getKnowledgeId).collect(Collectors.toSet()));
            }
        });
    }

    private RobotKnowledgeEmbeddingVO.Embedding initEmbeddingData(KnowledgeBase knowledgeBase,String firstDirectName,String robotCode,String directoryCode){
        RobotKnowledgeEmbeddingVO.Embedding embedding = new RobotKnowledgeEmbeddingVO.Embedding();
        embedding.setKnowledgeId(knowledgeBase.getKnowledgeId());
        embedding.setTitle(knowledgeBase.getTitle());
        embedding.setDirectoryCode(directoryCode);
        embedding.setRobotCode(robotCode);
        embedding.setSource("1");
        embedding.setFullTitle(StringUtil.join(firstDirectName,"-",
                knowledgeBase.getSecondDirectName(),"-",knowledgeBase.getTitle()));
        List<RobotKnowledgeEmbeddingVO.Item> itemList = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(knowledgeBase.getChapters())){
            knowledgeBase.getChapters().forEach(chapter-> itemList.add(initEmbeddingItem(
                    chapter.getChapterTitle(),getChapterAnswer(chapter),chapter.getChapterType(),chapter.getEnclosureName())));
        }else {
            itemList.add(initEmbeddingItem(knowledgeBase.getTitle(),getAnswer(knowledgeBase.getChapters(),knowledgeBase.getSubContent()), null,null));
        }
        embedding.setItemList(itemList);
        return embedding;
    }
    public String getChapterAnswer(KnowledgeChapter chapter) {
        if (ChatKnowledgeTypeEnum.FILE.getKey().equals(chapter.getChapterType())
                || ChatKnowledgeTypeEnum.VIDEO.getKey().equals(chapter.getChapterType())) {
            //文件附件
            return chapter.getEnclosureUrl();
        }else {
            return chapter.getChapterContent();
        }
    }

    public String getAnswer(List<KnowledgeChapter> chapters,String subContent){
        if(CollectionUtils.isEmpty(chapters)) {
            return subContent;
        }else {
            return chapters.get(0).getChapterContent();
        }
    }
    /**
    * @Description: 章节数据
    * @Param: [knowledgeId, question, answer, firstDicName, secondDicName, qaList]
    * @return: void
    * @Author: husw
    * @Date: 2024/6/14 11:17
    */
    private RobotKnowledgeEmbeddingVO.Item initEmbeddingItem(String chapterTitle, String answer,String type,String fileName) {
        RobotKnowledgeEmbeddingVO.Item item = new RobotKnowledgeEmbeddingVO.Item();
        item.setType(EmptyUtils.isEmpty(type)?ChatKnowledgeTypeEnum.TEXT.getKey():type);
        item.setChapterTitle(chapterTitle);
        item.setContent(EmptyUtils.isEmpty(answer)?"":answer);
        item.setFileName(fileName);
        return item;
    }
    /**
     * @Description: 删除物料库知识
     * @Param: [secondDirect]
     * @return: void
     * @Author: husw
     * @Date: 2023/5/29 11:54
     */
    public void deleteKnowledgeByCode(String directoryCode,String robotCode){
        RobotKnowledgeDelVO delVo = new RobotKnowledgeDelVO();
        delVo.setDirectoryCode(directoryCode);
        delVo.setRobotCode(robotCode);
        maasHttpService.embeddingDel(delVo);
    }
    /**
    * @Description: 根据知识ID删除
    * @Param: [knowledgeId]
    * @return: void
    * @Author: husw
    * @Date: 2025/3/12 14:46
    */
    public void deleteKnowledgeById(List<String> knowledgeIds,String robotCode){
        RobotKnowledgeDelVO delVo = new RobotKnowledgeDelVO();
        delVo.setKnowledgeIds(knowledgeIds);
        delVo.setRobotCode(robotCode);
        maasHttpService.embeddingDel(delVo);
    }

    /**
     * @Description: 同步云知识库知识
     * @Param: [knowledgePackage, knowledgeBase]
     * @return: void
     * @Author: husw
     * @Date: 2023/5/26 10:59
     */
    public void embeddingDataByCode(List<KnowledgeBase> knowledgeBases,String robotCode, String directoryCode, String onlyFile) {
        List<RobotKnowledgeEmbeddingVO.Embedding> embeddings = new ArrayList<>();
        for (KnowledgeBase bases:knowledgeBases){
            //查询完整的知识数据
            KnowledgeBase knowledgeBase = kbcKmService.findById(bases.getKnowledgeId());
            if (!"2".equals(knowledgeBase.getState())){
                //未发布知识不同步
                continue;
            }
            // 添加只同步包含文件知识逻辑
            if ("1".equals(onlyFile)) {
                List<KnowledgeChapter> chapters = knowledgeBase.getChapters();
                boolean hasFile = chapters.stream().anyMatch(chapter -> ChatKnowledgeTypeEnum.FILE.getKey().equals(chapter.getChapterType()));
                if (!hasFile){
                    continue;
                }
            }
//            embeddings.add(initEmbeddingData(knowledgeBase,bases.getFirstDirectName(), robotCode,directoryCode));
            RobotKnowledgeEmbeddingVO.Embedding embedding = initEmbeddingData(knowledgeBase, bases.getFirstDirectName(), robotCode, directoryCode);
            sendLargeEmbedding(embedding);
        }
        //推送数据
//        sendLargeEmbedding(embeddings);
//        log.info("同步知识数量：{}",embeddings.size());
    }

    /**
     * @Description: 同步云知识库知识
     * @Param: [knowledgePackage, knowledgeBase]
     * @return: void
     * @Author: husw
     * @Date: 2023/5/26 10:59
     */
    public void embeddingDataFromClaim(List<RobotKnowledgeClaimSyncVO> claimSyncVOS) {
        log.info("同步好赔知识信息数量：{}",claimSyncVOS.size());
        String defaultRobotCode = getDefaultRobotCode();
        if (defaultRobotCode.contains(",")){
            String[] split = defaultRobotCode.split(",");
            for (String robotCode:split){
                syncToEmbedding(claimSyncVOS,robotCode);
            }
        }else {
            syncToEmbedding(claimSyncVOS,defaultRobotCode);
        }
        log.info("同步好赔知识完成}");
    }
    /**
    * @Description: 同步好赔内容
    * @Param: [claimSyncVOS, robotCode]
    * @return: void
    * @Author: husw
    * @Date: 2025/3/12 16:47
    */
    private void syncToEmbedding(List<RobotKnowledgeClaimSyncVO> claimSyncVOS,String robotCode){
        List<String> delIds = new ArrayList<>();
        for (RobotKnowledgeClaimSyncVO claimSyncVO:claimSyncVOS){
            if ("3".equals(claimSyncVO.getSyncType())){
                delIds.add(claimSyncVO.getKnowledgeId());
            }else {
                RobotKnowledgeEmbeddingVO.Embedding embedding = new RobotKnowledgeEmbeddingVO.Embedding();
                embedding.setTitle(claimSyncVO.getTitle());
                embedding.setFullTitle(claimSyncVO.getTitle());
                embedding.setKnowledgeId(claimSyncVO.getKnowledgeId());
                embedding.setSource("2");
                embedding.setRobotCode(robotCode);
                embedding.setLink(claimSyncVO.getLink());
                List<RobotKnowledgeEmbeddingVO.Item> items = new ArrayList<>();
                if (EmptyUtils.isNotEmpty(claimSyncVO.getFileUrl())){
                    claimSyncVO.getFileUrl().forEach(x->{
                        RobotKnowledgeEmbeddingVO.Item item = new RobotKnowledgeEmbeddingVO.Item();
                        item.setType("4");
                        item.setFileUrl(x);
                        item.setFileName(claimSyncVO.getTitle()+".jpg");
                        items.add(item);
                    });
                }else {
                    RobotKnowledgeEmbeddingVO.Item item = new RobotKnowledgeEmbeddingVO.Item();
                    item.setType("0");
                    items.add(item);
                }
                items.get(0).setContent(claimSyncVO.getContent());
                embedding.setItemList(items);
                //向量化
                sendLargeEmbedding(embedding);
            }
        }
        if (EmptyUtils.isNotEmpty(delIds)){
            deleteKnowledgeById(delIds,robotCode);
        }
    }
    /**
    * @Description: 获取默认智能体编码
    * @Param: []
    * @return: java.lang.String
    * @Author: husw
    * @Date: 2025/3/12 15:04
    */
    private String getDefaultRobotCode(){
        String key = redisUtil.generateKey(SYNC_DEFAULT_ROBOT_CODE);
        String robotCode = (String)redisUtil.get(key);
        return EmptyUtils.isEmpty(robotCode)?"RT000006":robotCode;
    }
}
