package com.kbao.kbcchatbot.maas.channel.channelcommoncard.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.common.convert.ConvertService;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean.ChannelCommonCardVO;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.dao.ChannelCommonCardMapper;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelUpdateVO;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 渠道常用卡片Service类
 * @Date 2023-05-22
 */
@Service
@Slf4j
public class ChannelCommonCardService extends BaseSQLServiceImpl<ChannelCommonCard, Long, ChannelCommonCardMapper> {

    @Autowired
    private ChannelService channelService;

    @Transactional(rollbackFor = Exception.class)
    public int insert(ChannelCommonCard channelCommonCard) {
        String currentUserId = BscUserUtils.getUserId();
        String nowStr = DateUtils.thisDateTime();
        channelCommonCard.setCreateTime(nowStr);
        channelCommonCard.setCreateId(currentUserId);
        channelCommonCard.setUpdateTime(nowStr);
        channelCommonCard.setUpdateId(currentUserId);
        channelCommonCard.setIsDeleted(0);
        return this.mapper.insertSelective(channelCommonCard);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(ChannelCommonCardVO channelCommonCardVO) {
        checkParam(channelCommonCardVO);
        ChannelUpdateVO channelUpdateVO = channelService.getMapper().selectByChannelCode(channelCommonCardVO.getChannelCode());

        ChannelCommonCard channelCommonCard = new ChannelCommonCard();
        BeanUtils.copyProperties(channelCommonCardVO, channelCommonCard);
        if (channelCommonCardVO.getContent() != null) {
            channelCommonCard.setContent(channelCommonCardVO.getContent().getBytes(StandardCharsets.UTF_8));
        }
        channelCommonCard.setChannelId(Long.valueOf(channelUpdateVO.getChannelId()));
        channelCommonCard.setUpdateTime(DateUtils.thisDateTime());
        channelCommonCard.setUpdateId(BscUserUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(channelCommonCard);
    }

    /**
     * 新增渠道常用卡片
     *
     * @param channelCommonCardVO 渠道常用卡片VO
     */
    @Transactional(rollbackFor = Exception.class)
    public ChannelCommonCard addChannelCommonCard(ChannelCommonCardVO channelCommonCardVO) {
        log.info("新增渠道常用卡片，channelCommonCardVO={}", JSONObject.toJSONString(channelCommonCardVO));

        checkParam(channelCommonCardVO);

        ChannelUpdateVO channelUpdateVO = channelService.getMapper().selectByChannelCode(channelCommonCardVO.getChannelCode());

        ChannelCommonCard channelCommonCard = new ChannelCommonCard();
        BeanUtils.copyProperties(channelCommonCardVO, channelCommonCard);
        channelCommonCard.setChannelId(Long.valueOf(channelUpdateVO.getChannelId()));
        channelCommonCard.setContent(channelCommonCardVO.getContent().getBytes(StandardCharsets.UTF_8));
        channelCommonCard.setTitle(channelCommonCardVO.getTitle());
        insert(channelCommonCard);
        return channelCommonCard;
    }

    private void checkParam(ChannelCommonCardVO channelCommonCardVO) {

        if (StringUtil.isNotBlank(channelCommonCardVO.getTitle()) && channelCommonCardVO.getChannelCode() != null) {
            Map<String, Object> queryMap = new HashMap<>(2);
            if (StringUtil.isNotBlank(channelCommonCardVO.getTitle()) && channelCommonCardVO.getChannelCode() != null) {
                queryMap.put("channelCode", channelCommonCardVO.getChannelCode());
                queryMap.put("titleKey", channelCommonCardVO.getTitle());
                List<ChannelCommonCard> channelCommonCards = selectByParam(queryMap);
                channelCommonCards.removeIf(x -> x.getId().equals(channelCommonCardVO.getId()));
                if (CollectionUtils.isNotEmpty(channelCommonCards)) {
                    throw new BusinessException("该渠道已存在该标题的常用卡片");
                }
            }
        }

    }

    /**
     * 根据id获取渠道常用卡片
     *
     * @param id 渠道常用卡片id
     * @return 渠道常用卡片VO
     */
    @Transactional(readOnly = true)
    public ChannelCommonCardVO getChannelCommonCardById(Long id) {
        ChannelCommonCard channelCommonCard = selectByPrimaryKey(id);
        if (channelCommonCard != null) {
            ChannelCommonCardVO channelCommonCardVO = new ChannelCommonCardVO();
            BeanUtils.copyProperties(channelCommonCard, channelCommonCardVO);
            if (channelCommonCard.getContent() != null) {
                channelCommonCardVO.setContent(new String(channelCommonCard.getContent(), StandardCharsets.UTF_8));
            }
            return channelCommonCardVO;
        }
        return null;
    }

    /**
     * 根据渠道id查询渠道常用卡片列表
     *
     * @param channelId 渠道id
     * @return 渠道常用卡片VO列表
     */
    public List<ChannelCommonCardVO> getChannelCommonCardListByChannelId(Long channelId,String status) {
        Map<String,Object> map = new HashMap<>(2);
        map.put("channelId",channelId);
        map.put("status", status);
        return ConvertService.getList(this.mapper::getChannelCommonCardListByChannelId, map, ChannelCommonCardVO.class);
    }


    /**
     * 删除常用卡片
     * @param channelCommonCard 常用卡片实体类
     */
    public void deleteChannelCommonCard(ChannelCommonCard channelCommonCard) {
        ChannelCommonCard channelCommonCardData = selectByPrimaryKey(channelCommonCard.getId());
        if (Objects.nonNull(channelCommonCardData)){
            channelCommonCardData.setIsDeleted(1);
            mapper.updateByPrimaryKeySelective(channelCommonCardData);
            log.info("当前删除的常用卡片为={}",JSONObject.toJSONString(channelCommonCardData));
        }else {
            throw new BusinessException("当前常用卡片不存在，无法删除" + channelCommonCard);
        }
    }

    /**
     * 根据渠道id查询渠道常用卡片列表
     *
     * @param channelCode 渠道编码
     * @return 渠道常用卡片VO列表
     */
    public List<ChannelCommonCardVO> getChannelCommonCardListByChannelCode(String channelCode,String status) {
        Map<String,Object> map = new HashMap<>(2);
        map.put("channelCode",channelCode);
        map.put("status", status);
        return ConvertService.getList(this.mapper::getChannelCommonCardListByChannelCode, map, ChannelCommonCardVO.class);
    }
}
