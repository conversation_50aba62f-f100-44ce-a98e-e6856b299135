package com.kbao.kbcchatbot.maas.product.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.product.bean.FileItemVo;
import com.kbao.kbcchatbot.maas.product.bean.ProductFileAddVo;
import com.kbao.kbcchatbot.maas.product.dao.ProductFileMapper;
import com.kbao.kbcchatbot.maas.product.entity.ProductFile;
import com.kbao.kbcchatbot.upload.UploadService;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import com.kbao.tool.util.DateUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-04-24
*/
@Service
public class ProductFileService extends BaseSQLServiceImpl<ProductFile, Integer, ProductFileMapper> {
    @Autowired
    private MaasHttpService maasHttpService;
    @Autowired
    private UploadService uploadService;

    public void addProductFiles(String productId, String fileType, MultipartFile[] files) {
        List<FileItemVo> list = new ArrayList<>();
        for (MultipartFile file : files) {
            Result<FileUploadResponse> response = uploadService.uploadFileWeb(file, "file");
            String fileId = response.getDatas().getFileId();
            FileItemVo itemVo = new FileItemVo(fileId, file.getOriginalFilename(), response.getDatas().getForeignPath());
            list.add(itemVo);
        }
        ProductFileAddVo param = new ProductFileAddVo(productId, fileType, list);
        maasHttpService.postMaasApi(MapUtils.objectToMap(param), "/ex/product/file/save");
    }

}
