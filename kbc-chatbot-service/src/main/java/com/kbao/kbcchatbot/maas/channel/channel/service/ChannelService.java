package com.kbao.kbcchatbot.maas.channel.channel.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig;
import com.kbao.kbcchatbot.maas.channel.channel.dao.ChannelMapper;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.service.ChannelBasicConfigService;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelPageReqVO;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelPageRespVO;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelUpdateVO;
import com.kbao.kbcchatbot.maas.channel.channelrobot.service.ChannelRobotService;
import com.kbao.kbcchatbot.maas.channel.channel.entity.Channel;
import com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotListVo;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @Description maas渠道Service类
* @Date 2024-12-17
*/
@Service
public class ChannelService extends BaseSQLServiceImpl<Channel, Integer, ChannelMapper> {

    @Autowired
    private ChannelBasicConfigService channelBasicConfigService;
    @Autowired
    private ChannelRobotService channelRobotService;

    @Transactional(rollbackFor = Exception.class)
    public void insert(ChannelUpdateVO channelUpdateVO) {
        if(EmptyUtils.isEmpty(channelUpdateVO.getProjectId()) || EmptyUtils.isEmpty(channelUpdateVO.getChannelCode()) || EmptyUtils.isEmpty(channelUpdateVO.getChannelName()) || EmptyUtils.isEmpty(channelUpdateVO.getStatus())){
            throw new BusinessException("参数缺失！");
        }
        int count = this.mapper.isExistChannel(channelUpdateVO.getChannelCode(), channelUpdateVO.getChannelId());
        if(count > 0){
            throw new BusinessException("渠道编码已存在！");
        }
        String currentUserId = BscUserUtils.getUserId();
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        Channel channel = new Channel();
        BeanUtils.copyProperties(channelUpdateVO, channel);
        channel.setCreateBy(currentUserId);
        channel.setUpdateBy(currentUserId);
        channel.setCreateTime(DateUtils.getCurrentDate());
        channel.setUpdateTime(DateUtils.getCurrentDate());
        channel.setTenantId(tenantId);
        this.mapper.insertSelective(channel);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(ChannelUpdateVO channelUpdateVO) {
        if(EmptyUtils.isEmpty(channelUpdateVO.getProjectId()) || EmptyUtils.isEmpty(channelUpdateVO.getChannelName()) || EmptyUtils.isEmpty(channelUpdateVO.getStatus())){
            throw new BusinessException("参数缺失！");
        }
        int count = this.mapper.isExistChannel(channelUpdateVO.getChannelCode(), channelUpdateVO.getChannelId());
        if(count > 0){
            throw new BusinessException("渠道编码已存在！");
        }
        String currentUserId = BscUserUtils.getUserId();
        Channel channel = new Channel();
        BeanUtils.copyProperties(channelUpdateVO, channel);
        channel.setId(channelUpdateVO.getChannelId());
        channel.setUpdateBy(currentUserId);
        channel.setUpdateTime(DateUtils.getCurrentDate());
        this.mapper.updateByPrimaryKeySelective(channel);
        //更新渠道关联表
        ChannelBasicConfig channelBasicConfig = new ChannelBasicConfig();
        BeanUtils.copyProperties(channelUpdateVO, channelBasicConfig);
        channelBasicConfig.setChannelId(Long.valueOf(channelUpdateVO.getChannelId()));
        channelBasicConfigService.updateChannelBasicConfig(channelBasicConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateChannel(Channel channel) {
        String currentUserId = BscUserUtils.getUserId();
        channel.setUpdateBy(currentUserId);
        channel.setUpdateTime(DateUtils.getCurrentDate());
        this.mapper.updateByPrimaryKeySelective(channel);
    }

    public PageInfo<ChannelPageRespVO> getChannelPage(PageRequest<ChannelPageReqVO> reqVo){
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize(), "t.create_time desc");
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        reqVo.getParam().setTenantId(tenantId);
        List<ChannelPageRespVO> channelList = this.mapper.getChannelList(reqVo.getParam());
        for(ChannelPageRespVO channel : channelList){
            //获取机器人列表
            List<ChannelRobotListVo> channelRobotList = channelRobotService.getChannelRobotList(channel.getChannelId());
            if(EmptyUtils.isNotEmpty(channelRobotList)){
                //用逗号拼接机器人名称
                StringBuilder robotName = new StringBuilder();
                for(ChannelRobotListVo channelRobot : channelRobotList){
                    robotName.append(channelRobot.getRobotName()).append(",");
                }
                if(robotName.length() > 0){
                    //去掉最后一个逗号
                    robotName.deleteCharAt(robotName.length()-1);
                }
                channel.setBindRobots(robotName.toString());
            }
        }
        return new PageInfo<>(channelList);
    }
    public ChannelUpdateVO getChannel(String channelCode){
        ChannelUpdateVO channelUpdateVO = this.mapper.selectByChannelCode(channelCode);
        return channelUpdateVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(ChannelUpdateVO channelUpdateVO) {
        if(EmptyUtils.isEmpty(channelUpdateVO.getChannelId()) || EmptyUtils.isEmpty(channelUpdateVO.getStatus())){
            throw new BusinessException("参数缺失！");
        }
        String currentUserId = BscUserUtils.getUserId();
        Channel channel = new Channel();
        channel.setId(channelUpdateVO.getChannelId());
        channel.setStatus(channelUpdateVO.getStatus());
        channel.setUpdateBy(currentUserId);
        channel.setUpdateTime(DateUtils.getCurrentDate());
        this.mapper.updateByPrimaryKeySelective(channel);
    }

    public void deleteChannel(Integer channelId){
        List<ChannelRobotListVo> robotList = channelRobotService.getChannelRobotList(channelId);
        robotList.forEach(robot -> {
            channelRobotService.delChannelRobot(robot.getId());
        });
        this.mapper.deleteByPrimaryKey(channelId);
    }
}
