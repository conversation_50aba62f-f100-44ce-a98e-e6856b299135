package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.dao;

import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgePackageCloud;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface KnowledgePackageCloudRepository extends ElasticsearchRepository<KnowledgePackageCloud, String> {

    List<KnowledgePackageCloud> findByRobotIdAndEnvironmentAndSecondDirect(Long robotId,Integer environment,String secondDirect);

    KnowledgePackageCloud findByRobotIdAndEnvironmentAndKnowledgeId(Long robotId,Integer environment,String knowledgeId);
    void deleteAllByRobotIdAndEnvironmentAndSecondDirect(Long robotId,Integer environment,String secondDirect);

    void deleteAllByRobotIdAndEnvironment(Long robotId,Integer environment);
}
