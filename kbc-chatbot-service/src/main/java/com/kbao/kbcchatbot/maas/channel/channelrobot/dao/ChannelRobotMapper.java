package com.kbao.kbcchatbot.maas.channel.channelrobot.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotListVo;
import com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotPageReqVo;
import com.kbao.kbcchatbot.maas.channel.channelrobot.entity.ChannelRobot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @Description maas渠道机器人关联表Dao类
* @Date 2024-12-17
*/
public interface ChannelRobotMapper extends BaseMapper<ChannelRobot, Integer>{

    int isExistChannelRobot(@Param("channelId") Integer channelId, @Param("robotType") String robotType);
    List<ChannelRobotListVo> getChannelRobotList(@Param("channelId") Integer channelId);
    List<ChannelRobotListVo> getChannelRobotPage(ChannelRobotPageReqVo reqVo);

}