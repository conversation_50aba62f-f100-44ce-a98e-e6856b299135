package com.kbao.kbcchatbot.maas.robot.robotknowledge.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.bean.RobotKnowledgeDataPageReqVo;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.bean.RobotKnowledgeDataPageRespVo;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.entity.RobotKnowledgeData;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @Description maas知识数据Dao类
* @Date 2024-12-12
*/
public interface RobotKnowledgeDataMapper extends BaseMapper<RobotKnowledgeData, Integer>{

    /**
     * 获取知识数据列表
     * @param req
     * @return
     */
    List<RobotKnowledgeDataPageRespVo> getKnowledgeDataList(RobotKnowledgeDataPageReqVo req);

    int updateDirectoryCodeByKnowledgeIds(Map<String,Object> maps);

    List<RobotKnowledgeDataPageRespVo> getChapterDataList(RobotKnowledgeDataPageReqVo req);

    int hasUnfinishedKnowledgeData(@Param("robotCode") String robotCode, @Param("knowledgeId") String knowledgeId);
}