package com.kbao.kbcchatbot.maas.chatsession.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerRespVO;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerTypeEnum;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatModelAnswerVO;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatKnowledgeTypeEnum;
import com.kbao.kbcchatbot.externalapi.model.LargeModelAnswerVO;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgePackageCloud;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.KnowledgePackageCloudApiService;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcchatbot.websocket.WebSocketServer;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description: 模型搜索
 * @author: husw
 * @create: 2024-06-04 10:01
 **/
@Service
@Slf4j
public class KnowledgeModelSearchService extends ChatSearchAbstractService{

    @Autowired
    private MaasHttpService maasHttpService;

    @Autowired
    private KnowledgePackageCloudApiService knowledgePackageCloudApiService;

    @Override
    protected ChatQueryAnswerRespVO query(String question, Map<String, Object> slots, Integer sceneShowType, String recordId,boolean isAsync) {
        //敏感词
        List<String> sensitiveWordsList = ChatUserUtil.getUser().getChatSessionCache().getSensitiveWords();
        //敏感词回复文案
        String sensitiveWords = ChatUserUtil.getUser().getChatSessionCache().getSensitiveWordsReply();
        //客服跳转链接
        String custServPlatAddr = ChatUserUtil.getUser().getChatSessionCache().getCustServPlatAddr();
        //敏感词校验
        boolean isSensitiveWords = checkSensitiveWords(question, sensitiveWordsList);
        if (isSensitiveWords) {
            return getCustomResp(ChatAnswerTypeEnum.SENSITIVE_WORD_ANSWER, sensitiveWords, recordId,null);
        }
        //转人工关键字校验
        if (checkTransferLabor(ChatUserUtil.getUser().getChatSessionCache(), question, YesNoEnum.YES.getValue(), null)) {
            return getCustomResp(ChatAnswerTypeEnum.TRANSFER_LABOR, custServPlatAddr,recordId, null);
        }
        JSONObject answer = maasHttpService.queryAnswerStream(question,ChatUserUtil.getUserId(),recordId);
        LargeModelAnswerVO answerVO = JSONObject.toJavaObject(answer, LargeModelAnswerVO.class);
        if(EmptyUtils.isNotEmpty(answerVO)){
            ChatModelAnswerVO chatModelAnswerVO = new ChatModelAnswerVO();
            BeanUtils.copyProperties(answerVO,chatModelAnswerVO);
            if (EmptyUtils.isNotEmpty(answerVO.getReferences())){
                chatModelAnswerVO.setReferences(getReferences(answerVO));
            }
            chatModelAnswerVO.setChunkStatus(1);
            chatModelAnswerVO.setVote("1");
            return getModelAnswerResps(Collections.singletonList(chatModelAnswerVO), null,answerVO.getAnswerId());
        }
        return getNoAnswerResp(null,recordId,null);
    }
    /**
    * @Description: 引用信息处理
    * @Param: [answerVO]
    * @return: java.util.List<com.kbao.kbcchatbot.chatsession.bean.qa.ChatModelAnswerVO.Reference>
    * @Author: husw
    * @Date: 2024/7/24 15:16
    */
    private List<ChatModelAnswerVO.Reference> getReferences(LargeModelAnswerVO answerVO) {
        List<ChatModelAnswerVO.Reference> referenceList = new ArrayList<>();
        answerVO.getReferences().forEach(x->{
            ChatModelAnswerVO.Reference reference = new ChatModelAnswerVO.Reference();
            reference.setType(ChatKnowledgeTypeEnum.TEXT.getKey());
            BeanUtils.copyProperties(x,reference);
            KnowledgePackageCloud knowledgePackageCloud = knowledgePackageCloudApiService.selectByKnowledgeId(x.getKnowledgeId(),
                    ChatUserUtil.getUser().getRobotId(), EnvTypeEnum.PROD_ENV.getCode());
            if (EmptyUtils.isNotEmpty(knowledgePackageCloud) && EmptyUtils.isNotEmpty(knowledgePackageCloud.getChapters())){
                knowledgePackageCloud.getChapters().forEach(item-> reference.setType(item.getChapterType()));
            }else {
                reference.setType(ChatKnowledgeTypeEnum.TEXT.getKey());
            }
            referenceList.add(reference);
        });
        return referenceList;
    }

    /**
    * @Description: 异步推送消息
    * @Param: [answerVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/7/2 16:18
    */
    public void pushAnswer(LargeModelAnswerVO answerVO){
        //推送消息
        ChatModelAnswerVO chatModelAnswerVO = new ChatModelAnswerVO();
        BeanUtils.copyProperties(answerVO,chatModelAnswerVO);
        chatModelAnswerVO.setChunkStatus(0);
        ChatQueryAnswerRespVO modelAnswerResps = getModelAnswerResps(Collections.singletonList(chatModelAnswerVO), null,answerVO.getAnswerId());
        WebSocketServer.sendInfo(JSONObject.toJSONString(modelAnswerResps),ChatUserUtil.getUser().getToken());
    }
}
