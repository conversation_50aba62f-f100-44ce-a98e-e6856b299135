package com.kbao.kbcchatbot.maas.channel.channelcommoncard.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @Description 渠道常用卡片Dao类
* @Date 2023-05-22
*/
public interface ChannelCommonCardMapper  extends BaseMapper<ChannelCommonCard, Long>{

    /**
     * 根据渠道ID获取渠道常用卡片列表(包含content)
     * @param map
     * @return 渠道常用卡片列表
     */
    List<ChannelCommonCard> getChannelCommonCardListByChannelId(Map<String,Object> map);

    /**
     * 根据渠道ID获取渠道常用卡片列表(包含content)
     * @param map
     * @return 渠道常用卡片列表
     */
    List<ChannelCommonCard> getChannelCommonCardListByChannelCode(Map<String,Object> map);

    ChannelCommonCard getChannelCommonCardByCode(@Param("channelCode") String channelCode);
}