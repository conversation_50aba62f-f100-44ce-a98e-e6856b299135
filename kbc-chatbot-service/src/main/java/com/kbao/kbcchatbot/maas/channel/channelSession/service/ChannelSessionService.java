package com.kbao.kbcchatbot.maas.channel.channelSession.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelPageReqVO;
import com.kbao.kbcchatbot.maas.channel.channelSession.bean.ChannelSessionReqVo;
import com.kbao.kbcchatbot.maas.channel.channelSession.dao.ChannelSessionMapper;
import com.kbao.kbcchatbot.maas.channel.channelSession.entity.ChannelSession;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitReqVO;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.tool.util.MapUtils;
import com.kbao.tool.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import com.kbao.tool.util.DateUtils;

import java.util.*;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-05-22
*/
@Service
public class ChannelSessionService extends BaseSQLServiceImpl<ChannelSession, Integer, ChannelSessionMapper> {
    @Autowired
    private MaasHttpService maasHttpService;

    public PageInfo<ChannelSession> getApiSessionList(RequestObjectPage<ChannelPageReqVO> requestPage) {
        PageHelper.startPage(requestPage.getPageNum(), requestPage.getPageSize());
        List<ChannelSession> list = mapper.getSessionList(ChatUserUtil.getUserId(), requestPage.getParam().getChannelCode());
        return new PageInfo<>(list);
    }

    public void renameSession(ChannelSession session) {
        mapper.renameSession(session.getSessionId(), session.getTitle());
    }

    public PageInfo<JSONObject> qaRecordPage(PageRequest<ChannelSessionReqVo> pageRequest) {
        pageRequest.getParam().setUserId(ChatUserUtil.getUserId());
        pageRequest.setPageSize(pageRequest.getPageSize() / 2);
        JSONObject data = maasHttpService.postMaasApi(MapUtils.objectToMap(pageRequest), "/ex/qa/record/sessionRecords");
        int total = data.getIntValue("total") * 2;
        Integer totalPages = data.getInteger("totalPages");
        JSONArray items = data.getJSONArray("items");
        List<JSONObject> records = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(items)) {
            for (int i = 0; i < items.size(); i++) {
                JSONObject item = items.getJSONObject(i);
                String hasError = item.getString("hasError");
                Object o = item.get("answer");
                if (o != null || "1".equals(hasError)) {
                    JSONObject answerRecord = new JSONObject();
                    answerRecord.put("subject", "2");
                    answerRecord.put("id", item.get("recordId"));
                    answerRecord.put("createTime", item.getString("answerTime"));
                    answerRecord.put("hasError", hasError);
                    if (o != null) {
                        answerRecord.put("content", Collections.singleton(o));
                    }
                    answerRecord.put("question", item.get("question"));
                    answerRecord.put("sessionId", item.get("sessionId"));
                    answerRecord.put("productId", item.get("productId"));
                    answerRecord.put("voteResult", item.get("voteResult"));
                    records.add(answerRecord);
                }
                JSONObject questionRecord = new JSONObject();
                questionRecord.put("subject", "1");
                questionRecord.put("content", item.get("question"));
                questionRecord.put("createTime", item.getString("createTime"));
                questionRecord.put("files", item.get("files"));
                records.add(questionRecord);
            }
        }
        PageInfo<JSONObject> pageInfo = new PageInfo<>(records);
        pageInfo.setTotal(total);
        pageInfo.setPages(totalPages);
        return pageInfo;
    }

    public ChannelSession get(ChannelSessionReqVo req) {
        if(StringUtil.isNotEmpty(req.getSessionId())) {
            Map<String, Object> param = new HashMap<>();
            param.put("sessionId", req.getSessionId());
            List<ChannelSession> list = this.mapper.selectAll(param);
            if(CollectionUtils.isNotEmpty(list)) {
                return list.get(0);
            }
        }
        return null;

    }
}
