package com.kbao.kbcchatbot.maas.mcp.appserver.service;

import com.kbao.commons.enums.DeleteEnum;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.mcp.appserver.dao.McpAppServerMapper;
import com.kbao.kbcchatbot.maas.mcp.appserver.entity.McpAppServer;
import com.kbao.kbcchatbot.series.service.SeriesDataService;
import com.kbao.tool.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * MCP应用服务器管理服务
 * 
 * <AUTHOR>
 * @date 2024-01-24
 */
@Service
public class McpAppServerService extends BaseSQLServiceImpl<McpAppServer, Long, McpAppServerMapper> {

    public static final String PREFIX = "AS";
    private static final String SERIES_NAME = "MCP_APP_SERVER";
    private static final int NUM = 6;

    @Autowired
    private SeriesDataService seriesDataService;

    /**
     * redis 服务
     */
    @Autowired
    public RedisUtil redisUtil;


    /**
     * 应用服务器新增
     *
     * @param mcpAppServer
     * @return
     * <AUTHOR>
     * @date 2024-01-24
     */
    @Transactional(rollbackFor = Exception.class)
    public McpAppServer add(McpAppServer mcpAppServer) {
        // 取当前登录用户 id
        String userId = BscUserUtils.getUserId();
        mcpAppServer.setAppServerCode(getNextId());
        mcpAppServer.setCreateTime(DateUtils.getCurrentDate());
        mcpAppServer.setCreateId(userId);
        mcpAppServer.setIsDeleted(DeleteEnum.NORMAL.getValue());
        mcpAppServer.setAppServerStatus(1); // 默认启用
        // 设置租户ID，如果未设置则使用当前用户的租户ID
        if (mcpAppServer.getTenantId() == null) {
            mcpAppServer.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        }
        mapper.insert(mcpAppServer);
        return mcpAppServer;
    }

    /**
     * 应用服务器更新
     *
     * @param mcpAppServer
     * @return
     * <AUTHOR>
     * @date 2024-01-24
     */
    @Transactional(rollbackFor = Exception.class)
    public McpAppServer update(McpAppServer mcpAppServer) {
        // 取当前登录用户 id
        String userId = BscUserUtils.getUserId();
        mcpAppServer.setUpdateTime(DateUtils.getCurrentDate());
        mcpAppServer.setUpdateId(userId);
        mapper.updateByPrimaryKey(mcpAppServer);
        return mcpAppServer;
    }

    /**
     * 获取下一个序列主键
     *
     * @return
     * <AUTHOR>
     * @date 2024-01-24
     */
    private String getNextId() {
        // 这里需要根据实际的序列生成服务来实现
        return seriesDataService.getNextId(SERIES_NAME, PREFIX, NUM);
    }

}