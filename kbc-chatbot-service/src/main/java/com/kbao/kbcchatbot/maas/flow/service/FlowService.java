package com.kbao.kbcchatbot.maas.flow.service;

import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.flow.dao.FlowMapper;
import com.kbao.kbcchatbot.maas.flow.entity.Flow;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @Description 聊天会话Service类
* @Date 2023-06-06
*/
@Service
@Slf4j
public class FlowService extends BaseSQLServiceImpl<Flow, Integer, FlowMapper> {

    public void save(Flow flow){
        flow.setFlowId(IdWorker.get32UUID());
        flow.setCreateTime(DateUtils.getCurrentDate());
        flow.setUpdateTime(DateUtils.getCurrentDate());
        flow.setCreateBy(BscUserUtils.getUserId());
        flow.setUpdateBy(BscUserUtils.getUserId());
        this.insert(flow);
    }

    public void update(Flow flow){
        flow.setUpdateTime(DateUtils.getCurrentDate());
        flow.setUpdateBy(BscUserUtils.getUserId());
        this.updateByPrimaryKeySelective(flow);
    }
    public List<Flow> selectList(Flow flow){
        Map<String, Object> params = MapUtils.objectToMap(flow);
        return this.mapper.selectList(params);
    }


}
