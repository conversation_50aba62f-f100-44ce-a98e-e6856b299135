package com.kbao.kbcchatbot.maas.mcp.server.service;

import com.kbao.commons.enums.DeleteEnum;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.mcp.server.dao.McpServerMapper;
import com.kbao.kbcchatbot.maas.mcp.server.entity.McpServer;
import com.kbao.kbcchatbot.series.service.SeriesDataService;
import com.kbao.tool.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * MCP服务器管理服务
 * 
 * <AUTHOR>
 * @date 2024-01-22
 */
@Service
public class McpServerService extends BaseSQLServiceImpl<McpServer, Long, McpServerMapper> {

    public static final String PREFIX = "MT";
    private static final String SERIES_NAME = "MCP_TOOL";
    private static final int NUM = 6;

    @Autowired
    private SeriesDataService seriesDataService;


    /**
     * redis 服务
     */
    @Autowired
    public RedisUtil redisUtil;

    /**
     * 服务器新增
     *
     * @param mcpServer
     * @return
     * <AUTHOR>
     * @date 2024-01-22
     */
    @Transactional(rollbackFor = Exception.class)
    public McpServer add(McpServer mcpServer) {
        // 取当前登录用户 id
        String userId = BscUserUtils.getUserId();
        mcpServer.setServerCode(getNextId());
        mcpServer.setCreateTime(DateUtils.getCurrentDate());
        mcpServer.setCreateId(userId);
        mcpServer.setIsDeleted(DeleteEnum.NORMAL.getValue());
        mcpServer.setServerStatus(1); // 默认启用
        // 设置租户ID，如果未设置则使用当前用户的租户ID
        if (mcpServer.getTenantId() == null) {
            mcpServer.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        }
        mapper.insert(mcpServer);
        return mcpServer;
    }

    /**
     * 服务器更新
     *
     * @param mcpServer
     * @return
     * <AUTHOR>
     * @date 2024-01-22
     */
    @Transactional(rollbackFor = Exception.class)
    public McpServer update(McpServer mcpServer) {
        // 取当前登录用户 id
        String userId = BscUserUtils.getUserId();
        mcpServer.setUpdateTime(DateUtils.getCurrentDate());
        mcpServer.setUpdateId(userId);
        mapper.updateByPrimaryKey(mcpServer);
        return mcpServer;
    }

    /**
     * 获取下一个序列主键
     *
     * @return
     * <AUTHOR>
     * @date 2024-01-22
     */
    private String getNextId() {
        // 这里需要根据实际的序列生成服务来实现
        return seriesDataService.getNextId(SERIES_NAME, PREFIX, NUM);
    }

}