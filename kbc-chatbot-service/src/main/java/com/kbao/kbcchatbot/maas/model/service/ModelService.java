package com.kbao.kbcchatbot.maas.model.service;

import com.github.pagehelper.PageHelper;
import com.kbao.kbcbsc.model.RequestPage;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.constants.SettingConstant;
import com.kbao.kbcchatbot.maas.commonsetting.service.CommonSettingService;
import com.kbao.kbcchatbot.maas.model.bean.ModelPageResVo;
import com.kbao.kbcchatbot.maas.model.dao.ModelMapper;
import com.kbao.kbcchatbot.maas.model.entity.Model;
import com.kbao.kbcchatbot.utils.MaasPyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;

import java.util.List;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-01-08
*/
@Service
public class ModelService extends BaseSQLServiceImpl<Model, Integer, ModelMapper> {
    @Autowired
    private CommonSettingService commonSettingService;
    @Autowired
    private RedisUtil redisUtil;

    public void save(Model model) {
        String currentUserId = BscUserUtils.getUserId();
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        if (model.getId() == null) {
            model.setCreateBy(currentUserId);
            model.setTenantId(tenantId);
            mapper.insert(model);
        } else {
            model.setUpdateBy(currentUserId);
            mapper.updateByPrimaryKeySelective(model);
        }
    }

    public List<ModelPageResVo> getModelPage(RequestPage page) {
        PageHelper.startPage(page.getPageNum(), page.getPageSize());
        return mapper.getModelPage(page.getParam());
    }

    public void setCommonModel(Integer id, String settingCode) {
        Model model = mapper.selectByPrimaryKey(id);
        commonSettingService.saveSetting(settingCode, model.getModelName());
        // 删除缓存
        String redisKey = MaasPyUtil.getRedisKey(settingCode);
        redisUtil.del(redisKey);
    }

    public List<Model> getModelList() {
        return mapper.getModelList();
    }
}
