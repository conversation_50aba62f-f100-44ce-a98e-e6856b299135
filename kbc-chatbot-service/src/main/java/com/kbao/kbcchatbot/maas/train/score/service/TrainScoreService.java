package com.kbao.kbcchatbot.maas.train.score.service;

import com.hankcs.hanlp.mining.word2vec.Train;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitReqVO;
import com.kbao.kbcchatbot.maas.train.score.dao.TrainScoreMapper;
import com.kbao.kbcchatbot.maas.train.score.entity.TrainScore;
import com.kbao.kbcchatbot.utils.MaasPyUtil;
import com.kbao.kbcchatbot.utils.StringUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-03-18
*/
@Service
public class TrainScoreService extends BaseSQLServiceImpl<TrainScore, Integer, TrainScoreMapper> {
    @Autowired
    private MaasHttpService maasHttpService;

    public TrainScore addTrainScore(TrainSceneInitReqVO reqVO) {
        TrainScore score;
        if (StringUtils.isEmpty(reqVO.getExternalId())) {
            score = mapper.getTrainScoreByUserId(reqVO.getSceneCode(), ChatUserUtil.getUserId(), reqVO.getSource());
        } else {
            score = mapper.getTrainScoreByExternalId(reqVO.getSource(), reqVO.getExternalId());
            if (score != null && !reqVO.getSceneCode().equals(score.getSceneCode())) {
                throw new BusinessException("参数错误：场景编码不一致");
            }
        }
        if (score != null) {
            reqVO.setExternalId(score.getExternalId());
            maasHttpService.trainAddCache(reqVO);
            return score;
        }
        TrainScore trainScore = TrainScore.builder().sceneCode(reqVO.getSceneCode()).userId(ChatUserUtil.getUserId())
                .userName(ChatUserUtil.getUser().getUserName())
                .source(reqVO.getSource()).externalId(reqVO.getExternalId())
                .tenantId(ChatUserUtil.getUser().getTenantId()).createBy("sys").build();
        if (StringUtils.isEmpty(trainScore.getExternalId())) {
            trainScore.setExternalId("S#" + IdWorker.getIdStr());
        }
        mapper.insert(trainScore);
        return trainScore;
    }
}
