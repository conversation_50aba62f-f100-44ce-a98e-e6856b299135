package com.kbao.kbcchatbot.maas.robot.robotknowledge.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcchatbot.config.ExternalUrlConfig;
import com.kbao.kbcchatbot.config.LargeModelConfig;
import com.kbao.kbcchatbot.externalapi.model.LargeModelCleanSession;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.product.bean.ProductSyncVo;
import com.kbao.kbcchatbot.maas.record.bean.RecordInfoResVo;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.bean.*;
import com.kbao.kbcchatbot.utils.HttpClientUtil;
import com.kbao.tool.util.JsonLUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * maas知识库分片业务层
 * @author: xiaojiayao
 * @time: 2024/12/16 17:05
 */
@Service
@Slf4j
public class RobotKnowledgeHttpService {
    @Autowired
    private HttpClientUtil httpClientUtil;
    @Autowired
    private MaasHttpService maasHttpService;
    @Autowired
    private ExternalUrlConfig externalUrlConfig;
    @Autowired
    private LargeModelConfig largeModelConfig;
    public RobotKnowledgeSlicePageRespVo getSliceList(PageRequest<RobotKnowledgeSlicePageReqVo> reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getGetSliceList(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("查询maas知识库分片列表异常:{}",respObj.getString("msg"));
            throw new BusinessException("查询maas知识库分片列表异常！"+respObj.getString("msg"));
        }
        return respObj.getObject("data", RobotKnowledgeSlicePageRespVo.class);
    }

    public void knowledgeDataStatusUpdate(RobotKnowledgeDataUpdateVo reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getKnowledgeDataStatusUpdate(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("更新知识数据状态异常:{}",respObj.getString("msg"));
            throw new BusinessException("更新知识数据状态异常！"+respObj.getString("msg"));
        }
    }
    public void knowledgeDataDel(RobotKnowledgeDataUpdateVo reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getKnowledgeDataDel(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("删除知识数据状态异常:{}",respObj.getString("msg"));
            throw new BusinessException("删除知识数据状态异常！"+respObj.getString("msg"));
        }
    }

    public void knowledgeSliceAdd(RobotKnowledgeSliceAddVo reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getKnowledgeSliceAdd(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("添加知识库切片异常:{}",respObj.getString("msg"));
            throw new BusinessException("添加知识库切片异常！"+respObj.getString("msg"));
        }
    }
    public void knowledgeSliceDel(RobotKnowledgeSliceIdVo reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getKnowledgeSliceDel(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("删除知识库切片异常:{}",respObj.getString("msg"));
            throw new BusinessException("删除知识库切片异常！"+respObj.getString("msg"));
        }
    }
    public void knowledgeSliceContentUpdate(RobotKnowledgeSliceUpdateVo reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getKnowledgeSliceContentUpdate(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("更新知识库切片内容异常:{}",respObj.getString("msg"));
            throw new BusinessException("更新知识库切片内容异常！"+respObj.getString("msg"));
        }
    }
    public void knowledgeSliceStatusUpdate(RobotKnowledgeSliceStatusVo reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getKnowledgeSliceStatusUpdate(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("更新知识库切片状态异常:{}",respObj.getString("msg"));
            throw new BusinessException("更新知识库切片状态异常！"+respObj.getString("msg"));
        }
    }

    public JSONObject getQueryQaRecordInfo(RecordInfoResVo reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getQueryQaRecordInfo(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("查询maas问答日志异常:{}",respObj.getString("msg"));
            throw new BusinessException("查询maas知识库分片列表异常！"+respObj.getString("msg"));
        }
        return respObj.getJSONObject("data");
    }

    public JSONArray getQueryQaRecordReferences(RecordInfoResVo reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getQueryQaRecordReferences(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("查询maas问答日志异常:{}",respObj.getString("msg"));
            throw new BusinessException("查询maas知识库分片列表异常！"+respObj.getString("msg"));
        }
        return respObj.getJSONArray("data");
    }

    public Integer syncProductData(PageRequest<ProductSyncVo> reqVo){
        String result = httpClientUtil.postJson(externalUrlConfig.getSyncProductData(), JSONObject.toJSONString(reqVo), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("syncProductData error:{}",respObj.getString("msg"));
            throw new BusinessException("syncProductData error！"+respObj.getString("msg"));
        }
        Integer productNum = respObj.getInteger("data");
        return productNum > 0 ? productNum : 0;
    }

    public void syncProductFileData(){
        String result = httpClientUtil.postJson(externalUrlConfig.getSyncProductFile(), "{}", getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("syncProductData error:{}",respObj.getString("msg"));
            throw new BusinessException("syncProductData error！"+respObj.getString("msg"));
        }
    }

    public void syncCompanyFileData(){
        String result = httpClientUtil.postJson(externalUrlConfig.getSyncCompanyFile(), "{}", getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("syncProductData error:{}",respObj.getString("msg"));
            throw new BusinessException("syncProductData error！"+respObj.getString("msg"));
        }
    }

    private Map<String, Object> getHeader(){
        Map<String, Object> header = new HashMap<>();
        header.put("token", maasHttpService.getToken(largeModelConfig.getCode(), largeModelConfig.getSecretKey()));
        return header;
    }
}
