package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service;

import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatSearchResultDTO;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerSourceEnum;
import com.kbao.kbcchatbot.elasticsearch.search.BaseSearchService;
import com.kbao.kbcchatbot.elasticsearch.search.bean.BusinessQueryDTO;
import com.kbao.kbcchatbot.elasticsearch.search.bean.SearchQuery;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.dao.KnowledgePackageCloudRepository;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgePackageCloud;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums.KnowledgeStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.kbao.kbcchatbot.constants.ElasticsearchConstants.BUSINESS_QUERY_OPERATOR_EQUALS;

/**
 * <AUTHOR>
 * @Description 云知识库Api Service类
 * @Date 2023-06-09
 */
@Service
@Slf4j
public class KnowledgePackageCloudApiService {

    @Autowired
    private BaseSearchService baseSearchService;

    @Autowired
    private KnowledgePackageCloudRepository knowledgePackageCloudRepository;

    public List<ChatSearchResultDTO> queryAnswer(String question, Long robotId, String indId, int maxCount, String conditionConcat,List<String> secondsDirect) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(question);
        searchQuery.setIndId(indId);
        searchQuery.setRobotId(robotId);

        //检索知识包
        SearchHits<KnowledgePackageCloud> searchHits = baseSearchService.search(KnowledgePackageCloud.class, searchQuery, maxCount, getBusinessQuery(robotId,secondsDirect), conditionConcat);
        List<ChatSearchResultDTO> searchResultDTOList = new ArrayList<>();
        if(searchHits.getTotalHits() > 0) {
            searchHits.getSearchHits().forEach(searchHit -> {
                ChatSearchResultDTO searchResultDTO = new ChatSearchResultDTO();
                searchResultDTO.setQaId(searchHit.getContent().getId());
                searchResultDTO.setAnswer(searchHit.getContent().getAnswer());
                searchResultDTO.setSimilarQuestions(searchHit.getContent().getSimilarQuestions());
                searchResultDTO.setQuestion(searchHit.getContent().getTitle());
                searchResultDTO.setQaSource(ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_CLOUD.getKey());
                searchResultDTO.setKnowledgeId(searchHit.getContent().getKnowledgeId());
                searchResultDTO.setSecondDirectId(searchHit.getContent().getSecondDirect());
                searchResultDTOList.add(searchResultDTO);
            });
        }
        return searchResultDTOList;
    }

    private List<BusinessQueryDTO> getBusinessQuery(Long robotId,List<String> secondsDirect) {
        List<BusinessQueryDTO> businessQueryDTOList = new ArrayList<>();
        BusinessQueryDTO businessQueryDTO1 = new BusinessQueryDTO();
        businessQueryDTO1.setColumnName("robotId");
        businessQueryDTO1.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
        businessQueryDTO1.setValue(robotId);
        businessQueryDTOList.add(businessQueryDTO1);
        BusinessQueryDTO businessQueryDTO2 = new BusinessQueryDTO();
        businessQueryDTO2.setColumnName("environment");
        businessQueryDTO2.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
        businessQueryDTO2.setValue(EnvTypeEnum.PROD_ENV.getCode());
        businessQueryDTOList.add(businessQueryDTO2);
        BusinessQueryDTO businessQueryDTO3 = new BusinessQueryDTO();
        businessQueryDTO3.setColumnName("isDeleted");
        businessQueryDTO3.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
        businessQueryDTO3.setValue("0");
        businessQueryDTOList.add(businessQueryDTO3);
        BusinessQueryDTO businessQueryDTO4 = new BusinessQueryDTO();
        businessQueryDTO4.setColumnName("state");
        businessQueryDTO4.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
        businessQueryDTO4.setValue(KnowledgeStateEnum.YES.getCode());
        businessQueryDTOList.add(businessQueryDTO4);
//        BusinessQueryDTO businessQueryDTO5 = new BusinessQueryDTO();
//        businessQueryDTO5.setColumnName("secondDirect");
//        businessQueryDTO5.setOperator(BUSINESS_QUERY_OPERATOR_EQUALS);
//        businessQueryDTO5.setValue(secondsDirect);
//        businessQueryDTOList.add(businessQueryDTO5);
        return businessQueryDTOList;
    }

    public KnowledgePackageCloud selectByQAId(String bizId) {
        Optional<KnowledgePackageCloud> knowledgePackageCloudOptional = knowledgePackageCloudRepository.findById(bizId);
        if(knowledgePackageCloudOptional.isPresent()) {
            return knowledgePackageCloudOptional.get();
        }
        return null;
    }

    public KnowledgePackageCloud selectByKnowledgeId(String knowledgeId,Long robotId,Integer environment) {
        return knowledgePackageCloudRepository.findByRobotIdAndEnvironmentAndKnowledgeId(robotId, environment, knowledgeId);
    }

}
