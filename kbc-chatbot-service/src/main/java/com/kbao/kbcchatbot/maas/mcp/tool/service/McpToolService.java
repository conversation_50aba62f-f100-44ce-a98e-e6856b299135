package com.kbao.kbcchatbot.maas.mcp.tool.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.commons.enums.DeleteEnum;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.mcp.server.dao.McpServerMapper;
import com.kbao.kbcchatbot.maas.mcp.server.entity.McpServer;
import com.kbao.kbcchatbot.maas.mcp.server.service.McpServerService;
import com.kbao.kbcchatbot.maas.mcp.tool.dao.McpToolMapper;
import com.kbao.kbcchatbot.maas.mcp.tool.entity.McpTool;
import com.kbao.kbcchatbot.series.service.SeriesDataService;
import com.kbao.tool.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP工具管理服务
 * 
 * <AUTHOR>
 * @date 2024-01-22
 */
@Service
public class McpToolService extends BaseSQLServiceImpl<McpTool, Long, McpToolMapper> {

    public static final String PREFIX = "MT";
    private static final String SERIES_NAME = "MCP_TOOL";
    private static final int NUM = 6;

    @Autowired
    private SeriesDataService seriesDataService;

    /**
     * MCP服务器数据访问层
     */
    @Autowired
    private McpServerService mcpServerService;

    /**
     * JSON对象映射器
     */
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 工具新增
     *
     * @param mcpTool
     * @return
     * <AUTHOR>
     * @date 2024-01-22
     */
    @Transactional(rollbackFor = Exception.class)
    public McpTool add(McpTool mcpTool) {
        // 数据校验
        validateToolData(mcpTool, true);
        
        // 取当前登录用户 id
        String userId = BscUserUtils.getUserId();
        mcpTool.setToolCode(getNextId());
        mcpTool.setCreateTime(DateUtils.getCurrentDate());
        mcpTool.setCreateId(userId);
        mcpTool.setIsDeleted(DeleteEnum.NORMAL.getValue());
        mcpTool.setToolStatus(1); // 默认启用
        // 设置租户ID，如果未设置则使用当前用户的租户ID
        if (mcpTool.getTenantId() == null) {
            mcpTool.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        }
        mapper.insert(mcpTool);
        return mcpTool;
    }

    /**
     * 工具更新
     *
     * @param mcpTool
     * @return
     * <AUTHOR>
     * @date 2024-01-22
     */
    @Transactional(rollbackFor = Exception.class)
    public McpTool update(McpTool mcpTool) {
        // 数据校验
        validateToolData(mcpTool, false);
        
        // 取当前登录用户 id
        String userId = BscUserUtils.getUserId();
        mcpTool.setUpdateTime(DateUtils.getCurrentDate());
        mcpTool.setUpdateId(userId);
        mapper.updateByPrimaryKey(mcpTool);
        return mcpTool;
    }

    /**
     * 数据校验
     *
     * @param mcpTool 工具对象
     * @param isAdd 是否为新增操作
     * @throws RuntimeException 校验失败时抛出异常
     */
    private void validateToolData(McpTool mcpTool, boolean isAdd) {
        // 1. 基础字段校验
        if (!StringUtils.hasText(mcpTool.getToolName())) {
            throw new RuntimeException("工具名称不能为空");
        }
        if (mcpTool.getServerId() != null) {
            throw new RuntimeException("所属服务器不能为空");
        }
        
        // 2. 工具名称重复检查（新增时检查全局重复，更新时检查除自身外的重复）
        if (isToolNameDuplicate(mcpTool.getToolName(), isAdd ? null : mcpTool.getToolId())) {
            throw new RuntimeException("工具名称已存在，请使用其他名称");
        }
        
        // 3. 服务器ID有效性验证
        if (!isServerIdValid(mcpTool.getServerId())) {
            throw new RuntimeException("所属服务器不存在或已禁用");
        }
        
        // 4. JSON Schema格式验证
        if (StringUtils.hasText(mcpTool.getInputSchema())) {
            if (!isValidJsonSchema(mcpTool.getInputSchema())) {
                throw new RuntimeException("输入参数结构格式不正确，请输入有效的JSON格式");
            }
        }
        
        if (StringUtils.hasText(mcpTool.getOutputSchema())) {
            if (!isValidJsonSchema(mcpTool.getOutputSchema())) {
                throw new RuntimeException("输出参数结构格式不正确，请输入有效的JSON格式");
            }
        }
        
        // 5. 工具状态校验
        if (mcpTool.getToolStatus() != null && mcpTool.getToolStatus() != 0 && mcpTool.getToolStatus() != 1) {
            throw new RuntimeException("工具状态值无效，只能为0（禁用）或1（启用）");
        }
    }
    
    /**
     * 检查工具名称是否重复
     *
     * @param toolName 工具名称
     * @param excludeToolId 排除的工具ID（更新时使用）
     * @return true表示重复，false表示不重复
     */
    private boolean isToolNameDuplicate(String toolName, Long excludeToolId) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("toolName", toolName);
        queryParams.put("isDeleted", DeleteEnum.NORMAL.getValue());
        
        List<McpTool> existingTools = mapper.selectAll(queryParams);
        
        if (existingTools.isEmpty()) {
            return false;
        }
        
        // 如果是更新操作，排除当前工具ID
        if (excludeToolId != null) {
            return existingTools.stream().anyMatch(tool -> excludeToolId != tool.getToolId());
        }
        
        return true;
    }
    
    /**
     * 验证服务器ID是否有效
     *
     * @param serverId 服务器ID
     * @return true表示有效，false表示无效
     */
    private boolean isServerIdValid(Long serverId) {
        try {
            McpServer server = mcpServerService.selectByPrimaryKey(serverId);
            return server != null && server.getServerStatus() == 1 && server.getIsDeleted() == DeleteEnum.NORMAL.getValue();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 验证JSON Schema格式是否正确
     *
     * @param jsonSchema JSON Schema字符串
     * @return true表示格式正确，false表示格式错误
     */
    private boolean isValidJsonSchema(String jsonSchema) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonSchema);
            return jsonNode != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取下一个序列主键
     * 
     * @return
     * <AUTHOR>
     * @date 2024-01-22
     */
    private String getNextId() {
        // 这里需要根据实际的序列生成服务来实现
        return seriesDataService.getNextId(SERIES_NAME, PREFIX, NUM);
    }

}