package com.kbao.kbcchatbot.maas.train.scene.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.train.scene.entity.TrainScene;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2025-03-18
*/
public interface TrainSceneMapper extends BaseMapper<TrainScene, Integer>{

    TrainScene selectByCode(@Param("sceneCode") String sceneCode);

    int isExistSceneCode(@Param("sceneCode") String sceneCode, @Param("id") Integer id);
}