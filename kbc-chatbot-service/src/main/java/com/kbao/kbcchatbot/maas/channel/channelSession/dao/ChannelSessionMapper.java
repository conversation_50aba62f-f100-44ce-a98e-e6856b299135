package com.kbao.kbcchatbot.maas.channel.channelSession.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.channel.channelSession.entity.ChannelSession;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2025-05-22
*/
public interface ChannelSessionMapper extends BaseMapper<ChannelSession, Integer>{

    List<ChannelSession> getSessionList(@Param("userId") String userId, @Param("channelCode") String channelCode);

    void renameSession(@Param("sessionId") String sessionId, @Param("title") String title);
	
}