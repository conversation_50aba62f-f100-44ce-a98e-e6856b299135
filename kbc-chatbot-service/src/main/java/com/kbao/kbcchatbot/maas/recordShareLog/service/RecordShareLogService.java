package com.kbao.kbcchatbot.maas.recordShareLog.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.util.BscApiContext;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.recordShareLog.dao.RecordShareLogMapper;
import com.kbao.kbcchatbot.maas.recordShareLog.entity.RecordShareLog;
import com.kbao.kbcchatbot.utils.FeignUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcucs.client.UserApiClientService;
import com.kbao.kbcucs.client.UserApiV2ClientService;
import com.kbao.kbcucs.user.model.UserInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import com.kbao.tool.util.DateUtils;

import java.util.HashMap;
import java.util.Map;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-05-23
*/
@Slf4j
@Service
public class RecordShareLogService extends BaseSQLServiceImpl<RecordShareLog, String, RecordShareLogMapper> {
    @Autowired
    private UserApiV2ClientService userApiV2ClientService;
    @Autowired
    private MaasHttpService maasHttpService;

    public String addRecordShareLog(String recordId) {
        String shareUserId = ChatUserUtil.getUserId();
        UserInfoResp userInfo = this.getUserInfo(shareUserId);
        if (userInfo == null) {
            userInfo = new UserInfoResp();
        }
        String shareId = IdWorker.get32UUID();
        RecordShareLog shareLog = RecordShareLog.builder()
                .shareId(shareId).userId(shareUserId).recordId(recordId)
                .userName(userInfo.getUserName()).headImage(userInfo.getHeadImage())
                .tenantId(BscApiContext.TenantId.get())
                .build();
        mapper.insert(shareLog);
        return shareId;
    }

    public RecordShareLog getShareInfo(String shareId) {
        RecordShareLog shareLog = mapper.selectByPrimaryKey(shareId);
        if (shareLog != null) {
            Map<String, Object> params = new HashMap<>();
            params.put("recordId", shareLog.getRecordId());
            params.put("base", "1");
            JSONObject recordData = maasHttpService.postMaasApi(params, "/ex/qa/record/recordInfo");
            shareLog.setRecordData(recordData);
        }
        return shareLog;
    }

    private UserInfoResp getUserInfo(String userId) {
        try {
            FeignUtil.setBaseFeignHeader(BscApiContext.TenantId.get());
            Result<UserInfoResp> result = userApiV2ClientService.getByUserId(userId);
            if (result != null && result.getDatas() != null) {
                return result.getDatas();
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
        }
        return null;
    }
}
