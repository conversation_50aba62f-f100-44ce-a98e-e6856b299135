package com.kbao.kbcchatbot.maas.channel.channelrobotprompt.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.bean.ChannelRobotPromptQueryVo;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.bean.ChannelRobotPromptUpdateVo;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.dao.ChannelRobotPromptMapper;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.entity.ChannelRobotPrompt;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @Description maas机器人提示词表Service类
* @Date 2024-12-17
*/
@Service
public class ChannelRobotPromptService extends BaseSQLServiceImpl<ChannelRobotPrompt, Integer, ChannelRobotPromptMapper> {

    @Transactional(rollbackFor = Exception.class)
    public void save(ChannelRobotPromptUpdateVo channelRobotPromptUpdateVo) {
        if(EmptyUtils.isEmpty(channelRobotPromptUpdateVo.getChannelRobotId()) || EmptyUtils.isEmpty(channelRobotPromptUpdateVo.getPromptType())){
            throw new BusinessException("参数不能为空！");
        }
        int count = this.mapper.isExistChannelRobotPrompt(channelRobotPromptUpdateVo.getChannelRobotId(), channelRobotPromptUpdateVo.getPromptType(), channelRobotPromptUpdateVo.getId());
        if(count > 0){
            throw new BusinessException("已存在相同类型的提示词！");
        }
        String currentUserId = BscUserUtils.getUserId();
        ChannelRobotPrompt channelRobotPrompt = new ChannelRobotPrompt();
        channelRobotPrompt.setChannelRobotId(channelRobotPromptUpdateVo.getChannelRobotId());
        channelRobotPrompt.setPromptType(channelRobotPromptUpdateVo.getPromptType());
        channelRobotPrompt.setPrompt(channelRobotPromptUpdateVo.getPrompt());
        channelRobotPrompt.setUpdateBy(currentUserId);
        channelRobotPrompt.setUpdateTime(DateUtils.getCurrentDate());
        if (channelRobotPromptUpdateVo.getId() == null) {
            this.mapper.insertSelective(channelRobotPrompt);
        } else {
            channelRobotPrompt.setId(channelRobotPromptUpdateVo.getId());
            this.mapper.updateByPrimaryKeySelective(channelRobotPrompt);
        }
    }

    public List<ChannelRobotPrompt> getChannelRobotPromptList(ChannelRobotPromptQueryVo channelRobotPromptQueryVo) {
        return this.mapper.getChannelRobotPromptList(channelRobotPromptQueryVo);
    }

    public void delByChannelRobotId(Integer channelRobotId) {
        this.mapper.delByChannelRobotId(channelRobotId);
    }
}
