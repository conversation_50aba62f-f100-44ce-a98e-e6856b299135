package com.kbao.kbcchatbot.maas.channel.channelcommonphrase.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.common.convert.ConvertService;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.dao.ChannelCommonPhraseMapper;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.service.ChannelBasicConfigService;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean.ChannelCommonPhraseVO;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.entity.ChannelCommonPhrase;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelUpdateVO;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.kbao.kbcchatbot.constants.Constant.STATUS_DISABLE;
import static com.kbao.kbcchatbot.constants.Constant.STATUS_ENABLE;

/**
 * <AUTHOR>
 * @Description 渠道常用短语Service类
 * @Date 2023-05-26
 */
@Service
@Slf4j
public class ChannelCommonPhraseService extends BaseSQLServiceImpl<ChannelCommonPhrase, Long, ChannelCommonPhraseMapper> {

    @Autowired
    private ChannelBasicConfigService channelBasicConfigService;
    @Autowired
    private ChannelService channelService;

    @Transactional(rollbackFor = Exception.class)
    public int insert(ChannelCommonPhrase channelCommonPhrase) {
        String currentUserId = BscUserUtils.getUserId();
        String nowStr = DateUtils.thisDateTime();
        channelCommonPhrase.setCreateTime(nowStr);
        channelCommonPhrase.setCreateId(currentUserId);
        channelCommonPhrase.setUpdateTime(nowStr);
        channelCommonPhrase.setUpdateId(currentUserId);
        channelCommonPhrase.setIsDeleted(0);
        return this.mapper.insertSelective(channelCommonPhrase);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(ChannelCommonPhraseVO channelCommonPhraseVO) {
        ChannelCommonPhrase channelCommonPhraseHistory = this.mapper.selectByPrimaryKey(channelCommonPhraseVO.getId());
        if (EmptyUtils.isEmpty(channelCommonPhraseHistory)) {
            throw new BusinessException("常用短语不存在！");
        }
        ChannelUpdateVO channelUpdateVO = channelService.getMapper().selectByChannelCode(channelCommonPhraseVO.getChannelCode());

        //参数检验
        channelCommonPhraseVO.setChannelCode(channelCommonPhraseHistory.getChannelCode());
        checkChannelParam(channelCommonPhraseVO);
        ChannelCommonPhrase channelCommonPhrase = new ChannelCommonPhrase();
        BeanUtils.copyProperties(channelCommonPhraseVO, channelCommonPhrase);
        if (channelCommonPhraseVO.getContent() != null) {
            channelCommonPhrase.setContent(channelCommonPhraseVO.getContent().getBytes(StandardCharsets.UTF_8));
        }
        channelCommonPhrase.setChannelId(Long.valueOf(channelUpdateVO.getChannelId()));
        channelCommonPhrase.setUpdateTime(DateUtils.thisDateTime());
        channelCommonPhrase.setUpdateId(BscUserUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(channelCommonPhrase);
    }

    /**
     * 新增渠道常用短语
     *
     * @param channelCommonPhraseVO 渠道常用短语VO
     * @return ChannelCommonPhrase
     */
    @Transactional(rollbackFor = Exception.class)
    public ChannelCommonPhrase addChannelCommonPhrase(ChannelCommonPhraseVO channelCommonPhraseVO) {
        //参数检验
        checkChannelParam(channelCommonPhraseVO);
        ChannelUpdateVO channelUpdateVO = channelService.getMapper().selectByChannelCode(channelCommonPhraseVO.getChannelCode());

        ChannelCommonPhrase channelCommonPhrase = new ChannelCommonPhrase();
        BeanUtils.copyProperties(channelCommonPhraseVO, channelCommonPhrase);
        channelCommonPhrase.setChannelId(Long.valueOf(channelUpdateVO.getChannelId()));
        channelCommonPhrase.setContent(channelCommonPhraseVO.getContent().getBytes(StandardCharsets.UTF_8));
        insert(channelCommonPhrase);
        return channelCommonPhrase;
    }

    /**
     * 根据id获取渠道常用短语
     *
     * @param id 渠道常用短语id
     * @return ChannelCommonPhraseVO
     */
    public ChannelCommonPhraseVO getChannelCommonPhraseById(Long id) {
        ChannelCommonPhrase channelCommonPhrase = selectByPrimaryKey(id);
        if (Objects.isNull(channelCommonPhrase)) {
            return null;
        }
        ChannelCommonPhraseVO channelCommonPhraseVO = new ChannelCommonPhraseVO();
        BeanUtils.copyProperties(channelCommonPhrase, channelCommonPhraseVO);
        if (channelCommonPhrase.getContent() != null) {
            channelCommonPhraseVO.setContent(new String(channelCommonPhrase.getContent(), StandardCharsets.UTF_8));
        }
        return channelCommonPhraseVO;
    }


    /**
     * 根据渠道id获取渠道猜你想问
     *
     * @param channelId 渠道id
     * @return 渠道猜你想问VO
     */
    public List<ChannelCommonPhraseVO> getChannelCommonPhraseListByChannelId(Long channelId, String status) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("channelId", channelId);
        map.put("status", status);
        return ConvertService.getList(this.mapper::getChannelCommonPhraseListByChannelId, map, ChannelCommonPhraseVO.class);
    }

    /**
     * 校验渠道猜你想问条数 当状态为启用时校验 大于七条就抛出异常
     */
    public void checkChannelParam(ChannelCommonPhraseVO channelCommonPhraseVO) {
        //没有phrase则是状态变更
        if (EmptyUtils.isNotEmpty(channelCommonPhraseVO.getPhrase())) {
            Map<String, Object> queryMap = new HashMap<>(2);
            queryMap.put("channelCode", channelCommonPhraseVO.getChannelCode());
            queryMap.put("phraseEq", channelCommonPhraseVO.getPhrase());
            List<ChannelCommonPhrase> channelCommonPhrases = selectByParam(queryMap);
            if (EmptyUtils.isNotEmpty(channelCommonPhraseVO.getId())) {
                //更新时排除自己
                channelCommonPhrases.removeIf(x -> x.getId().equals(channelCommonPhraseVO.getId()));
            }
            if (EmptyUtils.isNotEmpty(channelCommonPhrases)) {
                throw new BusinessException("该渠道已存在相同的短语");
            }
        }

        if (STATUS_DISABLE.equals(channelCommonPhraseVO.getStatus())) {
            return;
        }

        List<ChannelBasicConfig> channelBasicConfigListByChannelCode = channelBasicConfigService.getChannelBasicConfigListByChannelCode(channelCommonPhraseVO.getChannelCode());
        if (CollectionUtils.isNotEmpty(channelBasicConfigListByChannelCode)) {
            ChannelBasicConfig channelBasicConfig = channelBasicConfigListByChannelCode.get(0);
            // 常用短语是启用状态时才校验条数
            if (Objects.equals(channelBasicConfig.getCommonPhraseStatus(), STATUS_ENABLE)) {

                Map<String, Object> map = new HashMap<>(1);
                map.put("channelCode", channelCommonPhraseVO.getChannelCode());
                map.put("status", STATUS_ENABLE);
                List<ChannelCommonPhrase> channelCommonPhraseList = selectByParam(map);

                // 筛选出状态为启用的条数
                if (EmptyUtils.isNotEmpty(channelCommonPhraseList)) {
                    //排除自己
                    channelCommonPhraseList.removeIf(x -> x.getId().equals(channelCommonPhraseVO.getId()));
                    if (channelCommonPhraseList.size() + 1 > 20) {
                        throw new BusinessException("渠道常用短语最多只能添加20条");
                    }
                }
            }
        }
    }

    /**
     * 逻辑删除常用短语
     *
     * @param channelCommonPhrase 常用短语实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteChannelCommonPhrase(ChannelCommonPhrase channelCommonPhrase) {
        ChannelCommonPhrase channelCommonPhraseData = selectByPrimaryKey(channelCommonPhrase.getId());
        if (Objects.nonNull(channelCommonPhraseData)) {
            channelCommonPhraseData.setIsDeleted(1);
            mapper.updateByPrimaryKeySelective(channelCommonPhraseData);
            log.info("当前删除的常用短语为={}", JSONObject.toJSONString(channelCommonPhraseData));
        }else {
            throw new BusinessException("当前常用短语不存在，无法删除" + channelCommonPhrase);
        }

    }


    /**
     * 根据渠道id获取渠道猜你想问
     *
     * @param channelCode 渠道code
     * @return 渠道猜你想问VO
     */
    public List<ChannelCommonPhraseVO> getChannelCommonPhraseListByChannelCode(String channelCode, String status) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("channelCode", channelCode);
        map.put("status", status);
        return ConvertService.getList(this.mapper::getChannelCommonPhraseListByChannelCode, map, ChannelCommonPhraseVO.class);
    }
}
