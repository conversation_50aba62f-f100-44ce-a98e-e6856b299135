package com.kbao.kbcchatbot.maas.channel.channelcommonphrase.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.entity.ChannelCommonPhrase;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @Description 渠道常用短语Dao类
* @Date 2023-05-26
*/
public interface ChannelCommonPhraseMapper  extends BaseMapper<ChannelCommonPhrase, Long>{

    /**
     * 根据渠道id获取渠道常用短语列表(带content)
     * @param map
     * @return List<ChannelCommonPhrase>
     */
    List<ChannelCommonPhrase> getChannelCommonPhraseListByChannelId(Map<String,Object> map);


    List<ChannelCommonPhrase> getChannelCommonPhraseListByChannelCode(Map<String,Object> map);

    /**
     * 根据渠道id获取常用短语条数
     */
    int getChannelCommonPhraseCountByChannelId(Long channelId);
}