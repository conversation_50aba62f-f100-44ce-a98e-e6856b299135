package com.kbao.kbcchatbot.maas.mcp.log.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcchatbot.maas.mcp.log.model.McpCallLogMo;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * MCP调用日志MongoDB数据访问层
 */
@Repository
public class McpCallLogDao extends BaseMongoDaoImpl<McpCallLogMo, String> {

    /**
     * 根据服务器ID查询调用日志
     *
     * @param serverId 服务器ID
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByServerId(String serverId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("server_id").is(serverId));
        return this.find(query);
    }

    /**
     * 根据工具ID查询调用日志
     *
     * @param toolId 工具ID
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByToolId(String toolId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tool_id").is(toolId));
        return this.find(query);
    }

    /**
     * 根据用户ID查询调用日志
     *
     * @param userId 用户ID
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByUserId(String userId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("user_id").is(userId));
        return this.find(query);
    }

    /**
     * 根据租户ID查询调用日志
     *
     * @param tenantId 租户ID
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByTenantId(String tenantId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tenant_id").is(tenantId));
        return this.find(query);
    }

    /**
     * 根据调用状态查询调用日志
     *
     * @param callStatus 调用状态
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByCallStatus(Integer callStatus) {
        Query query = new Query();
        query.addCriteria(Criteria.where("call_status").is(callStatus));
        return this.find(query);
    }

    /**
     * 根据条件查询所有调用日志
     *
     * @param mcpCallLog 查询条件
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findAll(McpCallLogMo mcpCallLog) {
        Query query = getQuery(mcpCallLog);
        return this.find(query);
    }

    public Query getQuery(McpCallLogMo mcpCallLog) {
        Query query = new Query();
        if (mcpCallLog != null) {
            if (EmptyUtils.isNotEmpty(mcpCallLog.getServerId())) {
                query.addCriteria(Criteria.where("server_id").is(mcpCallLog.getServerId()));
            }
            if (EmptyUtils.isNotEmpty(mcpCallLog.getToolId())) {
                query.addCriteria(Criteria.where("tool_id").is(mcpCallLog.getToolId()));
            }
            if (EmptyUtils.isNotEmpty(mcpCallLog.getUserId())) {
                query.addCriteria(Criteria.where("user_id").is(mcpCallLog.getUserId()));
            }
            if (EmptyUtils.isNotEmpty(mcpCallLog.getTenantId())) {
                query.addCriteria(Criteria.where("tenant_id").is(mcpCallLog.getTenantId()));
            }
            if (mcpCallLog.getCallStatus() != null) {
                query.addCriteria(Criteria.where("call_status").is(mcpCallLog.getCallStatus()));
            }
        }
        return query;
    }
}