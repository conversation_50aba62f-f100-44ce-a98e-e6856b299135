package com.kbao.kbcchatbot.maas.train.score.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.train.score.entity.TrainScore;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2025-03-18
*/
public interface TrainScoreMapper extends BaseMapper<TrainScore, Integer>{

    TrainScore getUnfinishedTrain(@Param("sceneCode") String sceneCode,
                                  @Param("source") String source,
                                  @Param("externalId") String externalId);

    TrainScore getTrainScoreByExternalId(@Param("source") String source,
                                         @Param("externalId") String externalId);

    TrainScore getTrainScoreByUserId(@Param("sceneCode") String sceneCode, @Param("userId") String userId,
                                     @Param("source") String source);
}