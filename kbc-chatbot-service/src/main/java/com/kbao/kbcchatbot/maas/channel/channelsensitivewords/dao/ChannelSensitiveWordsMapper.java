package com.kbao.kbcchatbot.maas.channel.channelsensitivewords.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.channel.channelsensitivewords.entity.ChannelSensitiveWords;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @Description maas渠道敏感词配置Dao类
* @Date 2024-12-25
*/
public interface ChannelSensitiveWordsMapper extends BaseMapper<ChannelSensitiveWords, Long>{
    int selectCountByWords(@Param("words") String words, @Param("channelCode") String channelCode);

}