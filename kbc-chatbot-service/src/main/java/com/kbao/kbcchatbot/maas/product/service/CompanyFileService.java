package com.kbao.kbcchatbot.maas.product.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.product.bean.*;
import com.kbao.kbcchatbot.maas.product.dao.CompanyFileMapper;
import com.kbao.kbcchatbot.maas.product.entity.CompanyFile;
import com.kbao.kbcchatbot.maas.product.entity.ProductData;
import com.kbao.kbcchatbot.upload.UploadService;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import com.kbao.tool.util.DateUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-04-24
*/
@Service
public class CompanyFileService extends BaseSQLServiceImpl<CompanyFile, Integer, CompanyFileMapper> {
    @Autowired
    private UploadService uploadService;
    @Autowired
    private MaasHttpService maasHttpService;

    public PageInfo<CompanyListResVo> getCompanyList(RequestObjectPage<CompanyFile> requestPage) {
        PageHelper.startPage(requestPage.getPageNum(), requestPage.getPageSize());
        List<CompanyListResVo> companyList = mapper.getCompanyList(requestPage.getParam());
        return new PageInfo<>(companyList);
    }

    public void addCompanyFiles(String companyId, String fileType, MultipartFile[] files) {
        List<FileItemVo> list = new ArrayList<>();
        for (MultipartFile file : files) {
            Result<FileUploadResponse> response = uploadService.uploadFileWeb(file, "file");
            String fileId = response.getDatas().getFileId();
            FileItemVo itemVo = new FileItemVo(fileId, file.getOriginalFilename(), response.getDatas().getForeignPath());
            list.add(itemVo);
        }
        CompanyFileAddVo param = new CompanyFileAddVo(companyId, fileType, list);
        maasHttpService.postMaasApi(MapUtils.objectToMap(param), "/ex/company/file/save");
    }
}
