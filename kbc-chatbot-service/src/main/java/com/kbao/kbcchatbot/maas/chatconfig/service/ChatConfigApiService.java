package com.kbao.kbcchatbot.maas.chatconfig.service;

import cn.hutool.core.util.RandomUtil;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.redis.util.RedisUtil;
//import com.kbao.kbcchatbot.channel.service.ChannelService;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelUpdateVO;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.service.ChannelBasicConfigService;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.service.ChannelCommonCardService;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean.ChannelCommonPhraseVO;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.service.ChannelCommonPhraseService;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.service.ChannelGuessQuestionService;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.kbcchatbot.config.LargeModelConfig;
import com.kbao.kbcchatbot.externalapi.model.LargeModelMqttTokenVO;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.ConfigCacheVO;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.RobotInitConfigRespVO;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.service.RobotManualServiceConfigService;
import com.kbao.kbcchatbot.maas.onlineUser.service.OnlineUserStatService;
import com.kbao.kbcchatbot.maas.project.entity.Project;
import com.kbao.kbcchatbot.maas.project.service.ProjectService;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.bean.ChatUser;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.kbao.kbcchatbot.constants.CacheConstant.CHAT_TOKEN;
import static com.kbao.kbcchatbot.constants.CacheConstant.SESSION_MAX_TIME;

/**
 * @program: kbc-chatbot
 * @description: 机器人配置
 * @author: husw
 * @create: 2023-06-09 17:11
 **/
@Slf4j
@Service
public class ChatConfigApiService {

    @Autowired
    private ChannelService channelService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ChannelBasicConfigService channelBasicConfigService;

    @Autowired
    private ChannelCommonCardService channelCommonCardService;

    @Autowired
    private ChannelCommonPhraseService channelCommonPhraseService;

    @Autowired
    private ChannelGuessQuestionService channelGuessQuestionService;

    @Autowired
    private ChatSessionRecordService chatSessionRecordService;

    @Autowired
    private MaasHttpService maasHttpService;

    @Autowired
    private RobotManualServiceConfigService robotManualServiceConfigService;

    @Autowired
    private LargeModelConfig largeModelConfig;

    @Autowired
    private OnlineUserStatService onlineUserStatService;
    @Autowired
    private ProjectService projectService;

    /**
     * 猜你想问限制条数
     */
    private static final int GUESS_NUM = 4;
    /**
    * @Description: 查询初始化配置
    * @Param: []
    * @return: com.kbao.kbcchatbot.robotbasicconfig.vo.RobotInitConfigRespVO
    * @Author: husw
    * @Date: 2023/6/13 17:20
    */
    public RobotInitConfigRespVO getInitConfig() {
        Long channelId = ChatUserUtil.getUser().getChannelId();
        String channelCode = ChatUserUtil.getUser().getChannelCode();
        String userId = ChatUserUtil.getUser().getUserId();
        RobotInitConfigRespVO respVO = new RobotInitConfigRespVO();
        // 获取技能组标识
//        String userKey = redisUtil.generateKey(userId);
//        Object skillMarker = redisUtil.get(userKey);
//        if (skillMarker != null) {
//            respVO.setSkillMarker(String.valueOf(skillMarker));
//        }
        Project project = projectService.getMapper().selectByPrimaryKey(ChatUserUtil.getUser().getProjectId());
        respVO.setCode(EmptyUtils.isNotEmpty(project)?project.getCode():largeModelConfig.getCode());
        respVO.setUserId(userId);
        respVO.setChannelId(channelId);
        respVO.setChannelCode(ChatUserUtil.getUser().getChannelCode());
        respVO.setUserName(ChatUserUtil.getUser().getUserName());
        respVO.setRobotCode(ChatUserUtil.getUser().getRobotCode());
        respVO.setAgentCode(ChatUserUtil.getUser().getAgentCode());
        //查询渠道转人工配置
//        RobotManualServiceConfigGetReqVO param = new RobotManualServiceConfigGetReqVO();
//        param.setRobotId(ChatUserUtil.getUser().getRobotId());
//        param.setType(EnvTypeEnum.PROD_ENV.getCode());
//        RobotManualServiceConfigDetailVO robotManualServiceConfigDetailVO = robotManualServiceConfigService.get(param);
//        if (EmptyUtils.isNotEmpty(robotManualServiceConfigDetailVO)){
//            respVO.setCustServPlatAccessType(robotManualServiceConfigDetailVO.getCustServPlatAccessType());
//            respVO.setCustServPlatAddr(robotManualServiceConfigDetailVO.getCustServPlatAddr());
//            if (PlatAccessTypeEnum.SYSTEM.getCode().equals(robotManualServiceConfigDetailVO.getCustServPlatAccessType())){
//                respVO.setSkillMarker(robotManualServiceConfigDetailVO.getCustServPlatAddr());
//            }
//        }
        ChannelUpdateVO channelBasicConfig = channelService.getChannel(channelCode);
        BeanUtils.copyProperties(channelBasicConfig, respVO);

//            //常用卡片开启状态 0-未开启 1-开启
//            if (YesNoEnum.YES.getValue().toString().equals(channelBasicConfig.getCommonCardStatus())){
//                List<ChannelCommonCardVO> channelCommonCards = channelCommonCardService.getChannelCommonCardListByChannelId(channelId,YesNoEnum.YES.getValue().toString());
//                if (EmptyUtils.isNotEmpty(channelCommonCards)) {
//                    respVO.setChannelCommonCards(channelCommonCards);
//                }
//            }
        //常用短语开启状态 0-未开启 1-开启
        if (YesNoEnum.YES.getValue().toString().equals(channelBasicConfig.getCommonPhraseStatus())){
            List<ChannelCommonPhraseVO> channelCommonPhraseVOS = channelCommonPhraseService.getChannelCommonPhraseListByChannelCode(channelCode,YesNoEnum.YES.getValue().toString());
            if (EmptyUtils.isNotEmpty(channelCommonPhraseVOS)) {
                respVO.setChannelCommonPhrases(channelCommonPhraseVOS);
            }
        }
        //猜你想问开启状态 0-未开启 1-开启
//            if (YesNoEnum.YES.getValue().toString().equals(channelBasicConfig.getGuessQuestionStatus())){
//                List<ChannelGuessQuestionVO> channelGuessQuestions = channelGuessQuestionService.getChannelGuessQuestionListByChannelId(channelId,YesNoEnum.YES.getValue().toString());
//                if (EmptyUtils.isNotEmpty(channelGuessQuestions)){
//                    respVO.setChannelGuessQuestions(randomGuessQuestion(channelGuessQuestions));
//                }
//            }
        respVO.setSessionId(maasHttpService.getSessionId(userId, channelCode));
        onlineUserStatService.record(userId);
        return respVO;
    }

    public ChatSessionRecord saveRobotRecord(Long channelId, String channelCode,Object answer, String answerType, String qaId, String qaSource, String roundId, String title,
                                             String sessionId, String userId, String userName,
                                             String relatedArticles, String relatedArticlesName , String subject , Integer sceneShowType) {
        ChatSessionRecord record = new ChatSessionRecord();
        record.setContent(answer);
        record.setChannelId(channelId);
        record.setChannelCode(channelCode);
        record.setSubject(subject);
        record.setAnswerType(answerType);
        record.setQaId(qaId);
        record.setQaSource(qaSource);
        record.setRelatedArticles(relatedArticles);
        record.setRelatedArticlesName(relatedArticlesName);
        record.setRoundId(roundId);
        record.setSessionId(sessionId);
        record.setId(IdWorker.get32UUID());
        record.setUserId(userId);
        record.setUserName(userName);
        record.setCreateTime(record.recordCreateTime());
        record.setSceneShowType(sceneShowType);
        // 判断如果ChatUserUtil.getUser() == null 则将tenantId设置为T0001
        ChatUser chatUser = ChatUserUtil.getUser();
        if (Objects.isNull(chatUser)) {
            record.setTenantId("T0001");
        }else {
            record.setTenantId(chatUser.getTenantId());
        }
        chatSessionRecordService.save(record);
        return record;
    }
    /**
    * @Description: 换一换
    * @Param: [sourceType 猜你想问来源]
    * @return: java.util.List<com.kbao.kbcchatbot.channelguessquestion.bean.ChannelGuessQuestionVO>
    * @Author: husw
    * @Date: 2023/6/13 11:11
    */
    public List<ChannelGuessQuestionVO> exchange(String sourceType) {
        String channelCode = ChatUserUtil.getUser().getChannelCode();
        List<ChannelBasicConfig> channelBasicConfigList = channelBasicConfigService.getChannelBasicConfigListByChannelCode(channelCode);
        if (EmptyUtils.isEmpty(channelBasicConfigList)){
            return null;
        }
        if (YesNoEnum.NO.getValue().toString().equals(channelBasicConfigList.get(0).getGuessQuestionStatus())){
            return null;
        }
        List<ChannelGuessQuestionVO> channelGuessQuestions = channelGuessQuestionService.getChannelGuessQuestionListByChannelCode(channelCode, YesNoEnum.YES.getValue().toString(),sourceType);
        if (EmptyUtils.isEmpty(channelGuessQuestions)){
            return null;
        }
        return randomGuessQuestion(channelGuessQuestions);
    }
    /**
    * @Description: 随机生成猜你想问
    * @Param: [channelGuessQuestions]
    * @return: java.util.List<com.kbao.kbcchatbot.channelguessquestion.bean.ChannelGuessQuestionVO>
    * @Author: husw
    * @Date: 2023/6/13 11:11
    */
    public List<ChannelGuessQuestionVO> randomGuessQuestion(List<ChannelGuessQuestionVO> channelGuessQuestions){
        //随机生成4条
        List<ChannelGuessQuestionVO> resultQuestionVO = new ArrayList<>();
        List<ChannelGuessQuestionVO> dealQuestions = new ArrayList<>(channelGuessQuestions);
//        String redisKey = redisUtil.generateKey(ChatUserUtil.getUser().getToken());
        String redisKey = redisUtil.generateKey(MessageFormat.format(CHAT_TOKEN, ChatUserUtil.getUser().getChannelCode(), ChatUserUtil.getUser().getToken()));
        ConfigCacheVO configCacheVO = (ConfigCacheVO) redisUtil.get(redisKey);
        if (dealQuestions.size() <= GUESS_NUM){
            resultQuestionVO.addAll(dealQuestions);
            configCacheVO.setGuessQuestionId(dealQuestions.stream().map(ChannelGuessQuestionVO::getId).collect(Collectors.toList()));
        }else {
            if (EmptyUtils.isNotEmpty(configCacheVO) && EmptyUtils.isNotEmpty(configCacheVO.getGuessQuestionId())){
                //缓存有数据
                //删除本次会话已展示的猜你想问
                dealQuestions.removeIf(x->configCacheVO.getGuessQuestionId().contains(x.getId()));
                if (dealQuestions.size() > GUESS_NUM){
                    for (int i=0;i<GUESS_NUM;i++){
                        ChannelGuessQuestionVO channelGuessQuestionVO = dealQuestions.get(RandomUtil.randomInt(dealQuestions.size()));
                        resultQuestionVO.add(channelGuessQuestionVO);
                        dealQuestions.remove(channelGuessQuestionVO);
                    }
                    configCacheVO.getGuessQuestionId().addAll(resultQuestionVO.stream().map(ChannelGuessQuestionVO::getId).collect(Collectors.toList()));
                }else {
                    //一轮循环结束重置缓存  然后补全差量
                    int value = GUESS_NUM - dealQuestions.size();
                    for (int i=0;i<value;i++){
                        //在当前剩余未循环到的猜你想问以外的全量数据内随机补全
                        channelGuessQuestions.removeAll(dealQuestions);
                        ChannelGuessQuestionVO channelGuessQuestionVO = channelGuessQuestions.get(RandomUtil.randomInt(channelGuessQuestions.size()));
                        resultQuestionVO.add(channelGuessQuestionVO);
                        channelGuessQuestions.remove(channelGuessQuestionVO);
                    }
                    configCacheVO.setGuessQuestionId(resultQuestionVO.stream().map(ChannelGuessQuestionVO::getId).collect(Collectors.toList()));
                    resultQuestionVO.addAll(dealQuestions);
                }
            }else {
                //缓存无数据
                for (int i=0;i<GUESS_NUM;i++){
                    ChannelGuessQuestionVO channelGuessQuestionVO = dealQuestions.get(RandomUtil.randomInt(dealQuestions.size()));
                    resultQuestionVO.add(channelGuessQuestionVO);
                    dealQuestions.remove(channelGuessQuestionVO);
                }
                configCacheVO.setGuessQuestionId(resultQuestionVO.stream().map(ChannelGuessQuestionVO::getId).collect(Collectors.toList()));
            }
        }
        //加入到本次会话缓存
        redisUtil.set(redisKey,configCacheVO,SESSION_MAX_TIME);
        return resultQuestionVO;
    }
    /**
    * @Description: 查询mqtt的token
    * @Param: []
    * @return: com.kbao.kbcchatbot.externalapi.model.LargeModelMqttTokenVO
    * @Author: husw
    * @Date: 2024/8/26 14:14
    */
    public LargeModelMqttTokenVO getMToken(){
        return maasHttpService.getMqttToken();
    }
}
