package com.kbao.kbcchatbot.maas.train.scene.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitReqVO;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitResVo;
import com.kbao.kbcchatbot.maas.train.scene.dao.TrainSceneMapper;
import com.kbao.kbcchatbot.maas.train.scene.entity.TrainScene;
import com.kbao.kbcchatbot.maas.train.score.entity.TrainScore;
import com.kbao.kbcchatbot.maas.train.score.service.TrainScoreService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-03-18
*/
@Service
public class TrainSceneService extends BaseSQLServiceImpl<TrainScene, Integer, TrainSceneMapper> {
    @Autowired
    private TrainScoreService trainScoreService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private MaasHttpService maasHttpService;

    public int insert(TrainScene trainScene) {
        int existSceneCode = mapper.isExistSceneCode(trainScene.getSceneCode(), trainScene.getId());
        if (existSceneCode > 0) {
            throw new RuntimeException("场景编码已存在");
        }
        trainScene.setCreateBy(BscUserUtils.getUserId());
        trainScene.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        return this.mapper.insert(trainScene);
    }

    public int update(TrainScene trainScene) {
        int existSceneCode = mapper.isExistSceneCode(trainScene.getSceneCode(), trainScene.getId());
        if (existSceneCode > 0) {
            throw new RuntimeException("场景编码已存在");
        }
        trainScene.setUpdateBy(BscUserUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(trainScene);
    }

    public TrainSceneInitResVo trainSceneInit(TrainSceneInitReqVO reqVO) {
        TrainScene trainScene = mapper.selectByCode(reqVO.getSceneCode());
        TrainSceneInitResVo trainSceneInitResVo = TrainSceneInitResVo.fromTrainScene(trainScene);
        TrainScore score = trainScoreService.addTrainScore(reqVO);
        trainSceneInitResVo.setExternalId(score.getExternalId());
        return trainSceneInitResVo;
    }

    public PageInfo<JSONObject> trainRecordPage(PageRequest<TrainSceneInitReqVO> pageRequest, boolean isApi) {
        List<JSONObject> records;
        JSONObject data = maasHttpService.getTrainRecordList(pageRequest);
        Integer total = data.getInteger("total");
        Integer totalPages = data.getInteger("totalPages");
        JSONArray items = data.getJSONArray("items");
        if (isApi) {
            records = getApiRecordList(items);
        } else {
            records = getWebRecordList(items);
        }
        PageInfo<JSONObject> pageInfo = new PageInfo<>(records);
        pageInfo.setTotal(total);
        pageInfo.setPages(totalPages);
        return pageInfo;
    }

    private List<JSONObject> getApiRecordList(JSONArray items) {
        List<JSONObject> records = new ArrayList<>();
        if (CollectionUtils.isEmpty(items)) {
            return records;
        }
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = items.getJSONObject(i);
            Object o = item.get("answer");
            if (o != null) {
                JSONObject a_record = new JSONObject();
                a_record.put("subject", "2");
                a_record.put("id", item.get("answerId"));
                a_record.put("createTime", item.getString("createTime"));
                a_record.put("content", Collections.singleton(o));
                records.add(a_record);
            }
            JSONObject q_record = new JSONObject();
            q_record.put("subject", "1");
            q_record.put("content", item.get("question"));
            q_record.put("createTime", item.getString("createTime"));
            q_record.put("files", item.get("files"));
            records.add(q_record);
        }
        return records;
    }

    private List<JSONObject> getWebRecordList(JSONArray items) {
        List<JSONObject> records = new ArrayList<>();
        if (CollectionUtils.isEmpty(items)) {
            return records;
        }
        for (int i = items.size() - 1; i >= 0; i--) {
            JSONObject item = items.getJSONObject(i);
            JSONObject record = new JSONObject();
            record.put("question", item.get("question"));
            record.put("createTime", item.getString("createTime"));
            JSONObject o = item.getJSONObject("answer");
            if (o != null) {
                record.put("answer", o.get("text"));
                record.put("answerUrls", o.get("urls"));
            }
            record.put("files", item.get("files"));
            record.put("filesContent", item.get("filesContent"));
            records.add(record);
        }
        return records;
    }
}
