package com.kbao.kbcchatbot.maas.product.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.maas.product.bean.ProductListResVo;
import com.kbao.kbcchatbot.maas.product.dao.ProductDataMapper;
import com.kbao.kbcchatbot.maas.product.entity.ProductData;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;

import java.util.List;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-04-24
*/
@Service
public class ProductDataService extends BaseSQLServiceImpl<ProductData, Integer, ProductDataMapper> {

    public PageInfo<ProductListResVo> getProductList(RequestObjectPage<ProductData> requestPage) {
        PageHelper.startPage(requestPage.getPageNum(), requestPage.getPageSize());
        List<ProductListResVo> productList = mapper.getProductList(requestPage.getParam());
        return new PageInfo<>(productList);
    }
}
