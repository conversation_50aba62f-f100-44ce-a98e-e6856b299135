package com.kbao.kbcchatbot.maas.chatsessionrecord.service;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.export.ExcelUtils;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcbsc.util.BscApiContext;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.ChatSessionConvRecord;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.ChatSessionConvRecordExportVO;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.ChatSessionConvRecordReqVO;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotChatSessionRecordReqVO;
import com.kbao.kbcchatbot.externalapi.enums.LargeAnswerSourceEnum;
import com.kbao.kbcchatbot.externalapi.model.*;
import com.kbao.kbcchatbot.kbc.ucs.KbcUcsService;
import com.kbao.kbcchatbot.maas.channel.channel.entity.Channel;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean.ChannelCommonCardVO;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatKnowledgeClearRespVO;
import com.kbao.kbcchatbot.maas.chatsession.enums.*;
import com.kbao.kbcchatbot.maas.chatsession.service.ChatSessionService;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.maas.chatsessionrecord.dao.ChatSessionRecordDao;
import com.kbao.kbcchatbot.maas.robot.robot.entity.Robot;
import com.kbao.kbcchatbot.maas.robot.robot.service.RobotService;
import com.kbao.kbcchatbot.utils.StringUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.bean.ChatUser;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 聊天会话记录Service类
 * @Date 2023-6-6
 */
@Service
@Slf4j
public class ChatSessionRecordService extends BaseMongoServiceImpl<ChatSessionRecord, String, ChatSessionRecordDao> {

    public static final String PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";
    private static final int PAGE_SIZE = 100;
    public static final String Q = "0";
    public static final String A = "1";

    @Autowired
    private KbcUcsService kbcUcsService;

    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private MaasHttpService maasHttpService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private RobotService robotService;


    /**
     * @Description: 查询会话最新一条记录
     * @Param: [sessionId]
     * @return: void
     * @Author: husw
     * @Date: 2023/6/20 18:00
     */
    public ChatSessionRecord getLastSessionRecord(String sessionId) {
        Query query = new Query();
        Criteria criteria = Criteria.where("sessionId").is(sessionId);
        query.addCriteria(criteria);
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        query.with(sort);
        return this.dao.findOne(query);
    }

    /**
     * @Description: 查询指定记录聊天记录
     * @Param: [sessionId, subject, answerType, limit]
     * @return: java.util.List<com.kbao.kbcchatbot.chatsessionrecord.ChatSessionRecord>
     * @Author: husw
     * @Date: 2023/6/28 10:50
     */
    public List<ChatSessionRecord> findBySubAndTypeLimit(String sessionId, String subject, int limit) {
        Query query = new Query();
        Criteria criteria = Criteria.where("sessionId").is(sessionId)
                .and("subject").is(subject);
        query.addCriteria(criteria);
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        query.with(sort);
        query.limit(limit);
        return this.dao.find(query);
    }

    public long findTotalRound(String sessionId, String channelCode, String robotCode, String startTime) {
        String dayStartTime = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parseDate(startTime)));
        String dayEndTime = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(startTime)));
        Query query = new Query();
        Criteria criteria = Criteria.where("sessionId").is(sessionId)
                .and("channelCode").is(channelCode)
                .and("robotCode").is(robotCode)
                .and("subject").is("2")
                .and("createTime").gte(dayStartTime).lte(dayEndTime)
                .and("roundId").ne(null);
        query.addCriteria(criteria);
        return this.dao.count(query);
    }

    public long findRoundByAnswerType(String sessionId, List<String> answerTypes, String channelCode, String robotCode, String startTime) {
        String dayStartTime = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parseDate(startTime)));
        String dayEndTime = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(startTime)));
        Query query = new Query();
        Criteria criteria = Criteria.where("sessionId").is(sessionId)
                .and("channelCode").is(channelCode)
                .and("robotCode").is(robotCode)
                .and("createTime").gte(dayStartTime).lte(dayEndTime)
                .and("answerType").in(answerTypes);
        query.addCriteria(criteria);
        return this.dao.count(query);
    }

    public long findRoundByAnswerSource(String sessionId, String answerSource,String channelCode, String robotCode, String startTime) {
        String dayStartTime = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parseDate(startTime)));
        String dayEndTime = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(startTime)));
        Query query = new Query();
        Criteria criteria = Criteria.where("sessionId").is(sessionId)
                .and("channelCode").is(channelCode)
                .and("robotCode").is(robotCode)
                .and("createTime").gte(dayStartTime).lte(dayEndTime)
                .and("answerSource").is(answerSource);
        query.addCriteria(criteria);
        return this.dao.count(query);
    }

    public long findRoundByAnswerSource(String sessionId, List<String> answerSourceList,String channelCode, String robotCode, String startTime) {
        String dayStartTime = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parseDate(startTime)));
        String dayEndTime = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(startTime)));
        Query query = new Query();
        Criteria criteria = Criteria.where("sessionId").is(sessionId)
                .and("channelCode").is(channelCode)
                .and("robotCode").is(robotCode)
                .and("createTime").gte(dayStartTime).lte(dayEndTime)
                .and("answerSource").in(answerSourceList);
        query.addCriteria(criteria);
        return this.dao.count(query);
    }

    public long findVoteRound(String sessionId, String voteResult,String channelCode, String robotCode, String startTime) {
        String dayStartTime = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parseDate(startTime)));
        String dayEndTime = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parseDate(startTime)));
        Query query = new Query();
        Criteria criteria = Criteria.where("sessionId").is(sessionId)
                .and("channelCode").is(channelCode)
                .and("robotCode").is(robotCode)
                .and("createTime").gte(dayStartTime).lte(dayEndTime)
                .and("voteResult").is(voteResult);
        query.addCriteria(criteria);
        return this.dao.count(query);
    }

    public PageInfo<ChatSessionRecord> pageInfo(RequestObjectPage<RobotChatSessionRecordReqVO> pageRequest) {
        RobotChatSessionRecordReqVO param = pageRequest.getParam();
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and("tenantId").is(BscApiContext.TenantId.get());
        if (EmptyUtils.isNotEmpty(param.getSessionId())) {
            criteria.and("sessionId").is(param.getSessionId());
        }
        if (EmptyUtils.isNotEmpty(param.getContent())) {
            criteria.and("content").regex(StringUtil.getMongoPattern(param.getContent()));
        }
        query.addCriteria(criteria);
        Pagination<ChatSessionRecord> pagination = new Pagination<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        pagination.setSort(EmptyUtils.isEmpty(pageRequest.getSort()) ? "createTime desc" : pageRequest.getSort());
        PageInfo<ChatSessionRecord> page = super.page(query, pagination);
        page.getList().forEach(x -> {
            //知识澄清内容
            if (ChatAnswerTypeEnum.QUESTION_CLEAR.getKey().equals(x.getAnswerType())) {
                String contentStr = JSONArray.toJSONString(x.getContent());
                List<ChatKnowledgeClearRespVO> chatKnowledgeClearRespVOS = JSONArray.parseArray(contentStr).toJavaList(ChatKnowledgeClearRespVO.class);
                String content = chatKnowledgeClearRespVOS.stream().map(ChatKnowledgeClearRespVO::getQuestion).collect(Collectors.joining("<br/>"));
                x.setContent(content);
            }
            //渠道，机器人
            /*Channel channel = channelService.getMapper().selectByPrimaryKey(x.getChannelId());
            if (EmptyUtils.isNotEmpty(channel)) {
                x.setChannelName(channel.getName());
                x.setRobotId(channel.getRobotId());
                Robot robot = robotService.getMapper().selectByPrimaryKey(x.getRobotId());
                if (EmptyUtils.isNotEmpty(robot)) {
                    x.setRobotName(robot.getName());
                }
            }*/

        });
        return page;
    }

    public PageInfo<ChatSessionConvRecord> pageConversation(RequestObjectPage<ChatSessionConvRecordReqVO> reqVO) {
        List<AggregationOperation> aggregationOperationList = getConversationAggregationOperations(reqVO.getParam());

        long start1 = System.currentTimeMillis();
        //查询总量
        List<AggregationOperation> countAggregationOperationList = new ArrayList<>(aggregationOperationList);
        countAggregationOperationList.add(Aggregation.count().as("count"));
        TypedAggregation<ChatSessionRecord> agg = Aggregation.newAggregation(ChatSessionRecord.class, countAggregationOperationList)
                .withOptions(Aggregation.newAggregationOptions().allowDiskUse(true).build());
        List<JSONObject> countResult = aggregate(agg, "ChatSessionRecord");
        long end1 = System.currentTimeMillis();
        log.info("查询总量耗时：{}",end1-start1);

        //查询列表数据
        long start2 = System.currentTimeMillis();
        aggregationOperationList.add(Aggregation.skip((reqVO.getPageNum() - 1) * reqVO.getPageSize()));
        aggregationOperationList.add(Aggregation.limit(reqVO.getPageSize()));
        TypedAggregation<ChatSessionRecord> listAgg = Aggregation.newAggregation(ChatSessionRecord.class, aggregationOperationList)
                .withOptions(Aggregation.newAggregationOptions().allowDiskUse(true).build());
        List<ChatSessionConvRecord> list = getCoversationList(listAgg);
        long end2 = System.currentTimeMillis();
        log.info("查询列表耗时：{}",end2-start2);

        //封装结果数据
        PageInfo<ChatSessionConvRecord> result = new PageInfo<>();
        if (countResult.size() > 0) {
            result.setTotal(countResult.get(0).getLong("count"));
        } else {
            result.setTotal(0);
        }
        result.setPageNum(reqVO.getPageNum());
        result.setPageSize(reqVO.getPageSize());
        result.setList(list);
        return result;
    }

    public PageInfo<ChatSessionConvRecord> pageConversationNew(RequestObjectPage<ChatSessionConvRecordReqVO> reqVO) {
        PageRequest<LargeModelSyncVO> pageRequest = new PageRequest<>();
        pageRequest.setPageNum(reqVO.getPageNum());
        pageRequest.setPageSize(reqVO.getPageSize());
        pageRequest.setSort(reqVO.getSort());
        LargeModelSyncVO syncVO = new LargeModelSyncVO();
        ChatSessionConvRecordReqVO param = reqVO.getParam();
        syncVO.setSessionId(param.getSessionId());
        syncVO.setModel(param.getModel());
        syncVO.setIntention(param.getIntention());
        syncVO.setVoteResult(param.getUserVoteResult());
        syncVO.setQuestion(param.getUserContent());
        syncVO.setChannelCode(param.getChannelCode());
        syncVO.setLlmAnswerType(param.getAnswerSource());
        syncVO.setQuestionId(param.getConvId());
        syncVO.setIsCopy(param.getIsCopy());
        syncVO.setDeviceType(param.getDeviceType());
        if (EmptyUtils.isEmpty(param.getStartTime())){
            String date = DateUtils.dateTime2Str(DateUtils.addMonths(new Date(), -3),null);
            param.setStartTime(date);
        }
        syncVO.setStartDate(param.getStartTime());
        if (EmptyUtils.isEmpty(param.getEndTime())){
            String date = DateUtils.thisDateTime();
            param.setEndTime(date);
        }
        syncVO.setEndDate(param.getEndTime());
        syncVO.setUsername(param.getUserName());
        pageRequest.setParam(syncVO);
        LargeModelListVO chatRecord = maasHttpService.getChatRecord(pageRequest);
        //封装结果数据
        Map<String, Channel> channelCacheMap = new HashMap<>();
        Map<String, Robot> robotCacheMap = new HashMap<>();
        PageInfo<ChatSessionConvRecord> result = new PageInfo<>();
        if (EmptyUtils.isNotEmpty(chatRecord)){
            result.setTotal(chatRecord.getTotal());
            List<ChatSessionConvRecord> list = new ArrayList<>();
            chatRecord.getItems().forEach(x->{
                ChatSessionConvRecord convRecord = new ChatSessionConvRecord();
                convRecord.setSessionId(x.getSessionId());
                if (EmptyUtils.isNotEmpty(x.getCost())){
                    convRecord.setTime(Long.parseLong(x.getCost().split("\\.")[0]));
                }
                convRecord.setIntention(x.getAnswer().getIntention());
                convRecord.setIsCopy(x.getIsCopy());
                convRecord.setAnswerContent(x.getAnswer().getText());
                convRecord.setContextualizeQuestion(x.getContextualizeQuestion());
                convRecord.setAnswerSource(x.getAnswer().getLlmAnswerType());
                convRecord.setModel(x.getModel());
                convRecord.setUserId(x.getUserId());
                convRecord.setUserName(x.getUsername());
                convRecord.setUserVoteResult(x.getVoteResult());
                convRecord.setUserVoteRemark(x.getVoteRemark());
                convRecord.setRecordId(x.getRecordId());
                convRecord.setStartTime(x.getCreateTime());
                convRecord.setUserContent(x.getQuestion());
                convRecord.setIsCopy(x.getIsCopy());
                convRecord.setDeviceType(x.getDeviceType());
                Channel channel;
                if (channelCacheMap.containsKey(x.getChannelCode())) {
                    channel = channelCacheMap.get(x.getChannelCode());
                } else {
                    channel = channelService.getMapper().selectByCode(x.getChannelCode());
                    channelCacheMap.put(x.getChannelCode(), channel);
                }
                if (EmptyUtils.isNotEmpty(channel)) {
                    convRecord.setChannelName(channel.getChannelName());
                    Robot robot;
                    if (robotCacheMap.containsKey(x.getRobotCode())) {
                        robot = robotCacheMap.get(x.getRobotCode());
                    } else {
                        robot = robotService.getMapper().selectByCode(x.getRobotCode());
                        robotCacheMap.put(x.getRobotCode(), robot);
                    }
                    if (EmptyUtils.isNotEmpty(robot)) {
                        convRecord.setRobotName(robot.getRobotName());
                        convRecord.setRobotId(Long.valueOf(robot.getId()));
                    }
                }
                list.add(convRecord);
            });
            result.setList(list);
        }
        return result;
    }

    private List<ChatSessionConvRecord> getCoversationList(TypedAggregation<ChatSessionRecord> agg) {
        List<JSONObject> recordResult = aggregate(agg, "ChatSessionRecord");
        List<ChatSessionConvRecord> list = new ArrayList<>();
        Map<String, Channel> channelCacheMap = new HashMap<>();
        Map<String, Robot> robotCacheMap = new HashMap<>();
        recordResult.forEach(item -> {
//            item.put("channelId", item.getJSONObject("_id").getString("channelId"));
            item.put("channelCode", item.getJSONObject("_id").getString("channelCode"));
            item.put("sessionId", item.getJSONObject("_id").getString("sessionId"));
            item.put("convId", item.getJSONObject("_id").getString("roundId"));
            ChatSessionConvRecord chatSessionConvRecord = item.toJavaObject(ChatSessionConvRecord.class);
            Channel channel;
            if (channelCacheMap.containsKey(chatSessionConvRecord.getChannelCode())) {
                channel = channelCacheMap.get(chatSessionConvRecord.getChannelCode());
            } else {
                channel = channelService.getMapper().selectByCode(chatSessionConvRecord.getChannelCode());
                channelCacheMap.put(chatSessionConvRecord.getChannelCode(), channel);
            }
            if (EmptyUtils.isNotEmpty(chatSessionConvRecord.getAnswerContent()) &&
                    chatSessionConvRecord.getAnswerContent().equals(chatSessionConvRecord.getUserContent())) {
                chatSessionConvRecord.setAnswerContent("");
            }
            if (ChatAnswerTypeEnum.TRANSFER_LABOR.getKey().equals(chatSessionConvRecord.getRobotAnswerType())) {
                chatSessionConvRecord.setAnswerContent("");
            }
            try {
                JSONArray answerArray = JSONArray.parseArray(chatSessionConvRecord.getAnswerContent());
                LargeModelAnswerVO answerVO = JSONObject.toJavaObject(answerArray.getJSONObject(0), LargeModelAnswerVO.class);
                chatSessionConvRecord.setIntention(answerVO.getIntention());
            } catch (Exception e) {
                log.error("意图转换异失败,sessionId={}", chatSessionConvRecord.getSessionId(), e);
            }
            try {
                Date startTime = DateUtils.str2DateTime(chatSessionConvRecord.getStartTime(), "yyyy-MM-dd HH:mm:ss.SSS");
                Date endTime = DateUtils.str2DateTime(chatSessionConvRecord.getEndTime(), "yyyy-MM-dd HH:mm:ss.SSS");
                chatSessionConvRecord.setTime(DateUtil.between(startTime, endTime, DateUnit.SECOND));
            } catch (Exception e) {
                log.error("时间转换异常,sessionId={}", chatSessionConvRecord.getSessionId(), e);
            }
            if (EmptyUtils.isNotEmpty(channel)) {
                chatSessionConvRecord.setChannelName(channel.getChannelName());
                Robot robot;
                if (robotCacheMap.containsKey(chatSessionConvRecord.getRobotCode())) {
                    robot = robotCacheMap.get(chatSessionConvRecord.getRobotCode());
                } else {
                    robot = robotService.getMapper().selectByCode(chatSessionConvRecord.getRobotCode());
                    robotCacheMap.put(chatSessionConvRecord.getRobotCode(), robot);
                }
                if (EmptyUtils.isNotEmpty(robot)) {
                    chatSessionConvRecord.setRobotName(robot.getRobotName());
                    chatSessionConvRecord.setRobotId(Long.valueOf(robot.getId()));

                }
            }
            if (StringUtils.isEmpty(chatSessionConvRecord.getIsCopy())) {
                chatSessionConvRecord.setIsCopy("0");
            }
            list.add(chatSessionConvRecord);
        });
        return list;
    }

    private static List<AggregationOperation> getConversationAggregationOperations(ChatSessionConvRecordReqVO param) {
        //定义查询对象
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.sort(Sort.Direction.ASC, "createTime"));
//        aggregationOperationList.add(Aggregation.match(Criteria.where("channelId").exists(true)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("channelCode").exists(true)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("sessionId").exists(true)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("roundId").exists(true)));
        aggregationOperationList.add(Aggregation.group("channelCode", "sessionId", "roundId")
                .min("createTime").as("startTime")
                .max("createTime").as("endTime")
                .first("userId").as("userId")
                .first("userName").as("userName")
                .first("content").as("userContent")
                .first("id").as("answerId")
                .last("content").as("answerContent")
                .last("answerType").as("robotAnswerType")
                .last("contextualizeQuestion").as("contextualizeQuestion")
                .last("model").as("model")
                .last("robotCode").as("robotCode")
                .last("answerSource").as("answerSource")
                .last("voteResult").as("userVoteResult")
                .last("voteRemark").as("userVoteRemark")
                .last("userName").as("userName")
                .last("isCopy").as("isCopy")
        );
        aggregationOperationList.add(Aggregation.sort(Sort.Direction.DESC, "startTime"));
        //处理查询条件
        if (StringUtils.isNotEmpty(param.getStartTime())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("startTime").gte(param.getStartTime() + ".000")));
        }else {
//            String date = DateUtils.dateTime2Str(DateUtils.addMonths(new Date(), -3),null);
//            param.setStartTime(date);
//            aggregationOperationList.add(Aggregation.match(Criteria.where("startTime").gte(param.getStartTime() + ".000")));
        }
        if (StringUtils.isNotEmpty(param.getEndTime())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("endTime").lte(param.getEndTime() + ".999")));
        }
        if (StringUtils.isNotEmpty(param.getRobotAnswerType())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("robotAnswerType").is(param.getRobotAnswerType())));
        }
        if (StringUtils.isNotEmpty(param.getUserVoteResult())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("userVoteResult").is(param.getUserVoteResult())));
        }
//        if (param.getChannelId() != null) {
//            aggregationOperationList.add(Aggregation.match(Criteria.where("_id.channelId").is(param.getChannelId())));
//        }
        if (EmptyUtils.isNotEmpty(param.getChannelCode())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("_id.channelCode").is(param.getChannelCode())));
        }
        if (StringUtils.isNotEmpty(param.getConvId())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("_id.roundId").is(param.getConvId())));
        }
        if (StringUtils.isNotEmpty(param.getSessionId())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("_id.sessionId").is(param.getSessionId())));
        }
        if (StringUtils.isNotEmpty(param.getUserContent())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("userContent").regex(param.getUserContent(), "i")));
        }
        if (StringUtils.isNotEmpty(param.getUserName())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("userName").regex(param.getUserName(), "i")));
        }
        if (StringUtils.isNotEmpty(param.getModel())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("model").is(param.getModel())));
        }
        if (StringUtils.isNotEmpty(param.getAnswerSource())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("answerSource").is(param.getAnswerSource())));
        }
        if (StringUtils.isNotEmpty(param.getIntention())) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("answerContent.intention").is(param.getIntention())));
        }
        if (StringUtils.isNotEmpty(param.getIsCopy())) {
            if ("1".equals(param.getIsCopy())) {
                aggregationOperationList.add(Aggregation.match(Criteria.where("isCopy").is("1")));
            } else {
                aggregationOperationList.add(Aggregation.match(Criteria.where("isCopy").ne("1")));
            }
        }
        return aggregationOperationList;
    }

    public void exportConversation(ChatSessionConvRecordReqVO reqVO, HttpServletResponse response) throws Exception {
        List<AggregationOperation> aggregationOperationList = getConversationAggregationOperations(reqVO);
        TypedAggregation<ChatSessionRecord> listAgg = Aggregation.newAggregation(ChatSessionRecord.class, aggregationOperationList)
                .withOptions(Aggregation.newAggregationOptions().allowDiskUse(true).build());
        List<ChatSessionConvRecord> recordList = getCoversationList(listAgg);

        //转换成导出对象
        List<ChatSessionConvRecordExportVO> exportList = new ArrayList<>();
        recordList.forEach(item -> {
            ChatSessionConvRecordExportVO exportVO = new ChatSessionConvRecordExportVO();
            BeanUtils.copyProperties(item, exportVO);
//            exportVO.setRobotAnswerTypeDesc(ChatAnswerTypeEnum.findNameByKey(item.getRobotAnswerType()));
            exportVO.setUserVoteResultDesc(ChatUserVoteResultEnum.findNameByKey(item.getUserVoteResult()));
            exportVO.setAnswerSourceDesc(LargeAnswerSourceEnum.findNameByKey(item.getAnswerSource()));
            exportVO.setIntention(ChatIntentionTypeEnum.findNameByKey(item.getIntention()));
            exportVO.setIsCopy("1".equals(item.getIsCopy()) ? "是" : "否");
            if (ChatAnswerTypeEnum.MODEL_MESSAGE.getKey().equals(item.getRobotAnswerType())){
                try {
                    JSONArray objects = JSONArray.parseArray(item.getAnswerContent());
                    exportVO.setAnswerContent(objects.getJSONObject(0).getString("text"));
                }catch (Exception e){
                    log.error("当前内容不支持解析：{}", item.getSessionId(), e);
                }
            }
            if (EmptyUtils.isNotEmpty(exportVO.getAnswerContent()) &&
                    exportVO.getAnswerContent().equals(exportVO.getUserContent())){
                exportVO.setAnswerContent("");
            }
            exportList.add(exportVO);
        });

        //导出
        ExcelUtils<ChatSessionConvRecordExportVO> exportsExcelUtils = new ExcelUtils<>(ChatSessionConvRecordExportVO.class);
        exportsExcelUtils.writeExcel(exportList, "对话记录_" + DateUtils.thisDate(), response);
    }

    public void exportConversationNew(ChatSessionConvRecordReqVO param, HttpServletResponse response) throws Exception {
        RequestObjectPage<ChatSessionConvRecordReqVO> reqVO = new RequestObjectPage<>();
        reqVO.setPageNum(1);
        reqVO.setPageSize(10000);
        reqVO.setParam(param);
        PageInfo<ChatSessionConvRecord> result = pageConversationNew(reqVO);
        if (EmptyUtils.isNotEmpty(result) && EmptyUtils.isNotEmpty(result.getList())){
            //转换成导出对象
            List<ChatSessionConvRecordExportVO> exportList = new ArrayList<>();
            result.getList().forEach(item -> {
                ChatSessionConvRecordExportVO exportVO = new ChatSessionConvRecordExportVO();
                BeanUtils.copyProperties(item, exportVO);
//            exportVO.setRobotAnswerTypeDesc(ChatAnswerTypeEnum.findNameByKey(item.getRobotAnswerType()));
                exportVO.setUserVoteResultDesc(ChatUserVoteResultEnum.findNameByKey(item.getUserVoteResult()));
                exportVO.setAnswerSourceDesc(LargeAnswerSourceEnum.findNameByKey(item.getAnswerSource()));
                exportVO.setIntention(ChatIntentionTypeEnum.findNameByKey(item.getIntention()));
                exportVO.setIsCopy("1".equals(item.getIsCopy()) ? "是" : "否");
                exportVO.setTime(String.valueOf(item.getTime()));
                if (ChatAnswerTypeEnum.MODEL_MESSAGE.getKey().equals(item.getRobotAnswerType())){
                    try {
                        JSONArray objects = JSONArray.parseArray(item.getAnswerContent());
                        exportVO.setAnswerContent(objects.getJSONObject(0).getString("text"));
                    }catch (Exception e){
                        log.error("当前内容不支持解析：{}", item.getSessionId(), e);
                    }
                }
                if (EmptyUtils.isNotEmpty(exportVO.getAnswerContent()) &&
                        exportVO.getAnswerContent().equals(exportVO.getUserContent())){
                    exportVO.setAnswerContent("");
                }
                exportList.add(exportVO);
            });

            //导出
            ExcelUtils<ChatSessionConvRecordExportVO> exportsExcelUtils = new ExcelUtils<>(ChatSessionConvRecordExportVO.class);
            exportsExcelUtils.writeExcel(exportList, "对话记录_" + DateUtils.thisDate(), response);
        }else {
            throw new BusinessException("导出数据为空！");
        }
    }

    public void fixRecordUserName() {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").exists(true).ne(""));
        query.addCriteria(Criteria.where("userName").exists(false));
        List<ChatSessionRecord> recordList = find(query);
        for (ChatSessionRecord record : recordList) {
            UserInfoResp userInfoResp = kbcUcsService.getUserInfo(record.getUserId(), "T0001");
            if (userInfoResp != null) {
                record.setUserName(userInfoResp.getAgentName());
                update(record);
            }
        }
    }

    public void fixRecordTenantId() {
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").exists(false));
        update(query, Update.update("tenantId", "T0001"));
    }

    /**
     * 根据userId查询当天会话记录
     */
    public List<ChatSessionRecord> findByUserId(String userId) {
        //获取当天的起始时间和结束时间
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date start = calendar.getTime();
        calendar.add(Calendar.DATE, 1);
        Date end = calendar.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startStr = sdf.format(start);
        String endStr = sdf.format(end);
        // 获取今日开始时间和结束

        //构建查询条件，userId等于给定的值，createTime在当天的范围内
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(userId));
        query.addCriteria(Criteria.where("createTime").gte(startStr).lte(endStr));
        return find(query);
    }

    /**
     * 根据userId查询最后一条记录
     *
     * @param userId 用户id
     * @return
     */
    public ChatSessionRecord getLastRecordByUserId(String userId) {
        Query query = new Query();
        Criteria criteria = Criteria.where("userId").is(userId);
        query.addCriteria(criteria);
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        query.with(sort);
        return this.dao.findOne(query);
    }
    /**
    * @Description: 保存问题记录
    * @Param: [largeModelMsgVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/8/23 16:42
    */
    public void saveLargeMsgQRecord(LargeModelMsgVO largeModelMsgVO) {
        // 查询问题记录
        ChatSessionRecord qRecord = this.dao.findById(largeModelMsgVO.getQuestionId());
        if (EmptyUtils.isNotEmpty(qRecord)){
            //已存在数据则不处理
            logger.info("数据已存在，不处理。QId:{}",largeModelMsgVO.getQuestionId());
            return;
        }
        LargeModelMsgVO.External external = largeModelMsgVO.getExternal();
        Long channelId = null;
        if (EmptyUtils.isNotEmpty(external)){
            channelId = external.getChannelId();
            if (EmptyUtils.isEmpty(channelId) && EmptyUtils.isNotEmpty(largeModelMsgVO.getChannelCode())){
//                Channel channel = channelService.getMapper().selectKeyByCode(largeModelMsgVO.getChannelCode());
//                channelId = channel.getId();
                Channel channel = channelService.getMapper().selectByCode(largeModelMsgVO.getChannelCode());
                channelId = Long.valueOf(channel.getId());
            }
        }
        ChatSessionRecord record = ChatSessionRecord.builder().id(largeModelMsgVO.getQuestionId())
                .sessionId(largeModelMsgVO.getSessionId()).userId(largeModelMsgVO.getUserId())
                .userName(largeModelMsgVO.getUsername()).content(largeModelMsgVO.getQuestion())
                .customParam(getCustomParam(largeModelMsgVO)).deviceType(largeModelMsgVO.getDeviceType())
                .tenantId(external.getTenantId()).channelId(channelId).channelCode(largeModelMsgVO.getChannelCode()).sceneShowType(0)
                .subject(ChatSessionRecordSubjectEnum.USER.getKey()).roundId(IdWorker.get32UUID())
                .qaSource(ChatAnswerSourceEnum.LARGE_MODEL.getKey())
                .files(largeModelMsgVO.getFiles())
                .createTime(largeModelMsgVO.getCreateTime()).build();
        save(record);
    }

    private Map<String, Object> getCustomParam(LargeModelMsgVO largeModelMsgVO) {
        Map<String,Object> customParam = new HashMap<>();
        if (EmptyUtils.isNotEmpty(largeModelMsgVO.getProductId())){
            customParam.put("productId", largeModelMsgVO.getProductId());
            customParam.put("productName", largeModelMsgVO.getProductName());
        }
        return customParam;
    }

    /**
    * @Description: 保存答案记录
    * @Param: [largeModelMsgVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/8/23 16:42
    */
    public void saveLargeMsgARecord(LargeModelMsgVO largeModelMsgVO) {
        // 查询问题记录
        ChatSessionRecord qRecord = this.dao.findById(largeModelMsgVO.getQuestionId());
        if (EmptyUtils.isEmpty(qRecord)){
            qRecord = new ChatSessionRecord();
        }
        LargeModelAnswerVO answer = largeModelMsgVO.getAnswer();
        ChatSessionRecord record = ChatSessionRecord.builder().id(largeModelMsgVO.getRecordId())
                .sessionId(qRecord.getSessionId()).userId(qRecord.getUserId()).userName(qRecord.getUserName())
                .channelId(qRecord.getChannelId()).channelCode(qRecord.getChannelCode()).answerSource(answer.getLlmAnswerType()).cost(largeModelMsgVO.getCost())
                .content(Collections.singletonList(largeModelMsgVO.getAnswer())).tenantId(qRecord.getTenantId())
                .qaSource(ChatAnswerSourceEnum.LARGE_MODEL.getKey()).model(largeModelMsgVO.getModel())
                .contextualizeQuestion(largeModelMsgVO.getContextualizeQuestion()).deviceType(qRecord.getDeviceType())
                .robotCode(largeModelMsgVO.getRobotCode()).customParam(getCustomParam(largeModelMsgVO))
                .subject(ChatSessionRecordSubjectEnum.ROBOT.getKey()).roundId(qRecord.getRoundId())
                .qaId(qRecord.getId()).createTime(largeModelMsgVO.getCreateTime()).build();
        if (LargeAnswerSourceEnum.TRANSFER_LABOR.getKey().equals(answer.getLlmAnswerType())){
            record.setAnswerType(ChatAnswerTypeEnum.TRANSFER_LABOR.getKey());
        }else {
            record.setAnswerType(ChatAnswerTypeEnum.MODEL_MESSAGE.getKey());
        }
        //查询答案
        ChatSessionRecord aRecord = this.dao.findOne(Query.query(Criteria.where("id").is(largeModelMsgVO.getRecordId())));
        if (EmptyUtils.isNotEmpty(aRecord)){
            //由于答案是由mass平台异步推送，可能存在先评价后同步数据情况，此时则更新(不更新创建时间)
            record.setCreateTime(aRecord.getCreateTime());
            update(record);
        }else {
            save(record);
        }
//        chatSessionService.updateById(largeModelMsgVO);
        //保存会话记录
        chatSessionService.createSession(largeModelMsgVO);
    }


    public ChatSessionRecord savePushMsgRecord(Long channelId, Object answer, String answerType, String qaId, String qaSource, String roundId, String title,
                                               String sessionId, String userId, String userName,
                                               String relatedArticles, String relatedArticlesName, String subject,
                                               Integer sceneShowType, String sid, String mid, String createTime) {
        ChatSessionRecord record = new ChatSessionRecord();
        record.setContent(answer);
        record.setChannelId(channelId);
        record.setSubject(subject);
        record.setAnswerType(answerType);
        record.setQaId(qaId);
        record.setQaSource(qaSource);
        record.setRelatedArticles(relatedArticles);
        record.setRelatedArticlesName(relatedArticlesName);
        record.setRoundId(roundId);
        record.setSessionId(sessionId);
        record.setId(IdWorker.get32UUID());
        record.setUserId(userId);
        record.setUserName(userName);
        record.setSceneShowType(sceneShowType);
        record.setSid(sid);
        record.setMid(mid);
//        record.setCreateTime(createTime);
        record.setCreateTime(record.recordCreateTime());
        // 判断如果ChatUserUtil.getUser() == null 则将tenantId设置为T0001
        ChatUser chatUser = ChatUserUtil.getUser();
        if (Objects.isNull(chatUser)) {
            record.setTenantId("T0001");
        } else {
            record.setTenantId(chatUser.getTenantId());
        }
        save(record);
        return record;
    }


    /**
     * 根据sid和mid查询会话记录
     */
    public List<ChatSessionRecord> findBySidAndMid(ChatSessionRecord chatSessionRecordReq) {
        String sid = chatSessionRecordReq.getSid();
        String mid = chatSessionRecordReq.getMid();
        String userId = chatSessionRecordReq.getUserId();

        if (StringUtils.isBlank(sid)) {
            return Collections.emptyList();
        }

        Query query = new Query();
        Criteria criteria = Criteria.where("sid").is(sid).and("answerType").is(ChatAnswerTypeEnum.CUSTOMER_SERVICE_ANSWER.getKey());

        if (StringUtils.isNotBlank(mid)) {
            criteria = criteria.and("mid").is(mid);
        } else if (StringUtils.isNotBlank(userId)) {
            criteria = criteria.and("userId").is(userId);
        }

        List<ChatSessionRecord> chatSessionRecords = find(query.addCriteria(criteria));
        if (chatSessionRecords.isEmpty()) {
            return Collections.emptyList();
        }

        ChatSessionRecord chatSessionRecord = chatSessionRecords.get(0);
        String createTime = chatSessionRecord.getCreateTime();

        if (StringUtils.isNotBlank(userId)) {
            Query queryRecord = new Query();
            Criteria criteria1 = Criteria.where("userId").is(userId).and("createTime").gt(createTime).and("answerType").is(ChatAnswerTypeEnum.CUSTOMER_SERVICE_ANSWER.getKey());
            return find(queryRecord.addCriteria(criteria1));
        }

        return Collections.emptyList();
    }

    /**
     * 根据userId查询近3天的会话记录
     *
     * @param userId 用户id
     * @return
     */
    public List<ChatSessionRecord> findRecordByUserId(String userId) {

        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        String formattedDateTime = threeDaysAgo.format(formatter);

        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(userId).and("createTime").gte(formattedDateTime));
        List<ChatSessionRecord> chatSessionRecords = find(query);
        Collections.reverse(chatSessionRecords);
        return chatSessionRecords;
    }

    /**
     * 根据制定日期会话
     * @return
     */
    public List<ChatSessionRecord> findRecordByDate(String date) {
        Query query = new Query();
        query.addCriteria(Criteria.where("createTime").gte(date + " 00:00:00.000").lte(date + " 23:59:59.999")
                .and("sessionId").exists(true).and("channelId").exists(true));
        return find(query);
    }

    /**
     * 转换聊天记录里 answerType类型为16 15的数据 由JSON转为对象
     */
    public void convertAnswerType() {
        Query query = new Query();
        Criteria criteria = Criteria.where("answerType").in("15", "16");
        query.addCriteria(criteria);
        List<ChatSessionRecord> chatSessionRecords = find(query);
        for (ChatSessionRecord chatSessionRecord : chatSessionRecords) {
            Object content = chatSessionRecord.getContent();
            if (content instanceof List) {
                String contentStr = JSONArray.toJSONString(content);
                List<ChatKnowledgeClearRespVO> chatKnowledgeClearRespVOS = JSONArray.parseArray(contentStr).toJavaList(ChatKnowledgeClearRespVO.class);
                chatSessionRecord.setContent(chatKnowledgeClearRespVOS);
                update(chatSessionRecord);
            }
        }
    }

    public void convertJSONData() {
        convertRecord(ChatAnswerTypeEnum.COMMON_CARD.getKey(), ChannelCommonCardVO.class);
        convertRecord(ChatAnswerTypeEnum.GUESS_QUESTION.getKey(), ChannelGuessQuestionVO.class);
    }

    private <T> void convertRecord(String answerType, Class<T> clazz) {
        Query query = new Query();
        Criteria criteria = Criteria.where("answerType").is(answerType).and("content").ne(null);
        query.addCriteria(criteria);
        List<ChatSessionRecord> chatSessionRecords = find(query);
        for (ChatSessionRecord chatSessionRecord : chatSessionRecords) {
            String contentStr = chatSessionRecord.getContent().toString();
            if (!JSON.isValid(contentStr)) {
                continue;
            }
            List<T> contentObjects = JSONArray.parseArray(contentStr, clazz);
            chatSessionRecord.setContent(contentObjects);
            update(chatSessionRecord);
        }
    }
    /**
    * @Description: 同步历史消息
    * @Param: [syncVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/10/18 9:51
    */
    public void syncSessionRecord(LargeModelSyncVO syncVO) {
        int pageNum = 1;
        boolean flag = false;
        do {
            //分页查询知识
            PageRequest<LargeModelSyncVO> pageRequest = new PageRequest<>();
            pageRequest.setPageNum(pageNum);
            pageRequest.setPageSize(PAGE_SIZE);
            pageRequest.setParam(syncVO);
            LargeModelListVO chatRecord = maasHttpService.getChatRecord(pageRequest);
            if(EmptyUtils.isEmpty(chatRecord) || EmptyUtils.isEmpty(chatRecord.getItems())
                    || chatRecord.getTotalPages() >= PAGE_SIZE){
                flag = true;
            }else {
                Map<String,Long> channelMap = new HashMap<>();
                //保存历史消息
                for (LargeModelMsgVO chatRecordItem : chatRecord.getItems()) {
                    LargeModelMsgVO.External external = new LargeModelMsgVO.External();
                    external.setTenantId(syncVO.getTenantId());
                    if (EmptyUtils.isEmpty(channelMap.get(chatRecordItem.getChannelCode()))){
//                        Channel channel = channelService.getMapper().selectKeyByCode(chatRecordItem.getChannelCode());
//                        channelMap.put(chatRecordItem.getChannelCode(),channel.getId());
                        Channel channel = channelService.getMapper().selectByCode(chatRecordItem.getChannelCode());
                        channelMap.put(chatRecordItem.getChannelCode(), Long.valueOf(channel.getId()));
                    }
                    external.setChannelId(channelMap.get(chatRecordItem.getChannelCode()));
                    chatRecordItem.setExternal(external);
                    //保存问题
                    saveLargeMsgQRecord(chatRecordItem);
                    long time = new BigDecimal(chatRecordItem.getCost()).multiply(BigDecimal.valueOf(1000)).longValue();
                    long timeStart = DateUtils.str2DateTime(chatRecordItem.getCreateTime(), null).getTime();
                    chatRecordItem.setCreateTime(DateUtils.date2Str(new Date(timeStart+time),"yyyy-MM-dd HH:mm:ss.SSS"));
                    //保存答案
                    saveLargeMsgARecord(chatRecordItem);
                }
                pageNum++;
            }
        } while (flag);
    }

    /**
     * 同步指定期间附加信息
     * @param startTime
     * @param endTime
     */
    public void syncVoteAndCopy(String startTime, String endTime){
        Query query = new Query();
        Criteria criteria = Criteria.where("createTime").gte(startTime).lte(endTime)
                .orOperator(Criteria.where("isCopy").is("1"),
                        Criteria.where("voteResult").is("1"),
                        Criteria.where("voteResult").is("0"));
        query.addCriteria(criteria);
        List<ChatSessionRecord> chatSessionRecords = this.dao.find(query,"createTime desc","_id,voteResult,voteRemark,isCopy");
        if (EmptyUtils.isNotEmpty(chatSessionRecords)){
            for (ChatSessionRecord record:chatSessionRecords){
                MaasUpdateChatReq chatReq = new MaasUpdateChatReq();
                chatReq.setRecordId(record.getId());
                chatReq.setVoteResult(record.getVoteResult());
                chatReq.setVoteRemark(record.getVoteRemark());
                chatReq.setIsCopy(record.getIsCopy());
                maasHttpService.updateChatRecord(chatReq);
            }
        }
    }
}
