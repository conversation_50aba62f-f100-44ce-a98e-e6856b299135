package com.kbao.kbcchatbot.maas.robot.robot.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.robot.robot.dao.RobotMapper;
import com.kbao.kbcchatbot.maas.robot.robot.bean.RobotSimpleVO;
import com.kbao.kbcchatbot.maas.robot.robot.bean.RobotAddReqVO;
import com.kbao.kbcchatbot.maas.robot.robot.bean.RobotPageReqVO;
import com.kbao.kbcchatbot.maas.robot.robot.entity.Robot;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @Description 算法规则-策略执行节点表Service类
* @Date 2024-12-11
*/
@Service
public class RobotService extends BaseSQLServiceImpl<Robot, Integer, RobotMapper> {

    @Transactional(rollbackFor = Exception.class)
    public int insert(RobotAddReqVO robotAddReqVO) {
        if(EmptyUtils.isEmpty(robotAddReqVO.getRobotCode()) || EmptyUtils.isEmpty(robotAddReqVO.getRobotName()) || EmptyUtils.isEmpty(robotAddReqVO.getRobotType())){
            throw new BusinessException("机器人编码、名称和类型不能为空！");
        }
        Robot robotCheck = this.mapper.selectByCode(robotAddReqVO.getRobotCode());
        if(EmptyUtils.isNotEmpty(robotCheck)){
            throw new BusinessException("机器人编码已存在！");
        }
        Robot robot = new Robot();
        String currentUserId = BscUserUtils.getUserId();
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        robot.setRobotCode(robotAddReqVO.getRobotCode());
        robot.setRobotName(robotAddReqVO.getRobotName());
        robot.setRobotType(robotAddReqVO.getRobotType());
        robot.setSimilarityThreshold(robotAddReqVO.getSimilarityThreshold());
        robot.setRetrievalThreshold(robotAddReqVO.getRetrievalThreshold());
        robot.setCreateBy(currentUserId);
        robot.setUpdateBy(currentUserId);
        robot.setTenantId(tenantId);
        robot.setCreateTime(DateUtils.getCurrentDate());
        robot.setUpdateTime(DateUtils.getCurrentDate());
        return this.mapper.insert(robot);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(RobotAddReqVO robotAddReqVO) {
        Robot robot = this.mapper.selectByPrimaryKey(robotAddReqVO.getId());
        if (EmptyUtils.isEmpty(robot)) {
            throw new BusinessException("机器人ID不存在！");
        }
        robot.setRobotName(robotAddReqVO.getRobotName());
        robot.setSimilarityThreshold(robotAddReqVO.getSimilarityThreshold());
        robot.setRetrievalThreshold(robotAddReqVO.getRetrievalThreshold());
        this.mapper.updateByPrimaryKey(robot);
    }

    public PageInfo<Robot> getRobotPage(PageRequest<RobotPageReqVO> reqVo){
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize(), "t.create_time desc");
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        reqVo.getParam().setTenantId(tenantId);
        List<Robot> robotList = this.mapper.getRobotList(reqVo.getParam());
        return new PageInfo<>(robotList);
    }

    public List<RobotSimpleVO> getRobotList(RobotPageReqVO robotPageReqVO){
        String tenantId = BscUserUtils.getUser().getUser().getTenantId();
        robotPageReqVO.setTenantId(tenantId);
        List<RobotSimpleVO> robotList = this.mapper.getSimpleRobotList(robotPageReqVO);
        return robotList;
    }
    public Robot getRobot(RobotPageReqVO robotPageReqVO){
        return this.mapper.selectByCode(robotPageReqVO.getRobotCode());
    }
}
