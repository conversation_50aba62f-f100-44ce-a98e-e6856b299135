package com.kbao.kbcchatbot.maas.channel.channel.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelPageReqVO;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelPageRespVO;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelUpdateVO;
import com.kbao.kbcchatbot.maas.channel.channel.entity.Channel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @Description maas渠道Dao类
* @Date 2024-12-17
*/
public interface ChannelMapper extends BaseMapper<Channel, Integer>{
    int isExistChannel(@Param("channelCode") String channelCode, @Param("id") Integer id);
    List<ChannelPageRespVO> getChannelList(ChannelPageReqVO channelPageReqVO);
    int count(Map<String, Object> params);
    ChannelUpdateVO selectByChannelCode(@Param("channelCode") String channelCode);
    Channel selectByCode(@Param("channelCode") String channelCode);

}