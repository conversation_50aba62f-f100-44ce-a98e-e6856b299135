package com.kbao.kbcchatbot.maas.channel.channelbasicconfig.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.bean.ChannelBasicManualServiceVO;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.dao.ChannelBasicConfigMapper;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.service.ChannelCommonCardService;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.service.ChannelCommonPhraseService;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.entity.ChannelCommonPhrase;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.entity.ChannelGuessQuestion;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.service.ChannelGuessQuestionService;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.StringUtil;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.kbao.kbcchatbot.constants.Constant.STATUS_DISABLE;
import static com.kbao.kbcchatbot.constants.Constant.STATUS_ENABLE;

/**
 * <AUTHOR>
 * @Description 渠道基础配置Service类
 * @Date 2023-05-19
 */
@Service
@Slf4j
public class ChannelBasicConfigService extends BaseSQLServiceImpl<ChannelBasicConfig, Long, ChannelBasicConfigMapper> {

//    @Autowired
//    @Lazy
//    private ChannelService channelService;

/*    @Autowired
    @Lazy
    private RobotService robotService;*/
    @Autowired
    private ChannelCommonCardService channelCommonCardService;

    @Autowired
    private ChannelCommonPhraseService channelCommonPhraseService;

    @Autowired
    private ChannelGuessQuestionService channelGuessQuestionService;

    @Transactional(rollbackFor = Exception.class)
    public int insert(ChannelBasicConfig channelBasicConfig) {
        String currentUserId = SysLoginUtils.getUserId();
        String nowStr = DateUtils.thisDateTime();
        channelBasicConfig.setUpdateTime(nowStr);
        channelBasicConfig.setUpdateId(currentUserId);
        return this.mapper.insertSelective(channelBasicConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(ChannelBasicConfig channelBasicConfig) {
        if (STATUS_ENABLE.equals(channelBasicConfig.getStatus())) {

            if (STATUS_DISABLE.equals(channelBasicConfig.getGuessQuestionStatus())
                    && STATUS_DISABLE.equals(channelBasicConfig.getCommonPhraseStatus())
                    && STATUS_DISABLE.equals(channelBasicConfig.getAnswerEvaluateStatus())
                    && STATUS_DISABLE.equals(channelBasicConfig.getCommonCardStatus())
            ) {
                throw new RuntimeException("启用渠道，功能不能全部关闭");
            }
            this.checkGuessQuestionCount(channelBasicConfig.getChannelCode(), channelBasicConfig.getGuessQuestionStatus());
            this.checkPhraseCount(channelBasicConfig.getChannelCode(), channelBasicConfig.getCommonPhraseStatus());
        }
        channelBasicConfig.setUpdateTime(DateUtils.thisDateTime());
        channelBasicConfig.setUpdateId(SysLoginUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(channelBasicConfig);
    }

    /**
     * 校验猜你想问数量
     *
     * @param channelCode 渠道编码
     * @param status    渠道状态
     */
    public void checkGuessQuestionCount(String channelCode, String status) {

        //如果渠道状态不是启用状态，不校验
        if (checkIsEnable(channelCode, status)) return;

//        Map<String, Object> queryMap = new HashMap<>(1);
//        queryMap.put("channelCode", channelCode);
//        List<ChannelGuessQuestion> channelGuessQuestions = channelGuessQuestionService.selectByParam(queryMap);
//
//        // 筛选channelGuessQuestions中的status为启用的数量
//        long channelGuessQuestionCount = channelGuessQuestions.stream().filter(channelGuessQuestion -> Objects.equals(channelGuessQuestion.getStatus(), STATUS_ENABLE)).count();
//        if (channelGuessQuestionCount < 12) {
//            throw new BusinessException("猜你想问问题数量不能少于12个");
//        }

    }

    private boolean checkIsEnable(String channelCode, String status) {

        if (Objects.equals(status, STATUS_DISABLE)) {
            return true;
        }

        if (channelCode == null) {
            return true;
        }

        Map<String, Object> queryMap = new HashMap<>(1);
        queryMap.put("channelCode", channelCode);

        List<ChannelBasicConfig> basicConfigList = this.selectByParam(queryMap);
        if (CollectionUtils.isEmpty(basicConfigList)) {
            return true;
        }
        return false;
    }

    /**
     * 校验常用短语数量
     *
     * @param channelCode 渠道编码
     * @param status    渠道状态
     */
    public void checkPhraseCount(String channelCode, String status) {

        if (checkIsEnable(channelCode, status)) return;

        Map<String, Object> queryMap = new HashMap<>(1);
        queryMap.put("channelCode", channelCode);
        List<ChannelCommonPhrase> channelCommonPhrases = channelCommonPhraseService.selectByParam(queryMap);

        // 筛选channelCommonPhrases中的status为启用的数量
        long channelPhraseCount = channelCommonPhrases.stream().filter(channelCommonPhrase -> Objects.equals(channelCommonPhrase.getStatus(), STATUS_ENABLE)).count();
        if (channelPhraseCount > 20) {
            throw new BusinessException("常用短语数量不能大于20个");
        }

    }

    /**
     * 根据渠道id获取渠道基础配置
     *
     * @param channelId 渠道id
     * @return 渠道基础配置
     */
//    public ChannelVO getChannelBasicConfigByChannelId(Long channelId) {
//        ChannelVO channelVO = new ChannelVO();
//
//        // 查询渠道和渠道基础配置的信息
//        Channel channel = channelService.selectByPrimaryKey(channelId);
//        if (Objects.isNull(channel)) {
//            throw new RuntimeException("渠道不存在");
//        }
//
//        // 查询机器人信息
//        Robot robot = null;
//        if (channel.getRobotId() != null) {
//            robot = robotService.selectByPrimaryKey(channel.getRobotId());
//            if (robot == null) {
//                throw new RuntimeException("机器人不存在");
//            }
//        }
//
//        // 将渠道的属性拷贝到 channelVO
//        BeanUtils.copyProperties(channel, channelVO);
//
//        // 配置基础配置信息
//        List<ChannelBasicConfig> channelBasicConfigList = getChannelBasicConfigListByChannelId(channelId);
//        if (CollectionUtils.isNotEmpty(channelBasicConfigList)) {
//            ChannelBasicConfig channelBasicConfig = channelBasicConfigList.get(0);
//            channelVO.setAnswerEvaluateStatus(channelBasicConfig.getAnswerEvaluateStatus());
//            channelVO.setCommonCardStatus(channelBasicConfig.getCommonCardStatus());
//            channelVO.setGuessQuestionStatus(channelBasicConfig.getGuessQuestionStatus());
//            channelVO.setCommonPhraseStatus(channelBasicConfig.getCommonPhraseStatus());
//            channelVO.setWelcomeReply(channelBasicConfig.getWelcomeReply());
//            channelVO.setVoiceStatus(channelBasicConfig.getVoiceStatus());
//            channelVO.setInputPlaceHolder(channelBasicConfig.getInputPlaceHolder());
//            channelVO.setChannelBasicConfigId(channelBasicConfig.getId());
//            channelVO.setRemark(channelBasicConfig.getRemark());
//            channelVO.setPassId(channelBasicConfig.getPassId());
//        }
//
//        // 设置属性值
//        channelVO.setChannelName(channel.getName());
//        channelVO.setRobotName(robot != null ? robot.getName() : null);
//        return channelVO;
//    }

    /**
     * 新增渠道基础配置
     *
     * @param channelBasicConfig 渠道基础配置
     */
    @Transactional(rollbackFor = Exception.class)
    public ChannelBasicConfig addChannelBasicConfig(ChannelBasicConfig channelBasicConfig) {
        log.info("ChannelBasicConfigService 新增渠道基础配置: {}", channelBasicConfig);

        if (StringUtil.isBlank(channelBasicConfig.getWelcomeReply())) {
            throw new RuntimeException("欢迎语不能为空");
        }

        if (StringUtil.isBlank(channelBasicConfig.getInputPlaceHolder())) {
            throw new RuntimeException("输入框引导语不能为空");
        }

        // 检查渠道是否存在
//        Channel channel = channelService.selectByPrimaryKey(channelBasicConfig.getChannelId());
//        if (Objects.isNull(maasChannel)) {
//            throw new RuntimeException("渠道不存在");
//        }

        // 检查渠道基础配置是否已存在
        List<ChannelBasicConfig> existingConfigs = getChannelBasicConfigListByChannelCode(channelBasicConfig.getChannelCode());
        if (CollectionUtils.isNotEmpty(existingConfigs)) {
            throw new RuntimeException("渠道基础配置已存在");
        }

        // 更新渠道信息
//        channel.setName(channelBasicConfig.getChannelName());
//        channel.setStatus(channelBasicConfig.getStatus());
//        channelService.update(channel);

        // 插入渠道基础配置
        insert(channelBasicConfig);

        // 校验猜你想问以及常用短语的数量

        if (STATUS_ENABLE.equals(channelBasicConfig.getStatus())) {

            if (STATUS_DISABLE.equals(channelBasicConfig.getGuessQuestionStatus())
                    && STATUS_DISABLE.equals(channelBasicConfig.getCommonPhraseStatus())
                    && STATUS_DISABLE.equals(channelBasicConfig.getAnswerEvaluateStatus())
                    && STATUS_DISABLE.equals(channelBasicConfig.getCommonCardStatus())
            ) {
                throw new RuntimeException("启用渠道，功能不能全部关闭");
            }
            this.checkGuessQuestionCount(channelBasicConfig.getChannelCode(), channelBasicConfig.getGuessQuestionStatus());
            this.checkPhraseCount(channelBasicConfig.getChannelCode(), channelBasicConfig.getCommonPhraseStatus());
        }

        return channelBasicConfig;
    }

    /**
     * 更新渠道基础配置
     *
     * @param channelBasicConfig 渠道基础配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateChannelBasicConfig(ChannelBasicConfig channelBasicConfig) {
        List<ChannelBasicConfig> existingConfigs = getChannelBasicConfigListByChannelCode(channelBasicConfig.getChannelCode());
        if(EmptyUtils.isNotEmpty(existingConfigs)){
            channelBasicConfig.setId(existingConfigs.get(0).getId());
            // 更新渠道基础配置
            update(channelBasicConfig);
        } else {
            insert(channelBasicConfig);
        }
    }

    /**
     * 检查机器人状态
     *
     * @param robotId 机器人id
     */
/*    private void checkRobotStatus(Long robotId) {
        // 机器人状态检查不是1或者3的时候，不允许启用
        Robot robot = robotService.selectByPrimaryKey(robotId);
        if (Objects.isNull(robot)) {
            throw new RuntimeException("机器人不存在");
        }
        if (!Objects.equals(RobotStatusEnum.PUBLISHED.getCode(), robot.getStatus()) && !Objects.equals(RobotStatusEnum.EDITING.getCode(), robot.getStatus())) {
            throw new RuntimeException("机器人状态不允许启用");
        }
    }*/


    /**
     * 根据渠道id查询渠道基础配置List
     */
    public List<ChannelBasicConfig> getChannelBasicConfigListByChannelId(Long channelId) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("channelId", channelId);
        return selectByParam(params);
    }
    /**
     * 根据渠道code查询渠道基础配置List
     */
    public List<ChannelBasicConfig> getChannelBasicConfigListByChannelCode(String channelCode) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("channelCode", channelCode);
        return selectByParam(params);
    }

    @Transactional(rollbackFor = Exception.class)
    public int manualServiceUpdate(ChannelBasicConfig channelBasicConfig) {
        ChannelBasicConfig channelBasicConfigQuery = this.mapper.selectByChannelCode(channelBasicConfig.getChannelCode());
        channelBasicConfigQuery.setCustServPlatAccessType(channelBasicConfig.getCustServPlatAccessType());
        channelBasicConfigQuery.setCustServPlatAddr(channelBasicConfig.getCustServPlatAddr());
        channelBasicConfigQuery.setUpdateTime(DateUtils.thisDateTime());
        channelBasicConfigQuery.setUpdateId(SysLoginUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(channelBasicConfigQuery);
    }

    public ChannelBasicManualServiceVO manualServiceFind(String channelCode){
        ChannelBasicConfig channelBasicConfigQuery = this.mapper.selectByChannelCode(channelCode);
        ChannelBasicManualServiceVO channelBasicManualServiceVO = new ChannelBasicManualServiceVO();
        if(EmptyUtils.isNotEmpty(channelBasicConfigQuery)){
            BeanUtils.copyProperties(channelBasicConfigQuery, channelBasicManualServiceVO);
        } else {
            return null;
        }
        return channelBasicManualServiceVO;
    }
}
