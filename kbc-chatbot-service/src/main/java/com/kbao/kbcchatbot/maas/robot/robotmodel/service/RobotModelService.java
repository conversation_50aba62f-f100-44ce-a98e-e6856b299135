package com.kbao.kbcchatbot.maas.robot.robotmodel.service;

import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcchatbot.maas.model.entity.Model;
import com.kbao.kbcchatbot.maas.robot.robot.entity.Robot;
import com.kbao.kbcchatbot.maas.robot.robot.service.RobotService;
import com.kbao.kbcchatbot.maas.robot.robotmodel.bean.RobotModelAddReqVo;
import com.kbao.kbcchatbot.maas.robot.robotmodel.bean.RobotModelResVo;
import com.kbao.kbcchatbot.maas.robot.robotmodel.dao.RobotModelMapper;
import com.kbao.kbcchatbot.maas.robot.robotmodel.entity.RobotModel;
import com.kbao.kbcchatbot.utils.MaasPyUtil;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2025-01-09
*/
@Service
public class RobotModelService extends BaseSQLServiceImpl<RobotModel, Integer, RobotModelMapper> {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RobotService robotService;

    @Transactional(rollbackFor = Exception.class)
    public void save(RobotModelAddReqVo reqVo) {
        Robot robot = robotService.selectByPrimaryKey(reqVo.getRobotId());
        mapper.deleteByRobotId(reqVo.getRobotId());

        String tenantId = SysLoginUtils.getUser().getTenantId();
        String userId = SysLoginUtils.getUserId();
        List<RobotModel> robotModels = reqVo.getModels();
        for (RobotModel model : robotModels) {
            model.setRobotId(reqVo.getRobotId());
            model.setCreateBy(userId);
            model.setTenantId(tenantId);
        }
        this.batchInsert(robotModels);

        String redisKey = MaasPyUtil.getRedisKey(robot.getRobotCode());
        redisUtil.deleteAllBykeyPre(redisKey);
    }

    public List<RobotModelResVo> getModelByRobotId(Integer robotId) {
        return mapper.getModelByRobotId(robotId);
    }
}
