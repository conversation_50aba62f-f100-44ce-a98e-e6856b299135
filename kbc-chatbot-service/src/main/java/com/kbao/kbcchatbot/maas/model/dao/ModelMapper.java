package com.kbao.kbcchatbot.maas.model.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.model.bean.ModelPageResVo;
import com.kbao.kbcchatbot.maas.model.entity.Model;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2025-01-08
*/
public interface ModelMapper extends BaseMapper<Model, Integer>{

    List<Model> getModelList();

    List<ModelPageResVo> getModelPage(Map params);
}