package com.kbao.kbcchatbot.maas.channel.channelguessquestion.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.common.convert.ConvertService;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.service.ChannelBasicConfigService;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.dao.ChannelGuessQuestionMapper;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.entity.ChannelGuessQuestion;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelUpdateVO;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.tool.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.kbao.kbcchatbot.constants.Constant.STATUS_DISABLE;
import static com.kbao.kbcchatbot.constants.Constant.STATUS_ENABLE;

/**
 * <AUTHOR>
 * @Description 渠道猜你想问Service类
 * @Date 2023-05-24
 */
@Service
@Slf4j
public class ChannelGuessQuestionService extends BaseSQLServiceImpl<ChannelGuessQuestion, Long, ChannelGuessQuestionMapper> {

    @Autowired
    private ChannelBasicConfigService channelBasicConfigService;

    @Autowired
    private ChannelService channelService;
    @Transactional(rollbackFor = Exception.class)
    public int insert(ChannelGuessQuestion channelGuessQuestion) {
        String currentUserId = BscUserUtils.getUserId();
        String nowStr = DateUtils.thisDateTime();
        channelGuessQuestion.setCreateTime(nowStr);
        channelGuessQuestion.setCreateId(currentUserId);
        channelGuessQuestion.setUpdateTime(nowStr);
        channelGuessQuestion.setUpdateId(currentUserId);
        channelGuessQuestion.setIsDeleted(0);
        return this.mapper.insertSelective(channelGuessQuestion);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(ChannelGuessQuestionVO channelGuessQuestionVO) {
        //参数校验
        checkChannelParam(channelGuessQuestionVO, "update");
        ChannelUpdateVO channelUpdateVO = channelService.getMapper().selectByChannelCode(channelGuessQuestionVO.getChannelCode());

        ChannelGuessQuestion channelGuessQuestion = new ChannelGuessQuestion();
        BeanUtils.copyProperties(channelGuessQuestionVO, channelGuessQuestion);
        if (channelGuessQuestionVO.getContent() != null) {
            channelGuessQuestion.setContent(channelGuessQuestionVO.getContent().getBytes(StandardCharsets.UTF_8));
        }
        channelGuessQuestion.setChannelId(Long.valueOf(channelUpdateVO.getChannelId()));
        channelGuessQuestion.setUpdateTime(DateUtils.thisDateTime());
        channelGuessQuestion.setUpdateId(BscUserUtils.getUserId());
        return this.mapper.updateByPrimaryKeySelective(channelGuessQuestion);
    }

    /**
     * 新增渠道猜你想问
     *
     * @param channelGuessQuestionVO 渠道猜你想问VO
     */
    @Transactional(rollbackFor = Exception.class)
    public ChannelGuessQuestion addChannelGuessQuestion(ChannelGuessQuestionVO channelGuessQuestionVO) {
        log.info("ChannelGuessQuestionService 新增渠道猜你想问，参数：{}", JSONObject.toJSONString(channelGuessQuestionVO));
        ChannelGuessQuestion channelGuessQuestion = new ChannelGuessQuestion();
        BeanUtils.copyProperties(channelGuessQuestionVO, channelGuessQuestion);
        channelGuessQuestion.setContent(channelGuessQuestionVO.getContent().getBytes(StandardCharsets.UTF_8));
        insert(channelGuessQuestion);
        return channelGuessQuestion;
    }

    /**
     * 创建一个渠道猜你想问，并返回创建的对象
     *
     * @param channelGuessQuestionVO 渠道猜你想问的视图对象，不能为null
     * @return 创建的渠道猜你想问对象
     */
    @Transactional(rollbackFor = Exception.class)
    public ChannelGuessQuestion createChannelGuessQuestion(ChannelGuessQuestionVO channelGuessQuestionVO) {
        log.info("ChannelGuessQuestionService 创建渠道猜你想问，参数：{}", JSONObject.toJSONString(channelGuessQuestionVO));
        //参数校验
        checkChannelParam(channelGuessQuestionVO, "create");
        // 复制属性
        ChannelUpdateVO channelUpdateVO = channelService.getMapper().selectByChannelCode(channelGuessQuestionVO.getChannelCode());
        ChannelGuessQuestion channelGuessQuestion = new ChannelGuessQuestion();
        BeanUtils.copyProperties(channelGuessQuestionVO, channelGuessQuestion);
        channelGuessQuestion.setChannelId(Long.valueOf(channelUpdateVO.getChannelId()));
        channelGuessQuestion.setContent(channelGuessQuestionVO.getContent().getBytes(StandardCharsets.UTF_8));
        // 插入数据
        insert(channelGuessQuestion);
        return channelGuessQuestion;
    }


    /**
     * 根据id获取渠道猜你想问
     *
     * @param id 渠道猜你想问id
     * @return 渠道猜你想问VO
     */
    @Transactional(readOnly = true)
    public ChannelGuessQuestionVO getChannelGuessQuestionById(Long id) {
        ChannelGuessQuestion channelGuessQuestion = selectByPrimaryKey(id);
        if (channelGuessQuestion != null) {
            ChannelGuessQuestionVO channelGuessQuestionVO = new ChannelGuessQuestionVO();
            BeanUtils.copyProperties(channelGuessQuestion, channelGuessQuestionVO);
            if (channelGuessQuestion.getContent() != null) {
                channelGuessQuestionVO.setContent(new String(channelGuessQuestion.getContent(), StandardCharsets.UTF_8));
            }
            return channelGuessQuestionVO;
        }
        return null;
    }

    /**
     * 根据渠道id获取渠道猜你想问
     *
     * @param channelId 渠道id
     * @return 渠道猜你想问VO
     */
    public List<ChannelGuessQuestionVO> getChannelGuessQuestionListByChannelId(Long channelId, String status , String sourceType) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("channelId", channelId);
        map.put("status", status);
        if (StringUtils.isNotBlank(sourceType)) {
            map.put("sourceType", sourceType);
        }
        return ConvertService.getList(this.mapper::getChannelGuessQuestionListByChannelId, map, ChannelGuessQuestionVO.class);
    }

    /**
     * 当启用状态为关闭时校验渠道猜你想问条数是否小于12条 小于抛出异常
     */
    public void checkChannelParam(ChannelGuessQuestionVO channelGuessQuestionVO, String type) {

        // 校验标题是否重复
        checkTitle(channelGuessQuestionVO);

        //渠道内容校验
        Objects.requireNonNull(channelGuessQuestionVO, "渠道猜你想问对象不能为null");
        if (StringUtils.isBlank(channelGuessQuestionVO.getContent()) && StringUtils.isNotBlank(channelGuessQuestionVO.getTitle())) {
            throw new BusinessException("渠道猜你想问的内容不能为空");
        }

        if ("update".equals(type) && STATUS_DISABLE.equals(channelGuessQuestionVO.getStatus())) {
            Map<String, Object> queryMap = new HashMap<>(1);
            queryMap.put("channelCode", channelGuessQuestionVO.getChannelCode());

            List<ChannelBasicConfig> channelBasicConfigListByChannelCode = channelBasicConfigService.getChannelBasicConfigListByChannelCode(channelGuessQuestionVO.getChannelCode());
            if (CollectionUtils.isNotEmpty(channelBasicConfigListByChannelCode)) {
                ChannelBasicConfig channelBasicConfig = channelBasicConfigListByChannelCode.get(0);
                // 猜你想问是启用状态时才校验条数
                if (Objects.equals(channelBasicConfig.getGuessQuestionStatus(), STATUS_ENABLE)) {
                    List<ChannelGuessQuestion> channelGuessQuestions = selectByParam(queryMap);
                    // 筛选channelGuessQuestions中status为1的数据
                    List<ChannelGuessQuestion> channelGuessQuestionList = channelGuessQuestions.stream().filter(channelGuessQuestion -> Objects.equals(channelGuessQuestion.getStatus(), STATUS_ENABLE)).collect(Collectors.toList());
                    if (channelGuessQuestionList.size() - 1 < 12) {
                        throw new BusinessException("渠道猜你想问条数不能小于12条");
                    }
                }
            }
        }
    }

    public void checkTitle(ChannelGuessQuestionVO channelGuessQuestionVO) {

        if (StringUtils.isNotBlank(channelGuessQuestionVO.getTitle()) && channelGuessQuestionVO.getChannelCode() != null) {
            //渠道标题重复校验
            Map<String, Object> params = new HashMap<>(2);
            params.put("channelCode", channelGuessQuestionVO.getChannelCode());
            params.put("title", channelGuessQuestionVO.getTitle());
            List<ChannelGuessQuestion> channelGuessQuestionsData = selectByParam(params);
            channelGuessQuestionsData.removeIf(x -> x.getId().equals(channelGuessQuestionVO.getId()));
            if (CollectionUtils.isNotEmpty(channelGuessQuestionsData)) {
                throw new BusinessException("该渠道下已存在相同标题的猜你想问");
            }
        }
    }

    public void deleteChannelGuessQuestion(ChannelGuessQuestion channelGuessQuestionReq) {

        ChannelGuessQuestion channelGuessQuestionData = selectByPrimaryKey(channelGuessQuestionReq.getId());
        if (Objects.isNull(channelGuessQuestionData)) {
            throw new BusinessException("要删除的渠道猜你想问不存在=" + JSONObject.toJSONString(channelGuessQuestionReq));
        }

        List<ChannelBasicConfig> channelBasicConfigListByChannelId = channelBasicConfigService.getChannelBasicConfigListByChannelCode(channelGuessQuestionReq.getChannelCode());
        if (CollectionUtils.isNotEmpty(channelBasicConfigListByChannelId)) {
            ChannelBasicConfig channelBasicConfig = channelBasicConfigListByChannelId.get(0);
            // 猜你想问是启用状态时才校验条数
            if (Objects.equals(channelBasicConfig.getGuessQuestionStatus(), STATUS_ENABLE)) {
                Map<String, Object> queryMap = new HashMap<>(1);
                queryMap.put("channelId", channelGuessQuestionReq.getChannelId());
                List<ChannelGuessQuestion> channelGuessQuestions = selectByParam(queryMap);
                // 筛选channelGuessQuestions中status为1的数据
                List<ChannelGuessQuestion> channelGuessQuestionList = channelGuessQuestions.stream().filter(channelGuessQuestion -> Objects.equals(channelGuessQuestion.getStatus(), STATUS_ENABLE)).collect(Collectors.toList());
                if (channelGuessQuestionList.size() - 1 < 12) {
                    throw new BusinessException("无法删除-渠道猜你想问条数不能小于12条");
                }
            }
        }
        log.info("当前删除的猜你想问为={}",JSONObject.toJSONString(channelGuessQuestionReq));
        channelGuessQuestionData.setIsDeleted(1);
        mapper.updateByPrimaryKeySelective(channelGuessQuestionData);
    }



    /**
     * 根据渠道id获取渠道猜你想问
     *
     * @param channelCode 渠道编码
     * @return 渠道猜你想问VO
     */
    public List<ChannelGuessQuestionVO> getChannelGuessQuestionListByChannelCode(String channelCode, String status , String sourceType) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("channelCode", channelCode);
        map.put("status", status);
        if (StringUtils.isNotBlank(sourceType)) {
            map.put("sourceType", sourceType);
        }
        return ConvertService.getList(this.mapper::getChannelGuessQuestionListByChannelCode, map, ChannelGuessQuestionVO.class);
    }
}
