package com.kbao.kbcchatbot.maas.channel.channelrobot.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.service.ChannelRobotPromptService;
import com.kbao.kbcchatbot.maas.robot.robot.service.RobotService;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotAddReqVo;
import com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotListVo;
import com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotPageReqVo;
import com.kbao.kbcchatbot.maas.channel.channelrobot.dao.ChannelRobotMapper;
import com.kbao.kbcchatbot.maas.channel.channelrobot.entity.ChannelRobot;
import com.kbao.kbcchatbot.maas.robot.robot.entity.Robot;
import com.kbao.tool.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @Description maas渠道机器人关联表Service类
* @Date 2024-12-17
*/
@Service
public class ChannelRobotService extends BaseSQLServiceImpl<ChannelRobot, Integer, ChannelRobotMapper> {

    @Autowired
    private RobotService robotService;

    @Autowired
    private ChannelRobotPromptService channelRobotPromptService;

    @Transactional(rollbackFor = Exception.class)
    public int insert(ChannelRobotAddReqVo channelRobotAddReqVo) {
        Integer channelId = channelRobotAddReqVo.getChannelId();
        Integer robotId = channelRobotAddReqVo.getRobotId();
        Robot robot = robotService.getMapper().selectByPrimaryKey(robotId);
        int count = this.mapper.isExistChannelRobot(channelId, robot.getRobotType());
        if(count > 0){
            throw new BusinessException("该渠道下已存在同类型的机器人！");
        }
        String currentUserId = BscUserUtils.getUserId();
        ChannelRobot channelRobot = new ChannelRobot();
        channelRobot.setStatus("1");
        channelRobot.setChannelId(channelId);
        channelRobot.setRobotId(robotId);
        channelRobot.setCreateBy(currentUserId);
        channelRobot.setCreateTime(DateUtils.getCurrentDate());
        return this.mapper.insert(channelRobot);
    }

    public List<ChannelRobotListVo> getChannelRobotList(Integer channelId){
        List<ChannelRobotListVo> channelRobotList = this.mapper.getChannelRobotList(channelId);
        return channelRobotList;
    }

    public List<ChannelRobotListVo> getChannelRobotPage(ChannelRobotPageReqVo reqVo){
        List<ChannelRobotListVo> channelRobotList = this.mapper.getChannelRobotPage(reqVo);
        return channelRobotList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(ChannelRobot channelRobot){
        this.mapper.updateByPrimaryKeySelective(channelRobot);
    }

    public void delChannelRobot(Integer id){
        channelRobotPromptService.delByChannelRobotId(id);
        this.mapper.deleteByPrimaryKey(id);
    }
}
