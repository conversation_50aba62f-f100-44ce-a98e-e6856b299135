package com.kbao.kbcchatbot.maas.chatsession.service;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcchatbot.maas.chatsession.bean.ChatSessionCache;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerReqVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerRespVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryModelAnswerVO;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerTypeEnum;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatSessionRecordSubjectEnum;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatModelAnswerVO;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcchatbot.websocket.WebSocketServer;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public abstract class ChatSearchAbstractService {

    @Autowired
    protected ChatSessionRecordService chatSessionRecordService;

    @Autowired
    private KnowledgeModelSearchService knowledgeModelSearchService;

    public ChatQueryAnswerRespVO queryAnswer(ChatQueryAnswerReqVO req, boolean isAsync) {
        String roundId = IdWorker.get32UUID();
        String sceneId = null;
        if (EmptyUtils.isNotEmpty(req.getClearRecordId())){
            ChatSessionRecord sessionRecord = chatSessionRecordService.findById(req.getClearRecordId());
            if (EmptyUtils.isNotEmpty(sessionRecord)){
                roundId = sessionRecord.getRoundId();
                sceneId = sessionRecord.getSceneId();
            }
        }
        //保存用户提问记录
        ChatSessionRecord userRecord = saveUserRecord(ChatUserUtil.getUser().getChannelId(), req.getQuestion(),
                req.getBizType(), req.getBizId(), null, null, roundId,sceneId,
                ChatUserUtil.getUser().getSessionId(), ChatUserUtil.getUserId(), ChatUserUtil.getUser().getUserName(),
                req.getFieldData(), req.getSceneShowType());
        //获取答案
        ChatQueryAnswerRespVO respVO;
        if(StringUtil.isNotEmpty(req.getQaId())) {
            respVO = query(req.getQaId(),req.getSlots(),req.getSceneShowType(),userRecord.getId(),isAsync);
        }else if(StringUtil.isNotEmpty(req.getPayload())){
            respVO = query(req.getPayload(),req.getSlots(),req.getSceneShowType(),userRecord.getId(),isAsync);
        }else {
            respVO = query(req.getQuestion(),req.getSlots(),req.getSceneShowType(),userRecord.getId(),isAsync);
        }
        //校验回复是否转人工
        if (checkTransferLabor(ChatUserUtil.getUser().getChatSessionCache(),null, YesNoEnum.NO.getValue(),respVO)){
            respVO.setType(ChatAnswerTypeEnum.TRANSFER_LABOR.getKey());
            respVO.setAnswer(ChatUserUtil.getUser().getChatSessionCache().getCustServPlatAddr());
        }
        //保存机器人回复记录
        sceneId = EmptyUtils.isEmpty(respVO.getSceneId())?sceneId:respVO.getSceneId();
        ChatSessionRecord robotRecord = saveRobotRecord(ChatUserUtil.getUser().getChannelId(), respVO.getAnswer(),
                respVO.getType(), null, null, roundId,respVO.getTitle(), ChatUserUtil.getUser().getSessionId(),
                ChatUserUtil.getUserId(), ChatUserUtil.getUser().getUserName(), respVO.getRelatedArticles(), respVO.getRelatedArticlesName(),
                sceneId, respVO.getSceneShowType(),null);
        //封装返回值
        respVO.setRecordStartTime(userRecord.getCreateTime());
        respVO.setRecordId(robotRecord.getId());
        if (isAsync){
            sendMsg(respVO);
        }
        return respVO;
    }
    /**
    * @Description: 发送消息(此处不记录消息日志,由最终返回的完整结果来记录)
    * @Param: [respVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/7/9 9:31
    */
    public void sendMsg(ChatQueryAnswerRespVO respVO){
        //保存记录
//        ChatSessionRecord sessionRecord = saveAnswerRecord(respVO);
//        respVO.setRecordStartTime(sessionRecord.getCreateTime());
//        respVO.setRecordId(sessionRecord.getId());
        //推送消息
        WebSocketServer.sendInfo(JSONObject.toJSONString(respVO),ChatUserUtil.getUser().getToken());
    }
    /**
    * @Description: 异步查询结果
    * @Param: [req]
    * @return: void
    * @Author: husw
    * @Date: 2024/7/3 11:05
    */
//    public void queryAsyncAnswer(ChatQueryAnswerReqVO req) {
//        String roundId = IdWorker.get32UUID();
//        String sceneId = null;
//        if (EmptyUtils.isNotEmpty(req.getClearRecordId())){
//            ChatSessionRecord sessionRecord = chatSessionRecordService.findById(req.getClearRecordId());
//            if (EmptyUtils.isNotEmpty(sessionRecord)){
//                roundId = sessionRecord.getRoundId();
//                sceneId = sessionRecord.getSceneId();
//            }
//        }
//        //保存用户提问记录
//        ChatSessionRecord userRecord = saveUserRecord(ChatUserUtil.getUser().getChannelId(), req.getQuestion(),
//                req.getBizType(), req.getBizId(), null, null, roundId,sceneId,
//                ChatUserUtil.getUser().getSessionId(), ChatUserUtil.getUserId(), ChatUserUtil.getUser().getUserName(),
//                req.getFieldData(), req.getSceneShowType());
//        //获取答案
//        if(StringUtil.isNotEmpty(req.getQaId())) {
//            query(req.getQaId(),req.getSlots(),req.getSceneShowType(),userRecord.getId(),true);
//        }else if(StringUtil.isNotEmpty(req.getPayload())){
//            query(req.getPayload(),req.getSlots(),req.getSceneShowType(),userRecord.getId(),true);
//        }else {
//            query(req.getQuestion(),req.getSlots(),req.getSceneShowType(),userRecord.getId(),true);
//        }
//    }
    /**
    * @Description: 异步结果记录
    * @Param: [respVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/7/3 11:06
    */
//    public ChatSessionRecord saveAnswerRecord(ChatQueryAnswerRespVO respVO) {
//        ChatSessionRecord chatRecord = chatSessionRecordService.findById(respVO.getRecordId());
//        //保存机器人回复记录
//        return saveRobotRecord(ChatUserUtil.getUser().getChannelId(), respVO.getAnswer(),
//                respVO.getType(), null, null, chatRecord.getRoundId(),respVO.getTitle(), ChatUserUtil.getUser().getSessionId(),
//                ChatUserUtil.getUserId(), ChatUserUtil.getUser().getUserName(), respVO.getRelatedArticles(), respVO.getRelatedArticlesName(),
//                null, respVO.getSceneShowType(),respVO.getAnswerId());
//    }
    /**
     * @Description: 校验转人工配置
     * @Param: [chatSessionCache, question]
     * @return: java.lang.Integer
     * @Author: husw
     * @Date: 2023/6/28 11:17
     */
    protected boolean checkTransferLabor(ChatSessionCache chatSessionCache,String question,Integer isBefore,ChatQueryAnswerRespVO respVO) {
        List<ChatSessionRecord> sessionRecords = null;
        if (YesNoEnum.YES.getValue().equals(isBefore)){
            //关键字
            if (EmptyUtils.isNotEmpty(chatSessionCache.getKeywords())){
                for (String word:chatSessionCache.getKeywords()){
                    if (question.contains(word)){
                        log.info("触发关键字转人工");
                        return true;
                    }
                }
            }
            //用户重复提问次数限制
            if (EmptyUtils.isNotEmpty(chatSessionCache.getSameQuestionTransferTimes())){
                sessionRecords = chatSessionRecordService.findBySubAndTypeLimit(ChatUserUtil.getUser().getSessionId(),ChatSessionRecordSubjectEnum.USER.getKey(), chatSessionCache.getSameQuestionTransferTimes());
                if (EmptyUtils.isNotEmpty(sessionRecords)){
                    Set<Object> answer = sessionRecords.stream().map(ChatSessionRecord::getContent).collect(Collectors.toSet());
                    if (sessionRecords.size() == chatSessionCache.getSameQuestionTransferTimes() && answer.size() == 1){
                        log.info("用户重复提问次数限制转人工");
                        return true;
                    }
                }
            }
        }else {
            //无回复次数限制
            if (EmptyUtils.isNotEmpty(chatSessionCache.getNoReplyTransferTimes()) && ChatAnswerTypeEnum.NO_ANSWER.getKey().equals(respVO.getType())){
                if(chatSessionCache.getNoReplyTransferTimes().equals(1)){
                    log.info("无回复次数限制转人工");
                    return true;
                }else {
                    sessionRecords = chatSessionRecordService.findBySubAndTypeLimit(ChatUserUtil.getUser().getSessionId(),
                            ChatSessionRecordSubjectEnum.ROBOT.getKey(), chatSessionCache.getNoReplyTransferTimes()-1);
                    if (EmptyUtils.isNotEmpty(sessionRecords)){
                        long count = sessionRecords.stream().filter(x -> ChatAnswerTypeEnum.NO_ANSWER.getKey().equals(x.getAnswerType())
                                || ChatAnswerTypeEnum.EMOJI_ANSWER.getKey().equals(x.getAnswerType()) ).count();
                        if (chatSessionCache.getNoReplyTransferTimes().longValue()==count+1){
                            log.info("无回复次数限制转人工");
                            return true;
                        }
                    }
                }
            }
            //澄清重复回复次数限制
            if (EmptyUtils.isNotEmpty(chatSessionCache.getClearTransferTimes()) && ChatAnswerTypeEnum.QUESTION_CLEAR.getKey().equals(respVO.getType())){
                if(chatSessionCache.getClearTransferTimes().equals(1)){
                    log.info("澄清重复回复次数限制转人工");
                    return true;
                }else {
                    if (!chatSessionCache.getNoReplyTransferTimes().equals(chatSessionCache.getClearTransferTimes())){
                        sessionRecords = chatSessionRecordService.findBySubAndTypeLimit(ChatUserUtil.getUser().getSessionId(),
                                ChatSessionRecordSubjectEnum.ROBOT.getKey(), chatSessionCache.getClearTransferTimes()-1);
                    }
                    if (EmptyUtils.isNotEmpty(sessionRecords)){
                        Set<Object> answer = new HashSet<>();
                        answer.add(respVO.getAnswer());
                        long count = sessionRecords.stream().filter(x-> ChatAnswerTypeEnum.QUESTION_CLEAR.getKey().equals(x.getAnswerType())).peek(x -> answer.add(x.getContent())).count();
                        if (chatSessionCache.getClearTransferTimes().longValue()==count+1 && answer.size() == 1){
                            log.info("澄清重复回复次数限制转人工");
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private ChatSessionRecord saveUserRecord(Long channelId,String question, String questionType, String questionBizId,
                                             String qaSource, String questionClearRecordId, String roundId,String sceneId,
                                             String sessionId, String userId, String userName, Map<String,Object> fieldData, Integer sceneShowType) {
        ChatSessionRecord record = new ChatSessionRecord();
        record.setChannelId(channelId);
        record.setCreateTime(record.recordCreateTime());
        record.setQuestionType(questionType);
        record.setQuestionBizId(questionBizId);
        record.setQuestionClearRecordId(questionClearRecordId);
        record.setQaSource(qaSource);
        record.setContent(question);
        record.setSubject(ChatSessionRecordSubjectEnum.USER.getKey());
        record.setRoundId(roundId);
        record.setSceneId(sceneId);
        record.setSessionId(sessionId);
        record.setId(IdWorker.get32UUID());
        record.setUserId(userId);
        record.setUserName(userName);
//        record.setFieldData(fieldData);
        record.setSceneShowType(sceneShowType);
        record.setTenantId(ChatUserUtil.getUser().getTenantId());
        return chatSessionRecordService.save(record);
    }

    private ChatSessionRecord saveRobotRecord(Long channelId, Object answer, String answerType, String qaId, String qaSource, String roundId,String title,
                                              String sessionId, String userId, String userName, String relatedArticles, String relatedArticlesName,
                                              String sceneId, Integer sceneShowType,String answerId) {
        ChatSessionRecord record = new ChatSessionRecord();
        record.setContent(answer);
        record.setChannelId(channelId);
        record.setSubject(ChatSessionRecordSubjectEnum.ROBOT.getKey());
        record.setAnswerType(answerType);
        record.setAnswerId(answerId);
        record.setQaId(qaId);
        record.setQaSource(qaSource);
        record.setRelatedArticles(relatedArticles);
        record.setRelatedArticlesName(relatedArticlesName);
        record.setRoundId(roundId);
        record.setSceneId(sceneId);
        record.setSessionId(sessionId);
        record.setId(IdWorker.get32UUID());
        record.setUserId(userId);
        record.setUserName(userName);
        record.setSceneShowType(sceneShowType);
        record.setCreateTime(record.recordCreateTime());
        record.setTenantId(ChatUserUtil.getUser().getTenantId());
        chatSessionRecordService.save(record);
        return record;
    }

    protected boolean checkSensitiveWords(String question, List<String> sensitiveWords) {
        if (EmptyUtils.isNotEmpty(sensitiveWords)){
            for (String words: sensitiveWords){
                if (question.contains(words)){
                    return true;
                }
            }
        }
        return false;
    }

    protected ChatQueryAnswerRespVO getCustomResp(ChatAnswerTypeEnum chatAnswerTypeEnum, String answer, String recordId, String sceneId) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        respVO.setRecordId(recordId);
        respVO.setType(chatAnswerTypeEnum.getKey());
        respVO.setAnswer(answer);
        respVO.setSceneId(sceneId);
        return respVO;
    }

    protected ChatQueryAnswerRespVO getNoAnswerResp(String noAnswerContent,String recordId,String sceneId) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        respVO.setType(ChatAnswerTypeEnum.NO_ANSWER.getKey());
        respVO.setAnswer(noAnswerContent);
        respVO.setRecordId(recordId);
        respVO.setSceneId(sceneId);
        return respVO;
    }

    protected ChatQueryAnswerRespVO getModelAnswerResp(List<ChatQueryModelAnswerVO> answerVOList, String sceneId) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        respVO.setType(ChatAnswerTypeEnum.DIALOGUE_ANSWER.getKey());
        respVO.setAnswer(answerVOList);
        respVO.setSceneId(sceneId);
        return respVO;
    }
    protected ChatQueryAnswerRespVO getModelAnswerResps(List<ChatModelAnswerVO> answerVOList, String sceneId,String answerId) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        respVO.setType(ChatAnswerTypeEnum.MODEL_MESSAGE.getKey());
        respVO.setAnswer(answerVOList);
        respVO.setAnswerId(answerId);
        respVO.setSceneId(sceneId);
        return respVO;
    }

    protected abstract ChatQueryAnswerRespVO query(String question, Map<String, Object> slots,Integer sceneShowType, String sessionRecordId, boolean isAsync);

}
