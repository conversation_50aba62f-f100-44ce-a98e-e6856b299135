package com.kbao.kbcchatbot.maas.robot.robotmodel.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.model.entity.Model;
import com.kbao.kbcchatbot.maas.robot.robotmodel.bean.RobotModelResVo;
import com.kbao.kbcchatbot.maas.robot.robotmodel.entity.RobotModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2025-01-09
*/
public interface RobotModelMapper extends BaseMapper<RobotModel, Integer>{

    void deleteByRobotId(@Param("robotId") Integer robotId);

    List<RobotModelResVo> getModelByRobotId(@Param("robotId") Integer robotId);

}