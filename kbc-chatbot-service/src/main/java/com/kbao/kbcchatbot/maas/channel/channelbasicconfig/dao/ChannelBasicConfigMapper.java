package com.kbao.kbcchatbot.maas.channel.channelbasicconfig.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @Description 渠道基础配置Dao类
* @Date 2023-05-19
*/
public interface ChannelBasicConfigMapper  extends BaseMapper<ChannelBasicConfig, Long>{

    /**
     * 根据渠道ids获取渠道基础配置List
     * @param channelIds 渠道ids
     * @return 渠道基础配置List
     */
    List<ChannelBasicConfig> selectBatchByPrimaryKey(@Param("channelIds") List<Long> channelIds);

    ChannelBasicConfig selectByChannelCode(@Param("channelCode") String channelCode);
}