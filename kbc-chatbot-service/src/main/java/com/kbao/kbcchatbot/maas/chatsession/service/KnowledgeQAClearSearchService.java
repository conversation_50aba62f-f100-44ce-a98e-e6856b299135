package com.kbao.kbcchatbot.maas.chatsession.service;

import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerRespVO;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerTypeEnum;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackageQA;
import com.kbao.kbcchatbot.discard.knowledgepackage.service.KnowledgePackageQAApiService;
import com.kbao.kbcchatbot.utils.StringUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description QA问题澄清实现Service类
 * @Date 2023-6-13
 */
@Service
public class KnowledgeQAClearSearchService extends ChatSearchAbstractService {

    @Autowired
    private KnowledgePackageQAApiService knowledgePackageQAApiService;

    @Autowired
    private KnowledgeQASearchService knowledgeQASearchService;

    @Override
    protected ChatQueryAnswerRespVO query(String question, Map<String, Object> slots,Integer sceneShowType, String sessionRecordId, boolean isAsync) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        KnowledgePackageQA knowledgePackageQA = knowledgePackageQAApiService.selectByQAId(StringUtil.escape(question));
        respVO.setAnswer(knowledgePackageQA.getAnswer());
        respVO.setType(ChatAnswerTypeEnum.QUESTION_CLEAR_ANSWER.getKey());
        respVO.setTitle(knowledgePackageQA.getTitle());
        String relatedArticlesName = knowledgeQASearchService.getCloudTitle(knowledgePackageQA.getRelatedArticles(), ChatUserUtil.getUser().getRobotId());
        if (EmptyUtils.isNotEmpty(relatedArticlesName)){
            respVO.setRelatedArticles(knowledgePackageQA.getRelatedArticles());
            respVO.setRelatedArticlesName(relatedArticlesName);
        }
        return respVO;
    }

}
