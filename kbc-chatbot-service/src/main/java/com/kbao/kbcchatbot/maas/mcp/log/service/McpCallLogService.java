package com.kbao.kbcchatbot.maas.mcp.log.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcchatbot.maas.mcp.log.dao.McpCallLogDao;
import com.kbao.kbcchatbot.maas.mcp.log.model.McpCallLogMo;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * MCP调用日志MongoDB服务类
 */
@Service
public class McpCallLogService extends BaseMongoServiceImpl<McpCallLogMo, String, McpCallLogDao> {

    /**
     * 添加调用日志
     *
     * @param mcpCallLogMo 调用日志
     * @return 保存后的调用日志
     */
    public McpCallLogMo addCallLog(McpCallLogMo mcpCallLogMo) {
        // 生成唯一ID
        if (mcpCallLogMo.getId() == null) {
            mcpCallLogMo.setId(IdWorker.getIdStr());
        }
        if (mcpCallLogMo.getLogId() == null) {
            mcpCallLogMo.setLogId(IdWorker.getIdStr());
        }
        if (mcpCallLogMo.getCallTime() == null) {
            mcpCallLogMo.setCallTime(new Date());
        }
        if (mcpCallLogMo.getMapTime() == null) {
            mcpCallLogMo.setMapTime(new Date());
        }
        if (mcpCallLogMo.getTenantTime() == null) {
            mcpCallLogMo.setTenantTime(new Date());
        }
        return this.save(mcpCallLogMo);
    }

    /**
     * 根据服务器ID查询调用日志
     *
     * @param serverId 服务器ID
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByServerId(String serverId) {
        return this.dao.findByServerId(serverId);
    }

    /**
     * 根据工具ID查询调用日志
     *
     * @param toolId 工具ID
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByToolId(String toolId) {
        return this.dao.findByToolId(toolId);
    }

    /**
     * 根据用户ID查询调用日志
     *
     * @param userId 用户ID
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByUserId(String userId) {
        return this.dao.findByUserId(userId);
    }

    /**
     * 根据租户ID查询调用日志
     *
     * @param tenantId 租户ID
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByTenantId(String tenantId) {
        return this.dao.findByTenantId(tenantId);
    }

    /**
     * 根据调用状态查询调用日志
     *
     * @param callStatus 调用状态
     * @return 调用日志列表
     */
    public List<McpCallLogMo> findByCallStatus(Integer callStatus) {
        return this.dao.findByCallStatus(callStatus);
    }

    /**
     * 根据条件查询所有调用日志
     *
     * @param mcpCallLog 查询条件
     * @return 调用日志列表
     */
    public List<McpCallLogMo> selectAll(McpCallLogMo mcpCallLog) {
        return this.dao.findAll(mcpCallLog);
    }

    /**
     * 分页查询调用日志
     *
     * @param reqVO 分页请求对象
     * @return 分页结果
     */
    public PageInfo<McpCallLogMo> page(RequestObjectPage<McpCallLogMo> reqVO) {
        Query query = this.dao.getQuery(reqVO.getParam());
        Pagination<McpCallLogMo> pagination = new Pagination();
        pagination.setPageNum(reqVO.getPageNum());
        pagination.setPageSize(reqVO.getPageNum());
        return page(query, pagination);
    }
}