package com.kbao.kbcchatbot.maas.channel.channelrobotprompt.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.bean.ChannelRobotPromptQueryVo;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.entity.ChannelRobotPrompt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @Description maas机器人提示词表Dao类
* @Date 2024-12-17
*/
public interface ChannelRobotPromptMapper extends BaseMapper<ChannelRobotPrompt, Integer>{
    int isExistChannelRobotPrompt(@Param("channelRobotId") Integer channelRobotId, @Param("promptType") String promptType, @Param("id") Integer id);
    List<ChannelRobotPrompt> getChannelRobotPromptList(ChannelRobotPromptQueryVo channelRobotPromptQueryVo);

    int delByChannelRobotId(@Param("channelRobotId") Integer channelRobotId);
}