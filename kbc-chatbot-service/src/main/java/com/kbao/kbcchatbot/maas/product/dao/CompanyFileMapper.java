package com.kbao.kbcchatbot.maas.product.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.product.bean.CompanyListResVo;
import com.kbao.kbcchatbot.maas.product.entity.CompanyFile;

import java.util.List;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2025-04-24
*/
public interface CompanyFileMapper extends BaseMapper<CompanyFile, Integer>{

    List<CompanyListResVo> getCompanyList(CompanyFile companyFile);
}