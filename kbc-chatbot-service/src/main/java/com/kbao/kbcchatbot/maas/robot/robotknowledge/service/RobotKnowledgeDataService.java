package com.kbao.kbcchatbot.maas.robot.robotknowledge.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.dao.RobotKnowledgeDataMapper;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.bean.RobotKnowledgeDataPageReqVo;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.bean.RobotKnowledgeDataPageRespVo;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.entity.RobotKnowledgeData;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @Description maas知识数据Service类
* @Date 2024-12-12
*/
@Service
public class RobotKnowledgeDataService extends BaseSQLServiceImpl<RobotKnowledgeData, Integer, RobotKnowledgeDataMapper> {

    /**
     * 根据分页请求获取知识数据分页信息
     *
     * @param reqVo 分页请求对象，包含分页参数和查询参数
     * @return 包含知识数据分页信息的 PageInfo 对象
     */
    public PageInfo<RobotKnowledgeDataPageRespVo> getKnowledgeDataPage(PageRequest<RobotKnowledgeDataPageReqVo> reqVo){
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        List<RobotKnowledgeDataPageRespVo> knowledgeDataList = this.mapper.getKnowledgeDataList(reqVo.getParam());
        return new PageInfo<>(knowledgeDataList);
    }

    public PageInfo<RobotKnowledgeDataPageRespVo> getChapterDataList(PageRequest<RobotKnowledgeDataPageReqVo> reqVo){
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        List<RobotKnowledgeDataPageRespVo> knowledgeDataList = this.mapper.getChapterDataList(reqVo.getParam());
        return new PageInfo<>(knowledgeDataList);
    }

    public boolean hasUnfinishedKnowledgeData(String robotCode, String knowledgeId){
        return this.mapper.hasUnfinishedKnowledgeData(robotCode, knowledgeId) > 0;
    }
}
