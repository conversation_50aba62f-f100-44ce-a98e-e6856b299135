package com.kbao.kbcchatbot.maas.product.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.product.bean.ProductListResVo;
import com.kbao.kbcchatbot.maas.product.entity.ProductData;

import java.util.List;

/**
* <AUTHOR> jie
* @Description Dao类
* @Date 2025-04-24
*/
public interface ProductDataMapper extends BaseMapper<ProductData, Integer>{

    List<ProductListResVo> getProductList(ProductData reqVo);
}