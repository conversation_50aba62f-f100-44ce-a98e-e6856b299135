package com.kbao.kbcchatbot.maas.product.service;

import com.kbao.kbcchatbot.maas.product.dao.ProductSyncLogMapper;
import com.kbao.kbcchatbot.maas.product.entity.ProductSyncLog;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;

/**
* <AUTHOR> jie
* @Description Service类
* @Date 2024-12-23
*/
@Service
public class ProductSyncLogService extends BaseSQLServiceImpl<ProductSyncLog, Integer, ProductSyncLogMapper> {

    public ProductSyncLog getLastSyncLog() {
        return mapper.getLastSyncLog();
    }
}
