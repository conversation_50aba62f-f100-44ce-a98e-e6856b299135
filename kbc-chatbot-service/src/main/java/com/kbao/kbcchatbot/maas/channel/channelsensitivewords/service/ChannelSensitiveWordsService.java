package com.kbao.kbcchatbot.maas.channel.channelsensitivewords.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcchatbot.maas.channel.channelsensitivewords.bean.ChannelSensitiveWordsSaveVO;
import com.kbao.kbcchatbot.maas.channel.channelsensitivewords.dao.ChannelSensitiveWordsMapper;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.tool.util.DateUtils;
import com.kbao.kbcchatbot.maas.channel.channelsensitivewords.entity.ChannelSensitiveWords;

/**
* <AUTHOR>
* @Description maas渠道敏感词配置Service类
* @Date 2024-12-25
*/
@Service
public class ChannelSensitiveWordsService extends BaseSQLServiceImpl<ChannelSensitiveWords, Long, ChannelSensitiveWordsMapper> {

    /**
     * @Description: 保存敏感词
     * @Param: [param]
     * @return: void
     * @Author: xiaojiayao
     * @Date: 2024年12月25日09:42:48
     */
    public void save(ChannelSensitiveWordsSaveVO param) {
        int count = this.mapper.selectCountByWords(param.getWords(), param.getChannelCode());
        if (count > 0){
            throw new BusinessException("当前敏感词已存在！");
        }
        ChannelSensitiveWords channelSensitiveWords = new ChannelSensitiveWords();
        channelSensitiveWords.setChannelCode(param.getChannelCode());
        channelSensitiveWords.setChannelId(param.getChannelId());
        channelSensitiveWords.setWords(param.getWords());
        channelSensitiveWords.setCreateId(SysLoginUtils.getUserId());
        channelSensitiveWords.setCreateTime(DateUtils.getCurrentDate());
        channelSensitiveWords.setUpdateId(SysLoginUtils.getUserId());
        channelSensitiveWords.setUpdateTime(DateUtils.getCurrentDate());
        this.mapper.insert(channelSensitiveWords);
    }
}
