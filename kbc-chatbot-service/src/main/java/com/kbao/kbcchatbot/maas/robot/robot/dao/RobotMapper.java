package com.kbao.kbcchatbot.maas.robot.robot.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.robot.robot.bean.RobotPageReqVO;
import com.kbao.kbcchatbot.maas.robot.robot.bean.RobotSimpleVO;
import com.kbao.kbcchatbot.maas.robot.robot.entity.Robot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @Description maas机器人Dao类
* @Date 2024-12-11
*/
public interface RobotMapper extends BaseMapper<Robot, Integer>{
    /**
     * 根据编码查询机器人
     */
    Robot selectByCode(@Param("robotCode") String robotCode);

    List<Robot> getRobotList(RobotPageReqVO robotPageReqVO);


    List<RobotSimpleVO> getSimpleRobotList(RobotPageReqVO robotPageReqVO);

    RobotSimpleVO getRobotBySessionId(@Param("sessionId") String sessionId);
}