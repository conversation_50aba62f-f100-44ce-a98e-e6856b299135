package com.kbao.kbcchatbot.maas.chatsession.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.export.ExcelUtils;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscApiContext;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.common.redis.service.RedisLockService;
import com.kbao.kbcchatbot.constants.CacheConstant;
import com.kbao.kbcchatbot.discard.robot.bean.ChatSessionResolveStatusEnum;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotReceptionExportVO;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotReceptionPageReqVO;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotReceptionRespVO;
import com.kbao.kbcchatbot.externalapi.enums.LargeAnswerSourceEnum;
import com.kbao.kbcchatbot.externalapi.model.LargeModelMsgVO;
import com.kbao.kbcchatbot.maas.chatsession.dao.ChatSessionMapper;
import com.kbao.kbcchatbot.maas.chatsession.entity.ChatSession;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerTypeEnum;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.kbcchatbot.maas.robot.robot.entity.Robot;
import com.kbao.kbcchatbot.maas.robot.robot.service.RobotService;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
* <AUTHOR>
* @Description 聊天会话Service类
* @Date 2023-06-06
*/
@Service
@Slf4j
public class ChatSessionService extends BaseSQLServiceImpl<ChatSession, Long, ChatSessionMapper> {

/*    @Autowired
    private ChannelService channelService;*/

    @Autowired
    @Lazy
    private ChatSessionRecordService chatSessionRecordService;

    @Autowired
    private RobotService robotService;

    @Autowired
    private RedisLockService redisLockService;

    public PageInfo<RobotReceptionRespVO> pageInfo(RequestObjectPage<RobotReceptionPageReqVO> pageRequest) {
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<RobotReceptionRespVO> robotReceptionRespVO = getRobotReceptionRespVOList(pageRequest.getParam());
        return new PageInfo<>(robotReceptionRespVO);
    }

    public void export(RobotReceptionPageReqVO reqVO, HttpServletResponse response) throws Exception {
        List<RobotReceptionRespVO> robotReceptionRespVO = getRobotReceptionRespVOList(reqVO);

        //转换成导出对象
        List<RobotReceptionExportVO> exportList = new ArrayList<>();
        robotReceptionRespVO.forEach(item -> {
            RobotReceptionExportVO exportVO = new RobotReceptionExportVO();
            BeanUtils.copyProperties(item, exportVO);
            exportList.add(exportVO);
        });

        //导出
        ExcelUtils<RobotReceptionExportVO> exportsExcelUtils = new ExcelUtils<>(RobotReceptionExportVO.class);
        exportsExcelUtils.writeExcel(exportList, "接待概况_" + DateUtils.thisDate(), response);
    }

    private List<RobotReceptionRespVO> getRobotReceptionRespVOList(RobotReceptionPageReqVO reqVO) {
        Map<String, Object> paramMap = MapUtils.objectToMap(reqVO);
        paramMap.put("tenantId", BscApiContext.TenantId.get());
        List<RobotReceptionRespVO> robotReceptionRespVO = this.mapper.selectDataBoard(paramMap);
        robotReceptionRespVO.forEach(x->{
            x.setAverageTotalRound(BigDecimal.valueOf(x.getTotalRound()).divide(BigDecimal.valueOf(x.getReceptionNum()),2, RoundingMode.HALF_UP));
            x.setResolvePercent(x.getTotalRound()== 0 ?0:x.getResolveNum()*100/x.getTotalRound());
            x.setTransformPercent(x.getReceptionNum()== 0 ?0:x.getTransformCount()*100/x.getReceptionNum());
            x.setUpVoteRoundPercent(x.getTotalRound()== 0 ?0:x.getUpVoteRoundCount()*100/x.getTotalRound());
            x.setDownVoteRoundPercent(x.getTotalRound()== 0 ?0:x.getDownVoteRoundCount()*100/x.getTotalRound());
        });
        return robotReceptionRespVO;
    }

    /**
     * @Description: 创建聊天会话
     * @Param: [msgVO]
     * @Author: husw
     * @Date: 2024/9/12 9:46
     */
    public void createSession(LargeModelMsgVO msgVO) {
        String key = redisLockService.getBizKey(CacheConstant.CHAT_SESSION, msgVO.getSessionId());
        redisLockService.lock(key,()->{
            //查询当天会话记录
            ChatSession session = this.mapper.selectByUserIdCurrentDay(msgVO.getSessionId(),msgVO.getChannelCode(), msgVO.getRobotCode());
            if (EmptyUtils.isNotEmpty(session)){
                session.setUpdateStatus(YesNoEnum.NO.getValue());
                this.mapper.updateByPrimaryKeySelective(session);
                return;
            }
            LargeModelMsgVO.External external = msgVO.getExternal();
//        Channel channel = channelService.getMapper().selectByPrimaryKey(external.getChannelId());
//        MaasRobot maasRobot = maasRobotService.getMapper().selectByCode(msgVO.getRobotCode());
            ChatSession chatSession = new ChatSession();
            chatSession.setSessionId(msgVO.getSessionId());
            chatSession.setChannelId(external.getChannelId());
            chatSession.setChannelCode(msgVO.getChannelCode());
//        chatSession.setRobotId(Long.valueOf(maasRobot.getId()));
            chatSession.setRobotCode(msgVO.getRobotCode());
            chatSession.setUserId(msgVO.getUserId());
            chatSession.setUserName(msgVO.getUsername());
            chatSession.setResolveStatus(ChatSessionResolveStatusEnum.UNRESOLVED.getKey());
            chatSession.setStartTime(DateUtils.thisDateTime());
            chatSession.setEndTime(DateUtils.thisDateTime());
            chatSession.setUpdateStatus(YesNoEnum.NO.getValue());
            chatSession.setTenantId(external.getTenantId());
            insertSelective(chatSession);
        });
    }

    public void updateStatus(String sessionId, String channelCode, String robotCode) {
        this.mapper.updateStatusCurrentDay(sessionId, channelCode, robotCode);
    }
    /**
    * @Description: 修复会话数据
    * @Param: [date]
    * @return: void
    * @Author: husw
    * @Date: 2024/10/12 14:52
    */
//    public void fixChatSession(String date) {
//        List<ChatSessionRecord> recordByDate = chatSessionRecordService.findRecordByDate(date);
//        Map<String, List<ChatSessionRecord>> sessionRecordMap = recordByDate.stream().distinct().collect(Collectors.groupingBy(ChatSessionRecord::getSessionId));
//        if (EmptyUtils.isNotEmpty(sessionRecordMap)){
//            sessionRecordMap.forEach((sessionId, records) -> {
//                ChatSession chatSessionOld = this.mapper.selectByPrimaryKey(sessionId);
//                if (EmptyUtils.isEmpty(chatSessionOld)){
//                    ChatSessionRecord chatSessionRecord = records.get(0);
//                    ChatSession chatSession = new ChatSession();
//                    chatSession.setSessionId(chatSessionRecord.getSessionId());
//                    chatSession.setChannelId(chatSessionRecord.getChannelId());
//                    chatSession.setChannelCode(chatSessionRecord.getChannelCode());
//                    chatSession.setRobotCode(chatSessionRecord.getRobotCode());
//                    chatSession.setUserId(chatSessionRecord.getUserId());
//                    chatSession.setUserName(chatSessionRecord.getUserName());
//                    chatSession.setResolveStatus(ChatSessionResolveStatusEnum.UNRESOLVED.getKey());
//                    chatSession.setStartTime(DateUtils.dateTime2Str(DateUtils.str2DateTime(chatSessionRecord.getCreateTime()),null));
//                    chatSession.setTenantId(EmptyUtils.isEmpty(chatSessionRecord.getTenantId())? "T0001" : chatSessionRecord.getTenantId());
//                    chatSession.setEndTime(DateUtils.date2Str(DateUtils.str2Date(chatSessionRecord.getCreateTime()),null) + " 23:59:59");
//                    insertSelective(chatSession);
//                }
//            });
//        }
//    }

    @Transactional(rollbackFor = Exception.class)
    public int insert(ChatSession chatSession) {
        String currentUserId = BscUserUtils.getUserId();
        String nowStr = DateUtils.thisDateTime();
        return this.mapper.insert(chatSession);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(ChatSession chatSession) {
        return this.mapper.updateByPrimaryKeySelective(chatSession);
    }

    /**
     * 完成会话
     */
    @Transactional(rollbackFor = Exception.class)
    public void finishSession(String sessionId) {

    }

    /**
     * @Description: 更新会话统计数据
     * @Param: [chatSession]
     * @return: void
     * @Author: husw
     * @Date: 2023/6/28 17:43
     */
    public void updateChatSession(ChatSession chatSession,String date){
        //对话轮次
        long totalRound = chatSessionRecordService.findTotalRound(chatSession.getSessionId(), chatSession.getChannelCode(),
                chatSession.getRobotCode(),chatSession.getStartTime());
        chatSession.setTotalRound((int)totalRound);
        //知识对话轮次
        long knowledgeRound = chatSessionRecordService.findRoundByAnswerSource(chatSession.getSessionId(),
                LargeAnswerSourceEnum.KNOWLEDGE.getKey(), chatSession.getChannelCode(), chatSession.getRobotCode(),chatSession.getStartTime());
        chatSession.setKnowledgeRound((int)knowledgeRound);
        //闲聊对话轮次
        long chatRound = chatSessionRecordService.findRoundByAnswerSource(chatSession.getSessionId(),
                LargeAnswerSourceEnum.CHITCHAT.getKey(), chatSession.getChannelCode(), chatSession.getRobotCode(),chatSession.getStartTime());
        chatSession.setChatRound((int)chatRound);
        //人机对练轮次
        long manMachineRound = chatSessionRecordService.findRoundByAnswerSource(chatSession.getSessionId(),
                LargeAnswerSourceEnum.MAN_MACHINE.getKey(), chatSession.getChannelCode(), chatSession.getRobotCode(),chatSession.getStartTime());
        chatSession.setManMachineRound((int)manMachineRound);
        //产品解读轮次
        List<String> sources = Arrays.asList(LargeAnswerSourceEnum.PRODUCT_LIST.getKey(),LargeAnswerSourceEnum.PRODUCT_DATA.getKey(),
                LargeAnswerSourceEnum.PRODUCT_RECOMMEND.getKey(),LargeAnswerSourceEnum.PRODUCT_SELECT.getKey());
        long productAnalyzeRound = chatSessionRecordService.findRoundByAnswerSource(chatSession.getSessionId(), sources,
                chatSession.getChannelCode(), chatSession.getRobotCode(),chatSession.getStartTime());
        chatSession.setProductAnalyzeRound((int)productAnalyzeRound);
        //点赞对话轮次
        long upVoteRound = chatSessionRecordService.findVoteRound(chatSession.getSessionId(),
                YesNoEnum.YES.getValue().toString(), chatSession.getChannelCode(), chatSession.getRobotCode(),chatSession.getStartTime());
        chatSession.setUpVoteRoundCount((int)upVoteRound);
        //点踩对话轮次
        long downVoteRound = chatSessionRecordService.findVoteRound(chatSession.getSessionId(), YesNoEnum.NO.getValue().toString(),
                chatSession.getChannelCode(), chatSession.getRobotCode(),chatSession.getStartTime());
        chatSession.setDownVoteRoundCount((int)downVoteRound);
        //转人工人次，一个sessionID存在转人工则一次
        List<String> resolveStatusTypes = Collections.singletonList(ChatAnswerTypeEnum.TRANSFER_LABOR.getKey());
        long transferLaborNum = chatSessionRecordService.findRoundByAnswerType(chatSession.getSessionId(), resolveStatusTypes,
                chatSession.getChannelCode(), chatSession.getRobotCode(),chatSession.getStartTime());
        if (transferLaborNum > 0){
            chatSession.setTransformRound(1);
        }
        //统计对话解决量
        chatSession.setResolveCount((int)(totalRound - transferLaborNum - downVoteRound));
        if (EmptyUtils.isNotEmpty(date)){
            chatSession.setEndTime(chatSession.getStartTime().substring(0,10) + " 23:59:59");
        }else {
            chatSession.setEndTime(DateUtils.thisDateTime());
        }
        chatSession.setUpdateStatus(YesNoEnum.YES.getValue());
        //更新数据
        this.mapper.updateByPrimaryKeySelective(chatSession);
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateById(LargeModelMsgVO largeModelMsgVO) {
        ChatSession chatSession = new ChatSession();
        chatSession.setSessionId(largeModelMsgVO.getSessionId());
        Robot robot = robotService.getMapper().selectByCode(largeModelMsgVO.getRobotCode());
        if(EmptyUtils.isNotEmpty(robot)){
            chatSession.setRobotId(Long.valueOf(robot.getId()));
            chatSession.setRobotCode(robot.getRobotCode());
            this.mapper.updateByPrimaryKeySelective(chatSession);
        }
    }

}
