package com.kbao.kbcchatbot.maas.chatsession.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcchatbot.discard.knowledgepackage.service.KnowledgePackageQAApiService;
import com.kbao.kbcchatbot.discard.robot.bean.ChatSessionResolveStatusEnum;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.ConfigCacheVO;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.RobotInitConfigRespVO;
import com.kbao.kbcchatbot.elasticsearch.index.service.IndexService;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.externalapi.model.MaasUpdateChatReq;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelUpdateVO;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.service.ChannelBasicConfigService;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.service.ChannelCommonCardService;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.entity.ChannelCommonPhrase;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.service.ChannelCommonPhraseService;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.entity.ChannelGuessQuestion;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.service.ChannelGuessQuestionService;
import com.kbao.kbcchatbot.maas.chatconfig.service.ChatConfigApiService;
import com.kbao.kbcchatbot.maas.chatsession.bean.ChatSessionRecordPageReqVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.*;
import com.kbao.kbcchatbot.maas.chatsession.dao.ChatSessionMapper;
import com.kbao.kbcchatbot.maas.chatsession.entity.ChatSession;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerSourceEnum;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerTypeEnum;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatContentTypeEnum;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatSessionRecordSubjectEnum;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.KnowledgePackageCloudApiService;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.RobotKnowledgeConfigService;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeDetailRespVO;
import com.kbao.kbcchatbot.rasa3x.service.RasaApiService;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.MapUtils;
import com.kbao.tool.util.StringUtil;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Service
public class ChatSessionApiService extends BaseSQLServiceImpl<ChatSession, Long, ChatSessionMapper> {

    @Autowired
    private ChannelCommonCardService channelCommonCardService;

    @Autowired
    private ChannelGuessQuestionService channelGuessQuestionService;

    @Autowired
    private ChannelCommonPhraseService channelCommonPhraseService;

    @Autowired
    private KnowledgePackageQAApiService knowledgePackageQAApiService;

    @Autowired
    private RobotKnowledgeConfigService robotKnowledgeConfigService;

    @Autowired
    private KnowledgePackageCloudApiService knowledgePackageCloudApiService;

    @Autowired
    private ChatSessionRecordService chatSessionRecordService;

    @Autowired
    private KnowledgeQASearchService chatSessionQASearchService;

    @Autowired
    private KnowledgeQAClearSearchService knowledgeQAClearSearchService;

    @Autowired
    private RasaApiService rasaApiService;

    @Autowired
    private KnowledgeCloudClearSearchService knowledgeCloudClearSearchService;

    @Autowired
    private IndexService indexService;

    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private ChatConfigApiService chatConfigApiService;

    @Autowired
    private ChannelBasicConfigService channelBasicConfigService;

/*    @Autowired
    private RobotService robotService;*/

    @Autowired
    private ChannelService channelService;

    @Autowired
    private MaasHttpService maasHttpService;

    /**
     * 创建聊天会话
     *
     * @param configCacheVO
     * @return
     */
    public ChatSession createSession(ConfigCacheVO configCacheVO) {
        ChatSession chatSession = new ChatSession();
        chatSession.setSessionId(IdWorker.get32UUID());
        chatSession.setChannelId(configCacheVO.getChannelId());
        chatSession.setRobotId(configCacheVO.getRobotId());
        chatSession.setRobertVersion(configCacheVO.getVersion());
        chatSession.setUserId(configCacheVO.getUserId());
        chatSession.setUserName(configCacheVO.getUserName());
        chatSession.setResolveStatus(ChatSessionResolveStatusEnum.UNRESOLVED.getKey());
        chatSession.setStartTime(DateUtils.thisDateTime());
        chatSession.setTenantId(configCacheVO.getTenantId());
        insertSelective(chatSession);
        return chatSession;
    }

    /**
     * 查询回复
     *
     * @param req
     * @return
     */
/*    public ChatQueryAnswerRespVO queryAnswer(ChatQueryAnswerReqVO req,boolean isAsync) {
        Robot robot = robotService.getMapper().selectByPrimaryKey(ChatUserUtil.getUser().getRobotId());
        if (RobotReleaseTypeEnum.RASA.getCode().equals(robot.getReleaseType())){
            return rasaApiService.queryAnswer(req,isAsync);
        } else if (RobotReleaseTypeEnum.BIG_MODEL.getCode().equals(robot.getReleaseType())) {
            return knowledgeModelSearchService.queryAnswer(req,isAsync);
        }else {
            return chatSessionQASearchService.queryAnswer(req,isAsync);
        }
    }*/

    /**
     * 查询回复(socket)
     *
     * @param req
     * @return
     */
//    public void queryAnswerStream(ChatQueryAnswerReqVO req, boolean isAsync) {
//        Robot robot = robotService.getMapper().selectByPrimaryKey(ChatUserUtil.getUser().getRobotId());
//        if (RobotReleaseTypeEnum.RASA.getCode().equals(robot.getReleaseType())){
//            rasaApiService.queryAnswer(req);
//        } else if (RobotReleaseTypeEnum.BIG_MODEL.getCode().equals(robot.getReleaseType())) {
//            knowledgeModelSearchService.queryAnswer(req);
//        }else {
//            chatSessionQASearchService.queryAnswer(req);
//        }
//    }
    /**
     * 问题澄清回复
     *
     * @param req
     * @return
     */
    public ChatQueryAnswerRespVO questionClearAnswer(ChatQueryAnswerReqVO req) {
        if (ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_QA.getKey().equals(req.getQaSource())) {
            return knowledgeQAClearSearchService.queryAnswer(req,false);
        } else if (ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_CLOUD.getKey().equals(req.getQaSource())) {
            return knowledgeCloudClearSearchService.queryAnswer(req,false);
        } else {
            throw new BusinessException("系统异常，来源【" + req.getQaSource() + "】未对接，请转人工处理");
        }
    }

    /**
     * 问题联想
     *
     * @param req
     * @return
     */
    /*public List<ChatQuestionAssociateRespVO> questionAssociate(ChatQuestionAssociateReqVO req) {
        long robotId = ChatUserUtil.getUser().getRobotId();

        Robot robot = robotService.getMapper().selectByPrimaryKey(robotId);
        if(RobotReleaseTypeEnum.BIG_MODEL.getCode().equals(robot.getReleaseType())){
            return null;
        }
        if (EmptyUtils.isEmpty(req.getQuery())) {
            return null;
        }
        //问题联想最大条数
        Integer maxCount = ChatUserUtil.getUser().getChatSessionCache().getAssociateMaxCount();
        if (maxCount == null) {
            maxCount = 5;
        }

        //查询可见目录
        //List<String> visibleSecondDirectIds = kbcKmService.getVisibleSecondDirectIds();
        List<String> visibleSecondDirectIds = null;
        //检索云知识库
        List<ChatSearchResultDTO> packageCloudSearchResultDTOList = knowledgePackageCloudApiService.queryAnswer(req.getQuery(), robotId, ElasticsearchConstants.MATERIALS_KNOWLEDGE_PACKAGE_CLOUD_INDEX_ID, maxCount, Operator.AND.name(), visibleSecondDirectIds);
        //检索知识包
        List<ChatSearchResultDTO> packageQASearchResultDTOList = knowledgePackageQAApiService.queryAnswer(req.getQuery(), robotId, ElasticsearchConstants.MATERIALS_KNOWLEDGE_PACKAGE_QA_INDEX_ID, maxCount, Operator.AND.name(), visibleSecondDirectIds);

        // 根据KnowledgeId去重
        packageCloudSearchResultDTOList = packageCloudSearchResultDTOList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ChatSearchResultDTO::getKnowledgeId))), ArrayList::new));

        packageQASearchResultDTOList = packageQASearchResultDTOList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ChatSearchResultDTO::getKnowledgeId))), ArrayList::new));

        //合并、过滤最大条数后返回
        List<ChatSearchResultDTO> resultDTOList = new ArrayList<>(packageCloudSearchResultDTOList.size() + packageQASearchResultDTOList.size());
        resultDTOList.addAll(packageCloudSearchResultDTOList);
        resultDTOList.addAll(packageQASearchResultDTOList);
//        if(resultDTOList.size() > maxCount) {
//            resultDTOList = resultDTOList.subList(0, maxCount - 1);
//        }
        //获取分词
        List<AnalyzeResponse.AnalyzeToken> questionTokenList = indexService.getIkAnalysisList(req.getQuery(), ElasticsearchConstants.ANALYZER_IK_MAX_WORD_WITH_SYNONYM);
        questionTokenList = chatSessionQASearchService.filterTokenList(questionTokenList);

        //封装联想返回对象集合
        List<ChatQuestionAssociateRespVO> respVOList = new ArrayList<>(resultDTOList.size());
        for (ChatSearchResultDTO resultDTO : resultDTOList) {
            //计算匹配度
            ChatSessionMatchQuestionDTO matchQuestionDTO = chatSessionQASearchService.getMatchPercent(questionTokenList, resultDTO);

            ChatQuestionAssociateRespVO respVO = new ChatQuestionAssociateRespVO();
            respVO.setQuestion(matchQuestionDTO.getQuestion());
//            if(CollectionUtils.isEmpty(resultDTO.getSimilarQuestions())) {
//                respVO.setQuestion(resultDTO.getQuestion());
//            }else {
//                respVO.setQuestion(resultDTO.getSimilarQuestions().get(0));
//            }
            respVO.setMatchPercent(matchQuestionDTO.getMatchPercent());
            respVO.setSource(resultDTO.getQaSource());
            respVO.setBizId(resultDTO.getQaId());
            respVOList.add(respVO);
        }
        respVOList = respVOList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ChatQuestionAssociateRespVO::getQuestion))), ArrayList::new));
        List<ChatQuestionAssociateRespVO> respSortVOList = respVOList.stream().sorted(Comparator.comparing(ChatQuestionAssociateRespVO::getMatchPercent).reversed()).collect(Collectors.toList());
        if (respSortVOList.size() > maxCount) {
            respSortVOList = respSortVOList.subList(0, maxCount - 1);
        }
        return respSortVOList;
    }*/

    /**
     * 查询常用卡片回复
     *
     * @param req
     * @return
     */
    public ChatCommonCardAnswerRespVO commonCardAnswer(ChatCommonCardAnswerReqVO req) {
        ChatCommonCardAnswerRespVO respVO = new ChatCommonCardAnswerRespVO();
        ChannelCommonCard commonCard = channelCommonCardService.selectByPrimaryKey(req.getCommonCardId());
        //关联知识库
        if (ChatContentTypeEnum.REL.getKey().equals(commonCard.getType())) {
            RobotKnowledgeDetailRespVO detailRespVO = robotKnowledgeConfigService.getDetail(ChatUserUtil.getUser().getRobotId(), new String(commonCard.getContent(), StandardCharsets.UTF_8));
            if (detailRespVO != null) {
                respVO.setAnswer(detailRespVO.getKnowledgeId());
            } else {
                throw new BusinessException("未查询到常用卡片");
            }

        } else {
            respVO.setAnswer(new String(commonCard.getContent(), StandardCharsets.UTF_8));
        }
        respVO.setType(commonCard.getType());
        return respVO;
    }

    /**
     * 查询常用短语回复
     *
     * @param req
     * @return
     */
    public ChatCommonPhraseAnswerRespVO commonPhraseAnswer(ChatCommonPhraseAnswerReqVO req) {
        ChatCommonPhraseAnswerRespVO respVO = new ChatCommonPhraseAnswerRespVO();
        ChannelCommonPhrase commonPhrase = channelCommonPhraseService.selectByPrimaryKey(req.getCommonPhraseId());
        //关联知识库
        if (ChatContentTypeEnum.REL.getKey().equals(commonPhrase.getType())) {
            RobotKnowledgeDetailRespVO detailRespVO = robotKnowledgeConfigService.getDetail(ChatUserUtil.getUser().getRobotId(), new String(commonPhrase.getContent(), StandardCharsets.UTF_8));
            if (detailRespVO != null) {
                respVO.setAnswer(detailRespVO.getKnowledgeId());
            } else {
                throw new BusinessException("未查询到常用短语");
            }
        } else {
            respVO.setAnswer(new String(commonPhrase.getContent(), StandardCharsets.UTF_8));
        }
        respVO.setType(commonPhrase.getType());
        return respVO;
    }

    /**
     * 查询猜你想问回复
     *
     * @param req
     * @return
     */
    public ChatGuessQuestionAnswerRespVO guessQuestionAnswer(ChatGuessQuestionAnswerReqVO req) {
        ChatGuessQuestionAnswerRespVO respVO = new ChatGuessQuestionAnswerRespVO();
        ChannelGuessQuestion guessQuestion = channelGuessQuestionService.selectByPrimaryKey(req.getGuessQuestionId());
        if (EmptyUtils.isEmpty(guessQuestion)) {
            return null;
        }
        //关联知识库
        if (ChatContentTypeEnum.REL.getKey().equals(guessQuestion.getType())) {
            RobotKnowledgeDetailRespVO detailRespVO = robotKnowledgeConfigService.getDetail(ChatUserUtil.getUser().getRobotId(), new String(guessQuestion.getContent(), StandardCharsets.UTF_8));
            if (detailRespVO != null) {
                respVO.setAnswer(detailRespVO.getKnowledgeId());
            } else {
                throw new BusinessException("未查询到猜你想问");
            }
        } else {
            respVO.setAnswer(new String(guessQuestion.getContent(), StandardCharsets.UTF_8));
        }
        respVO.setType(guessQuestion.getType());
        return respVO;
    }

    /**
     * 点赞/点踩
     *
     * @param req
     * @return
     */
    public void vote(ChatKnowledgeAnswerVoteReqVO req) {
        ChatSessionRecord record = chatSessionRecordService.findById(req.getRecordId());
        if(EmptyUtils.isNotEmpty(record)){
            record.setVoteResult(req.getResult());
            record.setVoteRemark(req.getRemark());
            chatSessionRecordService.update(record);
        }else {
            //如果记录不存在就新增一条记录（后续在大模型推送消息的时候根据id进行更新）
            ChatSessionRecord chatRecord = ChatSessionRecord.builder().id(req.getRecordId())
                    .voteResult(req.getResult()).voteRemark(req.getRemark()).build();
            chatSessionRecordService.save(chatRecord);
        }
        //同步至maas
        MaasUpdateChatReq chatReq = new MaasUpdateChatReq();
        chatReq.setRecordId(req.getRecordId());
        chatReq.setVoteResult(req.getResult());
        chatReq.setVoteRemark(req.getRemark());
        maasHttpService.updateChatRecord(chatReq);
        //更新统计状态
        chatSessionService.updateStatus(record.getSessionId(),record.getChannelCode(),record.getRobotCode());
    }

    /**
     * 分页查询聊天记录
     *
     * @param req
     * @return
     */
    public PageInfo<ChatSessionRecord> pageRecord(RequestObjectPage<ChatSessionRecordPageReqVO> req) {
        String channelCode = ChatUserUtil.getUser().getChannelCode();
        String deviceType = ChatUserUtil.getUser().getDeviceType();
        ChannelUpdateVO channel = channelService.getChannel(channelCode);
        if ("1".equals(channel.getChannelType())) {
            return new PageInfo<>();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(ChatUserUtil.getUserId()));
        query.addCriteria(Criteria.where("channelCode").is(channelCode));
        if (StringUtil.isNotEmpty(req.getParam().getCreateTimeEnd())) {
            query.addCriteria(Criteria.where("createTime").lt(req.getParam().getCreateTimeEnd()));
        }
        if (StringUtil.isNotEmpty(deviceType)) {
            if ("0".equals(deviceType)) {
                query.addCriteria(new Criteria().orOperator(
                                Criteria.where("deviceType").is(deviceType),
                                Criteria.where("deviceType").exists(false)
                        )
                );
            } else {
                query.addCriteria(Criteria.where("deviceType").is(deviceType));
            }
        }
        if (StringUtil.isNotEmpty(req.getParam().getProductId())) {
            query.addCriteria(Criteria.where("customParam.productId").is(req.getParam().getProductId()));
        }
        Pagination<ChatSessionRecord> pagination = new Pagination<>(req.getPageNum(), req.getPageSize(), "createTime desc");
        PageInfo<ChatSessionRecord> page = chatSessionRecordService.page(query, pagination);
        initChatSession(page,req.getParam().getSourceType(), channel);
        return page;
    }

    /**
     * 开启新对话
     */
    public String cleanSession() {
        String userId = ChatUserUtil.getUserId();
        String channelCode = ChatUserUtil.getUser().getChannelCode();
        return maasHttpService.cleanSession(userId,channelCode);
    }

    /**
     * 根据开始时间和结束时间查询聊天记录
     */
    public List<ChatSessionRecord> listByStartTimeAndEndTime(ChatSessionRecordPageReqVO chatSessionRecordPageReqVO) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(ChatUserUtil.getUserId()));
        query.addCriteria(Criteria.where("channelId").is(ChatUserUtil.getUser().getChannelId()));
        query.addCriteria(Criteria.where("createTime").gte(chatSessionRecordPageReqVO.getStartTime()).lte(chatSessionRecordPageReqVO.getCreateTimeEnd()));
        query.with(org.springframework.data.domain.Sort.by(Sort.Direction.DESC, "createTime"));
        return chatSessionRecordService.find(query);
    }

    /**
     * 根据时间查询大于等于时间的聊天记录
     *
     * @param chatSessionRecordPageReqVO
     */
    public List<ChatSessionRecord> listByStartTime(ChatSessionRecordPageReqVO chatSessionRecordPageReqVO) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(ChatUserUtil.getUserId()));
        query.addCriteria(Criteria.where("channelId").is(ChatUserUtil.getUser().getChannelId()));
        query.addCriteria(Criteria.where("answerType").is(ChatAnswerTypeEnum.CUSTOMER_SERVICE_ANSWER.getKey()));
        query.addCriteria(Criteria.where("createTime").gte(chatSessionRecordPageReqVO.getStartTime()));
        query.with(org.springframework.data.domain.Sort.by(Sort.Direction.DESC, "createTime"));
        return chatSessionRecordService.find(query);
    }

    public void copyRecord(String recordId) {
        chatSessionRecordService.update(ChatSessionRecord.builder().id(recordId).isCopy("1").build());
        //将数据推送给maas
        MaasUpdateChatReq req = new MaasUpdateChatReq();
        req.setRecordId(recordId);
        req.setIsCopy("1");
        maasHttpService.updateChatRecord(req);
    }

    @SneakyThrows
    public void saveChatSessionRecord(ChatQueryAnswerReqVO req) {
        chatConfigApiService.saveRobotRecord(ChatUserUtil.getUser().getChannelId(), ChatUserUtil.getUser().getChannelCode(), req.getQuestion(), ChatAnswerTypeEnum.CUSTOMER_ANSWER.getKey(), null,
                null, null, null, ChatUserUtil.getUser().getSessionId(), ChatUserUtil.getUserId(), ChatUserUtil.getUser().getUserName(), null, null, ChatSessionRecordSubjectEnum.USER.getKey(), req.getSceneShowType());
    }

    /**
     * 初始化聊天会话。
     *
     * @param page 包含聊天会话记录的页面信息
     */
//    public void initChatSession(PageInfo<ChatSessionRecord> page , String sourceType) {
//        Long channelId = ChatUserUtil.getUser().getChannelId();
//        RobotInitConfigRespVO respVO = new RobotInitConfigRespVO();
//        List<ChannelBasicConfig> channelBasicConfigList = channelBasicConfigService.getChannelBasicConfigListByChannelId(channelId);
//        if (EmptyUtils.isNotEmpty(channelBasicConfigList) && CollectionUtils.isEmpty(chatSessionRecordService.findByUserId(ChatUserUtil.getUserId()))) {
//            ChannelBasicConfig channelBasicConfig = channelBasicConfigList.get(0);
//            BeanUtils.copyProperties(channelBasicConfig, respVO);
//
//            // 保存欢迎语，常用卡片，猜你想问并加入数据
//            List<ChatSessionRecord> list = page.getList();
//
//            //常用卡片开启状态 0-未开启 1-开启
//            respVO.setChannelCommonCards(getChannelConfig(channelBasicConfig.getCommonCardStatus(), channelId, channelCommonCardService::getChannelCommonCardListByChannelId));
//
//            //猜你想问开启状态 0-未开启 1-开启
//            if (YesNoEnum.YES.getValue().toString().equals(channelBasicConfig.getGuessQuestionStatus())) {
//                List<ChannelGuessQuestionVO> channelGuessQuestions = channelGuessQuestionService.getChannelGuessQuestionListByChannelId(channelId, YesNoEnum.YES.getValue().toString(),sourceType);
//                if (EmptyUtils.isNotEmpty(channelGuessQuestions)) {
//                    respVO.setChannelGuessQuestions(chatConfigApiService.randomGuessQuestion(channelGuessQuestions));
//                }
//                list.add(saveRobotRecord(channelId, respVO.getChannelGuessQuestions(), ChatAnswerTypeEnum.GUESS_QUESTION.getKey()));
//            }
//
//
//            list.add(saveRobotRecord(channelId, respVO.getWelcomeReply(), ChatAnswerTypeEnum.WELCOME_ANSWER.getKey()));
//            list.add(saveRobotRecord(channelId, respVO.getChannelCommonCards(), ChatAnswerTypeEnum.COMMON_CARD.getKey()));
//            list = list.stream().sorted(Comparator.comparing(ChatSessionRecord::getCreateTime).reversed()).collect(Collectors.toList());
//            page.setList(list);
//        }
//    }
    /**
     * 初始化聊天会话。
     *
     * @param page 包含聊天会话记录的页面信息
     */
    public void initChatSession(PageInfo<ChatSessionRecord> page , String sourceType, ChannelUpdateVO channel) {
        String channelCode = ChatUserUtil.getUser().getChannelCode();
        Long channelId = ChatUserUtil.getUser().getChannelId();
        RobotInitConfigRespVO respVO = new RobotInitConfigRespVO();
        if (channel != null && CollectionUtils.isEmpty(chatSessionRecordService.findByUserId(ChatUserUtil.getUserId()))) {
            BeanUtils.copyProperties(channel, respVO);

            // 保存欢迎语，常用卡片，猜你想问并加入数据
            List<ChatSessionRecord> list = page.getList();

            //常用卡片开启状态 0-未开启 1-开启
            respVO.setChannelCommonCards(getChannelConfig(channel.getCommonCardStatus(), channelCode, channelCommonCardService::getChannelCommonCardListByChannelCode));

            //猜你想问开启状态 0-未开启 1-开启
            if (YesNoEnum.YES.getValue().toString().equals(channel.getGuessQuestionStatus())) {
                List<ChannelGuessQuestionVO> channelGuessQuestions = channelGuessQuestionService.getChannelGuessQuestionListByChannelCode(channelCode, YesNoEnum.YES.getValue().toString(),sourceType);
                if (EmptyUtils.isNotEmpty(channelGuessQuestions)) {
                    respVO.setChannelGuessQuestions(chatConfigApiService.randomGuessQuestion(channelGuessQuestions));
                }
                list.add(saveRobotRecord(channelId, channelCode, respVO.getChannelGuessQuestions(), ChatAnswerTypeEnum.GUESS_QUESTION.getKey()));
            }
            list.add(saveRobotRecord(channelId, channelCode, respVO.getWelcomeReply(), ChatAnswerTypeEnum.WELCOME_ANSWER.getKey()));
            list.add(saveRobotRecord(channelId, channelCode, respVO.getChannelCommonCards(), ChatAnswerTypeEnum.COMMON_CARD.getKey()));
            list = list.stream().sorted(Comparator.comparing(ChatSessionRecord::getCreateTime).reversed()).collect(Collectors.toList());
            page.setList(list);
        }
    }
    /**
     * 根据渠道的状态获取频道的配置。
     *
     * @param status    渠道的状态
     * @param channelId 渠道的ID
     * @param function  用于获取渠道配置的函数
     * @return 渠道的配置，如果状态值不是"Yes"，则返回null
     */
    private <T> List<T> getChannelConfig(String status, Long channelId, BiFunction<Long, String, List<T>> function) {
        // 如果状态值为"Yes"
        if (YesNoEnum.YES.getValue().toString().equals(status)) {
            // 通过函数获取渠道配置
            List<T> list = function.apply(channelId, YesNoEnum.YES.getValue().toString());
            // 如果渠道配置不为空，直接返回；否则返回null
            return EmptyUtils.isNotEmpty(list) ? list : null;
        }
        // 状态值不为"Yes"，返回null
        return null;
    }


    /**
     * 保存机器人记录。
     *
     * @param channelId 渠道的ID
     * @param content   记录的内容
     * @param type      对话记录类型
     * @return 保存的机器人记录
     */
    private ChatSessionRecord saveRobotRecord(Long channelId, String channelCode,Object content, String type) {
        return chatConfigApiService.saveRobotRecord(channelId, channelCode, content, type, null, null, null, null, ChatUserUtil.getUser().getSessionId(), ChatUserUtil.getUserId(), ChatUserUtil.getUser().getUserName(), null, null, ChatSessionRecordSubjectEnum.ROBOT.getKey(), null);
    }

    /**
     * 根据渠道的状态获取频道的配置。
     *
     * @param status    渠道的状态
     * @param channelCode 渠道的code
     * @param function  用于获取渠道配置的函数
     * @return 渠道的配置，如果状态值不是"Yes"，则返回null
     */
    private <T> List<T> getChannelConfig(String status, String channelCode, BiFunction<String, String, List<T>> function) {
        // 如果状态值为"Yes"
        if (YesNoEnum.YES.getValue().toString().equals(status)) {
            // 通过函数获取渠道配置
            List<T> list = function.apply(channelCode, YesNoEnum.YES.getValue().toString());
            // 如果渠道配置不为空，直接返回；否则返回null
            return EmptyUtils.isNotEmpty(list) ? list : null;
        }
        // 状态值不为"Yes"，返回null
        return null;
    }

}
