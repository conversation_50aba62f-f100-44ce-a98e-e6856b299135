package com.kbao.kbcchatbot.maas.project.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.project.bean.ProjectPageReqVO;
import com.kbao.kbcchatbot.maas.project.bean.ProjectSimpleVO;
import com.kbao.kbcchatbot.maas.project.entity.Project;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @Description maas项目配置Dao类
* @Date 2024-12-19
*/
public interface ProjectMapper extends BaseMapper<Project, Integer>{
    int isExistProject(@Param("code") String code, @Param("id") Integer id);
    List<Project> getProjectList(ProjectPageReqVO projectPageReqVO);
    List<ProjectSimpleVO> getSimpleProjectList(@Param("tenantId") String tenantId);

}