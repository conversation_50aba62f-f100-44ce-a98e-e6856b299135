package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeDirectRespVO;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_robot_knowledge_config(聊天机器人知识配置表)】的数据库操作Mapper
* @createDate 2023-05-17 10:30:11
*/
public interface RobotKnowledgeConfigMapper extends BaseMapper<RobotKnowledgeConfig,Long> {

    List<RobotKnowledgeConfig> selectByRobotId(Long robotId);

    List<RobotKnowledgeConfig> selectByCondition(Map<String,Object> map);

    void deleteByRobotIdAndDirect(Map<String,Object> map);

    void updateHasRemoved(String directoryId);

    List<String> selectDirectId(Map<String,Object> map);

    void updateType(Long robotId);

    void updateByRobotIdAndDirect(Map<String,Object> map);

    List<RobotKnowledgeDirectRespVO> selectFirstDirectoryByRobot(Map<String,Object> map);
    List<RobotKnowledgeDirectRespVO> selectDirectoryByRobot(Map<String,Object> map);

}
