package com.kbao.kbcchatbot.maas.robot.robotknowledgeRecord.service;

import com.github.pagehelper.PageInfo;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeRecord.dao.RobotKnowledgeRecordDao;
import com.kbao.kbcchatbot.maas.robot.robotknowledgerecord.entity.RobotKnowledgeRecord;
import com.kbao.kbcchatbot.maas.robot.robotknowledgerecord.vo.RobotKnowledgeRecordIdReqVO;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 知识配置
 * @author: husw
 * @create: 2023-05-17 15:47
 **/
@Service
@Slf4j
public class RobotKnowledgeRecordService extends BaseMongoServiceImpl<RobotKnowledgeRecord,String, RobotKnowledgeRecordDao> {

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
    * @Description: 分页查询
    * @Param: [pageRequest]
    * @return: com.github.pagehelper.PageInfo<com.kbao.kbcchatbot.robotknowledgerecord.entity.RobotKnowledgeRecord>
    * @Author: husw
    * @Date: 2023/5/23 11:39
    */
    public PageInfo<RobotKnowledgeRecord> page(RequestObjectPage<RobotKnowledgeRecordIdReqVO> pageRequest) {
        Query query = new Query();
        query.addCriteria(Criteria.where("robotCode").is(pageRequest.getParam().getRobotCode()));
        Pagination<RobotKnowledgeRecord> pagination = new Pagination<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        pagination.setSort(EmptyUtils.isEmpty(pageRequest.getSort()) ? "bindTime desc" : pageRequest.getSort());
        return this.page(query, pagination);
    }
    /**
    * @Description: 批量保存
    * @Param: [records]
    * @return: void
    * @Author: husw
    * @Date: 2024/12/19 11:39
    */
    public void batchSave(List<RobotKnowledgeRecord> records){
        mongoTemplate.insertAll(records);
    }
}
