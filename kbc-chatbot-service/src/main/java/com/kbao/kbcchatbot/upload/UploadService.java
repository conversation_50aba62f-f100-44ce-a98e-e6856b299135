package com.kbao.kbcchatbot.upload;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.AppTenantClientAdapter;
import com.kbao.kbcbsc.adapter.BaseClientAdapter;
import com.kbao.kbcbsc.appfilechannel.bean.AppFileChannelListVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantConfigResVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantIdReq;
import com.kbao.kbcbsc.client.AppTenantClientService;
import com.kbao.kbcbsc.user.vo.UserFeignResp;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.kbc.bsc.config.KbcBscConfig;
import com.kbao.kbcchatbot.kbc.ufs.KbcUfsService;
import com.kbao.kbcchatbot.uploadfilelog.bean.CustomFileUploadRes;
import com.kbao.kbcchatbot.uploadfilelog.entity.UploadFileLog;
import com.kbao.kbcchatbot.uploadfilelog.service.UploadFileLogService;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcufs.adapter.FileClientAdapter;
import com.kbao.kbcufs.client.FileClientService;
import com.kbao.kbcufs.enums.FileTypeEnum;
import com.kbao.kbcufs.file.vo.client.BaseFileRequest;
import com.kbao.kbcufs.file.vo.client.FileRequest;
import com.kbao.kbcufs.file.vo.client.FileUploadRequest;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

import static com.kbao.kbcchatbot.constants.Constant.TENANT_ID_COMMON;
import static com.kbao.kbcchatbot.constants.Constant.TENANT_NAME_COMMON;

/**
 * <AUTHOR> qiuzb
 * @Description: 上传文件
 * @create 2023/5/23 14:20
 */
@Service
@Slf4j
public class UploadService extends BaseClientAdapter {


    @Autowired
    private FileClientService fileClientService;

    @Autowired
    private AppTenantClientService appTenantClientService;

    @Autowired
    private AppTenantClientAdapter appTenantClientAdapter;

    @Autowired
    private UploadFileLogService uploadFileLogService;

    @Autowired
    private KbcUfsService kbcUfsService;

    @Autowired
    private KbcBscConfig kbcBscConfig;

    @Autowired
    private FileClientAdapter fileClientAdapter;
    /**
    * @Description: 上传图片
    * @Param: [file]
    * @return: com.kbao.kbcufs.file.vo.client.FileUploadResponse
    * @Author: husw
    * @Date: 2023/11/7 15:13
    */
    public CustomFileUploadRes uploadImage(MultipartFile file, String bizModule){
        String tenantId = this.isWeb()?BscUserUtils.getUser().getUser().getTenantId():ChatUserUtil.getUser().getTenantId();
        String userId = this.isWeb()?BscUserUtils.getUserId():ChatUserUtil.getUserId();
        this.checkQaFile(userId, bizModule, file);
        FileUploadResponse response = kbcUfsService.uploadImage(tenantId, file);
        uploadFileLogService.saveUploadFileLog(response,bizModule,file.getOriginalFilename(),tenantId, userId);
        CustomFileUploadRes uploadRes = new CustomFileUploadRes();
        BeanUtils.copyProperties(response, uploadRes);
        uploadRes.setFileName(file.getOriginalFilename());
        uploadRes.setFileType(FilenameUtils.getExtension(file.getOriginalFilename()));
        return uploadRes;
    }

    public List<FileUploadResponse> uploadImages(MultipartFile[] images,String bizModule){
        List<FileUploadResponse> fileUploadResponses = new ArrayList<>();
        String tenantId = this.isWeb()?BscUserUtils.getUser().getUser().getTenantId():ChatUserUtil.getUser().getTenantId();
        String userId = this.isWeb()?BscUserUtils.getUserId():ChatUserUtil.getUserId();
        for (MultipartFile image : images) {
            String originalFilename = image.getOriginalFilename();
            FileUploadResponse response = kbcUfsService.uploadImage(tenantId, image);
            uploadFileLogService.saveUploadFileLog(response,bizModule,originalFilename,tenantId, userId);
            fileUploadResponses.add(response);
        }
        return fileUploadResponses;
    }

    public List<FileUploadResponse> deleteFile(String bizModule){
        List<UploadFileLog> files = uploadFileLogService.getFileByBizModule(bizModule,ChatUserUtil.getUser().getTenantId());
        List<FileUploadResponse> fileUploadResponses = new ArrayList<>();
        String tenantId = this.isWeb()?BscUserUtils.getUser().getUser().getTenantId():ChatUserUtil.getUser().getTenantId();
        for (UploadFileLog fileLog : files) {
            kbcUfsService.deleteFile(tenantId, fileLog.getUploadFileUrl());
        }
        return fileUploadResponses;
    }

    /**
     * 文件上传
     * @param file 文件
     * @param fileType 文件类型
     * @return 文件上传后回传的url地址
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<FileUploadResponse> uploadFileWeb(MultipartFile file, String fileType){
        if (file.isEmpty()) {
            throw new BusinessException("文件为空");
        }
        UserFeignResp resp = BscUserUtils.getUser();
        String path = file.getOriginalFilename();
        FileUploadRequest request = (FileUploadRequest) getOssFileChannel(resp.getUser().getTenantId(), resp.getFunction().getApplyId(),null, resp.getUser().getTenantName(), "FileUploadRequest");
        request.setFileType(fileType);
        request.setPath(path);
        request.setType(FileTypeEnum.FILE.getType());
        request.setFile(file);
        request.setCreateUser(resp.getUser().getNickName());
        request.setNetwork("internet");
        request.setNeedAbsolutePath(true);
        request.setRename(false);
        Result<FileUploadResponse> upload = fileClientService.upload(request);
        uploadFileLogService.saveUploadFileLog(upload.getDatas(),"channelCommonCard"
                ,file.getOriginalFilename(),resp.getUser().getTenantId(), BscUserUtils.getUserId());
        return upload;
    }
    /**
    * @Description: 上传文件
    * @Param: [file, fileType, userId]
    * @return: com.kbao.kbcufs.file.vo.client.FileUploadResponse
    * @Author: husw
    * @Date: 2025/4/30 14:09
    */
    public FileUploadResponse uploadFile(MultipartFile file, String fileType, String userId){
        if (file.isEmpty()) {
            throw new BusinessException("文件为空");
        }
        String path = file.getOriginalFilename();
        FileUploadRequest request = (FileUploadRequest) getOssFileChannel(TENANT_ID_COMMON, kbcBscConfig.getAppId(),kbcBscConfig.getAppCode(), TENANT_NAME_COMMON, "FileUploadRequest");
        request.setFileType(fileType);
        request.setPath(path);
        request.setType(FileTypeEnum.FILE.getType());
        request.setFile(file);
        request.setCreateUser(userId);
        request.setNetwork("internet");
        request.setNeedAbsolutePath(true);
        request.setRename(false);
        return kbcUfsService.uploadFile(request);
    }

    /**
     * 获取文件上传配置信息
     * @param tenantId 租户Id
     * @param appId 应用Id
     * @param tenantName 租户名字
     * @return FileUploadRequest
     */
    public BaseFileRequest getOssFileChannel(String tenantId, String appId, String appCode, String tenantName, String type){
        BaseFileRequest request;
        if ("FileUploadRequest".equals(type)) {
            request = new FileUploadRequest();
        } else {
            request = new FileRequest();
        }
        AppTenantIdReq req = new AppTenantIdReq();
        req.setAppId(appId);
        req.setAppCode(appCode);
        req.setTenantId(tenantId);
        // 获取配置信息
        Result<AppTenantConfigResVo> result = appTenantClientAdapter.getChannelConfigInfo(req);
        if (EmptyUtils.isNotEmpty(result) && ResultStatusEnum.SUCCESS.getStatus().equals(result.getResp_code())) {
            AppFileChannelListVo appFileChannelListVo = result.getDatas().getFileChannelList().get(0);
            // 生成编号
            String businessNo = IdWorker.get32UUID();
            String account = appFileChannelListVo.getAccount();

            Map<String, Object> param = new HashMap<>(8);
            param.put("userName", account);
            param.put("businessNo", businessNo);
            param.put("businessTenantId", tenantId);
            param.put("businessTenantName", tenantName);
            //获取字典排序json字符串
            String jsonString = SignUtil.getBodyString(param);

            //生成签名串
            String timestamp = DateUtils.getCurrentTime();
            request.setUserName(account);
            String sign = SignUtil.axSign(account, appFileChannelListVo.getSecretKey(), timestamp, jsonString);
            request.setSign(sign);
            request.setBusinessNo(businessNo);
            request.setBusinessTenantId(tenantId);
            request.setBusinessTenantName(tenantName);
            request.setTimestamp(timestamp);
            return request;
        }else {
            throw new BusinessException("读取消息渠道配置失败");
        }
    }

    public void checkQaFile(String userId, String bizModule, MultipartFile file) {
        if (!"chatQaFile".equals(bizModule)) {
            return;
        }
        String suffix = FilenameUtils.getExtension(file.getOriginalFilename());
        //支持文件JPG\JPEG\PNG\WORD\EXCEL\PDF\PPT\TXT
        List<String> suffixes = Arrays.asList("jpg", "jpeg", "png", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "txt");
        if (suffix == null || !suffixes.contains(suffix.toLowerCase())) {
            throw new BusinessException("暂不支持该文件类型");
        }
        if (file.getSize() > 5 * 1024 * 1024) {
            throw new BusinessException("单个文件大小不能超过5M");
        }
        long toDayUploadNum = uploadFileLogService.getToDayUploadNum(userId, bizModule);
        if (toDayUploadNum >= 50) {
            throw new BusinessException("今日上传文件数量已达上限");
        }
    }
}
