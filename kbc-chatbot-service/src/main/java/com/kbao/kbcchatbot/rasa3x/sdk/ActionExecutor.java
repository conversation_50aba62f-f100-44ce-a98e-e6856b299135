package com.kbao.kbcchatbot.rasa3x.sdk;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.ActionRequest;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.ActionResponse;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.exception.RasaException;
import com.kbao.kbcchatbot.rasa3x.sdk.util.StringUtils;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.ConfigCacheVO;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.*;

import static com.kbao.kbcchatbot.constants.CacheConstant.CHAT_TOKEN;

/**
 * Action executor
 *
 */
@Component
@Slf4j
public class ActionExecutor {
    private static final Logger LOGGER = LoggerFactory.getLogger(ActionExecutor.class);

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private List<Action> actions;

    private final Map<String, Action> actionsMap = new HashMap<>();


    @PostConstruct
    public void rasaActionExecutor() {
        actions.forEach(this::registerAction);
        log.info("ActionExecutor已加载完毕！");
    }

    public void registerAction(Action action) {
        if(StringUtils.isNullOrEmpty(action.name())) {
            throw new RasaException("An action must implement a name");
        }
        this.actionsMap.put(action.name(), action);
        LOGGER.info("Registered action for '{}'.", action.name());
    }

    private void validateEvents(List<AbstractEvent> events, String actionName) {
        Iterator<AbstractEvent> eventsIterator = events.iterator();
        while (eventsIterator.hasNext()) {
            AbstractEvent event = eventsIterator.next();
            if(StringUtils.isNullOrEmpty(event.getEvent())) {
                LOGGER.error("Your action '{}' returned an event without the 'event' property. Event will be ignored! Event: {}", actionName, event);
                eventsIterator.remove();
            }
        }
    }

    public ActionResponse run(ActionRequest actionRequest) {
        //token校验
        String token = actionRequest.getTracker().getSlotValue("token", String.class);
        String redisKey = redisUtil.generateKey(MessageFormat.format(CHAT_TOKEN, token));
        ConfigCacheVO cacheVO = (ConfigCacheVO)redisUtil.get(redisKey);
        if (EmptyUtils.isEmpty(cacheVO)){
            throw new BusinessException("会话已失效!");
        }
        // Check for version of Rasa.
        VersionChecker.checkVersionCompatibility(actionRequest.getVersion());

        if(StringUtils.isNotNullOrEmpty(actionRequest.getNextAction())) {
            LOGGER.debug("Received request to run '{}'", actionRequest.getNextAction());
            Action action = actionsMap.get(actionRequest.getNextAction());
            if(action == null) {
                throw new RasaException("No registered Action found for name '"+actionRequest.getNextAction()+"'.");
            }

            CollectingDispatcher dispatcher = new CollectingDispatcher();
            List<AbstractEvent> events = action.run(dispatcher, actionRequest.getTracker(), actionRequest.getDomain());
            if(events == null) {
                // make sure the action did not just return "null"...
                events = Collections.emptyList();
            }
            validateEvents(events, actionRequest.getNextAction());
            LOGGER.debug("Finished running '{}'", actionRequest.getNextAction());
            ActionResponse actionResponse = new ActionResponse();
            actionResponse.setEvents(events);
            // Rasa API require list of key-value pair objects
            actionResponse.setResponses(dispatcher.getMessagesList());

            return actionResponse;
        }
        LOGGER.warn("Received an action call without an action.");
        return null;
    }

    public List<String> getRegisteredActionNames() {
        return new ArrayList<>(this.actionsMap.keySet());
    }
}
