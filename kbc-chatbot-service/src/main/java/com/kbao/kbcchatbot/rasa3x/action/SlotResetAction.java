package com.kbao.kbcchatbot.rasa3x.action;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AllSlotsReset;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.tool.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 导入插槽重置操作
 * @author: husw
 * @create: 2023-08-07 15:06
 **/
@Slf4j
@Component
public class SlotResetAction implements Action {
    @Override
    public String name() {
        return "action_slot_reset";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_slot_reset>>>>>>>>>>");
        List<AbstractEvent> abstractEvents = new ArrayList<>();
        abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
        abstractEvents.add(new SlotSet("is_evaluate",tracker.getSlotValue("is_evaluate")));
        return abstractEvents;
    }
}
