package com.kbao.kbcchatbot.rasa3x.rasa.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.Objects;

public class BotAnswer extends ArrayList<BotMessage> {

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hash();
    }


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class BotAnswer [\n");
        for (int i = 0; i < this.size(); i ++) {
            BotMessage botMessage = this.get(i);
            sb.append("{" + botMessage.toString() + "}");
            if(i != this.size() -1) {
                sb.append(",\n");
            }
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
