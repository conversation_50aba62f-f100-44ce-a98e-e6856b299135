package com.kbao.kbcchatbot.rasa3x.service;

import com.kbao.kbcchatbot.rasa3x.sdk.ActionExecutor;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.ActionRequest;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.ActionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: kbc-chatbot
 * @description: rasa actions
 * @author: husw
 * @create: 2023-08-04 17:40
 **/
@Service
@Slf4j
public class RasaActionsService {

    @Autowired
    private ActionExecutor executor;

    public ActionResponse executeAction(ActionRequest actionRequest) {
        return executor.run(actionRequest);
    }
}
