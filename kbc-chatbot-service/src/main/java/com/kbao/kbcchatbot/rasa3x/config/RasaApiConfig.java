package com.kbao.kbcchatbot.rasa3x.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

@Component
@PropertySource(value = "classpath:/application.yml")
@ConfigurationProperties(prefix = "rasa.api")
@Data
public class RasaApiConfig {

    /**
     * RASA REST API URL
     */
    private String restUrl;

    /**
     * API JWT SECRET
     */
    private String jwtSecretKey;

    /**
     * RASA模型绝对路径
     */
    private String modelPath;

    /**
     * 训练模型成功后的回调地址
     */
    private String trainModelCallbackUrl;

}
