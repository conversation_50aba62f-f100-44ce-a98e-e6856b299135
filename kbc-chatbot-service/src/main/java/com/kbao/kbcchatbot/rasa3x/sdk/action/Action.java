package com.kbao.kbcchatbot.rasa3x.sdk.action;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;

import java.util.List;
import java.util.Map;

/**
 * Next action to be taken in response to a dialogue state.
 *
 */
public interface Action {

    /**
     * Unique identifier of this action.
     *
     * @return a name of this action
     */
    String name();

    /**
     * Execute the side effects of this action
     *
     * @param dispatcher the dispatcher which is used to send messages back to the user.
     *                   Use {@link CollectingDispatcher#utterMessage(String, Map)} or any other method.
     * @param tracker    the state tracker for the current user. You can access slot values using <code>tracker.getSlots().get(slotName)</code> (see: {@link Tracker}),
     *                   the most recent user message is <code>tracker.getLatestMessage().getText()</code> (see {@link Tracker}) and any other property.
     * @param domain     the bot's domain
     * @return A list of {@link AbstractEvent} instances that is returned through the endpoint
     */
    List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain);
}
