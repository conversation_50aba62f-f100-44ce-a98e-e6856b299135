package com.kbao.kbcchatbot.rasa3x.sdk.action.konwledgebase;

import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class KnowledgeBaseUtils {

    public static final String SLOT_MENTION = "mention";
    public static final String SLOT_OBJECT_TYPE = "object_type";
    public static final String SLOT_ATTRIBUTE = "attribute";
    public static final String SLOT_LISTED_OBJECTS = "knowledge_base_listed_objects";
    public static final String SLOT_LAST_OBJECT = "knowledge_base_last_object";
    public static final String SLOT_LAST_OBJECT_TYPE = "knowledge_base_last_object_type";

    public static String getObjectName(
            Tracker tracker,
            Map<String, OrdinalMentionMapping> ordinalMentionMapping,
            boolean useLastObjectMention
    ) {
        String mention = tracker.getSlotValue(SLOT_MENTION, String.class);
        String objectType = tracker.getSlotValue(SLOT_OBJECT_TYPE, String.class);

        if (mention != null) {
            return resolveMention(tracker, ordinalMentionMapping);
        }

        String objectName = tracker.getSlotValue(objectType, String.class);
        if (objectName != null) {
            return objectName;
        }

        if (useLastObjectMention) {
            return tracker.getSlotValue(SLOT_LAST_OBJECT, String.class);
        }

        return null;
    }

    private static String resolveMention(
            Tracker tracker,
            Map<String, OrdinalMentionMapping> ordinalMentionMapping
    ) {
        String mention = tracker.getSlotValue(SLOT_MENTION, String.class);
        List<Object> listedItems = tracker.getSlotValue(SLOT_LISTED_OBJECTS, List.class);
        String lastObject = tracker.getSlotValue(SLOT_LAST_OBJECT, String.class);
        String lastObjectType = tracker.getSlotValue(SLOT_LAST_OBJECT_TYPE, String.class);
        String currentObjectType = tracker.getSlotValue(SLOT_OBJECT_TYPE, String.class);

        if (mention == null) {
            return null;
        }

        if (listedItems != null && ordinalMentionMapping.containsKey(mention)) {
            OrdinalMentionMapping idxFunction = ordinalMentionMapping.get(mention);
            return idxFunction.apply(listedItems).toString();
        }

        if (currentObjectType.equals(lastObjectType)) {
            return lastObject;
        }

        return null;
    }

    public static List<SlotSet> getAttributeSlots(
            Tracker tracker, List<String> objectAttributes
    ) {
        List<SlotSet> attributes = new ArrayList<>();

        for (String attr : objectAttributes) {
            String attrVal = tracker.getSlotValue(attr, String.class);
            if (attrVal != null) {
                SlotSet slotSet = new SlotSet(attr, attrVal);
                attributes.add(slotSet);
            }
        }

        return attributes;
    }

    public static List<SlotSet> resetAttributeSlots(
            Tracker tracker, List<String> objectAttributes
    ) {
        List<SlotSet> slots = new ArrayList<>();

        for (String attr : objectAttributes) {
            String attrVal = tracker.getSlotValue(attr, String.class);
            if (attrVal != null) {
                slots.add(new SlotSet(attr, null));
            }
        }

        return slots;
    }
}