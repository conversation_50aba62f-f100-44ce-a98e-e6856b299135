package com.kbao.kbcchatbot.rasa3x.sdk.dto.event;

import lombok.Getter;

import java.sql.Timestamp;
@Getter
public class ActionExecuted extends AbstractEvent {

    //-----------------------------------------------
    // Fields
    //-----------------------------------------------

    private String name;
    private String policy;
    private Float confidence;

    //-----------------------------------------------
    // Constructors
    //-----------------------------------------------

    public ActionExecuted() {
        this(null);
    }

    public ActionExecuted(String name) {
        this(name,null);
    }

    public ActionExecuted(String name, Timestamp timestamp) {
        super("action", timestamp);
        this.name = name;
    }
}
