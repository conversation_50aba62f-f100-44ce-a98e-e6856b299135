package com.kbao.kbcchatbot.rasa3x.service;

import cn.hutool.http.HtmlUtil;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import com.kbao.kbcchatbot.maas.chatconfig.service.ChatConfigApiService;
import com.kbao.kbcchatbot.maas.chatsession.bean.ChatSessionMatchQuestionDTO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatIntentConvertDTO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatKnowledgeClearRespVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerRespVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatSearchResultDTO;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerSourceEnum;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerTypeEnum;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatSceneTypeEnum;
import com.kbao.kbcchatbot.maas.chatsession.service.ChatSearchAbstractService;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.kbcchatbot.constants.ElasticsearchConstants;
import com.kbao.kbcchatbot.constants.RasaConstant;
import com.kbao.kbcchatbot.elasticsearch.index.service.IndexService;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackageQA;
import com.kbao.kbcchatbot.discard.knowledgepackage.service.KnowledgePackageQAApiService;
import com.kbao.kbcchatbot.rasa3x.config.RasaApiConfig;
import com.kbao.kbcchatbot.rasa3x.rasa.RasaClient;
import com.kbao.kbcchatbot.rasa3x.rasa.model.*;
import com.kbao.kbcchatbot.rasa3x.utils.ApiException;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgePackageCloud;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.KnowledgePackageCloudApiService;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.indices.AnalyzeResponse;
import org.elasticsearch.index.query.Operator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.kbao.kbcchatbot.constants.Constant.*;

@Service
@Slf4j
public class RasaApiService  extends ChatSearchAbstractService {

    @Autowired
    private RasaApiConfig rasaApiConfig;

    @Autowired
    private KnowledgePackageQAApiService knowledgePackageQAApiService;

    @Autowired
    private KnowledgePackageCloudApiService knowledgePackageCloudApiService;

    @Autowired
    private ChatSessionRecordService chatSessionRecordService;

    @Autowired
    private IndexService indexService;

    @Autowired
    private ChatConfigApiService chatConfigApiService;


    @Override
    protected ChatQueryAnswerRespVO query(String question, Map<String, Object> slots,Integer sceneShowType, String sessionRecordId, boolean isAsync) {
        Long robotId = ChatUserUtil.getUser().getRobotId();
        String sceneId = IdWorker.get32UUID();
        //直接回复阈值
        BigDecimal directReplyThreshold = ChatUserUtil.getUser().getChatSessionCache().getDirectReplyThreshold();
        //澄清阈值
        BigDecimal clearReplyThreshold = ChatUserUtil.getUser().getChatSessionCache().getClearReplyThreshold();
        //澄清问题最大条数
        int clearMaxCount = ChatUserUtil.getUser().getChatSessionCache().getClearMaxCount();
        //敏感词
        List<String> sensitiveWordsList = ChatUserUtil.getUser().getChatSessionCache().getSensitiveWords();
        //敏感词回复文案
        String sensitiveWords = ChatUserUtil.getUser().getChatSessionCache().getSensitiveWordsReply();
        //客服跳转链接
//        String custServPlatAddr = ChatUserUtil.getUser().getChatSessionCache().getCustServPlatAddr();

        //敏感词校验
        boolean isSensitiveWords = checkSensitiveWords(question, sensitiveWordsList);
        if (isSensitiveWords) {
            initChatSessionSceneId(sessionRecordId,sceneId);
            return getCustomResp(ChatAnswerTypeEnum.SENSITIVE_WORD_ANSWER, sensitiveWords,sessionRecordId, sceneId);
        }
        //转人工关键字校验
        if (checkTransferLabor(ChatUserUtil.getUser().getChatSessionCache(), question, YesNoEnum.YES.getValue(),null)) {
            initChatSessionSceneId(sessionRecordId,sceneId);
            return  getCustomResp(ChatAnswerTypeEnum.TRANSFER_LABOR,null,sessionRecordId,sceneId);
        }
        //表情包固定回复
        if (ChatSceneTypeEnum.EMOJI.getKey().equals(sceneShowType)){
            initChatSessionSceneId(sessionRecordId,sceneId);
            return getCustomResp(ChatAnswerTypeEnum.EMOJI_ANSWER,EMOJI_RESPONSE,sessionRecordId,sceneId);
        }
        //意图转换
        ChatIntentConvertDTO chatIntentConvertDTO = convertIntent(question, slots, sceneShowType);
        question = chatIntentConvertDTO.getQuestion();
        slots = chatIntentConvertDTO.getSlots();

        //调用RASA服务获取回复
        ParseResult result = parse(question);
        log.info(">>>>Rasa parse result:{}", result);
        sceneId = addRasaEvent(result, sessionRecordId, slots);
        if(result.getIntent() != null) {
            if(result.getIntent().getName().startsWith("faq_")) {
                //使用RASA置信度
//                return getRasaRankAnswerRespVO(result, sceneId, clearMaxCount, robotId, directReplyThreshold, clearReplyThreshold);
                //使用ES分词算分
                return getKnowledgeRankAnswerRespVO(question, robotId, directReplyThreshold, clearMaxCount, clearReplyThreshold, sessionRecordId, sceneId);
            } else {
                //发送消息
                BotAnswer botAnswer = sendMessage(question);
                return getBotAnswer(botAnswer,sessionRecordId, sceneId);
            }
        }else {
            return getNoAnswerResp(ChatUserUtil.getUser().getChatSessionCache().getNoAnswerContent(),sessionRecordId,sceneId);
        }
    }
    /**
    * @Description: 获取回复
    * @Param: [botAnswer, sceneId]
    * @return: com.kbao.kbcchatbot.chatsession.bean.qa.ChatQueryAnswerRespVO
    * @Author: husw
    * @Date: 2024/1/29 11:38
    */
    private ChatQueryAnswerRespVO getBotAnswer(BotAnswer botAnswer,String recordId, String sceneId){
        //无答案回复
        if (EmptyUtils.isEmpty(botAnswer) || botAnswer.isEmpty() || NO_ANSWER.equals(botAnswer.get(0).getText())){
            return getNoAnswerResp(ChatUserUtil.getUser().getChatSessionCache().getNoAnswerContent(),recordId,sceneId);
        }
        //猜你想问回复
        if (GUESS_ANSWER.equals(botAnswer.get(0).getText()) ){
            return getGuessAnswerResp(sceneId);
        }
        //rasa回复
        return getRasaAnswerRespVO(botAnswer,sceneId);
    }
    /**
    * @Description: Rasa回复内容
    * @Param: [botAnswer, sceneId]
    * @return: com.kbao.kbcchatbot.chatsession.bean.qa.ChatQueryAnswerRespVO
    * @Author: husw
    * @Date: 2024/1/29 11:37
    */
    private ChatQueryAnswerRespVO getRasaAnswerRespVO(BotAnswer botAnswer, String sceneId) {
        Tracker tracker = getConversationTracker();
        //判断是否点赞，点踩
        if (EmptyUtils.isNotEmpty(tracker.getSlots())
                && EmptyUtils.isNotEmpty(tracker.getSlots().get("is_evaluate"))) {
            boolean evaluate = (boolean)tracker.getSlots().get("is_evaluate");
            botAnswer.get(0).setVote(evaluate?YesNoEnum.YES.getValue() : YesNoEnum.NO.getValue());
        }
        ChatQueryAnswerRespVO chatQueryAnswerRespVO = new ChatQueryAnswerRespVO();
        chatQueryAnswerRespVO.setType(ChatAnswerTypeEnum.DIALOGUE_ANSWER.getKey());
        chatQueryAnswerRespVO.setAnswer(botAnswer);
        chatQueryAnswerRespVO.setSceneId(sceneId);
        return chatQueryAnswerRespVO;
    }
    /**
    * @Description: 意图转换
    * @Param: [question, slots, sceneShowType]
    * @return: com.kbao.kbcchatbot.chatsession.bean.qa.ChatIntentConvertDTO
    * @Author: husw
    * @Date: 2024/1/24 10:08
    */
    private ChatIntentConvertDTO convertIntent(String question, Map<String, Object> slots, Integer sceneShowType) {
        ChatIntentConvertDTO convertDto = new ChatIntentConvertDTO(question,slots);
        //图片
        if (ChatSceneTypeEnum.IMAGE.getKey().equals(sceneShowType)
                || ChatSceneTypeEnum.VIDEO.getKey().equals(sceneShowType)){
            if(EmptyUtils.isEmpty(slots)){
                slots = new HashMap<>();
            }
            convertDto.setQuestion(IMAGE_TEXT_INTENT);
            slots.put("send_image", question);
            convertDto.setSlots(slots);
            return convertDto;
        }
        //订单
        if (ChatSceneTypeEnum.ORDER.getKey().equals(sceneShowType)
                && EmptyUtils.isNotEmpty(slots)
                && EmptyUtils.isNotEmpty(slots.get("orderId"))){
            convertDto.setQuestion(ORDER_TEXT_INTENT);
            return convertDto;
        }
        //理赔单
        if (ChatSceneTypeEnum.ORDER.getKey().equals(sceneShowType)
                && EmptyUtils.isNotEmpty(slots)
                && EmptyUtils.isNotEmpty(slots.get("submitInformationType"))){
            convertDto.setQuestion(SEND_CLAIM_WORK_INFO);
            return convertDto;
        }
        return convertDto;
    }

    /**
    * @Description: 使用RASA置信度分数
    * @Param: [result, sceneId, clearMaxCount, robotId, directReplyThreshold, clearReplyThreshold]
    * @return: com.kbao.kbcchatbot.chatsession.bean.qa.ChatQueryAnswerRespVO
    * @Author: husw
    * @Date: 2023/12/21 9:41
    */
    private ChatQueryAnswerRespVO getRasaRankAnswerRespVO(ParseResult result, String sceneId, int clearMaxCount, Long robotId, BigDecimal directReplyThreshold, BigDecimal clearReplyThreshold) {
        List<Response> responseList = new ArrayList<>();
        responseList.addAll(result.getResponseSelector().getFaqQA().getRanking());
        responseList.addAll(result.getResponseSelector().getFaqCloud().getRanking());
        if(responseList.size() == 0) {
            return getNoAnswerResp(ChatUserUtil.getUser().getChatSessionCache().getNoAnswerContent(),null, sceneId);
        }

        responseList = responseList.stream().sorted(Comparator.comparing(Response::getConfidence).reversed()).collect(Collectors.toList());

        if(responseList.size() > clearMaxCount) {
            responseList.subList(0, clearMaxCount);
        }
        List<ChatSearchResultDTO> searchResultDTOList = new ArrayList<>();
        responseList.forEach(response -> {
            String[] intentArr = response.getIntentResponseKey().split("/");
            String knowledgeId = intentArr[1];
            ChatSearchResultDTO chatSearchResultDTO;
            if("faq_cloud".equals(intentArr[0])) {
                chatSearchResultDTO = getChatSearchResultDTOFromCloud(robotId, response, knowledgeId);
            }else {
                chatSearchResultDTO = getChatSearchResultDTOFromQA(robotId, response, knowledgeId);
            }

            searchResultDTOList.add(chatSearchResultDTO);
        });

        ChatSearchResultDTO chatSearchResultDTO = searchResultDTOList.stream().max(Comparator.comparing(ChatSearchResultDTO::getMatchPercent)).get();
        //大于直接回复阈值
        if(chatSearchResultDTO.getMatchPercent().compareTo(directReplyThreshold) == 1) {
            return getDirectAnswerResp(chatSearchResultDTO,sceneId);
        }
        List<ChatKnowledgeClearRespVO> knowledgeClearVOList = new ArrayList<>();
        for (ChatSearchResultDTO searchResultDTO : searchResultDTOList) {
            //澄清阈值区间
            if(searchResultDTO.getMatchPercent().compareTo(clearReplyThreshold) == 1 && searchResultDTO.getMatchPercent().compareTo(directReplyThreshold) != 1) {
                ChatKnowledgeClearRespVO chatKnowledgeClearRespVO = new ChatKnowledgeClearRespVO();
                chatKnowledgeClearRespVO.setMatchPercent(searchResultDTO.getMatchPercent());
                chatKnowledgeClearRespVO.setQaSource(searchResultDTO.getQaSource());
                chatKnowledgeClearRespVO.setQaId(searchResultDTO.getQaId());
                //使用匹配上的question
                chatKnowledgeClearRespVO.setQuestion(searchResultDTO.getQuestion());
                chatKnowledgeClearRespVO.setKnowledgeId(searchResultDTO.getKnowledgeId());
                knowledgeClearVOList.add(chatKnowledgeClearRespVO);
            }
        }
        //澄清回复
        if(knowledgeClearVOList.size() > 0) {
            //处理排序
            List<ChatKnowledgeClearRespVO> knowledgeClearVOSortList = knowledgeClearVOList.stream()
                    .sorted(Comparator.comparing(ChatKnowledgeClearRespVO::getMatchPercent).reversed()).collect(Collectors.toList());
            return getClearAnswerResp(knowledgeClearVOSortList, clearMaxCount, sceneId);
        }
        //无回复
        else {
            return getNoAnswerResp(ChatUserUtil.getUser().getChatSessionCache().getNoAnswerContent(),null, sceneId);
        }
    }


    private ChatQueryAnswerRespVO getKnowledgeRankAnswerRespVO(String question,Long robotId,BigDecimal directReplyThreshold,int clearMaxCount,BigDecimal clearReplyThreshold,String recordId,String sceneId){
        question = com.kbao.kbcchatbot.utils.StringUtil.escape(question);
        List<String> visibleSecondDirectIds = null;
        //检索云知识库
        List<ChatSearchResultDTO> packageCloudSearchResultDTOList = knowledgePackageCloudApiService.queryAnswer(question, robotId, ElasticsearchConstants.MATERIALS_KNOWLEDGE_PACKAGE_CLOUD_INDEX_ID, clearMaxCount, Operator.OR.name(), visibleSecondDirectIds);
        //检索知识包
        List<ChatSearchResultDTO> packageQASearchResultDTOList = knowledgePackageQAApiService.queryAnswer(question, robotId, ElasticsearchConstants.MATERIALS_KNOWLEDGE_PACKAGE_QA_INDEX_ID, clearMaxCount, Operator.OR.name(), visibleSecondDirectIds);
        List<ChatSearchResultDTO> searchResultDTOList = new ArrayList<>(packageCloudSearchResultDTOList.size() + packageQASearchResultDTOList.size());
        //按照知识问题去重，优先保留QA
        Map<String,ChatSearchResultDTO> searchResultDTOMap = new HashMap<>();
        packageCloudSearchResultDTOList.forEach(x->searchResultDTOMap.put(x.getQuestion(),x));
        packageQASearchResultDTOList.forEach(x->searchResultDTOMap.put(x.getQuestion(),x));
        searchResultDTOList.addAll(searchResultDTOMap.values());
        if (searchResultDTOList.size() == 0) {
            return getNoAnswerResp(ChatUserUtil.getUser().getChatSessionCache().getNoAnswerContent(),recordId, sceneId);
        } else {
            List<AnalyzeResponse.AnalyzeToken> questionTokenList = indexService.getIkAnalysisList(question, ElasticsearchConstants.ANALYZER_IK_MAX_WORD_WITH_SYNONYM);
            questionTokenList = filterTokenList(questionTokenList);
            List<ChatKnowledgeClearRespVO> knowledgeClearVOList = new ArrayList<>();
            for (ChatSearchResultDTO searchResultDTO : searchResultDTOList) {
                //计算匹配度
                ChatSessionMatchQuestionDTO matchQuestionDTO = getMatchPercent(questionTokenList, searchResultDTO);
                searchResultDTO.setMatchPercent(matchQuestionDTO.getMatchPercent());
            }
            ChatSearchResultDTO chatSearchResultDTO = searchResultDTOList.stream().max(Comparator.comparing(ChatSearchResultDTO::getMatchPercent)).get();
            //大于直接回复阈值
            if (chatSearchResultDTO.getMatchPercent().compareTo(directReplyThreshold) == 1) {
                return getDirectAnswerResp(chatSearchResultDTO,sceneId);
            }

            for (ChatSearchResultDTO searchResultDTO : searchResultDTOList) {
                //澄清阈值区间
                if (searchResultDTO.getMatchPercent().compareTo(clearReplyThreshold) == 1 && searchResultDTO.getMatchPercent().compareTo(directReplyThreshold) != 1) {
                    ChatKnowledgeClearRespVO chatKnowledgeClearRespVO = new ChatKnowledgeClearRespVO();
                    chatKnowledgeClearRespVO.setMatchPercent(searchResultDTO.getMatchPercent());
                    chatKnowledgeClearRespVO.setQaSource(searchResultDTO.getQaSource());
                    chatKnowledgeClearRespVO.setQaId(searchResultDTO.getQaId());
                    chatKnowledgeClearRespVO.setKnowledgeId(searchResultDTO.getKnowledgeId());
                    //使用匹配上的question
                    chatKnowledgeClearRespVO.setQuestion(searchResultDTO.getQuestion());
                    knowledgeClearVOList.add(chatKnowledgeClearRespVO);
                }
            }
            //澄清回复
            if (knowledgeClearVOList.size() > 0) {
                //处理排序
                List<ChatKnowledgeClearRespVO> knowledgeClearVOSortList = knowledgeClearVOList.stream()
                        .sorted(Comparator.comparing(ChatKnowledgeClearRespVO::getMatchPercent).reversed()).collect(Collectors.toList());
                return getClearAnswerResp(knowledgeClearVOSortList, clearMaxCount,sceneId);
            }
            //无回复
            else {
                return getNoAnswerResp(ChatUserUtil.getUser().getChatSessionCache().getNoAnswerContent(),recordId, sceneId);
            }
        }
    }

    @SneakyThrows
    public List<AnalyzeResponse.AnalyzeToken> filterTokenList(List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionTokenList) {
        List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionList = searchHitsQuestionTokenList;
        searchHitsQuestionTokenList = searchHitsQuestionTokenList.stream().filter(searchHitQuestionToken -> searchHitQuestionToken.getTerm().length() > 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(searchHitsQuestionTokenList)) {
            searchHitsQuestionTokenList = searchHitsQuestionList;

        }
        List<AnalyzeResponse.AnalyzeToken> tokenList = new ArrayList<>();
        List<String> wordList = new ArrayList<>();
        searchHitsQuestionTokenList.forEach(item -> {
            if (!wordList.contains(item.getTerm())) {
                tokenList.add(item);
                wordList.add(item.getTerm());
            }
        });
        return tokenList;
    }

    public ChatSessionMatchQuestionDTO getMatchPercent(List<AnalyzeResponse.AnalyzeToken> questionTokenList, ChatSearchResultDTO searchResultDTO) {
        ChatSessionMatchQuestionDTO dto = new ChatSessionMatchQuestionDTO();
        String question = searchResultDTO.getQuestion();
        dto.setQuestion(question);
        BigDecimal questionMatchPercent = computerMatchPercent(questionTokenList, question);
        if (!CollectionUtils.isEmpty(searchResultDTO.getSimilarQuestions())) {
            for (String similarQuestion : searchResultDTO.getSimilarQuestions()) {
                BigDecimal similarQuestionMatchPercent = computerMatchPercent(questionTokenList, similarQuestion);
                if (similarQuestionMatchPercent.compareTo(questionMatchPercent) == 1) {
                    questionMatchPercent = similarQuestionMatchPercent;
                    dto.setQuestion(similarQuestion);
                }
            }
        }
        dto.setMatchPercent(questionMatchPercent);
        return dto;
    }

    private BigDecimal computerMatchPercent(List<AnalyzeResponse.AnalyzeToken> questionTokenList, String searchHitsQuestion) {
        int matchCount = 0;
        List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionTokenList = indexService.getIkAnalysisList(searchHitsQuestion, ElasticsearchConstants.ANALYZER_IK_MAX_WORD_WITH_SYNONYM);
        if (CollectionUtils.isEmpty(searchHitsQuestionTokenList)) {
            return BigDecimal.ZERO;
        }
        searchHitsQuestionTokenList = filterTokenList(searchHitsQuestionTokenList);

        long searchHitQuestionTokenSize = getSearchHitsQuestionTokenListSize(searchHitsQuestionTokenList);
        if (searchHitQuestionTokenSize == 0) {
            return BigDecimal.ZERO;
        }
        int synonymWordMatchCount = 0;
        for (AnalyzeResponse.AnalyzeToken questionToken : questionTokenList) {
            Optional<AnalyzeResponse.AnalyzeToken> searchHitQuestionTokenOptional = searchHitsQuestionTokenList.stream().filter(searchHitQuestionToken -> searchHitQuestionToken.getTerm().equals(questionToken.getTerm())).findFirst();
            if (searchHitQuestionTokenOptional.isPresent()) {
                if (ElasticsearchConstants.IK_TOKEN_TYPE_SYNONYM.equals(searchHitQuestionTokenOptional.get().getType())) {
                    synonymWordMatchCount++;
                }
                matchCount++;
            }
        }
        BigDecimal matchPercent;
        if (synonymWordMatchCount > 0) {
            matchPercent = new BigDecimal(matchCount - synonymWordMatchCount).divide(new BigDecimal(searchHitQuestionTokenSize), 2, BigDecimal.ROUND_UP);
        } else {
            matchPercent = new BigDecimal(matchCount).divide(new BigDecimal(searchHitQuestionTokenSize), 2, BigDecimal.ROUND_UP);
        }
        return matchPercent;
    }

    private long getSearchHitsQuestionTokenListSize(List<AnalyzeResponse.AnalyzeToken> searchHitsQuestionTokenList) {
        return searchHitsQuestionTokenList.stream().filter(item -> !ElasticsearchConstants.IK_TOKEN_TYPE_SYNONYM.equals(item.getTerm())).count();
    }


    /**
     * @Description: RASA事件处理
     * @Param: [req]
     * @return: com.kbao.kbcchatbot.rasa3x.rasa.model.ParseResult
     * @Author: husw
     * @Date: 2023/11/1 10:57
     */
    private String addRasaEvent(ParseResult parse,String sessionRecordId,Map<String,Object> slotsMap) {
        if (EmptyUtils.isEmpty(parse.getIntent()) || EmptyUtils.isEmpty(parse.getIntent().getName())){
            return null;
        }
        Map<String,Object> pushSlotsMap = new HashMap<>();
        List<Event> eventsList = new ArrayList<>();
        String newSceneId = IdWorker.get32UUID();
        String sceneId;
        boolean isRestart = false;
        if (parse.getIntent().getName().startsWith("faq_")){
            sceneId = newSceneId;
            isRestart = true;
        }else {
            Tracker tracker = getConversationTracker();
            //自定义词槽slots_map数据
            if (EmptyUtils.isNotEmpty(slotsMap)){
                Map oldSlots = (Map)tracker.getSlots().get("slots_map");
                if (EmptyUtils.isNotEmpty(oldSlots)){
                    oldSlots.putAll(slotsMap);
                    pushSlotsMap.put("slots_map",oldSlots);
                }else {
                    pushSlotsMap.put("slots_map",slotsMap);
                }
            }
            sceneId = (String)tracker.getSlots().get("scene_id");
            String tenantId = (String)tracker.getSlots().get("tenant_id");
            String userId = (String)tracker.getSlots().get("user_id");
            String token = (String)tracker.getSlots().get("token");
            Boolean isEvaluate = (Boolean)tracker.getSlots().get("is_evaluate");
            String sourceType = (String)tracker.getSlots().get("source_type");
            if (EmptyUtils.isEmpty(tenantId)){
                pushSlotsMap.put("tenant_id",ChatUserUtil.getUser().getTenantId());
            }
            if (EmptyUtils.isEmpty(userId)){
                pushSlotsMap.put("user_id",ChatUserUtil.getUserId());
            }
            if (EmptyUtils.isEmpty(sourceType)){
                pushSlotsMap.put("source_type",ChatUserUtil.getUser().getSourceType());
            }
            if (EmptyUtils.isEmpty(token)){
                pushSlotsMap.put("token",ChatUserUtil.getUser().getToken());
            }
            //场景对话是否开启评价
            if (EmptyUtils.isEmpty(isEvaluate) || isEvaluate){
                pushSlotsMap.put("is_evaluate",false);
            }
            if (EmptyUtils.isEmpty(sceneId) || parse.getIntent().getName().startsWith("begin_")){
                sceneId = newSceneId;
                pushSlotsMap.put("scene_id",newSceneId);
            }
            //判断是否是新的场景
            if (parse.getIntent().getName().startsWith("begin_")){
                isRestart = true;
            }
        }
        if (isRestart){
            eventsList.add(addActionEvent("session_started",null));
            eventsList.add(addActionEvent("action","action_listen"));
        }
        //保存slots_map结构数据
        if (EmptyUtils.isNotEmpty(pushSlotsMap) && !pushSlotsMap.isEmpty()){
            eventsList.addAll(addSlotEvent(pushSlotsMap));
        }
        if (!eventsList.isEmpty()){
            Event[] events = eventsList.toArray(new Event[0]);
            log.info("Rasa add Event:{}", eventsList);
            addConversationTrackerEvents(events);
        }
        //更新对话记录的场景ID
        initChatSessionSceneId(sessionRecordId,sceneId);
        return sceneId;
    }

    private void initChatSessionSceneId(String id,String sceneId){
        ChatSessionRecord chatSessionRecord = new ChatSessionRecord();
        chatSessionRecord.setId(id);
        chatSessionRecord.setSceneId(sceneId);
        chatSessionRecordService.getDao().update(chatSessionRecord);
    }

    private ChatSearchResultDTO getChatSearchResultDTOFromQA(Long robotId, Response response, String knowledgeId) {
        KnowledgePackageQA qa = knowledgePackageQAApiService.selectByKnowledgeId(knowledgeId, robotId, EnvTypeEnum.PROD_ENV.getCode());
        ChatSearchResultDTO chatSearchResultDTO = new ChatSearchResultDTO();
        if(!CollectionUtils.isEmpty(qa.getChapters())) {
            StringBuffer answer = new StringBuffer();
            qa.getChapters().forEach(chapter -> {
                if(answer.length() > 0) {
                    answer.append("\n");
                }
                answer.append(HtmlUtil.cleanHtmlTag(chapter.getChapterContent()));
            });
            chatSearchResultDTO.setAnswer(answer.toString());
        }else {
            chatSearchResultDTO.setAnswer("FAQ知识【" + qa.getKnowledgeId() + "】没有配置答案，请联系管理员处理");
        }
        chatSearchResultDTO.setQuestion(qa.getQuestion());
        chatSearchResultDTO.setKnowledgeId(qa.getKnowledgeId());
        chatSearchResultDTO.setRelatedArticles(qa.getRelatedArticles());
        chatSearchResultDTO.setSecondDirectId(qa.getSecondDirect());
        chatSearchResultDTO.setSimilarQuestions(qa.getSimilarQuestions());
        chatSearchResultDTO.setQaId(qa.getQaId());
        chatSearchResultDTO.setQaSource(ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_QA.getKey());
        chatSearchResultDTO.setMatchPercent(response.getConfidence());
        return chatSearchResultDTO;
    }

    private ChatSearchResultDTO getChatSearchResultDTOFromCloud(Long robotId, Response response, String knowledgeId) {
        KnowledgePackageCloud cloud = knowledgePackageCloudApiService.selectByKnowledgeId(knowledgeId, robotId, EnvTypeEnum.PROD_ENV.getCode());
        ChatSearchResultDTO chatSearchResultDTO = new ChatSearchResultDTO();
        if(!CollectionUtils.isEmpty(cloud.getChapters())) {
            StringBuffer answer = new StringBuffer();
            cloud.getChapters().forEach(chapter -> {
                if(StringUtil.isNotEmpty(chapter.getChapterContent())) {
                    if(answer.length() > 0) {
                        answer.append("\n");
                    }
                    answer.append(HtmlUtil.cleanHtmlTag(chapter.getChapterContent()));
                }
            });
            chatSearchResultDTO.setAnswer(answer.toString());
        }else {
            chatSearchResultDTO.setAnswer("FAQ知识【" + cloud.getKnowledgeId() + "】没有配置答案，请联系管理员处理");
        }

        chatSearchResultDTO.setQuestion(cloud.getTitle());
        chatSearchResultDTO.setKnowledgeId(cloud.getKnowledgeId());
        chatSearchResultDTO.setRelatedArticles(cloud.getRelatedArticles());
        chatSearchResultDTO.setSecondDirectId(cloud.getSecondDirect());
        chatSearchResultDTO.setSimilarQuestions(cloud.getSimilarQuestions());
        chatSearchResultDTO.setQaId(cloud.getId());
        chatSearchResultDTO.setQaSource(ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_CLOUD.getKey());
        chatSearchResultDTO.setMatchPercent(response.getConfidence());
        return chatSearchResultDTO;
    }

    private ChatQueryAnswerRespVO getClearAnswerResp(List<ChatKnowledgeClearRespVO> knowledgeClearVOList, int clearMaxCount,String sceneId) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        respVO.setType(ChatAnswerTypeEnum.QUESTION_CLEAR.getKey());
        respVO.setSceneId(sceneId);
        if(knowledgeClearVOList.size() > clearMaxCount) {
            respVO.setAnswer(knowledgeClearVOList.subList(0, clearMaxCount - 1));
        }else {
            respVO.setAnswer(knowledgeClearVOList);
        }
        return respVO;
    }

    private ChatQueryAnswerRespVO getGuessAnswerResp(String sceneId) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        respVO.setType(ChatAnswerTypeEnum.GUESS_QUESTION.getKey());
        respVO.setSceneId(sceneId);
        List<ChannelGuessQuestionVO> guessQuestionList = chatConfigApiService.exchange(ChatUserUtil.getUser().getSourceType());
        respVO.setAnswer(guessQuestionList);
        return respVO;
    }

    private ChatQueryAnswerRespVO getDirectAnswerResp(ChatSearchResultDTO searchResultDTO,String sceneId) {
        ChatQueryAnswerRespVO respVO = new ChatQueryAnswerRespVO();
        respVO.setType(ChatAnswerTypeEnum.KNOWLEDGE_ANSWER.getKey());
        respVO.setQaSource(searchResultDTO.getQaSource());
        respVO.setTitle(searchResultDTO.getQuestion());
        respVO.setSceneId(sceneId);
        if (ChatAnswerSourceEnum.KNOWLEDGE_PACKAGE_QA.getKey().equals(searchResultDTO.getQaSource())){
            respVO.setAnswer(searchResultDTO.getAnswer());
            //查询关联知识
            String relatedArticlesName = getCloudTitle(searchResultDTO.getRelatedArticles(),ChatUserUtil.getUser().getRobotId());
            if (EmptyUtils.isNotEmpty(relatedArticlesName)){
                respVO.setRelatedArticles(searchResultDTO.getRelatedArticles());
                respVO.setRelatedArticlesName(relatedArticlesName);
            }
        }else {
            respVO.setRelatedArticles(searchResultDTO.getKnowledgeId());
            respVO.setRelatedArticlesName(searchResultDTO.getQuestion());
        }
        return respVO;
    }

    public String getCloudTitle(String knowledgeId,Long robotId){
        if (EmptyUtils.isEmpty(knowledgeId)){
            return null;
        }
        KnowledgePackageCloud knowledgePackageCloud = knowledgePackageCloudApiService.selectByKnowledgeId(knowledgeId, robotId, EnvTypeEnum.PROD_ENV.getCode());
        if (EmptyUtils.isNotEmpty(knowledgePackageCloud)){
            return knowledgePackageCloud.getTitle();
        }
        return null;
    }

    private ParseResult parse(String msg) {
        RasaClient rasaClient = getRasaClientInstance();
        try {
            ParseResult parseResult = rasaClient.parseModelMessage(msg);
            return parseResult;
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    private BotAnswer sendMessage(String msg) {
        RasaClient rasaClient = getRasaClientInstance();
        try {
            UserMessage userMessage = new UserMessage();
            userMessage.setSender(getUsername());
            userMessage.setMessage(msg);
            BotAnswer botAnswer = rasaClient.sendUserMessage(userMessage);
            return botAnswer;
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    private Tracker getConversationTracker() {
        RasaClient rasaClient = getRasaClientInstance();
        try {
            return rasaClient.getConversationTracker(getUsername());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    private Tracker addConversationTrackerEvents(Event[] events) {
        RasaClient rasaClient = getRasaClientInstance();
        try {
            return rasaClient.addConversationTrackerEvents(getUsername(), events);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    private PredictResult predictConversationAction() {
        RasaClient rasaClient = getRasaClientInstance();
        try {
            return rasaClient.predictConversationAction(getUsername());
        } catch (ApiException e) {
            log.error("预测动作失败！",e);
            return new PredictResult();
//            throw new RuntimeException(e);
        }
    }


    private static List<Event> addSlotEvent(Map<String,Object> dataMap) {
        List<Event> events = new ArrayList<>();
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            Event event = new Event();
            event.setEvent("slot");
            event.setName(entry.getKey());
            event.setValue(entry.getValue());
            events.add(event);
        }
        return events;
    }

    private static Event addActionEvent(String eventName,String name) {
        Event event = new Event();
        event.setEvent(eventName);
        event.setName(name);
        return event;
    }

    private RasaClient getRasaClientInstance() {
        RasaClient rasaClient = new RasaClient(rasaApiConfig.getRestUrl()).setConnectionTimeout(10*60*1000).withJwtToken(rasaApiConfig.getJwtSecretKey(), getUsername(), RasaConstant.API_PAYLOAD_ROLE_USER);
        return rasaClient;
    }

    private static String getUsername() {
//        return StringUtil.isNotEmpty(ChatUserUtil.getUserId()) ? ChatUserUtil.getUserId() : ChatUserUtil.getUser().getToken();
        return ChatUserUtil.getUser().getToken();
    }

}
