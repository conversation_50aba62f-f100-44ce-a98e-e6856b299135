package com.kbao.kbcchatbot.rasa3x.sdk.action.form;

import cn.hutool.core.util.StrUtil;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.tool.util.EmptyUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public abstract class ValidationAction implements Action {

    private static final Logger logger = Logger.getLogger(ValidationAction.class.getName());
    private static final String ACTION_VALIDATE_SLOT_MAPPINGS = "action_validate_slot_mappings";
    protected static final String REQUESTED_SLOT = "requested_slot";

    @Override
    public String name() {
        return ACTION_VALIDATE_SLOT_MAPPINGS;
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {

        //必填词槽
        List<String> requiredSlots = requiredSlots(domainSlots(domain), dispatcher, tracker, domain);

        List<AbstractEvent> extractionEvents = getExtractionEvents(dispatcher, tracker, domain, requiredSlots);
        tracker.addSlots(extractionEvents);

        List<AbstractEvent> validationEvents = extractValidationEvents(dispatcher, tracker, domain,requiredSlots);
        tracker.addSlots(validationEvents);

        return validationEvents;
    }

    protected List<AbstractEvent> extractValidationEvents(CollectingDispatcher dispatcher, Tracker tracker, Domain domain, List<String> requiredSlots) {
        List<AbstractEvent> validationEvents = getValidationEvents(dispatcher, tracker, domain, requiredSlots);
        tracker.addSlots(validationEvents);

        return validationEvents;
    }

    private List<AbstractEvent> getExtractionEvents(CollectingDispatcher dispatcher, Tracker tracker, Domain domain,List<String> requiredSlots) {
        List<AbstractEvent> extractionEvents = new ArrayList<>();
//        List<String> slotsToExtract = requiredSlots(domainSlots(domain), dispatcher, tracker, domain);

        for (String slot : requiredSlots) {
            Map<String, Object> extractionOutput = extractSlot(slot, dispatcher, tracker, domain);
            if (EmptyUtils.isNotEmpty(extractionOutput.get(slot))){
                extractionEvents.add(new SlotSet(slot, extractionOutput.get(slot)));
                tracker.getSlots().put(slot, extractionOutput.get(slot));
            }
        }

        return extractionEvents;
    }
    /**
    * @Description: 校验表单词槽
    * @Param: [dispatcher, tracker, domain]
    * @return: java.util.List<com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent>
    * @Author: husw
    * @Date: 2023/8/24 9:05
    */
    protected List<AbstractEvent> getValidationEvents(CollectingDispatcher dispatcher, Tracker tracker, Domain domain, List<String> requiredSlots) {
        List<AbstractEvent> validationEvents = new ArrayList<>();
//        List<String> slotsToValidate = requiredSlots(domainSlots(domain), dispatcher, tracker, domain);
        Map<String, Object> slots = tracker.slotsToValidate(tracker.getEvents());

        for (Map.Entry<String, Object> entry : slots.entrySet()) {
            String slotName = entry.getKey();
            Object slotValue = entry.getValue();

            if (!requiredSlots.contains(slotName)) {
                slots.remove(slotName);
                continue;
            }

            String methodName = StrUtil.toCamelCase("validate_" + slotName.replace("-", "_"));
            Method validateMethod = getValidateMethod(methodName);

            if (validateMethod == null) {
                logger.warning("Skipping validation for '" + slotName + "' slot: no validation method specified.");
                continue;
            }
            Object validationOutput;
            try {
                validationOutput = validateMethod.invoke(this,slotValue, dispatcher, tracker, domain);
            } catch (IllegalAccessException | InvocationTargetException e) {
                logger.info("方法调用异常："+methodName);
                throw new RuntimeException(e);
            }

            if (validationOutput instanceof Map) {
                Map<String, Object> validationOutputMap = (Map<String, Object>) validationOutput;
                slots.putAll(validationOutputMap);
                tracker.getSlots().putAll(validationOutputMap);
            } else {
                logger.warning("Cannot validate '" + slotName + "': make sure the validation method returns the correct output.");
            }
        }

        for (Map.Entry<String, Object> entry : slots.entrySet()) {
            validationEvents.add(new SlotSet(entry.getKey(), entry.getValue()));
        }

        return validationEvents;
    }

    private Method getValidateMethod(String methodName) {
        try {
            return getClass().getMethod(methodName, Object.class, CollectingDispatcher.class, Tracker.class, Domain.class);
        } catch (NoSuchMethodException e) {
            return null;
        }
    }

    protected abstract List<String> requiredSlots(List<String> slots , CollectingDispatcher dispatcher, Tracker tracker, Domain domain);

    private Map<String, Object> extractSlot(String slotName, CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        String methodName = StrUtil.toCamelCase("extract_" + slotName.replace("-", "_"));

        List<String> domainSlots = domainSlots(domain);
        boolean slotInDomain = domainSlots.contains(slotName);

        Method extractMethod = getExtractMethod(methodName);

        if (extractMethod == null) {
            if (!slotInDomain) {
                logger.warning("No method '" + methodName + "' found for slot '" + slotName + "'. Skipping extraction for this slot.");
            }
            return new HashMap<>();
        }

        Object extracted;
        try {
            extracted = extractMethod.invoke(this, dispatcher, tracker, domain);
        } catch (IllegalAccessException | InvocationTargetException e) {
            logger.info("方法调用异常："+methodName);
            throw new RuntimeException(e);
        }
//        Object extracted = callPotentialCoroutine(extractMethod, dispatcher, tracker, domain);

        if (extracted instanceof Map) {
            return (Map<String, Object>) extracted;
        }

        logger.warning("Cannot extract '" + slotName + "': make sure the extract method returns the correct output.");
        return new HashMap<>();
    }

    private Method getExtractMethod(String methodName) {
        try {
            return getClass().getMethod(methodName, CollectingDispatcher.class, Tracker.class, Domain.class);
        } catch (NoSuchMethodException e) {
            return null;
        }
    }

    protected abstract List<String> domainSlots(Domain domain);
}