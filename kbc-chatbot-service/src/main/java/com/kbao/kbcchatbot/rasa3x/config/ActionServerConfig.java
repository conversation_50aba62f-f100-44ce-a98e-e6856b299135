package com.kbao.kbcchatbot.rasa3x.config;

import com.kbao.kbcchatbot.rasa3x.sdk.ActionExecutor;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Configuration
@ComponentScan(value = "com.kbao.kbcchatbot.rasa3x")
@Slf4j
public class ActionServerConfig {

    @Autowired
    private List<Action> actions;

//    @Bean
//    public ActionExecutor rasaActionExecutor() {
//        ActionExecutor executor = new ActionExecutor();
//        actions.forEach(executor::registerAction);
//        log.info("ActionExecutor已加载完毕！");
//        return executor;
//    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

}
