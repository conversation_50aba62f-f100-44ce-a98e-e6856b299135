package com.kbao.kbcchatbot.rasa3x.sdk.action.konwledgebase;

import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.*;

public abstract class KnowledgeBase {
    private Map<String, String> keyAttribute = new HashMap<>();
    private Map<String, RepresentationFunction> representationFunction = new HashMap<>();
    @Getter
    private static Map<String, OrdinalMentionMapping> ordinalMentionMapping = createDefaultOrdinalMentionMapping();

    public abstract List<String> getAttributesObject(String objectType);

    public abstract List<Map<String,Object>> getObjects(String objectType, List<SlotSet> attributes);

    public abstract Map<String, Object> getObject(String objectType, String objectIdentifier);

    public String getKeyAttributeOfObject(String objectType) {
        return keyAttribute.getOrDefault(objectType, "id");
    }

    public RepresentationFunction getRepresentationFunctionOfObject(String objectType) {
        return representationFunction.getOrDefault(objectType, obj -> obj.get("name").toString());
    }

    public void setOrdinalMentionMapping(Map<String, OrdinalMentionMapping> mapping) {
        ordinalMentionMapping = mapping;
    }

    public void setRepresentationFunctionOfObject(String objectType, RepresentationFunction function) {
        representationFunction.put(objectType, function);
    }

    public void setKeyAttributeOfObject(String objectType, String attribute) {
        keyAttribute.put(objectType, attribute);
    }

    private static Map<String, OrdinalMentionMapping> createDefaultOrdinalMentionMapping() {
        Map<String, OrdinalMentionMapping> defaultMapping = new HashMap<>();
        for (int i = 1; i <= 10; i++) {
            int index = i - 1;
            defaultMapping.put(String.valueOf(i), l -> l.get(index));
        }
        defaultMapping.put("ANY", l -> l.get(new Random().nextInt(l.size())));
        defaultMapping.put("LAST", l -> l.get(l.size() - 1));
        return defaultMapping;
    }
}