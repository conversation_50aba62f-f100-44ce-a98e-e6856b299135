package com.kbao.kbcchatbot.rasa3x.utils;

import java.util.HashSet;

/**
 * Representing a Server Variable for server URL template substitution.
 */
public class ServerVariable {
    public String description;
    public String defaultValue;
    public HashSet<String> enumValues = null;

    /**
     * @param description  A description for the server variable.
     * @param defaultValue The default value to use for substitution.
     * @param enumValues   An enumeration of string values to be used if the substitution options are from a limited set.
     */
    public ServerVariable(String description, String defaultValue, HashSet<String> enumValues) {
        this.description = description;
        this.defaultValue = defaultValue;
        this.enumValues = enumValues;
    }
}
