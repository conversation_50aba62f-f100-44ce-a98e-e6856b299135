package com.kbao.kbcchatbot.rasa3x.utils;

public class RasaUtil {

//    public static boolean isRasaRobot(Long robotId) {
//        RobotService robotService = SpringUtils.getBean(RobotService.class);
//        Robot robot = robotService.getMapper().selectByPrimaryKey(robotId);
//        return 1 == robot.getReleaseType();
////        if (BaseUtil.isTestEnvironment()){
////            return robotId == 41;
////        }else {
////            return robotId == 2;
////        }
//    }

}
