package com.kbao.kbcchatbot.rasa3x.rasa.model;

import java.util.ArrayList;
import java.util.Objects;

/**
 * SlotValue
 */

public class SlotValue extends ArrayList<String> {

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hash();
    }


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class SlotValue {\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

