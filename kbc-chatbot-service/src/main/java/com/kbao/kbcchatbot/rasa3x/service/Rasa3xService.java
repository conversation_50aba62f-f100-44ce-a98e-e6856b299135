package com.kbao.kbcchatbot.rasa3x.service;

import com.kbao.kbcchatbot.constants.RasaConstant;
import com.kbao.kbcchatbot.rasa3x.bean.TrainModelReqVO;
import com.kbao.kbcchatbot.rasa3x.config.RasaApiConfig;
import com.kbao.kbcchatbot.rasa3x.rasa.RasaClient;
import com.kbao.kbcchatbot.rasa3x.rasa.model.*;
import com.kbao.kbcchatbot.rasa3x.utils.ApiException;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.RobotKnowledgeConfigService;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeSubDetailVO;
import com.kbao.kbcchatbot.discard.robotreleaserecord.entity.RobotReleaseRecord;
import com.kbao.kbcchatbot.discard.robotreleaserecord.service.RobotReleaseRecordService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;


@Service
@Slf4j
public class Rasa3xService {

    @Autowired
    private RasaApiConfig rasaApiConfig;

    @Autowired
    private Configuration cfg;

    @Lazy
    @Autowired
    private RobotKnowledgeConfigService robotKnowledgeConfigService;

    @Autowired
    private RobotReleaseRecordService robotReleaseRecordService;

    public void tranModel(TrainModelReqVO reqVO) {
        List<RobotKnowledgeSubDetailVO> knowledgeSubDetailVOList = robotKnowledgeConfigService.getFAQKnowledgeList(reqVO.getRobotId(), reqVO.getEnvironment());
        log.info("本次训练数FAQ据量：{}",knowledgeSubDetailVOList.size());
        try {
            Template nluTemplate = cfg.getTemplate("nlu.ftl");
            // 设置模板数据
            Map<String, Object> data = new HashMap<>();
            data.put("faqList", knowledgeSubDetailVOList);
            String nlu = getTemplateContent(nluTemplate, data);

            Template domainTemplate = cfg.getTemplate("domain.ftl");
            // 设置模板数据
            String domain = getTemplateContent(domainTemplate, data);

            Template configTemplate = cfg.getTemplate("config.ftl");

            //data.clear();
            String config = getTemplateContent(configTemplate, null);

            Template rulesTemplate = cfg.getTemplate("rules.ftl");
            String rules = getTemplateContent(rulesTemplate, null);

            Template storiesTemplate = cfg.getTemplate("stories.ftl");
            String stories = getTemplateContent(storiesTemplate, null);

            // 训练模型
            RasaClient rasaClient = getRasaClientInstance();
            YAMLTrainingRequest yamlTrainingRequest = new YAMLTrainingRequest();
            yamlTrainingRequest
                    .config(config)
                    .domain(domain)
                    .nlu(nlu)
                    .stories(stories)
                    .rules(rules);
            rasaClient.trainModel(yamlTrainingRequest, rasaApiConfig.getTrainModelCallbackUrl()
                    + "?access_token=" + reqVO.getAccessToken()
                    + "&funcId=" + reqVO.getFuncId()
                    + "&tenantId=" + reqVO.getTenantId()
                    + "&robotId=" + reqVO.getRobotId()
                    + "&releaseRecordId=" + reqVO.getReleaseRecordId());
            log.info("已完成rasa模型训练调用，等待训练成功后回调！");
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        } catch (TemplateException e) {
            throw new RuntimeException(e);
        }
    }


    public RobotReleaseRecord models(Long robotId, String releaseRecordId) {
        RobotReleaseRecord record = robotReleaseRecordService.findModelsByRobotId(robotId, releaseRecordId);
        if(record != null) {
            return record;
        }
        return null;
    }

    public void replaceModel(String url) {
        RasaClient rasaClient = getRasaClientInstance();
        ModelRequest modelRequest = new ModelRequest();
        modelRequest.setModelFile(url);
        EndpointConfig endpointConfig = new EndpointConfig();
        endpointConfig.setUrl(url);
        endpointConfig.setWaitTimeBetweenPulls(null);
        modelRequest.modelServer(endpointConfig);
        try {
            rasaClient.replaceModel(modelRequest);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    public ParseResult parse(String msg) {
        RasaClient rasaClient = getRasaClientInstance();
        try {
            ParseResult parseResult = rasaClient.parseModelMessage(msg);
            return parseResult;
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    private RasaClient getRasaClientInstance() {
        RasaClient rasaClient = new RasaClient(rasaApiConfig.getRestUrl()).setConnectionTimeout(10*60*1000).withJwtToken(rasaApiConfig.getJwtSecretKey(), RasaConstant.API_PAYLOAD_USER_ADMIN, RasaConstant.API_PAYLOAD_ROLE_ADMIN);
        return rasaClient;
    }

    private static String getTemplateContent(Template template, Map<String, Object> data) throws TemplateException, IOException {
        // 生成文件内容
        Writer out = new StringWriter();
        template.process(data, out);
        String nlu = out.toString();
        return nlu;
    }

    public static void main(String[] args) throws ApiException, UnsupportedEncodingException {
//        //String subject = "推荐学平险";
//        Map payload = new HashMap();
//        Map user = new HashMap();
//        user.put("username", "admin");
//        user.put("role", "admin");
//        payload.put("user", user);
////        long currentTimeMillis = System.currentTimeMillis();
////        long expirationTimeMillis = currentTimeMillis + 3600000; // Token expiration time: 1 hour
//        String token = Jwts.builder()
//                //.setSubject("Davy")
//                .setClaims(payload)
////                .setIssuedAt(new Date(currentTimeMillis))
////                .setExpiration(new Date(expirationTimeMillis))
//                .signWith(SignatureAlgorithm.HS256, secretKey)
//                .compact();
//        Algorithm algorithm = Algorithm.HMAC256(RASA_REST_SECRET_KEY);
//        String token = JWT.create()
//                //.withIssuer()
//                //.withSubject("Baeldung Details")
//                .withPayload(payload)
//                .withIssuedAt(new Date())
//                .withExpiresAt(new Date(System.currentTimeMillis() + 5000L))
//                .withJWTId(UUID.randomUUID().toString())
//                .withNotBefore(new Date(System.currentTimeMillis()))
//                .sign(algorithm);

        // 生成JWT
//        String token = Jwts.builder()
//                .setSubject("admin")
//                .setExpiration(new Date(System.currentTimeMillis() + 86400000)) // 设置过期时间，这里是24小时
//                .signWith(SignatureAlgorithm.HS256, secretKey)
//                .compact();

        //System.out.println("Generated JWT: " + token);
        /*RasaClient usage example*/
//        RasaClient rasaClient = new RasaClient().withBasePath(RASA_REST_URL).withJwtToken(RASA_REST_SECRET_KEY);
//
//        //NLU example
//        ParseResult parseResult = rasaClient.parseModelMessage("推荐保险产品");
//        System.out.println("ParseResult:" + parseResult);

        //build your request from your training data
//        YAMLTrainingRequest yamlTrainingRequest = new YAMLTrainingRequest();
//        String config = getFileContent("yml/dev/config.yml");
//        String domain = getFileContent("yml/dev/domain.yml");
//        String nlu = getFileContent("yml/dev/nlu.yml");
//        String stories = getFileContent("yml/dev/stories.yml");
//        String rules = getFileContent("yml/dev/rules.yml");
//        yamlTrainingRequest
//                .config(config)
//                .domain(domain)
//                .nlu(nlu)
//                .stories(stories)
//                .rules(rules);
//
//        //training
//        rasaClient.trainModel(yamlTrainingRequest);

        //talking
//        BotAnswer botAnswer = rasaClient.sendMessage("你叫什么名字", "Davy");
//        botAnswer.forEach(answer -> {
//            System.out.println("botAnswer:" + answer.toString());
//        });

        //context
        //Context context = rasaClient.sendMessageWithContextRetrieval("推荐重疾险", "Davy");
//        ParseResult result = context.getParseResult();
//        System.out.println("intent:" + result.getIntent().toString());
//        System.out.println("entities:" + result.getEntities().toString());
//        System.out.println("slots:" + result.getEntities().toString());
        //System.out.println("context:" + context.toString());

        //context = rasaClient.sendMessageWithContextRetrieval("第一款产品详情", "Davy");
//        ParseResult result = context.getParseResult();
//        System.out.println("intent:" + result.getIntent().toString());
//        System.out.println("entities:" + result.getEntities().toString());
//        System.out.println("slots:" + result.getEntities().toString());
        //System.out.println("context:" + context.toString());

        /*ServerInformationApi usage example*/
//        ServerInformationApi serverInformationApi = new ServerInformationApi();
//        InlineResponse200 version = serverInformationApi.getVersion();
//        System.out.println("version: " + version);


        /*TrackerApi usage example*/
//        TrackerApi trackerApi = new TrackerApi();
//        Tracker conversationTracker = trackerApi.getConversationTracker("Davy", "ALL", null);
//        System.out.println("conversationTracker: " + conversationTracker);

        /*ModelApi usage example*/
//        ModelApi modelApi = new ModelApi();
//        ModelRequest modelRequest = new ModelRequest();
//        modelRequest = modelRequest.modelFile("D:\\workspace_python\\kbc-chatbot-rasa\\models\\" + "20230725-114632-forward-class.tar.gz");
//        modelApi.replaceModel(modelRequest);
    }

}
