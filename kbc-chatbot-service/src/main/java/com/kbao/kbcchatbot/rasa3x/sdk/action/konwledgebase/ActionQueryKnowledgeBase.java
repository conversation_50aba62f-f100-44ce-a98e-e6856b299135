package com.kbao.kbcchatbot.rasa3x.sdk.action.konwledgebase;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcchatbot.rasa3x.sdk.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.kbao.kbcchatbot.rasa3x.sdk.action.konwledgebase.KnowledgeBaseUtils.*;


public abstract class ActionQueryKnowledgeBase extends KnowledgeBase implements Action {
    private static final Logger logger = LoggerFactory.getLogger(ActionQueryKnowledgeBase.class);

//    protected KnowledgeBase knowledgeBase;
    private boolean useLastObjectMention;

    private final String name;

    public ActionQueryKnowledgeBase(boolean useLastObjectMention,String name) {
//        this.knowledgeBase = knowledgeBase;
        this.useLastObjectMention = useLastObjectMention;
        this.name = name;
    }

    @Override
    public String name() {
        return name;
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        String objectType = tracker.getSlotValue(SLOT_OBJECT_TYPE, String.class);
        String lastObjectType = tracker.getSlotValue(SLOT_LAST_OBJECT_TYPE, String.class);
        String attribute = tracker.getSlotValue(SLOT_ATTRIBUTE, String.class);

        boolean newRequest = !objectType.equals(lastObjectType);

        if (objectType.isEmpty()) {
            dispatcher.utterMessage("utter_ask_rephrase");
            return Collections.emptyList();
        }

        if (StringUtils.isNullOrEmpty(attribute) || newRequest) {
            return new ArrayList<>(queryObjects(dispatcher, objectType, tracker));
        } else {
            return new ArrayList<>(queryAttribute(dispatcher, objectType, attribute, tracker));
        }

    }

    private List<SlotSet> queryObjects(CollectingDispatcher dispatcher, String objectType, Tracker tracker) {
        List<String> objectAttributes = getAttributesObject(objectType);

        List<SlotSet> attributes = KnowledgeBaseUtils.getAttributeSlots(tracker, objectAttributes);

        List<Map<String, Object>> objects = getObjects(objectType, attributes);

        utterObjects(dispatcher, objectType, objects);

        if (objects.isEmpty()) {
            return KnowledgeBaseUtils.resetAttributeSlots(tracker, objectAttributes);
        }

        String keyAttribute = getKeyAttributeOfObject(objectType);

        String lastObject = objects.size() > 1 ? null : objects.get(0).get("name").toString();

        List<SlotSet> slots = new ArrayList<>();
        slots.add(new SlotSet(SLOT_OBJECT_TYPE, objectType));
        slots.add(new SlotSet(SLOT_MENTION, null));
        slots.add(new SlotSet(SLOT_ATTRIBUTE, null));
        slots.add(new SlotSet(SLOT_LAST_OBJECT, lastObject));
        slots.add(new SlotSet(SLOT_LAST_OBJECT_TYPE, objectType));

        List<Object> listedObjects = objects.stream()
                .map(obj -> obj.get(keyAttribute))
                .collect(Collectors.toList());
        slots.add(new SlotSet(SLOT_LISTED_OBJECTS, listedObjects));

        return Stream.concat(slots.stream(), KnowledgeBaseUtils.resetAttributeSlots(tracker, objectAttributes).stream())
                .collect(Collectors.toList());
    }

    private List<SlotSet> queryAttribute(CollectingDispatcher dispatcher, String objectType, String attribute,
                                         Tracker tracker) {
        String objectName = KnowledgeBaseUtils.getObjectName(tracker, getOrdinalMentionMapping(),
                useLastObjectMention);

        if (StringUtils.isNullOrEmpty(objectName) || StringUtils.isNullOrEmpty(attribute)) {
            dispatcher.utterMessage("utter_ask_rephrase");
            return Collections.singletonList(new SlotSet(SLOT_MENTION, null));
        }

        Map<String, Object> objectOfInterest = getObject(objectType, objectName);

        if (objectOfInterest == null || !objectOfInterest.containsKey(attribute)) {
            dispatcher.utterMessage("utter_ask_rephrase");
            return Collections.singletonList(new SlotSet(SLOT_MENTION, null));
        }

        String value = (String)objectOfInterest.get(attribute);

        String objectRepresentation = getRepresentationFunctionOfObject(objectType)
                .apply(objectOfInterest);

        String keyAttribute = getKeyAttributeOfObject(objectType);
        String objectIdentifier = (String)objectOfInterest.get(keyAttribute);

        utterAttributeValue(dispatcher, objectRepresentation, attribute, value);

        List<SlotSet> slots = new ArrayList<>();
        slots.add(new SlotSet(SLOT_OBJECT_TYPE, objectType));
        slots.add(new SlotSet(SLOT_MENTION, null));
        slots.add(new SlotSet(SLOT_ATTRIBUTE, null));
        slots.add(new SlotSet(SLOT_LAST_OBJECT, objectIdentifier));
        slots.add(new SlotSet(SLOT_LAST_OBJECT_TYPE, objectType));

        return slots;
    }

    public abstract void utterObjects(CollectingDispatcher dispatcher, String objectType, List<Map<String, Object>> objects);

    public abstract void utterAttributeValue(CollectingDispatcher dispatcher, String objectName, String attributeName,
                                     String attributeValue);
}