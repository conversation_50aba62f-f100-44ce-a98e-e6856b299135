package com.kbao.kbcchatbot.rasa3x.rasa;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.google.gson.reflect.TypeToken;
import com.kbao.kbcchatbot.rasa3x.connection.ApiClient;
import com.kbao.kbcchatbot.rasa3x.connection.Configuration;
import com.kbao.kbcchatbot.rasa3x.rasa.model.*;
import com.kbao.kbcchatbot.rasa3x.utils.ApiCallback;
import com.kbao.kbcchatbot.rasa3x.utils.ApiException;
import com.kbao.kbcchatbot.rasa3x.utils.ApiResponse;
import com.kbao.kbcchatbot.rasa3x.utils.Pair;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.lang.reflect.Type;
import java.net.Proxy;
import java.util.*;

@Slf4j
public class RasaClient {

    //private final static String DEFAULT_BASE_PATH = "http://localhost:5005";
    private final static String DEFAULT_CHANNEL = "rest";
    private final static String MIME_TYPE = "application/json";

    private ApiClient apiClient;

    private ModelApi modelApi;
    private DomainApi domainApi;
    private TrackerApi trackerApi;
    private ServerInformationApi serverInformationApi;
    private String basePath;
    private String channel;
    private String botPath;

    public RasaClient(String basePath) {
        this.basePath = basePath;
        this.channel = DEFAULT_CHANNEL;
        this.apiClient = Configuration.getDefaultApiClient();
        this.botPath = "/webhooks/" + this.channel + "/webhook";
        this.apiClient.setBasePath(this.basePath);
        this.apiClient.setDebugging(true);
        this.modelApi = new ModelApi();
        this.domainApi = new DomainApi();
        this.trackerApi = new TrackerApi();
        this.serverInformationApi = new ServerInformationApi(apiClient);
    }

//    public RasaClient withBasePath(String basePath) {
//        this.basePath = basePath;
//        this.apiClient.setBasePath(this.basePath);
//        return this;
//    }

    public RasaClient withChannel(String channel) {
        this.channel = channel;
        this.botPath = "/webhooks/" + this.channel + "/webhook";
        return this;
    }

    public RasaClient withAuthToken(String authToken) {
        this.apiClient.setApiKey(authToken);
        return this;
    }

    public RasaClient withJwtToken(String key, String username, String userRole) {
        Map payload = new HashMap();
        Map user = new HashMap();
        user.put("username", username);
        user.put("role", userRole);
        payload.put("user", user);
        Algorithm algorithm = Algorithm.HMAC256(key);
        Date effectiveDate = new Date(System.currentTimeMillis());
        Date expiresDate = new Date(System.currentTimeMillis() + 300000L);
        String token = JWT.create()
                //.withIssuer()
                //.withSubject("Baeldung Details")
                .withPayload(payload)
                .withIssuedAt(new Date())
                .withExpiresAt(expiresDate)
                .withJWTId(UUID.randomUUID().toString())
                .withNotBefore(effectiveDate)
                .sign(algorithm);
        this.apiClient.setBearerToken(token);
        //this.apiClient.addDefaultHeader("alg", jwtMethod);
        return this;
    }

    public RasaClient withProxy(Proxy proxy) {
        this.apiClient.setHttpClient(this.apiClient.getHttpClient().newBuilder().proxy(proxy).build());
        return this;
    }

    public BotAnswer sendMessage(String message, String sender) throws ApiException {
        UserMessage userMessage = new UserMessage().message(message).sender(sender);
        ApiResponse<BotAnswer> localVarResp = sendMessageWithHttpInfo(userMessage);
        return localVarResp.getData();
    }

    public BotAnswer sendUserMessage(UserMessage userMessage) throws ApiException {
        ApiResponse<BotAnswer> localVarResp = sendMessageWithHttpInfo(userMessage);
        return localVarResp.getData();
    }

    public Context sendMessageWithContextRetrieval(String message, String sender) throws ApiException {
        UserMessage userMessage = new UserMessage().message(message).sender(sender);
        BotAnswer botAnswer = sendUserMessage(userMessage);
        Tracker tracker = trackerApi.getConversationTracker(sender, "APPLIED", null);
        Context context = new Context();
        context.setBotAnswer(botAnswer);
        context.setLatestAction(tracker.getLatestAction());
        context.setParseResult(tracker.getLatestMessage());
        context.setSlots(tracker.getSlots());
        context.setUserMessage(userMessage);
        return context;
    }

    public void trainModel(YAMLTrainingRequest yamlTrainingRequest, String callbackUrl) throws ApiException {
        if (StringUtils.isEmpty(yamlTrainingRequest.getConfig())) {
            throw new ApiException("Missing the required parameter 'config' when calling trainModel method!");
        }
        if ((!StringUtils.isEmpty(yamlTrainingRequest.getStories())
                || !StringUtils.isEmpty(yamlTrainingRequest.getRules()))
                && StringUtils.isEmpty(yamlTrainingRequest.getDomain())) {
            throw new ApiException("Missing the required parameter 'domain' when calling trainModel method " +
                    "with stories or rules !");
        }

        //train and load the model
        modelApi.trainModelAsync(yamlTrainingRequest, true, false, callbackUrl, null);
    }

    public void replaceModel(ModelRequest modelRequest) throws ApiException {
        if (StringUtils.isEmpty(modelRequest.getModelFile())) {
            throw new ApiException("Missing the required parameter 'modelFile' when calling replaceModel method!");
        }

        modelApi.replaceModel(modelRequest);
    }

    public void trainModel(String config, String domain, String nluTrainingDataYaml,
                           String storiesTrainingDataYaml, String rulesTrainingDataYaml, String responses) throws ApiException {
        YAMLTrainingRequest yamlTrainingRequest = new YAMLTrainingRequest().config(config).domain(domain)
                .nlu(nluTrainingDataYaml).stories(storiesTrainingDataYaml).rules(rulesTrainingDataYaml)
                .responses(responses);
        trainModel(yamlTrainingRequest, "");
    }

    public void setChannel(String channel) {
        this.channel = channel;
        this.botPath = "/webhooks/" + this.channel + "/webhook";
    }

    public String getChannel() {
        return this.channel;
    }

    public String getBasePath() {
        return this.basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
        this.apiClient.setBasePath(basePath);
    }

    private ApiResponse<BotAnswer> sendMessageWithHttpInfo(UserMessage message) throws ApiException {
        okhttp3.Call localVarCall = sendMessageValidateBeforeCall(message);
        Type localVarReturnType = new TypeToken<BotAnswer>() {
        }.getType();
        return this.apiClient.execute(localVarCall, localVarReturnType);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call sendMessageValidateBeforeCall(UserMessage message) throws ApiException {

        // verify the required parameter 'sender' is set
        if (message.getSender() == null) {
            throw new ApiException("Missing the required parameter 'sender' when calling sendMessage");
        }
        if (message.getMessage() == null) {
            message.setMessage("");
        }
        okhttp3.Call localVarCall = sendMessageCall(message);
        return localVarCall;
    }

    private okhttp3.Call sendMessageCall(UserMessage message) throws ApiException {
        Object localVarPostBody = message;

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        localVarHeaderParams.put("Accept", MIME_TYPE);
        localVarHeaderParams.put("Content-Type", MIME_TYPE);

        String[] localVarAuthNames = new String[]{"JWT", "TokenAuth"};
        return apiClient.buildCall(botPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, null);
    }

    public ParseResult parseModelMessage(String message) throws ApiException {
        InlineObject inlineObject = new InlineObject();
        inlineObject.setText(message);
        //inlineObject.setMessageId();
        return modelApi.parseModelMessage(inlineObject, null);
    }

    public Tracker addConversationTrackerEvents(String conversationId, Event[] events) throws ApiException {
        return trackerApi.addConversationTrackerEvents(conversationId,events,null,null,false);
    }
    public Tracker getConversationTracker(String conversationId) throws ApiException {
        return trackerApi.getConversationTracker(conversationId,null,null);
    }

    public PredictResult predictConversationAction(String conversationId) throws ApiException {
        return trackerApi.predictConversationAction(conversationId);
    }

    public String getHealth() throws ApiException {
        return serverInformationApi.getHealth();
    }


    public RasaClient setConnectionTimeout(int timeout) {
        apiClient.setConnectTimeout(timeout);
        return this;
    }

    private class ModelLoader implements ApiCallback<File> {


        public ModelLoader() {
        }

        @Override
        public void onFailure(ApiException e, int statusCode, Map<String, List<String>> responseHeaders) {
            e.printStackTrace();
        }

        @Override
        public void onSuccess(File result, int statusCode, Map<String, List<String>> responseHeaders) {
            //log.info("Train model success>>>>fileName:{}", responseHeaders.get("filename"));
            loadModel(responseHeaders.get("filename"));
        }

        @Override
        public void onUploadProgress(long bytesWritten, long contentLength, boolean done) {

        }

        @Override
        public void onDownloadProgress(long bytesRead, long contentLength, boolean done) {

        }

        private void loadModel(List<String> fileProperties) {
            ModelRequest modelRequest = new ModelRequest();
            String modelPath = "/opt/project/models/" + fileProperties.get(0);
            //log.info("Train model success>>>>replaceModel:{}", modelPath);
            modelRequest = modelRequest.modelFile(modelPath);
            try {
                modelApi.replaceModel(modelRequest);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }
}
