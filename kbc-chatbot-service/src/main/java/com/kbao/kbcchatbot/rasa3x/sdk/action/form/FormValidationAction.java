package com.kbao.kbcchatbot.rasa3x.sdk.action.form;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public abstract class FormValidationAction extends ValidationAction {

    public abstract String name();

    public String formName() {
        return name().replace("validate_", "");
    }
    /**
    * @Description: 提取词槽
    * @Param: [dispatcher, tracker, domain]
    * @return: java.util.List<com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent>
    * @Author: husw
    * @Date: 2023/8/24 9:06
    */
    public List<AbstractEvent> extractValidationEvents(
            CollectingDispatcher dispatcher,
            Tracker tracker,
            Domain domain,
            List<String> requiredSlots
    ) {
        List<AbstractEvent> validationEvents = getValidationEvents(dispatcher, tracker, domain,requiredSlots);
        tracker.addSlots(validationEvents);

        Optional<AbstractEvent> nextSlot = nextRequestedSlot(dispatcher, tracker, domain, requiredSlots);
        nextSlot.ifPresent(validationEvents::add);

        return validationEvents;
    }

    public Optional<AbstractEvent> nextRequestedSlot(
            CollectingDispatcher dispatcher,
            Tracker tracker,
            Domain domain,
            List<String> requiredSlots
    ) {
//        List<String> requiredSlots = requiredSlots(domainSlots(domain), dispatcher, tracker, domain);
        if (requiredSlots.equals(domainSlots(domain))) {
            return Optional.empty();
        }

        List<String> missingSlots = new ArrayList<>();
        for (String slotName : requiredSlots) {
            if (tracker.getSlots().get(slotName) == null) {
                missingSlots.add(slotName);
            }
        }

        String nextSlot = missingSlots.isEmpty() ? null : missingSlots.get(0);
        return Optional.ofNullable(new SlotSet(REQUESTED_SLOT, nextSlot));
    }

    protected List<String> domainSlots(Domain domain){
        List<String> domainSlots = new ArrayList<>();
        Map<String, Object> forms = domain.getForms();
        Map<String, Object> form = (Map<String, Object>) forms.get(formName());

        if (form != null && form.containsKey("required_slots")) {
            List<String> requiredSlots = (List<String>) form.get("required_slots");
            if (requiredSlots != null) {
                domainSlots.addAll(requiredSlots);
            }
        }

        return domainSlots;
    }

    // Other abstract and helper methods

}