package com.kbao.kbcchatbot.uploadfilelog.service;

import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.uploadfilelog.dao.UploadFileLogDao;
import com.kbao.kbcchatbot.uploadfilelog.entity.UploadFileLog;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.joda.time.LocalDate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> qiuzb
 * @Description: 上传文件日志服务
 * @create 2023/5/23 16:47
 */
@Service
public class UploadFileLogService extends BaseMongoServiceImpl<UploadFileLog,String, UploadFileLogDao> {

    /**
    * @Description: 根据业务模块查询文件地址
    * @Param: [bizModule, tenantId]
    * @return: java.util.List<com.kbao.kbcchatbot.uploadfilelog.entity.UploadFileLog>
    * @Author: husw
    * @Date: 2023/11/8 11:55
    */
    public List<UploadFileLog> getFileByBizModule(String bizModule, String tenantId){
        Query query = new Query();
        query.addCriteria(Criteria.where("bizModule").is(bizModule).and("tenantId").is(tenantId));
        return this.dao.find(query);
    }

    /**
     * 保存上传文件日志信息
     * @param fileResponse 上传文件返回数据
     */
    public void saveUploadFileLog(FileUploadResponse fileResponse ,String bizModule, String fileName, String tenantId, String userId) {
        String thisDateTime = DateUtils.thisDateTime();
        UploadFileLog uploadFileLog = new UploadFileLog();
        uploadFileLog.setUploadFileUrl(fileResponse.getAbsolutePath());
        uploadFileLog.setForeignPath(fileResponse.getForeignPath());
        uploadFileLog.setBizModule(bizModule);
        uploadFileLog.setFileName(fileName);
        uploadFileLog.setFileId(fileResponse.getFileId());
        uploadFileLog.setCreateTime(thisDateTime);
        uploadFileLog.setCreateId(userId);
        uploadFileLog.setUpdateId(userId);
        uploadFileLog.setUpdateTime(thisDateTime);
        uploadFileLog.setTenantId(tenantId);
        save(uploadFileLog);
    }

    public long getToDayUploadNum(String userId, String bizModule) {
        Query query = Query.query(Criteria.where("createId").is(userId))
                .addCriteria(Criteria.where("bizModule").is(bizModule))
                .addCriteria(Criteria.where("createTime").gte(LocalDate.now().toDateTimeAtStartOfDay().toString("yyyy-MM-dd HH:mm:ss")));
        return this.dao.count(query);
    }
}
