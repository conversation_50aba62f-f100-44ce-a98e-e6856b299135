package com.kbao.kbcchatbot.elasticsearch.index.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.elasticsearch.column.bean.ColumnDTO;
import com.kbao.kbcchatbot.elasticsearch.index.bean.IndexReqVO;
import com.kbao.kbcchatbot.elasticsearch.index.dao.IndexMapper;
import com.kbao.kbcchatbot.elasticsearch.index.entity.Index;
import com.kbao.kbcchatbot.elasticsearch.search.bean.BusinessQueryDTO;
import com.kbao.kbcchatbot.constants.ElasticsearchConstants;
import com.kbao.kbcchatbot.discard.knowledgepackage.bean.KnowledgePackageQAEsAttribute;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackageQA;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.JsonLUtils;
import com.kbao.tool.util.StringUtil;
import lombok.SneakyThrows;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.GetAliasesResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.AnalyzeRequest;
import org.elasticsearch.client.indices.AnalyzeResponse;
import org.elasticsearch.client.tasks.GetTaskRequest;
import org.elasticsearch.client.tasks.GetTaskResponse;
import org.elasticsearch.cluster.metadata.AliasMetadata;
import org.elasticsearch.index.VersionType;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.index.reindex.ReindexRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.index.AliasAction;
import org.springframework.data.elasticsearch.core.index.AliasActionParameters;
import org.springframework.data.elasticsearch.core.index.AliasActions;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;

@Service
public class IndexService extends BaseSQLServiceImpl<Index, String, IndexMapper> {

    @Autowired
    private ElasticsearchOperations elasticsearchOperations;

    @Qualifier("elasticsearchClient")
    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private KnowledgePackageQAEsAttribute knowledgePackageQAEsAttribute;

    /**
     * @Description: 添加索引
     * @Param: [indexReqVO]
     * @return: void
     * @Author: 徐乐
     * @Date: 2020/11/16 17:06
     */
    @Transactional(rollbackFor = Exception.class)
    public void insert(IndexReqVO indexReqVO) {
        Index index = beforeSave(indexReqVO);
        index.setIndId(IdWorker.get32UUID());
        this.insertSelective(index);
    }

    /**
     * @Description: 修改索引
     * @Param: [indexReqVO]
     * @return: void
     * @Author: 徐乐
     * @Date: 2020/11/16 17:10
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(IndexReqVO indexReqVO) {
        Index index = beforeSave(indexReqVO);
        this.updateByPrimaryKeySelective(index);
    }

    /**
     * @Description: 删除索引
     * @Param: []
     * @return: void
     * @Author: 徐乐
     * @Date: 2020/11/16 17:47
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(IndexReqVO indexReqVO) {
        Index index = this.selectByPrimaryKey(indexReqVO.getIndId());
        String indexName = index.getIndexName();
        Map<String, Object> map = new HashMap<>();
        map.put("indexName", indexName);
        List<Index> byParam = this.selectByParam(map);
        if (EmptyUtils.isEmpty(byParam)) {
            throw new BusinessException("索引不存在");
        }
        this.delete(indexReqVO.getIndId());
    }

    /**
     * @Description: 调用分词
     * @Param: [text, analyzer, indId]
     * @return: java.util.List<org.elasticsearch.client.indices.AnalyzeResponse.AnalyzeToken>
     * @Author: 徐乐
     * @Date: 2020/12/1 18:09
     */
    @Transactional(readOnly = true)
    public List<AnalyzeResponse.AnalyzeToken> getIkAnalysisList(String text, String analyzer) {
        if (StringUtil.isNotEmpty(text)) {
            AnalyzeRequest request = AnalyzeRequest.withIndexAnalyzer(knowledgePackageQAEsAttribute.getIndexName(), analyzer, text);
            try {
                AnalyzeResponse analyze = restHighLevelClient.indices().analyze(request, RequestOptions.DEFAULT);
                return analyze.getTokens();
            } catch (IOException e) {
                e.printStackTrace();
                throw new BusinessException("获取分词失败，文本：" + text);
            }
        }
        return null;
    }

    /**
     * @Description: 重建索引
     * @Param: [indexName]
     * @return: void
     * @Author: 徐乐
     * @Date: 2020/11/23 17:37
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized void reindex(String indId, String settingJson) {
        Index index = this.selectByPrimaryKey(indId);
        String indexName = index.getIndexName();
        Map<String, Object> map = new HashMap<>();
        map.put("indexName", indexName);
        List<Index> byParam = this.selectByParam(map);
        if (EmptyUtils.isEmpty(byParam)) {
            throw new BusinessException("索引不存在");
        }

        Map<String, Object> mapping = elasticsearchOperations.indexOps(IndexCoordinates.of(indexName)).getMapping();
        //新建索引
        String currentTime = DateUtils.getCurrentTime();
        String[] split = indexName.split("\\+");
        String reindexName = split[0] + "+" + currentTime;
        index.setIndexName(reindexName);
        index.setUpdateId(BscUserUtils.getUserId());
        index.setUpdateTime(DateUtils.getCurrentDate());

        IndexOperations indexOperations = elasticsearchOperations.indexOps(IndexCoordinates.of(reindexName));
        String mappingJson = JsonLUtils.toJSon(mapping);
        Document mappings = Document.parse(mappingJson);
        Document settings = Document.parse(settingJson);
        indexOperations.create(settings);
        indexOperations.putMapping(mappings);
        //重建索引
        ReindexRequest reindexRequest = new ReindexRequest();
        //添加源
        reindexRequest.setSourceIndices(indexName);
        //添加目标
        reindexRequest.setDestIndex(reindexName);
        //全部覆盖
        reindexRequest.setDestVersionType(VersionType.INTERNAL);
        reindexRequest.setRefresh(true);

        IndexService indexService = this;
        restHighLevelClient.reindexAsync(reindexRequest, RequestOptions.DEFAULT, new ActionListener<BulkByScrollResponse>() {

            @SneakyThrows
            @Override
            public void onResponse(BulkByScrollResponse bulkResponse) {
                logger.info("重建索引成功");
                //成功的时候执行

                //删除就索引别名
                logger.info("开始删除旧的索引别名");
                AliasActions aliasActions = new AliasActions(new AliasAction.Remove(
                        AliasActionParameters.builder().withIndices(indexName).withAliases(index.getAliasName()).build()
                ));
                final boolean[] isSuc = {elasticsearchOperations.indexOps(IndexCoordinates.of(reindexName)).alias(aliasActions)};
                logger.info("结束删除旧的索引别名：" + isSuc[0]);

                //创建请求
                DeleteIndexRequest request = new DeleteIndexRequest(indexName);
                //执行请求
                restHighLevelClient.indices().deleteAsync(request, RequestOptions.DEFAULT, new ActionListener<AcknowledgedResponse>() {
                    @Override
                    public void onResponse(AcknowledgedResponse acknowledgedResponse) {
                        //新的索引创建别名
                        logger.info("开始新的索引创建别名");
                        AliasActions aliasActions = new AliasActions(new AliasAction.Add(
                                AliasActionParameters.builder().withIndices(reindexName).withAliases(index.getAliasName()).build()
                        ));
                        isSuc[0] = elasticsearchOperations.indexOps(IndexCoordinates.of(reindexName)).alias(aliasActions);
                        logger.info("结束新的索引创建别名：" + isSuc[0]);

                        //修改索引名
                        indexService.updateByPrimaryKeySelective(index);
                    }

                    @Override
                    public void onFailure(Exception e) {

                    }
                });

                /*//删除旧的索引
                logger.info("开始删除旧的索引");
                boolean isSuc = elasticsearchOperations.indexOps(IndexCoordinates.of(indexName)).delete();
                logger.info("结束删除旧的索引：" + isSuc);*/

                logger.info("重建索引成功");
            }
            @Override
            public void onFailure(Exception e) {
                logger.error("重建索引失败", e);
            }
        });
    }

    /**
     * @Description: 重建所有索引
     * @Param: [settingJson]
     * @return: org.elasticsearch.index.reindex.BulkByScrollResponse
     * @Author: 徐乐
     * @Date: 2020/12/15 10:41
     */
    public synchronized void reindexAll(String settingJson) {
        Map<String, Object> params = new HashMap<>();
        params.put("isDeleted", ElasticsearchConstants.STATUS_NOT_DELETE);
        List<Index> indices = selectByParam(params);
        indices.forEach(index -> {
            reindex(index.getIndId(), settingJson);
        });
    }

    /**
     * @Description: 获取es任务状态
     * @Param: [task]
     * @return: org.elasticsearch.tasks.TaskInfo
     * @Author: 徐乐
     * @Date: 2020/11/24 9:39
     */
    @Transactional(readOnly = true)
    public GetTaskResponse getTaskStatus(String task) {
        String[] split = task.split(":");
        GetTaskRequest getTaskRequest = new GetTaskRequest(split[0], Long.parseLong(split[1]));
        Optional<GetTaskResponse> response;
        try {
            response = restHighLevelClient.tasks().get(getTaskRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new BusinessException("获取ES任务状态失败");
        }
        return response.orElse(null);
    }

    /**
     * @Description: 基础搜索
     * @Param: [c, indexName, queryWord, interposeWord, fieldMap, analyzer]
     * @return: org.springframework.data.elasticsearch.core.SearchHits
     * @Author: 徐乐
     * @Date: 2020/11/24 14:06
     */
    @Transactional(readOnly = true)
    public SearchHits baseSearch(Class c, String aliasName, String queryWord, List<ColumnDTO> queryFields, Integer limit, List<BusinessQueryDTO> businessQuery, String conditionConcat) {
        BoolQueryBuilder boolQueryBuilder = getEsSearchQuery(queryWord, queryFields, businessQuery, conditionConcat);

        //FunctionScoreQueryBuilder.FilterFunctionBuilder[] filterFunctionBuilders = new FunctionScoreQueryBuilder.FilterFunctionBuilder[2];

        //加入新旧程度的权重排序
        //以origin这个时间点为中心，前后365天的范围内为最佳，每超过365天评分就下降
        /*GaussDecayFunctionBuilder gaussDecayFunctionBuilder = new GaussDecayFunctionBuilder(
                ElasticsearchConstants.MATERIALS_FIELD_UPDATE_TIME,
                ElasticsearchConstants.MATERIALS_SORT_UPDATE_TIME_ORIGIN,
                ElasticsearchConstants.MATERIALS_SORT_UPDATE_TIME_SCALE,
                ElasticsearchConstants.MATERIALS_SORT_UPDATE_TIME_OFFSET).setWeight(ElasticsearchConstants.MATERIALS_SORT_WEIGHT_DEFAULT);
        filterFunctionBuilders[0] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(gaussDecayFunctionBuilder);*/

        //加入点击量的权重排序
        /*String scoreScript;
        if(StringUtil.isNotEmpty(ChatbotUserUtils.getUserId()) && c.getSimpleName().equals(ElasticsearchConstants.MATERIALS_PRODUCT_BEAN_NAME)) {
            scoreScript = "int ind=doc['hitsDtoUserIdList.keyword'].indexOf('"+ ChatbotUserUtils.getUserId()+"');if(ind!=-1) {return 1+doc['hitsDtoNumList'][ind]/1.0;}else{return 1;}";
        }else {
            scoreScript = "return 1";
        }
        Script script = new Script(ScriptType.INLINE, "painless", scoreScript, Collections.emptyMap());
        ScriptScoreFunctionBuilder scriptScoreFunctionBuilder = ScoreFunctionBuilders.scriptFunction(script);;
        filterFunctionBuilders[1] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(scriptScoreFunctionBuilder);*/

        /*FunctionScoreQueryBuilder functionScoreQueryBuilder = new FunctionScoreQueryBuilder(boolQueryBuilder, filterFunctionBuilders)
                .scoreMode(FunctionScoreQuery.ScoreMode.MULTIPLY)
                .boostMode(CombineFunction.MULTIPLY);*/
        //NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().withQuery(functionScoreQueryBuilder).build();
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build();
        if (limit != null) {
            nativeSearchQuery.setMaxResults(limit);
        }
        return elasticsearchOperations.search(nativeSearchQuery, c, IndexCoordinates.of(aliasName));
    }

    /**
     * @Description: 分页搜索
     * @Param: [c, indexName, queryWord, interposeWord, fieldMap, analyzer, pageable]
     * @return: org.springframework.data.elasticsearch.core.SearchHits
     * @Author: Davy
     * @Date: 2020/12/10 18:37
     */
    /*@Transactional(readOnly = true)
    public SearchHits pageSearch(Class c, String aliasName, String queryWord, List<ColumnDTO> queryFields, Pageable pageable,List<BusinessQueryDTO> businessQuery) {
        BoolQueryBuilder boolQueryBuilder = getEsSearchQuery(queryWord, queryFields,businessQuery);
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build();
        nativeSearchQuery.setPageable(pageable);
        return elasticsearchOperations.search(nativeSearchQuery, c, IndexCoordinates.of(aliasName));
    }*/

    /**
     * 封装ES查询条件
     *
     * @param queryWord
     * @param queryFields
     * @return
     */
    private BoolQueryBuilder getEsSearchQuery(String queryWord, List<ColumnDTO> queryFields, List<BusinessQueryDTO> businessQuery, String conditionConcat) {
        //封装基础查询条件
        BoolQueryBuilder baseQueryBuilder = new BoolQueryBuilder();
        if (EmptyUtils.isNotEmpty(queryWord)) {
            queryFields.forEach(item -> {
                AbstractQueryBuilder abstractQueryBuilder = getBaseQuery(queryWord, item, conditionConcat);
                baseQueryBuilder.should(abstractQueryBuilder);
            });
        }
        //封装业务查询条件
        BoolQueryBuilder businessQueryBuilder = getBusinessQuery(businessQuery);
        //最终的查询条件
        BoolQueryBuilder finalQueryBuilder = new BoolQueryBuilder();
        //加入租户ID查询条件
        //finalQueryBuilder.must(QueryBuilders.termQuery("tenantId", tenantId));
        finalQueryBuilder.must(baseQueryBuilder);
        if (businessQueryBuilder != null) {
            finalQueryBuilder.must(businessQueryBuilder);
        }
        return finalQueryBuilder;
    }

    private BoolQueryBuilder getBusinessQuery(List<BusinessQueryDTO> businessQuery) {
        BoolQueryBuilder businessQueryBuilder = null;
        if (!CollectionUtils.isEmpty(businessQuery)) {
            businessQueryBuilder = new BoolQueryBuilder();
            for (BusinessQueryDTO businessQueryDto : businessQuery) {
                if(businessQueryDto.getValue() == null) {
                    continue;
                }
                AbstractQueryBuilder businessQueryBuilderItem;
                if (businessQueryDto.getValue() instanceof List) {
                    businessQueryBuilderItem = QueryBuilders.termsQuery(businessQueryDto.getColumnName(), (List) businessQueryDto.getValue());
                } else if (businessQueryDto.getValue() instanceof Object[]) {
                    Object[] valueArray = (Object[]) businessQueryDto.getValue();
                    businessQueryBuilderItem = QueryBuilders.termsQuery(businessQueryDto.getColumnName(), Arrays.asList(valueArray));
                } else if (businessQueryDto.getValue() instanceof Set) {
                    businessQueryBuilderItem = QueryBuilders.termsQuery(businessQueryDto.getColumnName(), (Set) businessQueryDto.getValue());
                } else {
                    businessQueryBuilderItem = QueryBuilders.termQuery(businessQueryDto.getColumnName(), businessQueryDto.getValue());
                }
                businessQueryBuilderItem = processSubFieldName(businessQueryBuilderItem, businessQueryDto.getColumnName());
                if (ElasticsearchConstants.BUSINESS_QUERY_OPERATOR_NOT_EQUALS.equals(businessQueryDto.getOperator())) {
                    businessQueryBuilder.mustNot(businessQueryBuilderItem);
                } else {
                    businessQueryBuilder.must(businessQueryBuilderItem);
                }
            }
        }
        return businessQueryBuilder;
    }

    private AbstractQueryBuilder getBaseQuery(String queryWord, ColumnDTO item, String conditionConcat) {
        AbstractQueryBuilder abstractQueryBuilder;
        String columnName = item.getName();
        String columnType = item.getType();
        String analyzer = item.getAnalyzer();
        Float fieldBoost = item.getWeight();
        //分词匹配
        if (StringUtil.isNotEmpty(analyzer)) {
            if(conditionConcat.equals(Operator.OR.name())) {
                abstractQueryBuilder = new QueryStringQueryBuilder(queryWord).field(columnName, fieldBoost).analyzer(analyzer).defaultOperator(Operator.OR).minimumShouldMatch("20%");
            }else {
                abstractQueryBuilder = new QueryStringQueryBuilder(queryWord).field(columnName, fieldBoost).analyzer(analyzer).defaultOperator(Operator.AND);
            }
        }
        //关键字匹配
        else if (ElasticsearchConstants.COLUMN_TYPE_KEYWORD.equals(columnType)) {
            abstractQueryBuilder = QueryBuilders.termQuery(columnName, queryWord).boost(fieldBoost);
        }
        //模糊匹配
        else if (ElasticsearchConstants.COLUMN_TYPE_TEXT.equals(columnType)) {
            abstractQueryBuilder = QueryBuilders.matchQuery(columnName , queryWord).boost(fieldBoost);
        }
        //其他情况
        else {
            abstractQueryBuilder = new QueryStringQueryBuilder(queryWord).field(columnName, fieldBoost);
        }
        abstractQueryBuilder = processSubFieldName(abstractQueryBuilder, columnName);
        return abstractQueryBuilder;
    }

    private AbstractQueryBuilder processSubFieldName(AbstractQueryBuilder abstractQueryBuilder, String columnName) {
        //如果查询子属性，封装成子属性查询对象
        int index = columnName.lastIndexOf(".");
        if (index != -1) {
            String path = columnName.substring(0, index);
            abstractQueryBuilder = new NestedQueryBuilder(path, abstractQueryBuilder, ScoreMode.Avg);
        }
        return abstractQueryBuilder;
    }

    /**
     * @Description: 获取所有索引以及别名
     * @Param: []
     * @return: java.lang.String[]
     * @Author: 徐乐
     * @Date: 2020/11/26 16:09
     */
    public Map<String, Set<AliasMetadata>> getIndices() {
        try {
            GetAliasesRequest request = new GetAliasesRequest();
            GetAliasesResponse getAliasesResponse = restHighLevelClient.indices().getAlias(request, RequestOptions.DEFAULT);
            return getAliasesResponse.getAliases();
        } catch (IOException e) {
            throw new BusinessException("获取索引别名失败");
        }
    }

    /**
     * @Description: save通用
     * @Param: [indexReqVO]
     * @return: com.kbao.kbcsearch.index.entity.Index
     * @Author: 徐乐
     * @Date: 2020/11/16 17:46
     */
    private Index beforeSave(IndexReqVO indexReqVO) {
        Index oldIndex = this.selectByPrimaryKey(indexReqVO.getIndId());
        //修改索引别名
        if (oldIndex != null && !oldIndex.getAliasName().equals(indexReqVO.getAliasName())) {
            throw new BusinessException("不能修改索引别名");
        }

        Integer existIndex = this.mapper.isExistIndexName(indexReqVO);
        if (existIndex != 0) {
            throw new BusinessException("索引名称已存在");
        }
        Index index = new Index();
        BeanUtils.copyProperties(indexReqVO, index);
        Date currentDate = DateUtils.getCurrentDate();
        String userId = BscUserUtils.getUserId();
        index.setUpdateId(userId);
        index.setUpdateTime(currentDate);
        if (EmptyUtils.isEmpty(indexReqVO.getIndId())) {
            index.setCreateId(userId);
            index.setCreateTime(currentDate);
        }
        return index;
    }

    @Transactional(readOnly = true)
    public Index selectForSearch(String appCode) {
        return this.mapper.selectByAppCode(appCode);
    }

    /**
     * @Description: 获取索引的映射
     * @Param: [indexReqVO]
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @Author: 徐乐
     * @Date: 2020/12/5 15:02
     */
    public Map<String, Object> getMapping(IndexReqVO indexReqVO) {
        Index index = this.selectByPrimaryKey(indexReqVO.getIndId());
        return elasticsearchOperations.indexOps(IndexCoordinates.of(index.getAliasName())).getMapping();
    }

    /**
     * @Description: 删除索引数据
     * @Param: []
     * @return: void
     * @Author: 徐乐
     * @Date: 2020/12/16 16:52
     */
    public void deleteAllData(IndexReqVO indexReqVO) {
        Index index = this.selectByPrimaryKey(indexReqVO.getIndId());
        String aliasName = index.getAliasName();
        //清空索引内容
        MatchAllQueryBuilder matchAllQueryBuilder = QueryBuilders.matchAllQuery();
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(aliasName).setQuery(matchAllQueryBuilder);
        try {
            restHighLevelClient.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new BusinessException("删除索引失败");
        }
    }

    public void heartBeat() {
        logger.info(">>>>>ES HEART BEAT START<<<<<");
        MatchQueryBuilder matchQueryBuilder = QueryBuilders
                .matchQuery("expandWord.keyword", "重大疾病")
                .analyzer(ElasticsearchConstants.ANALYZER_IK_MAX_WORD_WITH_SYNONYM);
        NativeSearchQuery build = new NativeSearchQueryBuilder()
                .withQuery(matchQueryBuilder)
                .build();
        try {
            elasticsearchOperations.search(build, KnowledgePackageQA.class, IndexCoordinates.of(knowledgePackageQAEsAttribute.getIndexName()));
            getIndices();
        }catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>ES HEART BEAT FAILED<<<<<");
        }
        logger.info(">>>>>ES HEART BEAT SUCCESS<<<<<");
    }
}
