package com.kbao.kbcchatbot.elasticsearch.search;

import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcchatbot.constants.ElasticsearchConstants;
import com.kbao.kbcchatbot.elasticsearch.column.bean.ColumnDTO;
import com.kbao.kbcchatbot.elasticsearch.column.entity.Column;
import com.kbao.kbcchatbot.elasticsearch.column.service.ColumnService;
import com.kbao.kbcchatbot.elasticsearch.index.entity.Index;
import com.kbao.kbcchatbot.elasticsearch.index.service.IndexService;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.service.QueryAnalysisService;
import com.kbao.kbcchatbot.elasticsearch.search.bean.BusinessQueryDTO;
import com.kbao.kbcchatbot.elasticsearch.search.bean.SearchQuery;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackageQA;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 搜索service抽象类
 * @Date 2020-12-4
 */
@Service
public class BaseSearchService {

    @Autowired
    public IndexService indexService;

    @Autowired
    public ColumnService columnService;

    @Autowired
    public QueryAnalysisService queryAnalysisService;

    @Autowired
    public RedisUtil redisUtil;

    /**
     * 查询物料库获取结果
     * @param clazz
     * @param searchQuery
     * @return
     */
    public SearchHits search(Class clazz, SearchQuery searchQuery, Integer limit,List<BusinessQueryDTO> businessQuery, String conditionConcat) {
        //获取索引
        Index index = indexService.selectByPrimaryKey(searchQuery.getIndId());

        //执行ES查询
        SearchHits searchHits = getSearchHits(clazz, searchQuery, index, limit,businessQuery, conditionConcat);

        return searchHits;
    }

    /**
     * 执行ES查询抽象方法
     * @param clazz
     * @param searchQuery
     * @param index
     * @return
     */
    public SearchHits getSearchHits(Class clazz, SearchQuery searchQuery, Index index, Integer limit,List<BusinessQueryDTO> businessQuery, String conditionConcat) {
        //从索引字段配置中动态生成ES检索条件
        List<ColumnDTO> columnDtoList = getColumnDtoList(searchQuery.getQuery(), index);

        //ES查询
        SearchHits<KnowledgePackageQA> searchHits = indexService.baseSearch(clazz, index.getAliasName(), searchQuery.getQuery(), columnDtoList, limit, businessQuery, conditionConcat);
        return searchHits;
    }

    /**
     * 通过查询配置生成查询条件
     * @param index
     * @return
     */
    private List<ColumnDTO> getColumnDtoList(String query, Index index) {
        List<ColumnDTO> columnDtoList = new ArrayList<>();
        //String queryColumn = queryAnalysis.getQueryColumn();
        //String[] queryColumnArray = queryColumn.split(",");
        List<Column> columnList = columnService.selectForSearch(index.getIndId(), null);
        if(CollectionUtils.isEmpty(columnList)) {
            columnList = columnService.selectForSearch(index.getIndId(), null);
        }

        for(Column column : columnList) {
            ColumnDTO columnDTO = new ColumnDTO();
            columnDTO.setName(column.getColumnName());
            columnDTO.setWeight(column.getColumnWeight());
            columnDTO.setType(column.getColumnType());
            //如果是长度小于等于设置值的查询词，采用模糊匹配策略
            if(query.length() > ElasticsearchConstants.QUERY_ANALYZER_MIN_LENGTH) {
                columnDTO.setAnalyzer(column.getAnalyzer());
            }

            columnDtoList.add(columnDTO);
        }
        return columnDtoList;
    }
}
