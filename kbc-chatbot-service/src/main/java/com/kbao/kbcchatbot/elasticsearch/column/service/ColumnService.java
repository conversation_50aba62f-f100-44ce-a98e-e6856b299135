package com.kbao.kbcchatbot.elasticsearch.column.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.constants.ElasticsearchConstants;
import com.kbao.kbcchatbot.elasticsearch.column.bean.ColumnReqVO;
import com.kbao.kbcchatbot.elasticsearch.column.bean.ColumnResVO;
import com.kbao.kbcchatbot.elasticsearch.column.dao.ColumnMapper;
import com.kbao.kbcchatbot.elasticsearch.column.entity.Column;
import com.kbao.kbcchatbot.elasticsearch.index.bean.IndexReqVO;
import com.kbao.kbcchatbot.elasticsearch.index.service.IndexService;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.service.QueryAnalysisService;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class ColumnService extends BaseSQLServiceImpl<Column, String, ColumnMapper> {

    @Autowired
    private IndexService indexService;

    @Autowired
    private QueryAnalysisService queryAnalysisService;

    /**
     * @Description: 添加索引字段
     * @Param: [columnReqVO]
     * @return: void
     * @Author: 徐乐
     * @Date: 2020/11/16 15:47
     */
    @Transactional(rollbackFor = Exception.class)
    public void insert(ColumnReqVO columnReqVO) {
        Column column = beforeSave(columnReqVO);
        column.setColumnId(IdWorker.get32UUID());
        this.insertSelective(column);
        //insertRecognitionLevel(columnReqVO.getTypeLevel(), column);
    }

    /**
     * @Description: 修改索引字段
     * @Param: [column]
     * @return: void
     * @Author: 代威
     * @Date: 2020/12/09 11:44
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(ColumnReqVO columnReqVO) {
        Column entity = selectByPrimaryKey(columnReqVO.getColumnId());
        entity.setUpdateTime(new Date());
        entity.setUpdateId(SysLoginUtils.getUserId());
        entity.setIsAnalysis(columnReqVO.getIsAnalysis());
        entity.setColumnWeight(columnReqVO.getColumnWeight());
        if (ElasticsearchConstants.COLUMN_TYPE_TEXT.equals(columnReqVO.getColumnType())) {
            entity.setAnalyzer(columnReqVO.getAnalyzer());
        }
        entity.setFieldName(columnReqVO.getFieldName());
        this.mapper.updateByPrimaryKey(entity);
        //insertRecognitionLevel(columnReqVO.getTypeLevel(), entity);
    }


    /**
     * @Description: 更新是否检索字段
     * @Param: [column]
     * @return: void
     * @Author: 代威
     * @Date: 2020/12/09 11:44
     */
    @Transactional(rollbackFor = Exception.class)
    public void setAnalyzerStatus(Column column) {
        Column entity = selectByPrimaryKey(column.getColumnId());
        entity.setIsAnalysis(column.getIsAnalysis());
        entity.setUpdateTime(new Date());
        entity.setUpdateId(SysLoginUtils.getUserId());
        this.mapper.updateByPrimaryKey(entity);
    }

    /**
     * @Description: save通用
     * @Param: [columnReqVO]
     * @return: void
     * @Author: 徐乐
     * @Date: 2020/11/16 16:00
     */
    private Column beforeSave(ColumnReqVO columnReqVO) {
        Column column = new Column();
        BeanUtils.copyProperties(columnReqVO, column);
        Date currentDate = DateUtils.getCurrentDate();
        String userId = BscUserUtils.getUserId();
        column.setUpdateId(userId);
        column.setUpdateTime(currentDate);
        column.setIsDeleted(0);
        if (EmptyUtils.isEmpty(columnReqVO.getColumnId())) {
            column.setCreateId(userId);
            column.setCreateTime(currentDate);
        }
        return column;
    }

    @Transactional(readOnly = true)
    public List<Column> selectForSearch(String indId, String[] queryColumnArray) {
        Map<String, Object> param = new HashMap<>();
        param.put("indId", indId);
        param.put("isAnalysis", ElasticsearchConstants.DICITEM_YORN_YES);
        param.put("columnIds", queryColumnArray);
        List<Column> columnList = selectByParam(param);
        return columnList;
    }

    @Transactional(readOnly = true)
    public List<Column> selectForReturn(String indId, String[] returnFieldArray) {
        Map<String, Object> param = new HashMap<>();
        param.put("indId", indId);
        param.put("isReturnField", ElasticsearchConstants.DICITEM_YORN_YES);
        param.put("columnIds", returnFieldArray);
        List<Column> columnList = selectByParam(param);
        return columnList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void generateColumn(IndexReqVO req) {
        queryAnalysisService.getMapper().deleteByIndId(req.getIndId());
        Map<String, Object> param = new HashMap<>();
        param.put("indId", req.getIndId());
        List<Column> columnList = selectByParam(param);
        if (!CollectionUtils.isEmpty(columnList)) {
            this.mapper.batchDelete(columnList);
        }

        Map<String, Object> mapping = indexService.getMapping(req);
        Map<String, Map<String, Object>> properties = (Map<String, Map<String, Object>>) mapping.get("properties");
        properties.forEach((key, value) -> {
            if (!"_class".equals(key)) {
                String type = (String) value.get("type");
                if ("nested".equals(type)) {
                    Map<String, Map<String, Object>> subProperties = (Map<String, Map<String, Object>>) value.get("properties");
                    subProperties.forEach((subKey, subValue) -> {
                        String subType = (String) subValue.get("type");
                        if ("nested".equals(subType)) {
                            Map<String, Map<String, Object>> lastSubProperties = (Map<String, Map<String, Object>>) subValue.get("properties");
                            lastSubProperties.forEach((lastSubKey, lastSubValue) -> {
                                saveColumn(req.getIndId(), key + "." + subKey + "." + lastSubKey, lastSubValue);
                            });
                        } else {
                            saveColumn(req.getIndId(), key + "." + subKey, subValue);
                        }
                    });
                } else {
                    saveColumn(req.getIndId(), key, value);
                }
            }
        });
        queryAnalysisService.getMapper().updateQueryColumn(req.getIndId());
    }

    private void saveColumn(String indexId, String name, Map<String, Object> map) {
        Column column = new Column();
        column.setColumnId(IdWorker.get32UUID());
        if (map.get("index") != null && !(Boolean) map.get("index")) {
            column.setDefaultIsAnalysis(ElasticsearchConstants.DICITEM_YORN_NO);
        } else {
            column.setDefaultIsAnalysis(ElasticsearchConstants.DICITEM_YORN_YES);
        }
        column.setIsAnalysis(column.getDefaultIsAnalysis());
        column.setColumnType((String) map.get("type"));
        column.setAnalyzer((String) map.get("analyzer"));
        column.setFieldName(name);
        column.setColumnName(name);
        column.setColumnWeight(1f);
        column.setIndId(indexId);
        column.setCreateId("SYSTEM");
        column.setCreateTime(new Date());
        column.setUpdateId(column.getCreateId());
        column.setUpdateTime(column.getCreateTime());
        column.setIsDeleted(0);
        insert(column);
    }

    public List<Column> list(Map<String, Object> params) {
        return this.mapper.selectAllSort(params);
    }

    /**
     * @Description: 分页查询
     * @Param: [req]
     * @return: com.github.pagehelper.PageInfo<com.kbao.kbcchatbot.elasticsearch.column.bean.ColumnResVO>
     * @Author: 徐乐
     * @Date: 2020/12/28 10:09
     */
    public PageInfo<ColumnResVO> pageByCondition(RequestObjectPage<Column> req) {
        PageInfo<Column> page = page(req);
        List<ColumnResVO> columnResVOList = new ArrayList<>();
        page.getList().forEach(column -> {
            ColumnResVO columnResVO = new ColumnResVO();
            BeanUtils.copyProperties(column, columnResVO);
            Map<String, Object> params = new HashMap<>();
            params.put("isDeleted", ElasticsearchConstants.STATUS_NOT_DELETE);
            params.put("columnId", column.getColumnId());
            /*List<RecognitionLevel> recognitionLevelList = recognitionLevelService.selectByParam(params);
            if (EmptyUtils.isNotEmpty(recognitionLevelList)) {
                columnResVO.setTypeLevel(recognitionLevelList.get(0).getTypeLevel());
            }*/
            columnResVOList.add(columnResVO);
        });
        PageInfo<ColumnResVO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(page, pageInfo);
        pageInfo.setList(columnResVOList);
        return pageInfo;
    }

}
