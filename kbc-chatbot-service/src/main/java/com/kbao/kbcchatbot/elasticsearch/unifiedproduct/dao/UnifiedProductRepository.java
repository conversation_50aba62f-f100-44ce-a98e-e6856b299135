package com.kbao.kbcchatbot.elasticsearch.unifiedproduct.dao;

import com.kbao.kbcchatbot.elasticsearch.unifiedproduct.entity.UnifiedProduct;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 统一产品库Repository类
 * @Date 2023-7-14
 */
@Repository
public interface UnifiedProductRepository extends ElasticsearchRepository<UnifiedProduct, String> {

    List<UnifiedProduct> findTop10ByTenantId(String tenantId);

    List<UnifiedProduct> findByProductTypeAndProductChannelAndTenantId(String productType, String productChannel, String tenantId);

    List<UnifiedProduct> findByProductTypeAndTenantId(String productType, String tenantId);
}
