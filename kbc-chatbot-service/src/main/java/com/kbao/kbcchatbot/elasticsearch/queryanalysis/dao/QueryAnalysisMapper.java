package com.kbao.kbcchatbot.elasticsearch.queryanalysis.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.entity.QueryAnalysis;
import org.apache.ibatis.annotations.Param;

public interface QueryAnalysisMapper extends BaseMapper<QueryAnalysis, String> {
    
    void deleteByIndId(@Param("indId") String indId);
    
    void updateQueryColumn(@Param("indId") String indId);
    
}