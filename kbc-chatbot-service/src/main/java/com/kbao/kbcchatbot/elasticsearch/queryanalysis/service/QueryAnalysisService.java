package com.kbao.kbcchatbot.elasticsearch.queryanalysis.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.user.dto.UserDTO;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.elasticsearch.column.entity.Column;
import com.kbao.kbcchatbot.elasticsearch.column.service.ColumnService;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.bean.QueryAnalysisReqVO;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.bean.QueryAnalysisResVO;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.dao.QueryAnalysisMapper;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.entity.QueryAnalysis;
import com.kbao.kbcchatbot.constants.ElasticsearchConstants;
import com.kbao.tool.util.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class QueryAnalysisService extends BaseSQLServiceImpl<QueryAnalysis, String, QueryAnalysisMapper> {

    @Autowired
    private ColumnService columnService;

    /**
     * @Description: 添加查询词分析配置
     * @Param: [queryAnalysisReqVo]
     * @return: void
     * @Author: 徐乐
     * @Date: 2020/11/16 13:58
     */
    @Transactional(rollbackFor = Exception.class)
    public void insert(QueryAnalysisReqVO queryAnalysisReqVo) {
        QueryAnalysis queryAnalysis = new QueryAnalysis();
        BeanUtils.copyProperties(queryAnalysisReqVo, queryAnalysis);
        Date currentDate = DateUtils.getCurrentDate();
        UserDTO user = BscUserUtils.getUser().getUser();
        queryAnalysis.setAnalysisStatus(ElasticsearchConstants.QUERY_ANALYSIS_STATUS_NORMAL);
        queryAnalysis.setIsDeleted(ElasticsearchConstants.STATUS_NOT_DELETE);
        queryAnalysis.setAnalyId(IdWorker.get32UUID());
        queryAnalysis.setCreateId(user.getUserId());
        queryAnalysis.setUpdateId(user.getUserId());
        queryAnalysis.setCreateTime(currentDate);
        queryAnalysis.setUpdateTime(currentDate);
        this.insert(queryAnalysis);
        processAfterSave(queryAnalysis);
    }

    private void processAfterSave(QueryAnalysis queryAnalysis) {
        if (ElasticsearchConstants.QUERY_ANALYSIS_STATUS_NORMAL.equals(queryAnalysis.getAnalysisStatus())) {
            Map<String, Object> param = new HashMap<>();
            param.put("indId", queryAnalysis.getIndId());
            param.put("analysisStatus", ElasticsearchConstants.QUERY_ANALYSIS_STATUS_NORMAL);
            if (queryAnalysis.getAnalyId() != null) {
                param.put("analyId_notEq", queryAnalysis.getAnalyId());
            }
            List<QueryAnalysis> queryAnalysisList = selectByParam(param);
            queryAnalysisList.forEach(item -> {
                item.setAnalysisStatus(ElasticsearchConstants.QUERY_ANALYSIS_STATUS_INVALID);
                this.updateByPrimaryKeySelective(item);
            });
        }

    }

    /**
     * @Description: 修改查询词分析配置
     * @Param: [queryAnalysisReqVo]
     * @return: void
     * @Author: 徐乐
     * @Date: 2020/11/16 13:58
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(QueryAnalysisReqVO queryAnalysisReqVo) {
        QueryAnalysis queryAnalysis = new QueryAnalysis();
        BeanUtils.copyProperties(queryAnalysisReqVo, queryAnalysis);
        Date currentDate = DateUtils.getCurrentDate();
        UserDTO user = BscUserUtils.getUser().getUser();
        queryAnalysis.setUpdateId(user.getUserId());
        queryAnalysis.setUpdateTime(currentDate);
        this.updateByPrimaryKeySelective(queryAnalysis);
        processAfterSave(queryAnalysis);
    }

    /**
    * @Description: 分页
    * @Param: [p]
    * @return: com.github.pagehelper.PageInfo<com.kbao.kbcchatbot.elasticsearch.queryanalysis.bean.QueryAnalysisResVo>
    * @Author: 徐乐
    * @Date: 2020/12/9 15:27
    */
    public PageInfo<QueryAnalysisResVO> pageByCondition(RequestObjectPage<QueryAnalysis> p) {
        PageInfo<QueryAnalysis> page = page(p);
        List<QueryAnalysis> QueryAnalysisList = page.getList();
        List<QueryAnalysisResVO> queryAnalysisResVOList = new ArrayList<>();
        QueryAnalysisList.forEach(queryAnalysis -> {
            QueryAnalysisResVO queryAnalysisResVo = new QueryAnalysisResVO();
            List<String> fieldNameList = new ArrayList<>();
            List<String> queryColumnList = Arrays.asList(queryAnalysis.getQueryColumn().split(","));
            queryColumnList.forEach(queryColumn -> {
                Column column = columnService.selectByPrimaryKey(queryColumn);
                if(column != null) {
                    fieldNameList.add(column.getFieldName());
                }
            });
            BeanUtils.copyProperties(queryAnalysis, queryAnalysisResVo);
            String fieldNames = String.join(",", fieldNameList);
            queryAnalysisResVo.setFieldNames(fieldNames);
            queryAnalysisResVOList.add(queryAnalysisResVo);
        });
        return new PageInfo<>(queryAnalysisResVOList);
    }

    public QueryAnalysis selectForSearch(String indId) {
        Map<String, Object> param = new HashMap<>();
        param.put("indId", indId);
        param.put("analysisStatus", ElasticsearchConstants.QUERY_ANALYSIS_STATUS_NORMAL);
        List<QueryAnalysis> queryAnalysisList = selectByParam(param);
        if (queryAnalysisList.size() > 0) {
            return queryAnalysisList.get(0);
        }
        return null;
    }
}
