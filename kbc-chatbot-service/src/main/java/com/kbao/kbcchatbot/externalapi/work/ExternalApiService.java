package com.kbao.kbcchatbot.externalapi.work;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.bean.ChannelBasicManualServiceVO;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.service.ChannelBasicConfigService;
import com.kbao.kbcchatbot.maas.chatconfig.service.ChatConfigApiService;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerTypeEnum;
import com.kbao.kbcchatbot.maas.chatsession.enums.ChatSessionRecordSubjectEnum;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.kbcchatbot.config.ExternalUrlConfig;
import com.kbao.kbcchatbot.customer.*;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.service.RobotManualServiceConfigService;
import com.kbao.kbcchatbot.utils.BaseUtil;
import com.kbao.kbcchatbot.utils.HttpClientUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR> qiuzb
 * @Description: 外部接口调用服务
 * @create 2023/10/23 14:46
 */
@Service
@Slf4j
public class ExternalApiService {

    @Autowired
    private ChatSessionRecordService chatSessionRecordService;

    @Autowired
    private ChatConfigApiService chatConfigApiService;

    @Autowired
    private ExternalUrlConfig externalUrlConfig;

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RobotManualServiceConfigService robotManualServiceConfigService;

    @Autowired
    private ChannelBasicConfigService channelBasicConfigService;

    /**
     * 客服系统创建工单
     *
     * @param workOrderReq 工单请求实体
     * @return 请求响应结果 status: 1是成功 0是失败
     */
    public WorkOrderVO createWorkOrder(WorkOrderReq workOrderReq) {

        if (Objects.isNull(workOrderReq)) {
            log.error("创建工单请求参数为空");
        }
        if (BaseUtil.isTestEnvironment()) {
            workOrderReq.setPhone("18515585250");
            workOrderReq.setName("马巧玲");
//            workOrderRequest.setPhone("13018061087");
//            workOrderRequest.setName("胡盛威");
//            WorkOrderVO workOrderVO = new WorkOrderVO();
//            workOrderVO.setId("111112222222");
//            return workOrderVO;
        }
        // 发送请求
        log.info("createWorkOrder={}", JSONObject.toJSONString(workOrderReq));
        String responseStr = httpClientUtil.postJson(externalUrlConfig.getCreateWorkOrderUrl(), JSONObject.toJSONString(workOrderReq));
        log.info("createWorkOrder={}", responseStr);

        // 解析响应结果
        if (StringUtils.isNotBlank(responseStr)) {
            JSONObject responseJson = JSONObject.parseObject(responseStr);
            String status = MapUtils.getString(responseJson, "status");
            if (!Objects.equals(status, "1")) {
                throw new BusinessException("创建工单失败");
            }
            JSONObject respData = responseJson.getJSONObject("data");
            return JSONObject.parseObject(JSONObject.toJSONString(respData), WorkOrderVO.class);
        }
        return null;
    }


    public TransferVO transferToHumanChat(TransferToHumanChatReq transferToHumanChatReq) {

        String skillMarker = transferToHumanChatReq.getSkillMarker();
        String userId = transferToHumanChatReq.getUserId();

//        String generateKey = redisUtil.generateKey(userId);
//        // 设置redis过期时间为3个小时
//        redisUtil.set(generateKey, skillMarker, 3000 * 60 * 60);


        TenantConfigVO skillTenantConfig = getSkillTenantConfig(skillMarker);
        if (Objects.isNull(skillTenantConfig)) {
            throw new BusinessException("获取租户配置信息失败");
        }
        //查询技能组配置编码
//        RobotManualServiceConfig serviceConfig = robotManualServiceConfigService.getMapper().selectProdByRobotId(ChatUserUtil.getUser().getRobotId());
        ChannelBasicManualServiceVO serviceConfig = channelBasicConfigService.manualServiceFind(ChatUserUtil.getUser().getChannelCode());
        Assert.notNull(serviceConfig, "机器人转人工配置不存在");

        getDtUserInfo(userId,serviceConfig.getCustServPlatAddr());

        SkillConfigVO skillConfig = getSkillConfig(skillMarker);
        if (Objects.isNull(skillConfig)) {
            throw new BusinessException("获取技能组信息失败");
        }
        //查询用户的SessionId
//        String sessionId = largeModelService.getSessionId(userId, ChatUserUtil.getUser().getRobotCode());

        SessionInitializationReq sessionInitializationReq = new SessionInitializationReq();
        sessionInitializationReq.setPhone(userId);
        sessionInitializationReq.setName("visitor");
        sessionInitializationReq.setSource("SMART_CHAT");
        sessionInitializationReq.setChannel(skillTenantConfig.getChannel());
        sessionInitializationReq.setBus(serviceConfig.getCustServPlatAddr());
        sessionInitializationReq.setScreenShotType("pp");
        sessionInitializationReq.setOpenId(userId);
        sessionInitializationReq.setRobotSessionId(ChatUserUtil.getUser().getSessionId());
//        sessionInitializationReq.setRobotSessionId(sessionId);
        sessionInitializationReq.setChatInlet(skillTenantConfig.getChannel());
        SessionInitializationVO sessionInitializationVO = initSessionInitialization(sessionInitializationReq);

        String urlWsV = sessionInitializationVO.getUrl_ws_v();
        String params = "sid=" + sessionInitializationVO.getSid() + "&" + "ry=" + "0";

        String initSocketResp = httpGet(params, urlWsV);
        // initSocketResp转成json
        JSONObject initSocketRespJson = JSONObject.parseObject(initSocketResp);
        String url = initSocketRespJson.getString("url");
        String sid = initSocketRespJson.getString("sid");

        TransferVO transferVO = new TransferVO();
        transferVO.setBus(serviceConfig.getCustServPlatAddr());
        transferVO.setSid(sid);
        transferVO.setChannel(skillTenantConfig.getChannel());
        transferVO.setSkillId(skillConfig.getSkillGroupId());
        transferVO.setUrl_ws_v(url);
        transferVO.setIsExist(sessionInitializationVO.isExist());
        transferVO.setWorkTimeFlag(isWorkTime(skillTenantConfig.getWorkTimeModel()));
//        // todo 测试写死非工作时间
//        transferVO.setWorkTimeFlag(false);
        if (StringUtils.isNotBlank(sessionInitializationVO.getSeatNo())) {
            transferVO.setSeatNo(sessionInitializationVO.getSeatNo());
        }

        if (StringUtils.isNotBlank(sessionInitializationVO.getSeatNo())) {
            transferVO.setNickName(getUserByLoginName(sessionInitializationVO.getSeatNo()));
        }


        return transferVO;

    }


    @SneakyThrows
    public TenantConfigVO getSkillTenantConfig(String skillMarker) {
        // 定义请求参数
        String skillTenantConfigUrl = externalUrlConfig.getSkillTenantConfigUrl();
        String url = "https://kefu.kbao123.com/yongLe/business/getSkillTenantConfig";
        String params = "skillMarker=" + URLEncoder.encode(skillMarker, "UTF-8");
        String httpGet = httpGet(params, url);

        // 把JSON字符串转换为Tenant对象，返回结果
        Gson gson = new Gson();
        return gson.fromJson(httpGet, TenantConfigVO.class);
    }

    @SneakyThrows
    public void getDtUserInfo(String userId, String code) {
        // 定义请求参数
        String bus = "bus=" + code + "&" + "custId=" + userId;
//        String custId = "custId=" + URLEncoder.encode(userId, "UTF-8");
        httpGet(bus, externalUrlConfig.getDtUserInfoUrl());
    }

    @SneakyThrows
    public SkillConfigVO getSkillConfig(String skillMarker) {
        // 拼接参数
        String params = "skillMarker=" + URLEncoder.encode(skillMarker, "UTF-8");
        // 发送GET请求获取技能组信息
        String httpGet = httpGet(params, externalUrlConfig.getSkillConfigUrl());
        // 如果返回结果为空，抛出异常
        if (StringUtils.isBlank(httpGet)) {
            throw new BusinessException("获取技能组信息失败");
        }
        // 把JSON字符串转换为Tenant对象，返回结果
        Gson gson = new Gson();
        return gson.fromJson(httpGet, SkillConfigVO.class);
    }


    public SessionInitializationVO initSessionInitialization(SessionInitializationReq sessionInitializationReq) {

        if (Objects.isNull(sessionInitializationReq)) {
            log.error("初始化会话请求参数为空");
        }

        // 发送请求
        log.info("initSessionInitialization={}", sessionInitializationReq);
        String responseStr = httpClientUtil.postJson(externalUrlConfig.getInitUrl(), JSONObject.toJSONString(sessionInitializationReq));
        log.info("initSessionInitialization={}", responseStr);

        // 解析响应结果
        if (StringUtils.isNotBlank(responseStr)) {
            JSONObject responseJson = JSONObject.parseObject(responseStr);
            String status = MapUtils.getString(responseJson, "code");
            if (!Objects.equals(status, "0")) {
                throw new BusinessException("初始化会话失败");
            }
            JSONObject respData = responseJson.getJSONObject("result");
            return JSONObject.parseObject(JSONObject.toJSONString(respData), SessionInitializationVO.class);
        }
        return null;
    }


    public String httpGet(String paramsOne, String url) {
        log.info("getSkillTenantConfig params={} , url={}", paramsOne, url);
        try {
            // 拼接完整的请求地址
            url = url + "?" + paramsOne;
            // 创建一个URL对象
            URL obj = new URL(url);
            // 打开一个HTTP连接
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();
            // 设置请求方式为GET
            con.setRequestMethod("GET");
            // 获取响应码
            int responseCode = con.getResponseCode();
            // 如果响应码为200，表示成功
            if (responseCode == 200) {
                // 创建一个缓冲读取器，从连接中读取输入流
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                // 定义一个字符串，用来存放每一行的数据
                String inputLine;
                // 定义一个字符串缓冲区，用来拼接数据
                StringBuilder response = new StringBuilder();
                // 循环读取每一行，直到为空
                while ((inputLine = in.readLine()) != null) {
                    // 把每一行的数据追加到字符串缓冲区中
                    response.append(inputLine);
                    log.info("getSkillTenantConfig response={}", response);
                }
                // 关闭缓冲读取器
                in.close();
                // 把字符串缓冲区转换为字符串，返回结果
                return response.toString();
            } else {
                // 如果响应码不为200，表示失败，返回空
                return null;
            }
        } catch (Exception e) {
            log.error("getSkillTenantConfig error={}", e.getMessage());
            return null;
        }
    }


    public void chatMessagePush(ChatMessagePush chatMessagePush) {
        log.info("chatMessagePush={}", JSON.toJSONString(chatMessagePush));

        if (Objects.isNull(chatMessagePush)) {
            log.error("chatMessagePush is null");
            return;
        }

        String content = chatMessagePush.getContent();
        if (chatMessagePush.getContentType().equals("system") && !Objects.equals(content, "AgentDestroyMeeting")){
            return;
        }


        if (StringUtils.isNotBlank(chatMessagePush.getType()) && !Objects.equals(chatMessagePush.getType(), "@imCloud_subject_meeting_evaluate@")) {
            String userId = chatMessagePush.getUserId();
            String sessionId = chatMessagePush.getSessionId();
            Long channelId = null;
            String userName = null;
            ChatSessionRecord lastRecordByUserId = chatSessionRecordService.getLastRecordByUserId(userId);

            // 如果sessionId为空则从lastRecordByUserId里获取赋值
            if (StringUtils.isBlank(sessionId) && Objects.nonNull(lastRecordByUserId)) {
                sessionId = lastRecordByUserId.getSessionId();
            }

            // lastRecordByUserId不为空的则赋值channelId和userName
            if (Objects.nonNull(lastRecordByUserId)) {
                channelId = lastRecordByUserId.getChannelId();
                userName = lastRecordByUserId.getUserName();
            }

            if (content.equals("AgentDestroyMeeting")){
                content = "感谢您的咨询，如果您有任何其他问题，欢迎随时再次联系我。祝您一切顺利，再见！";
            }

            JSONObject sender = new JSONObject();
            JSONObject jsonObject = new JSONObject();
            sender.put("channel", "chat");
            jsonObject.put("contentHtml", content);
            jsonObject.put("contentType", "1");
            jsonObject.put("nickName", chatMessagePush.getNickName());
            jsonObject.put("sender", sender);


            // 保存人工客服记录
//            chatConfigApiService.saveRobotRecord(channelId, jsonObject.toJSONString(), ChatAnswerTypeEnum.CUSTOMER_SERVICE_ANSWER.getKey(),
//                    null, null, null, null,
//                    sessionId, userId, userName, null, null, ChatSessionRecordSubjectEnum.ROBOT.getKey(), null);

            chatSessionRecordService.savePushMsgRecord(channelId, jsonObject.toJSONString(), ChatAnswerTypeEnum.CUSTOMER_SERVICE_ANSWER.getKey(),
                    null, null, null, null,
                    sessionId, userId, userName, null, null, ChatSessionRecordSubjectEnum.ROBOT.getKey(), null
            ,chatMessagePush.getSid(),chatMessagePush.getMid(),chatMessagePush.getCreateTime());
        }
    }


    /**
     * 根据技能组获取工作时间 在工作时间返回true
     */
    public boolean isWorkTime(String workTimeModel) {
        log.info("isWorkTime skillMarker={}", workTimeModel);
        Map<String, Object> req = new HashMap<>();
        req.put("workTimeModel", workTimeModel);
        String resp = sendGetRequestJSON(externalUrlConfig.getWorkTimeUrl(), req);
        log.info("isWorkTime resp={}", resp);
        JSONObject jsonObject = JSON.parseObject(resp);
        String result = (String) jsonObject.get("result");
        return result.equals("true");
    }


    /**
     * 留言功能
     */
    public void createLeavingMessage(LeaveMessageReq leaveMessageReq) {

        if (Objects.isNull(leaveMessageReq)) {
            return;
        }

        log.info("createLeavingMessage req = {}", leaveMessageReq);
        Map<String, String> createLeavingMessageMap;
        try {
            createLeavingMessageMap = BeanUtils.describe(leaveMessageReq);
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
        String postJson = sendPostByFormData(externalUrlConfig.getCreateLeavingMessageUrl(), createLeavingMessageMap);
        log.info("createLeavingMessage resp = {}", postJson);
        if (StringUtils.isNotBlank(postJson)) {
            if (!Objects.equals(JSONObject.parseObject(postJson).getString("code"), "0")){
                log.error("createLeavingMessage error={}", postJson);
                throw new RuntimeException("createLeavingMessage error");
            }
        }

        chatConfigApiService.saveRobotRecord(ChatUserUtil.getUser().getChannelId(), ChatUserUtil.getUser().getChannelCode(),leaveMessageReq.getLeavMessageContent(), ChatAnswerTypeEnum.CUSTOMER_LEAVING_MESSAGE.getKey(), null,
                null, null, null, ChatUserUtil.getUser().getSessionId(), ChatUserUtil.getUserId(), ChatUserUtil.getUser().getUserName(), null, null, ChatSessionRecordSubjectEnum.USER.getKey(), null);
    }


    /**
     * 写一个HTTP get请求的 传输JSON数据的方法
     */
    public String sendGetRequestJSON(String url, Map<String, Object> json) {
        try {
            // 设置请求的URL
            URL apiUrl = new URL(url);
            // 创建连接
            HttpURLConnection connection = (HttpURLConnection) apiUrl.openConnection();
            // 设置请求方法为GET
            connection.setRequestMethod("GET");
            // 设置请求头部为JSON数据
            connection.setRequestProperty("Content-Type", "application/json");
            // 设置请求体
            connection.setDoOutput(true);
            // json转为json字符串
            String jsonStr = JSONObject.toJSONString(json);
            connection.getOutputStream().write(jsonStr.getBytes(StandardCharsets.UTF_8));

            // 打开连接
            connection.connect();

            // 获取响应状态码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 读取响应数据
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                // 处理响应数据并返回
                return response.toString();
            }
        } catch (Exception e) {
            log.error("sendGetRequest error={}", e.getMessage());
        }
        return null;
    }

    public String getUserByLoginName(String loginName) {
        log.info("getUserByLoginName loginName={}", loginName);
        String queryName = "loginName=" + loginName;
        String userInfo = httpGet(queryName, externalUrlConfig.getLoginInfoUrl());
        JSONObject jsonObject = JSON.parseObject(userInfo);
        if (Objects.nonNull(jsonObject) && Objects.nonNull(jsonObject.getJSONObject("data"))){
            return jsonObject.getJSONObject("data").getString("nickName");
        }
        return "大童客服";
    }

    // 满意度评价接口
    @SneakyThrows
    public String submitSurvey(SurveyReq surveyReq) {
        log.info("submitSurvey surveyReq={}", surveyReq);
        // 将surveyReq转成Map
        Map<String, String> surveyReqMap = BeanUtils.describe(surveyReq);
        String surveyResp = sendPostByFormData(externalUrlConfig.getSurveyUrl(), surveyReqMap);
        log.info("submitSurvey surveyResp={}", surveyResp);
        JSONObject jsonObject = JSON.parseObject(surveyResp);
        if (jsonObject.getInteger("code") != 0) {
            log.error("满意度评价失败submitSurvey={}", surveyResp);
            throw new BusinessException("满意度评价失败");
        }
        return jsonObject.toJSONString();
    }


    /**
     * 使用表单数据发送POST请求
     *
     * @param url    请求的URL地址
     * @param params 请求参数，以Map形式存储
     * @return 返回请求结果
     */
    public String sendPostByFormData(String url, Map<String, String> params) {
        StringBuilder result = new StringBuilder();
        HttpURLConnection conn = null;
        try {
            URL realUrl = new URL(url);
            conn = (HttpURLConnection) realUrl.openConnection();
            conn.setRequestMethod("POST");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

            try (PrintWriter out = new PrintWriter(conn.getOutputStream())) {
                StringJoiner sj = new StringJoiner("&");
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    sj.add(URLEncoder.encode(entry.getKey(), "UTF-8") + "=" + URLEncoder.encode(entry.getValue(), "UTF-8"));
                }
                out.print(sj);
            }

            try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = in.readLine()) != null) {
                    result.append(line);
                }
            }
        } catch (IOException e) {
            log.error("sendPost error", e);
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
        return result.toString();
    }

}
