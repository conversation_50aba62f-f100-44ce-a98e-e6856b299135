package com.kbao.kbcchatbot.externalapi.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcchatbot.config.ExternalUrlConfig;
import com.kbao.kbcchatbot.config.LargeModelConfig;
import com.kbao.kbcchatbot.maas.chatsession.bean.ChatSessionIdReqVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.ChatSessionQueryModelVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatSessionConfigReqVO;
import com.kbao.kbcchatbot.maas.product.bean.ProductLabelVO;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeDelVO;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeEmbeddingVO;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitReqVO;
import com.kbao.kbcchatbot.utils.HttpClientUtil;
import com.kbao.kbcchatbot.utils.RSAUtil;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.JsonLUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kbao.kbcchatbot.constants.CacheConstant.LARGE_MODEL_MQTT_TOKEN;
import static com.kbao.kbcchatbot.constants.CacheConstant.LARGE_MODEL_TOKEN;

/**
 * @program: kbc-chatbot
 * @description: 模型平台接口
 * @author: husw
 * @create: 2024-06-07 09:47
 **/
@Service
@Slf4j
public class MaasHttpService {

    @Autowired
    private ExternalUrlConfig externalUrlConfig;

    @Autowired
    private LargeModelConfig largeModelConfig;

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Autowired
    private RedisUtil redisUtil;


    private static final long TOKEN_EXPIRE_TIME = 60*50;

    private static final long MQTT_TOKEN_EXPIRE_TIME = 60*5;

    public String getToken(String code, String secretKey){
//        String code = largeModelConfig.getCode();
        String redisKey = redisUtil.generateKey(MessageFormat.format(LARGE_MODEL_TOKEN,code));
        String token = (String)redisUtil.get(redisKey);
        if (EmptyUtils.isNotEmpty(token)){
            return token;
        }
        ChatSessionConfigReqVO configReqVO = new ChatSessionConfigReqVO();
        String encryptCotent = RSAUtil.encrypt(code + "#" + secretKey, largeModelConfig.getPublicKey());
        configReqVO.setT(encryptCotent);
        String result = httpClientUtil.postJson(externalUrlConfig.getQueryToken(), JSONObject.toJSONString(configReqVO));
        JSONObject respObj = JSON.parseObject(result);
        if(ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            String massToken = respObj.getString("data");
            String secretToken = code + "#" + massToken;
            secretToken = RSAUtil.encrypt(secretToken, largeModelConfig.getPublicKey());;
            redisUtil.set(redisKey,secretToken,TOKEN_EXPIRE_TIME);
            return secretToken;
        }else {
            throw new BusinessException("获取大模型token失败！认证编码："+code);
        }
    }
    /**
    * @Description: 获取mqttToken
    * @Param: [robotCode]
    * @return: com.kbao.kbcchatbot.externalapi.model.LargeModelMqttTokenVO
    * @Author: husw
    * @Date: 2024/8/22 14:49
    */
    public LargeModelMqttTokenVO getMqttToken(){
        String redisKey = redisUtil.generateKey(LARGE_MODEL_MQTT_TOKEN);
        LargeModelMqttTokenVO mqttTokenVO = (LargeModelMqttTokenVO)redisUtil.get(redisKey);
        if (EmptyUtils.isNotEmpty(mqttTokenVO)){
            return mqttTokenVO;
        }
        String result = httpClientUtil.postJson(externalUrlConfig.getQueryMqttToken(),"{}",getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.error("获取mqttToken返回结果：{}",respObj);
        if(ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            LargeModelMqttTokenVO msg = respObj.getJSONObject("msg").toJavaObject(LargeModelMqttTokenVO.class);
            redisUtil.set(LARGE_MODEL_MQTT_TOKEN,msg,MQTT_TOKEN_EXPIRE_TIME);
            return msg;
        }else {
            throw new BusinessException("获取mqttToken失败");
        }
    }
    /**
    * @Description: 外部系统获取mqttToken
    * @Param: [code, secretKey]
    * @return: com.kbao.kbcchatbot.externalapi.model.LargeModelMqttTokenVO
    * @Author: husw
    * @Date: 2025/6/5 16:33
    */
    public LargeModelMqttTokenVO getMqttToken(String code, String secretKey){
        String redisKey = redisUtil.generateKey(LARGE_MODEL_MQTT_TOKEN);
        LargeModelMqttTokenVO mqttTokenVO = (LargeModelMqttTokenVO)redisUtil.get(redisKey);
        if (EmptyUtils.isNotEmpty(mqttTokenVO)){
            return mqttTokenVO;
        }
        String result = httpClientUtil.postJson(externalUrlConfig.getQueryMqttToken(),"{}",getHeader(code,secretKey));
        JSONObject respObj = JSON.parseObject(result);
        log.error("获取mqttToken返回结果：{}",respObj);
        if(ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            LargeModelMqttTokenVO msg = respObj.getJSONObject("msg").toJavaObject(LargeModelMqttTokenVO.class);
            redisUtil.set(LARGE_MODEL_MQTT_TOKEN,msg,MQTT_TOKEN_EXPIRE_TIME);
            return msg;
        }else {
            throw new BusinessException("获取mqttToken失败");
        }
    }
    /**
    * @Description: 获取SessionId
    * @Param: [userId, robotCode]
    * @return: java.lang.String
    * @Author: husw
    * @Date: 2024/10/8 11:40
    */
    public String getSessionId(String userId, String channelCode){
        ChatSessionIdReqVO sessionIdReqVO = new ChatSessionIdReqVO();
        sessionIdReqVO.setUserId(userId);
        sessionIdReqVO.setChannelCode(channelCode);
        log.info("获取session入参：{}",sessionIdReqVO);
        String result = httpClientUtil.postJson(externalUrlConfig.getQuerySessionId(), JSONObject.toJSONString(sessionIdReqVO),getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info("获取session返回结果：{}",respObj);
        if(ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            return respObj.getString("data");
        }else {
            throw new BusinessException("获取sessionId失败");
        }
    }

    /**
     * @Description: 查询结果
     * @Param: [question]
     * @return: java.lang.String
     * @Author: husw
     * @Date: 2024/6/7 9:56
     */
    public JSONObject queryAnswerStream(String question,String userId,String recordId){
        ChatSessionQueryModelVO modelVO = new ChatSessionQueryModelVO();
        modelVO.setQuery(question);
        modelVO.setUserId(userId);
        modelVO.setRecordId(recordId);
        log.info("大模型流式查询结果入参：{}",modelVO);
        String result = httpClientUtil.postJson(externalUrlConfig.getQaSearchStream(), JSONObject.toJSONString(modelVO),getHeader());
        log.info("大模型流式查询结果返参：{}",result);
        JSONObject respObj = JSON.parseObject(result);
        if(ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            return respObj.getJSONObject("data");
        }else {
            return null;
        }
    }
    /**
    * @Description: 删除数据
    * @Param: [delVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/6/7 9:56
    */
    public void embeddingDel(RobotKnowledgeDelVO delVO){
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(delVO));
        String result = httpClientUtil.postJson(externalUrlConfig.getEmbeddingDelUrl(), JSONObject.toJSONString(delVO), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("删除大模型数据异常:{}",respObj.getInteger("msg"));
            throw new BusinessException("删除大模型数据异常！"+respObj.getInteger("msg"));
        }
    }
    /**
    * @Description: 新增数据
    * @Param: [embeddingVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/6/7 9:56
    */
    public void embeddingModel(RobotKnowledgeEmbeddingVO embeddingVO){
        if (EmptyUtils.isNotEmpty(embeddingVO.getDatas())){
            log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(embeddingVO));
            String result = httpClientUtil.postJson(externalUrlConfig.getEmbeddingUrl(), JSONObject.toJSONString(embeddingVO), getHeader());
            JSONObject respObj = JSON.parseObject(result);
            log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
            if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
                log.error("新增大模型数据异常:{}",respObj.getInteger("msg"));
                throw new BusinessException("新增大模型数据异常！"+respObj.getInteger("msg"));
            }
        }
    }
    /**
    * @Description: 查询纠错次
    * @Param: [embeddingVO]
    * @return: void
    * @Author: husw
    * @Date: 2025/1/15 17:41
    */
    public String searchCorrection(LargeModelCorrectionReq correctionReq){
        String url = externalUrlConfig.getMaasBaseUrl() + externalUrlConfig.getSearchCorrection();
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(correctionReq));
        String result = httpClientUtil.postJson(url, JSONObject.toJSONString(correctionReq), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("Maas异常:{}:{}",Thread.currentThread().getStackTrace()[1].getMethodName(),respObj.getInteger("msg"));
            throw new BusinessException("Maas异常！"+respObj.getInteger("msg"));
        }
        return respObj.getString("data");
    }

    /**
     * @Description: 查询推荐产品
     * @Param: [embeddingVO]
     * @return: void
     * @Author: husw
     * @Date: 2025/1/15 17:41
     */
    public List<String> productRecommend(LargeModelRecommendReq recommendReq){
        String url = externalUrlConfig.getMaasBaseUrl() + externalUrlConfig.getProductRecommend();
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(recommendReq));
        String result = httpClientUtil.postJson(url, JSONObject.toJSONString(recommendReq), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("Maas异常:{}:{}",Thread.currentThread().getStackTrace()[1].getMethodName(),respObj.getInteger("msg"));
            throw new BusinessException("Maas异常！"+respObj.getInteger("msg"));
        }
        String data = respObj.getString("data");
        return JSONArray.parseArray(data,String.class);
    }

    /**
    * @Description: 更新对话记录（ES）
    * @Param: [req]
    * @return: void
    * @Author: husw
    * @Date: 2025/4/17 13:34
    */
    public void updateChatRecord(MaasUpdateChatReq req){
        String url = externalUrlConfig.getMaasBaseUrl() + externalUrlConfig.getUpdateChatRecord();
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(req));
        String result = httpClientUtil.postJson(url, JSONObject.toJSONString(req), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("Maas异常:{}:{}",Thread.currentThread().getStackTrace()[1].getMethodName(),respObj.getInteger("msg"));
            throw new BusinessException("Maas异常！"+respObj.getInteger("msg"));
        }
    }

    /**
    * @Description: 查询语音文件
    * @Param: [req]
    * @return: void
    * @Author: husw
    * @Date: 2025/4/17 16:29
    */
    public String getTextToVoice(MaasToVoiceReq req){
        String url = externalUrlConfig.getMaasBaseUrl() + externalUrlConfig.getTextToVoice();
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(req));
        String result = httpClientUtil.postJson(url, JSONObject.toJSONString(req), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("Maas异常:{}:{}",Thread.currentThread().getStackTrace()[1].getMethodName(),respObj.getInteger("msg"));
            throw new BusinessException("Maas异常！"+respObj.getInteger("msg"));
        }
        return respObj.getString("data");
    }

    public String cleanSession(String userId, String channelCode){
        LargeModelCleanSession cleanSession = new LargeModelCleanSession();
        cleanSession.setUserId(userId);
        cleanSession.setChannelCode(channelCode);
        String url = externalUrlConfig.getMaasBaseUrl() + externalUrlConfig.getCleanSession();
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(cleanSession));
        String result = httpClientUtil.postJson(url, JSONObject.toJSONString(cleanSession), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("Maas异常:{}:{}",Thread.currentThread().getStackTrace()[1].getMethodName(),respObj.getInteger("msg"));
            throw new BusinessException("Maas异常！"+respObj.getInteger("msg"));
        }
        return respObj.getString("data");
    }

    public LargeModelListVO getChatRecord(PageRequest<LargeModelSyncVO> pageRequest){
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(pageRequest));
        String result = httpClientUtil.postJson(externalUrlConfig.getQueryChatRecord(), JSONObject.toJSONString(pageRequest), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("同步大模型数据异常:{}",respObj.getInteger("msg"));
            throw new BusinessException("同步大模型数据异常！"+respObj.getInteger("msg"));
        }
        return JSONObject.toJavaObject(respObj.getJSONObject("data"), LargeModelListVO.class);
    }

    public JSONObject getTrainRecordList(PageRequest<TrainSceneInitReqVO> pageRequest){
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(pageRequest));
        String result = httpClientUtil.postJson(externalUrlConfig.getTrainRecordList(), JSONObject.toJSONString(pageRequest), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("同步大模型数据异常:{}",respObj.getInteger("msg"));
            throw new BusinessException("同步大模型数据异常！"+respObj.getInteger("msg"));
        }
        return respObj.getJSONObject("data");
    }

    public void trainAddCache(TrainSceneInitReqVO reqVO){
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(reqVO));
        String result = httpClientUtil.postJson(externalUrlConfig.getTrainAddCache(), JSONObject.toJSONString(reqVO), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("添加记录缓存成功:{}",respObj.getInteger("msg"));
            throw new BusinessException("添加记录缓存异常！"+respObj.getInteger("msg"));
        }
    }

    public void getProductLabel(ProductLabelVO reqVO){
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(reqVO));
        String result = httpClientUtil.postJson(externalUrlConfig.getProductLabel(), JSONObject.toJSONString(reqVO), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("获取产品标签:{}",respObj.getInteger("msg"));
            throw new BusinessException("获取产品标签异常！"+respObj.getInteger("msg"));
        }
    }

    public String speakToText(String userId,String url){
        LlmSpeakParamVO paramVO = new LlmSpeakParamVO();
        paramVO.setUserId(userId);
        paramVO.setUrl(url);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", JsonLUtils.toJSon(paramVO));
        String result = httpClientUtil.postJson(externalUrlConfig.getSpeakToText(), JSONObject.toJSONString(paramVO), getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("语音转文本:{}",respObj.getInteger("msg"));
            throw new BusinessException("语音转文本异常！"+respObj.getInteger("msg"));
        }
        return respObj.getString("data");
    }

    public JSONObject postMaasApi(Map<String, Object> paramMap, String url){
        JSONObject respObj = doPostMaasApi(url, JSONObject.toJSONString(paramMap));
        return respObj.getJSONObject("data");
    }

    public JSONArray postMaasArrayApi(Map<String, Object> paramMap, String url){
        JSONObject respObj = doPostMaasApi(url, JSONObject.toJSONString(paramMap));
        return respObj.getJSONArray("data");
    }

    private JSONObject doPostMaasApi(String url, String param){
        String result = httpClientUtil.postJson(externalUrlConfig.getMaasBaseUrl() + url, param, getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("doPostMaasApi error, url:{} msg:{}", url,respObj.getString("msg"));
            throw new BusinessException(respObj.getString("msg"));
        }
        return respObj;
    }

    public JSONObject doPostMapApi(MultipartFile file, Map<String, Object> paramMap, String url){
        String result = httpClientUtil.postFileMap(externalUrlConfig.getMaasBaseUrl() + url, file, paramMap, getHeader());
        JSONObject respObj = JSON.parseObject(result);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", respObj);
        if(!ResultStatusEnum.isSuccess(respObj.getInteger("code"))){
            log.error("doPostMaasApi error, url:{} msg:{}", url,respObj.getString("msg"));
            throw new BusinessException(respObj.getString("msg"));
        }
        return respObj;
    }

    private Map<String, Object> getHeader(){
        Map<String, Object> header = new HashMap<>();
        header.put("token",getToken(largeModelConfig.getCode(), largeModelConfig.getSecretKey()));
        return header;
    }
    private Map<String, Object> getHeader(String code, String secretKey){
        Map<String, Object> header = new HashMap<>();
        header.put("token",getToken(code, secretKey));
        return header;
    }

}
