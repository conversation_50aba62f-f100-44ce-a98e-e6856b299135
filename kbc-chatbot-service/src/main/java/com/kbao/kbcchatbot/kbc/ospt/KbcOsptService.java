package com.kbao.kbcchatbot.kbc.ospt;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbc.ospt.common.model.OrderCodeRequest;
import com.kbao.kbc.ospt.order.entity.Order;
import com.kbao.kbcbsc.adapter.BaseClientAdapter;
import com.kbao.kbcchatbot.utils.FeignUtil;
import com.kbao.kbcospt.client.OsptOrderApiClientService;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: kbc-chatbot
 * @description: ospt
 * @author: husw
 * @create: 2023-08-24 10:09
 **/
@Service
@Slf4j
public class KbcOsptService extends BaseClientAdapter {

    @Autowired
    private OsptOrderApiClientService osptOrderApiClientService;
    /**
    * @Description: 根据订单号查询订单
    * @Param: [orderCode]
    * @return: com.kbao.kbc.ospt.order.entity.Order
    * @Author: husw
    * @Date: 2023/8/24 10:21
    */
    public Order getOrderByOrderCode(String orderCode,String tenantId){
        if (isWeb()){
            throw new BusinessException("WEB暂无该方法");
        }else {
            FeignUtil.setBaseFeignHeader(tenantId);
            OrderCodeRequest orderCodeRequest = new OrderCodeRequest();
            orderCodeRequest.setOrderCode(orderCode);
            Result<Order> orderResult = osptOrderApiClientService.getOrderByOrderCode(orderCodeRequest);
            if (EmptyUtils.isEmpty(orderResult) || !ResultStatusEnum.SUCCESS.getStatus().equals(orderResult.getResp_code())){
                log.warn("查询Ospt订单失败, Cause:{}", orderResult);
                return null;
            }
            return orderResult.getDatas();
        }
    }
}
