package com.kbao.kbcchatbot.kbc.ucs;

import com.kbao.common.constant.Constant;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.adapter.BaseClientAdapter;
import com.kbao.kbcucs.client.*;
import com.kbao.kbcucs.studio.studiousercard.model.UserCardReq;
import com.kbao.kbcucs.studio.studiousercard.model.UserCardResp;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.kbcucs.wallet.walletaccountdetail.model.DistributionStatusReq;
import com.kbao.kbcucs.wallet.walletaccountdetail.model.DistributionStatusVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 对接用户中心
 */
@Slf4j
@Service
public class KbcUcsService extends BaseClientAdapter {


    @Autowired
    private UserApiV2ClientService userApiV2ClientService;

    @Autowired
    private UserWebV2ClientService userWebV2ClientService;

    @Autowired
    private StudioExtraApiClientService studioExtraApiClientService;

    @Autowired
    private WalletDetailApiClientService walletDetailApiClientService;

    @Autowired
    private WalletDetailWebClientService walletDetailWebClientService;

    public UserInfoResp getUserInfo(String userId, String tenantId) {
        log.info("查询用户信息:userId={}，tenantId={}",userId,tenantId);
        setHeader(tenantId);
        Result<UserInfoResp> userInfo;
        if (isWeb()) {
            userInfo = userWebV2ClientService.getByUserId(userId);
        } else {
            userInfo = userApiV2ClientService.getByUserId(userId);
        }

        if (userInfo == null) {
            return null;
        }
        if (ResultStatusEnum.FAIL.getStatus().equals(userInfo.getResp_code())) {
            log.error("用户查询失败, Cause:{}", userInfo);
            return null;
        }
        log.info("查询用户信息结果 {}", userInfo.getDatas());
        return userInfo.getDatas();
    }
    /**
    * @Description: 工作室个人信息
    * @Param: [userId, tenantId]
    * @return: com.kbao.kbcucs.studio.studiousercard.model.UserCardResp
    * @Author: husw
    * @Date: 2023/10/27 10:40
    */
    public UserCardResp getUserCardById(String userId, String tenantId){
        setHeader(tenantId);
        if (isWeb()) {
            throw new BusinessException(Constant.ERROR_WEB_NO_METHOD);
        }
        UserCardReq userCardReq = new UserCardReq();
        userCardReq.setUserId(userId);
        Result<UserCardResp> result = studioExtraApiClientService.getUserCard(userCardReq);
        if (ResultStatusEnum.FAIL.getStatus().equals(result.getResp_code())) {
            log.error("getResultData:[{}]", result);
            return null;
        }
        return result.getDatas();
    }
    /**
    * @Description: 查询发佣明细
    * @Param: [req, tenantId]
    * @return: com.kbao.kbcucs.wallet.walletaccountdetail.model.DistributionStatusVo
    * @Author: husw
    * @Date: 2023/11/13 9:39
    */
    public DistributionStatusVo getWalletAccountDetails(DistributionStatusReq req,String tenantId){
        setHeader(tenantId);
        Result<DistributionStatusVo> walletAccountDetails;
        if (isWeb()) {
            walletAccountDetails = walletDetailWebClientService.getWalletAccountDetails(req);
        }else {
            walletAccountDetails = walletDetailApiClientService.getWalletAccountDetails(req);
        }
        if (ResultStatusEnum.FAIL.getStatus().equals(walletAccountDetails.getResp_code())) {
            log.error("getResultData:[{}]", walletAccountDetails);
            return null;
        }
        return walletAccountDetails.getDatas();
    }


    private void setHeader(String tenantId) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("tenantId", tenantId);
        FeignRequestHeader.Header.set(headerMap);
    }


}
