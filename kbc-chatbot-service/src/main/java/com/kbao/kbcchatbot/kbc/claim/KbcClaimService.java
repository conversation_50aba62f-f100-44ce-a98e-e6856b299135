package com.kbao.kbcchatbot.kbc.claim;

import com.alibaba.excel.util.StringUtils;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.adapter.PlatformConfig;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcclaim.client.chatbot.ClaimChatBotClientService;
import com.kbao.kbcclaim.client.chatbot.ClaimWebChatBotClientService;
import com.kbao.kbcclaim.order.model.AssociationOrderReq;
import com.kbao.kbcclaim.order.model.AssociationOrderResp;
import com.kbao.kbcclaim.order.model.ClaimAgingReq;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.kbao.kbcchatbot.constants.Constant.TENANT_ID_COMMON;

/**
 * <AUTHOR> qiuzb
 * @Description: Claim服务 好赔
 * @create 2023/10/25 15:05
 */
@Slf4j
@Service
public class KbcClaimService {

    @Autowired
    private PlatformConfig platformConfig;


    @Autowired
    private ClaimWebChatBotClientService chatBotWebService;

    @Autowired
    private ClaimChatBotClientService chatBotClientService;

    /**
     * 获取保单号
     *
     * @param associationOrderReq 请求参数
     * @return 保单号数组
     */
    public List<AssociationOrderResp> getAssociationOrder(AssociationOrderReq associationOrderReq,String tenantId) {

        if (CollectionUtils.isEmpty(associationOrderReq.getpIds()) || StringUtils.isBlank(associationOrderReq.getUserId())) {
            log.error("获取客户绑定信息失败，必传参数不能为空");
            return null;
        }
        setBaseFeignHeader(tenantId);
        log.info("KbcClaimService Req getAssociationOrder={}", associationOrderReq);
        Result<List<AssociationOrderResp>> listResult = !platformConfig.isWeb() ? chatBotClientService.getAssociationOrder(associationOrderReq) : chatBotWebService.getAssociationOrder(associationOrderReq);
        log.info("KbcClaimService Req getAssociationOrder={}", listResult);
        if (Objects.isNull(listResult) || !ResultStatusEnum.isSuccess(listResult.getResp_code())) {
            log.error("获取getAssociationOrder失败");
            return null;
        }

        return listResult.getDatas();
    }

    public Integer getClaimAging(ClaimAgingReq claimAgingReq,String tenantId) {

//        if (CollectionUtils.isEmpty(associationOrderReq.getpIds()) || StringUtils.isBlank(associationOrderReq.getUserId())) {
//            log.error("获取客户绑定信息失败，必传参数不能为空");
//            return null;
//        }
        setBaseFeignHeader(tenantId);
        log.info("KbcClaimService Req getClaimAging={}", claimAgingReq);
        Result<Integer> integerResult = !platformConfig.isWeb() ? chatBotClientService.getClaimAging(claimAgingReq) : chatBotWebService.getClaimAging(claimAgingReq);
        log.info("KbcClaimService Req getAssociationOrder={}", integerResult);
        if (Objects.isNull(integerResult) || !ResultStatusEnum.isSuccess(integerResult.getResp_code())) {
            log.error("获取getAssociationOrder失败");
            return null;
        }

        return integerResult.getDatas();
    }

    /**
     * 设置feign请求头
     *
     * @param tenantId 租户id
     */
    private void setBaseFeignHeader(String tenantId) {
        Map<String, String> headerMap = new HashMap<>(2);
        headerMap.put("tenantId", tenantId);
        FeignRequestHeader.Header.set(headerMap);
    }


    /**
     * 获取租户id方法
     *
     * @return 租户id
     */
    private String getTenantId() {
        String tenantId;
        if (!platformConfig.isWeb() && EmptyUtils.isNotEmpty(ChatUserUtil.getUser())) {
            tenantId = ChatUserUtil.getUser().getTenantId();
        } else {
            if (SysLoginUtils.getUser() != null) {
                tenantId = SysLoginUtils.getUser().getTenantId();
            } else {
                tenantId = TENANT_ID_COMMON;
            }
        }
        return tenantId;
    }
}
