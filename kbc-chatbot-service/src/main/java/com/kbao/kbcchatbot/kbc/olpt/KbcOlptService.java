package com.kbao.kbcchatbot.kbc.olpt;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbc.olpt.order.model.ChitChatOrderParam;
import com.kbao.kbc.olpt.order.model.ChitChatOrderVo;
import com.kbao.kbcbsc.adapter.BaseClientAdapter;
import com.kbao.kbcchatbot.utils.FeignUtil;
import com.kbao.kbcolpt.client.OlptOrderApiClientService;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: olpt
 * @author: husw
 * @create: 2023-08-24 10:09
 **/
@Service
@Slf4j
public class KbcOlptService extends BaseClientAdapter {

    @Autowired
    private OlptOrderApiClientService olptOrderApiClientService;


    public ChitChatOrderVo findChitchatOrderInfo(String orderCode,String tenantId){
        if (isWeb()){
            throw new BusinessException("WEB暂无该方法");
        }else {
            List<ChitChatOrderVo> chitchatOrderList = findChitchatOrderList(Collections.singletonList(orderCode), tenantId);
            return chitchatOrderList.get(0);
        }
    }
    /**
    * @Description: 根据订单号查询订单
    * @Param: [orderCode]
    * @return: com.kbao.kbc.ospt.order.entity.Order
    * @Author: husw
    * @Date: 2023/8/24 10:21
    */
    public List<ChitChatOrderVo> findChitchatOrderList(List<String> orderCodeList,String tenantId){
        if (isWeb()){
            throw new BusinessException("WEB暂无该方法");
        }else {
            FeignUtil.setBaseFeignHeader(tenantId);
            ChitChatOrderParam orderParam = new ChitChatOrderParam();
            orderParam.setOrderCodeList(orderCodeList);
            //renewalStatus 0-未续购、未续期
            //1-已续购、已续期
            //2-续购失败、续期失败
            Result<List<ChitChatOrderVo>> orderResult = olptOrderApiClientService.findChitchatOrderInfo(orderParam);
            if (EmptyUtils.isEmpty(orderResult) || !ResultStatusEnum.SUCCESS.getStatus().equals(orderResult.getResp_code())){
                log.warn("查询Olpt订单失败, Cause:{}", orderResult);
                return null;
            }
            return orderResult.getDatas();
        }
    }


}
