package com.kbao.kbcchatbot.kbc.pm;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.adapter.PlatformConfig;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcpm.client.customer.CustomerHostClientService;
import com.kbao.kbcpm.client.customer.CustomerHostWebClientService;
import com.kbao.kbcpm.common.PmCommonVO;
import com.kbao.kbcpm.customer.vo.customer.CustomerUnbindVO;
import com.kbao.kbcpm.customer.vo.host.CustomerBindInfoVO;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.kbao.kbcchatbot.constants.Constant.TENANT_ID_COMMON;

/**
 * <AUTHOR> qiuzb
 * @Description: PM服务
 * @create 2023/10/25 14:55
 */
@Service
@Slf4j
public class KbcPmService {

    @Autowired
    private PlatformConfig platformConfig;

    @Autowired
    private CustomerHostClientService customerHostClientService;

    @Autowired
    private CustomerHostWebClientService customerHostWebClientService;

    /**
     * 根据客户信息和顾问信息查询托管/服务关系
     *
     * @param pmCommonVO 客户信息入参
     * @return 客户绑定信息
     */
    public CustomerBindInfoVO getBindInfo(PmCommonVO pmCommonVO,String tenantId) {

        if (StringUtils.isBlank(pmCommonVO.getUserId()) || StringUtils.isBlank(pmCommonVO.getCustomerName()) || StringUtils.isBlank(pmCommonVO.getCertificatesNum())) {
            log.error("获取客户绑定信息失败，必传参数不能为空");
            return null;
        }
        setBaseFeignHeader(tenantId);
        log.info("Req getBindInfo={}", pmCommonVO);
        Result<CustomerBindInfoVO> customerBindInfoVOResult = !platformConfig.isWeb() ? customerHostClientService.getBindInfo(pmCommonVO) : customerHostWebClientService.getBindInfo(pmCommonVO);
        log.info("Resp getBindInfo={}", customerBindInfoVOResult);
        if (Objects.isNull(customerBindInfoVOResult) || !ResultStatusEnum.isSuccess(customerBindInfoVOResult.getResp_code())) {
            log.error("获取客户绑定信息失败");
            return null;
        }

        return customerBindInfoVOResult.getDatas();
    }

    /**
     * 调用解绑服务关系/托管关系接口
     *
     * @param customerUnbindVO 客户信息入参
     * @return 客户信息
     */
    public Boolean unbindAll(CustomerUnbindVO customerUnbindVO,String tenantId) {

        if (StringUtils.isBlank(customerUnbindVO.getUserId()) || StringUtils.isBlank(customerUnbindVO.getCustomerName())
                | StringUtils.isBlank(customerUnbindVO.getCertificatesNum()) || StringUtils.isBlank(customerUnbindVO.getUnBindReason())) {
            log.error("获取客户绑定信息失败，必传参数不能为空");
            return null;
        }

        log.info("Req unbindAll={}", customerUnbindVO);
        Result<String> stringResult;
        try {
            setBaseFeignHeader(tenantId);
            stringResult = !platformConfig.isWeb() ? customerHostClientService.unbindAll(customerUnbindVO) : customerHostWebClientService.unbindAll(customerUnbindVO);
        } catch (Exception e) {
            log.error("调用解绑服务关系/托管关系接口异常", e);
            return false;
        }
        log.info("Resp unbindAll={}", stringResult);
        if (EmptyUtils.isNotEmpty(stringResult) && ResultStatusEnum.isSuccess(stringResult.getResp_code())){
            return true;
        }
        return false;
    }


    /**
     * 设置feign请求头
     *
     * @param tenantId 租户id
     */
    private void setBaseFeignHeader(String tenantId) {
        Map<String, String> headerMap = new HashMap<>(2);
        headerMap.put("tenantId", tenantId);
        FeignRequestHeader.Header.set(headerMap);
    }


    /**
     * 获取租户id方法
     *
     * @return 租户id
     */
    private String getTenantId() {
        String tenantId;
        if (!platformConfig.isWeb()) {
            tenantId = ChatUserUtil.getUser().getTenantId();
        } else {
            if (SysLoginUtils.getUser() != null) {
                tenantId = SysLoginUtils.getUser().getTenantId();
            } else {
                tenantId = TENANT_ID_COMMON;
            }
        }
        return tenantId;
    }


    }
