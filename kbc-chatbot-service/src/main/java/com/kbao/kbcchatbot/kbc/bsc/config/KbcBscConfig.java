package com.kbao.kbcchatbot.kbc.bsc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@ConfigurationProperties(prefix = "kbcbsc")
@Component
@Data
public class KbcBscConfig {

    /**
     * appCode
     */
    private String appCode;

    /**
     *
     */
    private String appName;

    /**
     * appId
     */
    private String appId;

    /**
     * 角色ID
     */
    private String roleId;

}
