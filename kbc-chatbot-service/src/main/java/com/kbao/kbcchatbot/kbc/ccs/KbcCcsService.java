package com.kbao.kbcchatbot.kbc.ccs;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.adapter.PlatformConfig;
import com.kbao.kbcccs.dc.client.relation.CcsUserRelationApiClientService;
import com.kbao.kbcccs.dc.commons.vo.relation.UserAuthInfoRes;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.kbao.kbcchatbot.constants.Constant.TENANT_ID_COMMON;

/**
 * <AUTHOR> qiuzb
 * @Description: 童管家服务
 * @create 2023/10/27 11:22
 */
@Slf4j
@Service
public class KbcCcsService {

    @Autowired
    private PlatformConfig platformConfig;

    @Autowired
    private CcsUserRelationApiClientService ccsUserRelationApiClientService;

    /**
     * 根据客户证件号码查询是否认证过童管家
     *
     * @param certificateType 证件类型
     * @param certificateNum  证件号
     * @return UserAuthInfoRes用户信息
     */
    public UserAuthInfoRes authStatusByCertificate(String certificateType, String certificateNum) {

        log.info("KbcCcsService authStatusByCertificate certificateType={} , certificateNum={}", certificateType, certificateNum);
        Result<UserAuthInfoRes> userAuthInfoResResult;

        try {
            setBaseFeignHeader(getTenantId());
            userAuthInfoResResult = ccsUserRelationApiClientService.authStatusByCertificate(certificateType, certificateNum);
            log.info("KbcCcsService authStatusByCertificate resp={}", JSONObject.toJSONString(userAuthInfoResResult));
        } catch (Exception e) {
            log.error("KbcCcsService authStatusByCertificate接口调用异常", e);
            return null;
        }

        if (userAuthInfoResResult == null || !ResultStatusEnum.isSuccess(userAuthInfoResResult.getResp_code())) {
            log.error("KbcUocService getUocPolicyInfo获取订单数据失败");
            return null;
        }

        return userAuthInfoResResult.getDatas();
    }


    /**
     * 获取租户id方法
     *
     * @return 租户id
     */
    private String getTenantId() {
        String tenantId;
        if (!platformConfig.isWeb() && EmptyUtils.isNotEmpty(ChatUserUtil.getUser())) {
            tenantId = ChatUserUtil.getUser().getTenantId();
        } else {
            if (SysLoginUtils.getUser() != null) {
                tenantId = SysLoginUtils.getUser().getTenantId();
            } else {
                tenantId = TENANT_ID_COMMON;
            }
        }
        return tenantId;
    }

    /**
     * 设置feign请求头
     *
     * @param tenantId 租户id
     */
    private void setBaseFeignHeader(String tenantId) {
        Map<String, String> headerMap = new HashMap<>(2);
        headerMap.put("tenantId", tenantId);
        FeignRequestHeader.Header.set(headerMap);
    }
}
