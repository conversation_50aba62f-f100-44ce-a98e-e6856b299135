package com.kbao.kbcchatbot.kbc.ufs;

import com.alibaba.fastjson.JSON;
import com.kbao.commons.constant.OSSConstant;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.snowflake.IdWorker;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.appfilechannel.bean.AppFileChannelListVo;
import com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.constants.Constant;
import com.kbao.kbcchatbot.kbc.bsc.KbcBscService;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcufs.adapter.BaseClientAdapter;
import com.kbao.kbcufs.adapter.FileClientAdapter;
import com.kbao.kbcufs.enums.FileTypeEnum;
import com.kbao.kbcufs.file.vo.client.FileRequest;
import com.kbao.kbcufs.file.vo.client.FileUploadRequest;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.kbcufs.utils.SignatureUtil;
import com.kbao.kbcums.common.constant.Constants;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;


@Service
@Slf4j
public class KbcUfsService extends BaseClientAdapter {

    @Autowired
    private FileClientAdapter fileClientAdapter;

    @Autowired
    private KbcBscService kbcBscService;

    /**
     * 上传图片
     *
     * @param file
     * @return 文件的绝对地址
     */
    public FileUploadResponse uploadImage(String tenantId, MultipartFile file) {
        //上传文件存放的位置
        String path = OSSConstant.KBAO_IMAGE_SAVE_PATH;
        Result<FileUploadResponse> result = getuploadResult(tenantId, path, file);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(result.getResp_code())) {
            return result.getDatas();
        } else {
            KbcUfsService.log.error("图片上传失败，Cause:{}", JSON.toJSONString(result));
            throw new BusinessException("图片上传失败");
        }
    }

    public FileUploadResponse uploadFile(FileUploadRequest request) {
        setBaseFeignHeader(request.getBusinessTenantId());
        Result<FileUploadResponse> result = fileClientAdapter.upload(request);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(result.getResp_code())) {
            return result.getDatas();
        } else {
            KbcUfsService.log.error("文件上传失败，Cause:{}", JSON.toJSONString(result));
            throw new BusinessException("文件上传失败");
        }
    }

    public String generatePresignedUrl(String tenantId, String fileId, String userId) {
        AppFileChannelListVo appFileChannelListVo = getFileAccount(tenantId);
        //上传文件存放的位置
        TenantConfigInfoVo tenantConfigInfo = kbcBscService.getTenantConfigInfo(tenantId);
        FileRequest fileRequest = new FileRequest();
        fileRequest.setUserName(appFileChannelListVo.getAccount());
        fileRequest.setBusinessNo(IdWorker.getIdStr());
        fileRequest.setBusinessTenantId(tenantId);
        fileRequest.setBusinessTenantName(tenantConfigInfo.getTenant().getTenantName());
        fileRequest.setOperator(userId);
        fileRequest.setTimestamp(DateUtils.getCurrentTime());
        fileRequest.setParam(fileId);
        // 外网账号
        fileRequest.setNetwork("internet");
        String sign = SignatureUtil.getSign(fileRequest, appFileChannelListVo.getSecretKey());
        fileRequest.setSign(sign);
        Result<String> result = fileClientAdapter.generatePresignedUrl(fileRequest);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(result.getResp_code())) {
            return result.getDatas();
        } else {
            log.error("文件ID转链接失败，Cause:{}", JSON.toJSONString(result));
            throw new BusinessException("文件ID转链接失败");
        }
    }

    /**
    * @Description: 上传文件
    * @Param: [tenant, path, file]
    * @return: com.kbao.commons.web.Result<com.kbao.kbcufs.file.vo.client.FileUploadResponse>
    * @Author: husw
    * @Date: 2023/11/7 14:59
    */
    private Result<FileUploadResponse> getuploadResult(String tenant, String path, MultipartFile file) {
        String[] temps = file.getOriginalFilename().split("\\.");
        String detailPath = path + "/" + IdWorker.getIdStr() + "." + temps[temps.length - 1];

        AppFileChannelListVo appFileChannelListVo = getFileAccount(tenant);
        FileUploadRequest request = new FileUploadRequest();
        request.setUserName(appFileChannelListVo.getAccount());
        request.setBusinessNo(IdWorker.getIdStr());
        request.setBusinessTenantId(appFileChannelListVo.getAppTenantId());
        request.setBusinessTenantName(Constant.SYSTEM_NAME_CN);
        String userId = this.isWeb()? BscUserUtils.getUserId(): ChatUserUtil.getUserId();
        request.setCreateUser(userId);
        request.setTimestamp(DateUtils.getCurrentTime());
        String sign = SignatureUtil.getSign(request, appFileChannelListVo.getSecretKey());
        request.setSign(sign);
        request.setType(FileTypeEnum.FILE.getType());
        request.setFileType("static");
        request.setPath(detailPath);
        request.setFile(file);
        request.setNeedAbsolutePath(true);
        // 外网账号
        request.setNetwork("internet");
        KbcUfsService.log.debug("文件上传参数:{}", request);
        Result<FileUploadResponse> upload = fileClientAdapter.upload(request);
        KbcUfsService.log.debug("文件上传结果:{}", upload);
        return upload;
    }

    public String deleteFile(String tenantId, String absolutePath) {
        AppFileChannelListVo fileAccount = getFileAccount(tenantId);
        FileRequest request = new FileRequest();
        request.setUserName(fileAccount.getAccount());
        request.setBusinessNo(IdWorker.getIdStr());
        request.setBusinessTenantId(fileAccount.getAppTenantId());
        request.setBusinessTenantName(Constant.SYSTEM_NAME_CN);
        request.setOperator(SysLoginUtils.getUser() != null ? SysLoginUtils.getUser().getNickName() : Constants.StringConstants.UNKOWN);
        request.setParam(absolutePath);
        request.setTimestamp(DateUtils.getCurrentTime());
        String sign = SignatureUtil.getSign(request, fileAccount.getSecretKey());
        request.setSign(sign);
        KbcUfsService.log.debug("文件删除参数:{}", request);
        Result<String> deleteResult = fileClientAdapter.delete(request);
        KbcUfsService.log.debug("文件删除结果:{}", deleteResult);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(deleteResult.getResp_code())) {
            return deleteResult.getDatas();
        } else {
            throw new BusinessException("文件删除失败, 删除参数:{}" + JSON.toJSONString(request));
        }
    }

    private AppFileChannelListVo getFileAccount(String tenant) {
        return kbcBscService.getChannelConfigInfo(tenant).getFileChannelList().get(0);
    }

    /**
     * 设置feign请求头
     *
     * @param tenantId 租户id
     */
    private void setBaseFeignHeader(String tenantId) {
        Map<String, String> headerMap = new HashMap<>(2);
        headerMap.put("tenantId", tenantId);
        FeignRequestHeader.Header.set(headerMap);
    }

}
