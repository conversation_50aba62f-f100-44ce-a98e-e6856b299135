package com.kbao.kbcchatbot.kbc.ums;

import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.appmsgchannel.bean.AppMsgChannelListVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantConfigResVo;
import com.kbao.kbcbsc.commons.enums.AppMsgChannelTypeEnum;
import com.kbao.kbcchatbot.kbc.bsc.KbcBscService;
import com.kbao.kbcchatbot.kbc.bsc.config.KbcBscConfig;
import com.kbao.kbcums.client.adapter.MailClientAdapter;
import com.kbao.kbcums.client.util.UmsUtil;
import com.kbao.kbcums.common.bean.CommonApiResponse;
import com.kbao.kbcums.mailout.vo.MailApiRequestParam;
import com.kbao.kbcums.mailout.vo.MailApiRequestParamDetail;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName KbcUmsWebSevice
 * @Description 统一消息平台业务类
 * @date 2020/10/13 11:13
 * @since V1.0
 */
@Service
@Slf4j
@Validated
public class KbcUmsService {

    @Autowired
    private KbcBscService kbcBscService;

    @Autowired
    private KbcBscConfig kbcBscConfig;

    @Autowired
    private MailClientAdapter mailClientAdapter;

    /**
     * @param detailList: 发送邮件内容详情
     * @param tenantId: 租户ID
     * @param templateId: 模板id
     * @Description:发送邮件
     * @Author: wangyong
     * @Date: 2020/10/13 16:54
     * @return: com.kbao.commons.web.Result<com.kbao.kbcums.common.bean.CommonApiResponse>
     **/
    public Result<CommonApiResponse> sendEmail(@Valid @NotEmpty(message = "内容详情不能为空") List<MailApiRequestParamDetail> detailList,
                                                    @NotBlank(message = "租户Id不能为空") String tenantId,
                                                    String templateId) {
        MailApiRequestParam param = new MailApiRequestParam();
        param.setChannelAppId(kbcBscConfig.getAppId());
        param.setChannelTenantId(tenantId);
        if (EmptyUtils.isNotEmpty(templateId)) {
            param.setTemplateId(templateId);
        }
        param.setDetail(detailList);
        //入参排序并转换成json格式
        String bodyString = UmsUtil.getBodyString(param);
        //生成时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());
        AppMsgChannelListVo byMsgType = getByMsgType(AppMsgChannelTypeEnum.EMAIL.getKey(), tenantId);
        Assert.isTrue(EmptyUtils.isNotEmpty(byMsgType), "租户" + tenantId + "没有邮件消息渠道账号信息");
        //生成签名
        String sign = SignUtil.axSign(byMsgType.getAccount(), byMsgType.getSecretKey(), timestamp, bodyString);
        log.info("发送邮件发送入参:{}","detailList:"+detailList+"tenantId:"+tenantId);
        Result<CommonApiResponse> send = mailClientAdapter.send(param, sign, byMsgType.getAccount(), timestamp);
        log.info("邮件发送结果:{}",send);
        return send;
    }

    /**
     * @param msgType:  消息类型
     * @param tenantId: 租户ID
     * @Description: 得到对应的消息类型的账号数据
     * @Author: wangyong
     * @Date: 2020/10/13 13:53
     * @return: com.kbao.kbcbsc.appmsgchannel.bean.AppMsgChannelListVo
     **/
    private AppMsgChannelListVo getByMsgType(String msgType, String tenantId) {
        AppTenantConfigResVo channelConfigInfo = kbcBscService.getChannelConfigInfo(tenantId);
        Assert.isTrue(EmptyUtils.isNotEmpty(channelConfigInfo), "租户" + tenantId + "没有账号信息");
        List<AppMsgChannelListVo> msgChannelList = channelConfigInfo.getMsgChannelList();
        Assert.isTrue(EmptyUtils.isNotEmpty(msgChannelList), "租户" + tenantId + "没有消息渠道账号信息");
        for (AppMsgChannelListVo appMsgChannelListVo : msgChannelList) {
            if (appMsgChannelListVo.getMsgType().equals(msgType)) {
                return appMsgChannelListVo;
            }
        }
        return null;
    }

}
