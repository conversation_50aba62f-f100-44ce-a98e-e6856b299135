package com.kbao.kbcchatbot.kbc.uoc;

import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.adapter.BaseClientAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> qiuzb
 * @Description: 统一订单服务
 * @create 2023/10/26 9:39
 */
@Service
@Slf4j
public class KbcUocService extends BaseClientAdapter {

//    @Autowired
//    private PolicyClientService policyClientService;
//
//    @Autowired
//    private PolicyWebClientService policyWebClientService;

    /**
     * 查询统一订单详情
     * userId orderId orderType必传
     *
     * @param uocPolicyInfoReq 入参对象
     * @return UocPolicyInfoResp
     */
//    public PageInfo<UocPolicyInfoResp> getUocPolicyInfo(RequestObjectPage<UocPolicyInfoReq> uocPolicyInfoReq) {
//        setBaseFeignHeader(uocPolicyInfoReq.getParam().getTenantId());
//
//        log.info("KbcUocService getUocPolicyInfo req={}", uocPolicyInfoReq);
//        Result<PageInfo<UocPolicyInfoResp>> uocPolicyInfo = null;
//        try {
//            if (this.isWeb()){
//                uocPolicyInfo = policyWebClientService.getUocPolicyInfo(uocPolicyInfoReq);
//            }else {
//                uocPolicyInfo = policyClientService.getUocPolicyInfo(uocPolicyInfoReq);
//            }
//            log.info("KbcUocService getUocPolicyInfo resp={}", uocPolicyInfo);
//        } catch (Exception e) {
//            log.error("调用KbcUocService getUocPolicyInfo接口异常", e);
//            return null;
//        }
//
//        if (uocPolicyInfo == null || !ResultStatusEnum.isSuccess(uocPolicyInfo.getResp_code())) {
//            log.error("KbcUocService getUocPolicyInfo获取订单数据失败");
//            return null;
//        }
//
//        return uocPolicyInfo.getDatas();
//    }

    /**
     * 设置feign请求头
     *
     * @param tenantId 租户id
     */
    private void setBaseFeignHeader(String tenantId) {
        Map<String, String> headerMap = new HashMap<>(2);
        headerMap.put("tenantId", tenantId);
        FeignRequestHeader.Header.set(headerMap);
    }

}
