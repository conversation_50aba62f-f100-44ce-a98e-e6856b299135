package com.kbao.kbcchatbot.kbc.km;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.BaseClientAdapter;
import com.kbao.kbcbsc.util.BscApiContext;
import com.kbao.kbcchatbot.utils.FeignUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbckm.KnowledgeDirectWebClientService;
import com.kbao.kbckm.KnowledgeWebClientService;
import com.kbao.kbckm.client.KnowledgeDirectApiClientService;
import com.kbao.kbckm.directory.bean.DirectTreeReqVo;
import com.kbao.kbckm.directory.bean.DirectTreeVo;
import com.kbao.kbckm.knowledge.entity.KnowledgeBase;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 云知识库
 * @author: husw
 * @create: 2023-05-19 15:21
 **/
@Service
@Slf4j
public class KbcKmService extends BaseClientAdapter {

    @Autowired
    private KnowledgeDirectWebClientService knowledgeDirectWebClientService;

    @Autowired
    private KnowledgeWebClientService knowledgeWebClientService;

    @Autowired
    private KnowledgeDirectApiClientService knowledgeDirectApiClientService;
    /**
    * @Description: 查询云知识库目录
    * @Param: []
    * @return: java.util.List<com.kbao.kbckm.directory.bean.DirectTreeVo>
    * @Author: husw
    * @Date: 2023/5/19 15:28
    */
    public List<DirectTreeVo> list(){
        if (isWeb()){
            FeignUtil.setBaseFeignHeader(BscApiContext.TenantId.get());
            DirectTreeReqVo directTreeReqVo = new DirectTreeReqVo();
            directTreeReqVo.setTenantId(BscApiContext.TenantId.get());
            Result<List<DirectTreeVo>> directsTree = knowledgeDirectWebClientService.getDirectsTree(directTreeReqVo);
            if (EmptyUtils.isEmpty(directsTree) || !ResultStatusEnum.SUCCESS.getStatus().equals(directsTree.getResp_code())){
                log.error("查询云知识库目录失败, Cause:{}", directsTree);
                return null;
            }
            return directsTree.getDatas();
        }else {
            throw new BusinessException("API暂无该方法");
        }
    }
    /**
    * @Description: 查询知识列表
    * @Param: [var1]
    * @return: com.github.pagehelper.PageInfo<com.kbao.kbckm.knowledge.entity.KnowledgeBase>
    * @Author: husw
    * @Date: 2023/6/6 15:18
    */
    public PageInfo<KnowledgeBase> getKnowledgePageList(PageRequest<DirectTreeReqVo> var1){
        if (isWeb()){
            FeignUtil.setBaseFeignHeader(var1.getParam().getTenantId());
            Result<PageInfo<KnowledgeBase>> knowledgePageList = knowledgeDirectWebClientService.getKnowledgePageList(var1);
            if (EmptyUtils.isEmpty(knowledgePageList) || !ResultStatusEnum.SUCCESS.getStatus().equals(knowledgePageList.getResp_code())){
                log.error("查询云知识库知识失败, Cause:{}", knowledgePageList);
                return null;
            }
            return knowledgePageList.getDatas();
        }else {
            throw new BusinessException("API暂无该方法");
        }
    }
    /**
    * @Description: 查询知识完整信息
    * @Param: [knowledgeId]
    * @return: com.kbao.kbckm.knowledge.entity.KnowledgeBase
    * @Author: husw
    * @Date: 2023/6/6 15:19
    */
    public KnowledgeBase findById(String knowledgeId){
        if (isWeb()){
            FeignUtil.setBaseFeignHeader(BscApiContext.TenantId.get());
            Result<KnowledgeBase> knowledgePageResult = knowledgeWebClientService.findById(knowledgeId);
            if (EmptyUtils.isEmpty(knowledgePageResult) || !ResultStatusEnum.SUCCESS.getStatus().equals(knowledgePageResult.getResp_code())){
                log.error("查询云知识库知识完整信息失败, Cause:{}", knowledgePageResult);
                return null;
            }
            return knowledgePageResult.getDatas();
        }else {
            throw new BusinessException("API暂无该方法");
        }
    }

    public List<String> getVisibleSecondDirectIds(){
        FeignUtil.setBaseFeignHeader(BscApiContext.TenantId.get());
        if (isWeb()){
            throw new BusinessException("WEB暂无该方法");
        }else {
            FeignUtil.setUserFeignHeader(BscApiContext.TenantId.get(),ChatUserUtil.getUserId());
            Result<List<String>> visibleSecondDirectIds = knowledgeDirectApiClientService.getVisibleSecondDirectIds();
            if (EmptyUtils.isEmpty(visibleSecondDirectIds) || !ResultStatusEnum.SUCCESS.getStatus().equals(visibleSecondDirectIds.getResp_code())){
                log.error("查询云知识库目录权限, Cause:{}", visibleSecondDirectIds);
                return null;
            }
            return visibleSecondDirectIds.getDatas();
        }
    }

}
