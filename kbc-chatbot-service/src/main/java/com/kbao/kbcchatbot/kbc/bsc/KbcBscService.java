package com.kbao.kbcchatbot.kbc.bsc;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.adapter.AppTenantClientAdapter;
import com.kbao.kbcbsc.adapter.DicItemsClientAdapter;
import com.kbao.kbcbsc.adapter.ParamClientAdapter;
import com.kbao.kbcbsc.adapter.TenantClientAdapter;
import com.kbao.kbcbsc.apptenant.bean.AppTenantConfigResVo;
import com.kbao.kbcbsc.apptenant.bean.AppTenantIdReq;
import com.kbao.kbcbsc.apptenant.bean.AppTenantListVo;
import com.kbao.kbcbsc.client.WechatServiceClientService;
import com.kbao.kbcbsc.dicitems.entity.DicItems;
import com.kbao.kbcbsc.param.entity.Param;
import com.kbao.kbcbsc.tenant.bean.AppUserVo;
import com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo;
import com.kbao.kbcbsc.tenant.bean.TenantIdReq;
import com.kbao.kbcbsc.tenant.bean.TenantVo;
import com.kbao.kbcbsc.user.model.UserIdReq;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcbsc.wechat.request.AppParam;
import com.kbao.kbcbsc.wechat.request.WechatParam;
import com.kbao.kbcbsc.wechat.response.SignatureVO;
import com.kbao.kbcccs.dc.commons.context.HeaderContext;
import com.kbao.kbcchatbot.kbc.bsc.config.KbcBscConfig;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: bsc服务
 * @author: husw
 * @create: 2023-05-19 15:44
 **/
@Service
@Slf4j
public class KbcBscService {

    @Autowired
    private DicItemsClientAdapter dicItemsClientAdapter;

    @Autowired
    private TenantClientAdapter tenantClientAdapter;

    @Autowired
    private ParamClientAdapter paramClientAdapter;

    @Autowired
    private KbcBscConfig kbcBscConfig;

    @Autowired
    AppTenantClientAdapter appTenantClientAdapter;

    @Autowired
    private WechatServiceClientService wechatServiceClientService;

    /**
     * @param dicCode: 字典code
     * @Description:通过字典code来获取字典信息
     * @Author: wangyong
     * @Date: 2020/9/9 16:04
     * @return: java.util.List<com.kbao.kbcbsc.dicitems.entity.DicItems>
     **/
    public List<DicItems> getDicItems(String dicCode) {
        Result<List<DicItems>> dicItems = dicItemsClientAdapter.getDicItems(dicCode);
        Assert.isTrue(ResultStatusEnum.isSuccess(dicItems.getResp_code()), dicItems.getResp_msg());
        return dicItems.getDatas();
    }

    /**
     * 获取当前登录用户在用户应用下的租户列表
     */
    public Result<List<AppTenantListVo>> getWebUserTenants() {
        AppUserVo req = new AppUserVo();
        req.setAppId(BscUserUtils.getUser().getFunction().getApplyId());
        req.setUserId(BscUserUtils.getUserId());
        return tenantClientAdapter.getAppUserTenantList(req);
    }

    /**
     * 获取当前租户下的所有Web用户信息
     *
     * @return
     */
    public Result<List<UserIdReq>> getTenantUsers() {
        TenantIdReq tenantIdReq = new TenantIdReq();
        tenantIdReq.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        return tenantClientAdapter.getTenantUsers(tenantIdReq);
    }

    /**
     * 查询应用租户关联的各种账号信息 （支付渠道账号列表，消息渠道账 号列表，文件上传账号列表，）
     *
     * @param tenantId 租户编号
     * @return
     */
    public AppTenantConfigResVo getChannelConfigInfo(String tenantId) {
        AppTenantIdReq appTenantIdReq = new AppTenantIdReq();
        appTenantIdReq.setAppId(kbcBscConfig.getAppId());
        appTenantIdReq.setAppCode(kbcBscConfig.getAppCode());
        appTenantIdReq.setTenantId(tenantId);
        Result<AppTenantConfigResVo> channelConfigInfo = appTenantClientAdapter.getChannelConfigInfo(appTenantIdReq);
        Assert.isTrue(ResultStatusEnum.isSuccess(channelConfigInfo.getResp_code()), channelConfigInfo.getResp_msg());
        return channelConfigInfo.getDatas();
    }

    /**
     * @Description 查询租户的基本信息
     * @since V1.0
     * <AUTHOR>
     * @param: [tenantId]
     * @return: com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo
     * 2021/1/6  9:13
     *
     */
    public TenantConfigInfoVo getTenantConfigInfo(String tenantId){
        TenantVo vo = new TenantVo();
        vo.setTenantId(tenantId);
        Result<TenantConfigInfoVo> tenantConfigInfo = tenantClientAdapter.getTenantConfigInfo(vo);
        log.debug("查询租户的基本信息为:{}",tenantConfigInfo);
        Assert.isTrue(ResultStatusEnum.isSuccess(tenantConfigInfo.getResp_code()), tenantConfigInfo.getResp_msg());
        return tenantConfigInfo.getDatas();
    }
    /**
    * @Description: 查询参数配置
    * @Param: [paramCode]
    * @return: com.kbao.kbcbsc.param.entity.Param
    * @Author: husw
    * @Date: 2023/6/5 14:31
    */
    public Param getParam(String paramCode) {
        TenantIdReq tenantIdReq = new TenantIdReq();
        tenantIdReq.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        Result<Param> param = paramClientAdapter.getParam(paramCode);
        Assert.isTrue(ResultStatusEnum.isSuccess(param.getResp_code()), param.getResp_msg());
        return param.getDatas();
    }

    public AppParam getAppParam(WechatParam wechatParam, String tenantId, String appCode, String serviceType) {
        AppParam appParam = new AppParam();
        appParam.setTenantId(tenantId);
        appParam.setAppCode(appCode);
        appParam.setServiceType(serviceType);
        appParam.setWechatParam(wechatParam);
        return appParam;
    }

    public SignatureVO getSignature(AppParam appParam) {
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用入参：{}", appParam);
        Result<SignatureVO> signature = wechatServiceClientService.getSignature(appParam);
        log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + "调用出参：{}", signature);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(signature.getResp_code())) {
            return signature.getDatas();
        } else {
            throw new BusinessException("获取签字失败");
        }
    }




}
