package com.kbao.kbcchatbot.action.queryordervisit;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 为何需要回访
 * @author: husw
 * @create: 2023-10-25 10:24
 **/
@Component
@Slf4j
public class ActionWhyVisit implements Action {
    @Override
    public String name() {
        return "action_query_why_return_visit";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_query_why_return_visit>>>>:为何需要回访");
        dispatcher.utterTemplate("utter_nmi_why_visit_answer");
        return Collections.singletonList(new SlotSet("is_evaluate",true));
    }
}
