package com.kbao.kbcchatbot.action.openwhitelist;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AllSlotsReset;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;


/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-10-25 10:24
 **/
@Component
@Slf4j
public class ActionOpenWhiteType implements Action {
    private static final String MESSAGE = "此订单为{0}产品，不属于人保大护甲5号产品，需要转人工处理";

    @Override
    public String name() {
        return "action_open_white_type";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_open_white_type>>>>:开白订单类型判断");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        String riskName = slotsMap.get("riskName");
        if(EmptyUtils.isEmpty(riskName) || !riskName.contains("人保大护甲5号")){
            Map<String, Object> kwargs = new HashMap<>();
            String content = MessageFormat.format(MESSAGE, riskName);
            kwargs.put("message",content);
            dispatcher.utterTemplate("utter_custom_labor",kwargs);
            return Collections.singletonList(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
        }else {
            dispatcher.utterTemplate(Arrays.asList("utter_open_white_type","utter_open_white_type_tip"));
            return Collections.emptyList();
        }
    }
}
