package com.kbao.kbcchatbot.action.openwhitelist;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-10-25 10:24
 **/
@Component
@Slf4j
public class ActionSendExampleImage implements Action {
    @Override
    public String name() {
        return "action_send_example_image";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_send_example_image>>>>:发送样例图片");
        String questionType = tracker.getSlotValue("question_type",String.class);
        if ("occupation".equals(questionType)){
            dispatcher.utterTemplate("utter_provider_occupation_type");
        } else if ("actual_name".equals(questionType)) {
            dispatcher.utterTemplate("utter_provider_actual_name");
        }
        return Collections.emptyList();
    }
}
