package com.kbao.kbcchatbot.action.commissionnotreceived;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.kbao.common.enums.YesNoEnum;
import com.kbao.kbc.olpt.order.model.ChitChatOrderVo;
import com.kbao.kbcchatbot.constants.WorkOrderConstant;
import com.kbao.kbcchatbot.externalapi.work.ExternalApiService;
import com.kbao.kbcchatbot.kbc.enums.OrderTypeEnum;
import com.kbao.kbcchatbot.kbc.olpt.KbcOlptService;
import com.kbao.kbcchatbot.kbc.ucs.KbcUcsService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AllSlotsReset;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcchatbot.utils.BaseUtil;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.kbcucs.wallet.walletaccountdetail.model.DistributionStatusReq;
import com.kbao.kbcucs.wallet.walletaccountdetail.model.DistributionStatusVo;
import com.kbao.kbcucs.wallet.walletaccountdetail.model.WalletAccountDetailAppVo;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;

/**
 * @program: kbc-chatbot
 * @description: 查询佣金发放
 * @author: husw
 * @create: 2023-10-19 14:47
 **/
@Slf4j
@Component
public class ActionQueryCommission implements Action {


    @Autowired
    private KbcUcsService kbcUcsService;

    @Autowired
    private ExternalApiService externalApiService;

    @Autowired
    private KbcOlptService kbcOlptService;

    private static final String COMMISSION_TARGET_TEST = "https://www-sta.kbao123.com/ucs/DTIncomeList?DTWebType=BJ&chooseIndex=1&typeLabelValue=1&agentCode={0}&tradeOrderNo={1}";
    private static final String COMMISSION_TARGET_PROD = "https://kbc-prod.dtinsure.com/ucs/DTIncomeList?DTWebType=BJ&chooseIndex=1&typeLabelValue=1&agentCode={0}&tradeOrderNo={1}";

    @Override
    public String name() {
        return "action_query_commission";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("查询佣金发放情况");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        String tenantId = tracker.getSlotValue("tenant_id", String.class);
        String userId = tracker.getSlotValue("user_id", String.class);
        String orderCode = slotsMap.get("orderId");
        Integer orderType = Integer.parseInt(slotsMap.get("orderType"));
        List<AbstractEvent> abstractEvents = new ArrayList<>();
        //判断订单类型
        if(OrderTypeEnum.LPT.getType().equals(String.valueOf(orderType)) ||
                OrderTypeEnum.ONLINE.getType().equals(String.valueOf(orderType))){
            //创建工单
            setCreateOrderSlots(dispatcher,slotsMap,userId,tenantId);
            Map<String, Object> kwargs = new HashMap<>();
            kwargs.put("orderId",slotsMap.get("orderId"));
            kwargs.put("question","佣金发放");
            dispatcher.utterTemplate("utter_create_service_verify",kwargs);
            abstractEvents.add(new SlotSet("slots_map",slotsMap));
            return Collections.singletonList(new SlotSet("slots_map",slotsMap));
        } else if (OrderTypeEnum.SHORT.getType().equals(String.valueOf(orderType)) ||
                OrderTypeEnum.NMI.getType().equals(String.valueOf(orderType))) {
            abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
            UserInfoResp userInfo = kbcUcsService.getUserInfo(userId, tenantId);
            if (EmptyUtils.isEmpty(userInfo)){
                dispatcher.utterTemplate("utter_not_user_labor");
                return abstractEvents;
            }
            //查询发佣明细
            DistributionStatusReq req = new DistributionStatusReq();
            req.setUserId(userId);
            req.setTradeOrderNo(orderCode);
            DistributionStatusVo walletAccountDetails = kbcUcsService.getWalletAccountDetails(req,tenantId);
            if (EmptyUtils.isEmpty(walletAccountDetails) || EmptyUtils.isEmpty(walletAccountDetails.getDetails())){
                Map<String, Object> kwargs = new HashMap<>();
                String url = BaseUtil.isTestEnvironment() ? COMMISSION_TARGET_TEST : COMMISSION_TARGET_PROD;
                kwargs.put("commissionAddress",MessageFormat.format(url,userInfo.getAgentCode(),orderCode));
                dispatcher.utterTemplate("utter_commission_no_data_answer",kwargs);
                return abstractEvents;
            }
            List<WalletAccountDetailAppVo> details = walletAccountDetails.getDetails();
            //是否有互联网产品宣传资质  N-否、Y-是
            boolean allowInternetProductPromotion = "Y".equals(userInfo.getAllowInternetProductPromotion());
            boolean isAllDate = true;
            boolean isAllIssue = true;
            boolean isAllNotIssue = true;
            boolean isAllNotSettle = true;
            boolean isAllBusinessBefore = true;
            boolean isAllBusinessAfter = true;
            boolean isAllNotReview = true;
            String bankNo = null;
            for (WalletAccountDetailAppVo detail:details){
                //发放状态  发放时间  复核状态  复核时间  结算日期
                bankNo = detail.getLastFourbankAccountNo();
//                allowInternetProductPromotion = detail.isAllowInternetProductPromotion();
                if (YesNoEnum.YES.getCode().equals(detail.getStatus()) && EmptyUtils.isNotEmpty(detail.getCommissionBarMonth())){
                    //已发放
                    isAllNotIssue = false;
                }else {
                    //未发放
                    isAllIssue = false;
                }
                //是否全部未生效
                if (YesNoEnum.YES.getCode().equals(detail.getStatus())){
                    isAllNotSettle = false;
                }
                //是否全部生效
                if (YesNoEnum.NO.getCode().equals(detail.getStatus())){
                    isAllDate = false;
                }
                //审核状态 0待审核、1审核通过、2已打回
                String reviewStatus = detail.getReviewStatus();
                if (YesNoEnum.YES.getCodeString().equals(reviewStatus)){
                    isAllNotReview = false;
                    Date reviewTime = detail.getReviewTime();
                    DateTime dateTime = DateUtil.offsetDay(DateUtil.beginOfMonth(detail.getReviewTime()), 25);
                    if (reviewTime.after(dateTime)){
                        isAllBusinessBefore = false;
                    }
                    if (reviewTime.before(dateTime)){
                        isAllBusinessAfter = false;
                    }
                }else {
                    isAllBusinessBefore = false;
                    isAllBusinessAfter = false;
                }
            }
            if (isAllIssue){
                //全部已发
                Map<String, Object> kwargs = new HashMap<>();
                kwargs.put("bankNo",bankNo);
                dispatcher.utterTemplate("utter_commission_all_issue_answer",kwargs);
                abstractEvents.add(new SlotSet("is_evaluate",true));
                return abstractEvents;
            }else if (isAllNotIssue){
                //全部未发
                if (isAllDate){
                    //时间问题  核实审核状态
                    if (isAllBusinessBefore){
                        dispatcher.utterTemplate("utter_commission_month_end_answer");
                    }else if (isAllBusinessAfter){
                        dispatcher.utterTemplate("utter_commission_next_month_end_answer");
                    }else if (isAllNotReview){
                        dispatcher.utterTemplate("utter_commission_not_review_answer");
                    }else {
                        dispatcher.utterTemplate("utter_transfer_labor");
                        return abstractEvents;
                    }
                    abstractEvents.add(new SlotSet("is_evaluate",true));
                    return abstractEvents;
                }else if (isAllNotSettle){
                    //不满足发佣条件
                    boolean isEvaluate = dissatisfyCondition(dispatcher, slotsMap, orderType, allowInternetProductPromotion,tenantId);
                    if (isEvaluate){
                        abstractEvents.add(new SlotSet("is_evaluate",true));
                        return abstractEvents;
                    }
                }else {
                    //部分满足发佣条件
                    Map<String, Object> kwargs = new HashMap<>();
                    String url = BaseUtil.isTestEnvironment() ? COMMISSION_TARGET_TEST : COMMISSION_TARGET_PROD;
                    kwargs.put("commissionAddress",MessageFormat.format(url,userInfo.getAgentCode(),orderCode));
                    dispatcher.utterTemplate("utter_commission_party_issue_answer",kwargs);
                }
            }else {
                //部分未发
                Map<String, Object> kwargs = new HashMap<>();
                String url = BaseUtil.isTestEnvironment() ? COMMISSION_TARGET_TEST : COMMISSION_TARGET_PROD;
                kwargs.put("commissionAddress",MessageFormat.format(url,userInfo.getAgentCode(),orderCode));
                dispatcher.utterTemplate("utter_commission_party_no_issue_answer");
            }
        }
        return abstractEvents;
    }
    /**
    * @Description: 不满足发佣条件
    * @Param: [dispatcher, slotsMap, orderType, allowInternetProductPromotion]
    * @return: void
    * @Author: husw
    * @Date: 2023/11/13 13:49
    */
    private boolean dissatisfyCondition(CollectingDispatcher dispatcher, Map<String, String> slotsMap, Integer orderType,
                                               boolean allowInternetProductPromotion,String tenantId) {
        String policyType = slotsMap.get("policyType");
        //0:司内 1:司外
        if (YesNoEnum.YES.getCodeString().equals(policyType)){
            dispatcher.utterTemplate("utter_commission_prohibit_answer");
        }else {
            //司内
            String effectStatus = slotsMap.get("effectStatus");
            boolean effectStatusBl = YesNoEnum.YES.getCodeString().equals(effectStatus);
            if (OrderTypeEnum.SHORT.getType().equals(String.valueOf(orderType))){
                if (!allowInternetProductPromotion && effectStatusBl){
                    dispatcher.utterTemplate("utter_commission_not_allow_answer");
                }else if (allowInternetProductPromotion && !effectStatusBl){
                    dispatcher.utterTemplate("utter_commission_not_effect_answer");
                }else if (!allowInternetProductPromotion && !effectStatusBl){
                    dispatcher.utterTemplate("utter_commission_not_effect_allow_answer");
                }else {
                    dispatcher.utterTemplate("utter_transfer_labor");
                    return false;
                }
            }else {
                //查询订单续期信息
                ChitChatOrderVo orderInfo = kbcOlptService.findChitchatOrderInfo(slotsMap.get("orderId"),tenantId);
                if (EmptyUtils.isEmpty(orderInfo) || EmptyUtils.isEmpty(orderInfo.getOrderExceedHesitateDate())){
                    dispatcher.utterTemplate("utter_transfer_labor");
                    return false;
                }
                boolean isHesitation = YesNoEnum.YES.getCodeString().equals(orderInfo.getOrderExceedHesitateDate());
                boolean isVisit = YesNoEnum.YES.getCodeString().equals(slotsMap.get("visitStatus"));
                if (isHesitation && isVisit && !allowInternetProductPromotion){
                    dispatcher.utterTemplate("utter_commission_not_allow_answer");
                }else if (allowInternetProductPromotion && !isHesitation && isVisit){
                    dispatcher.utterTemplate("utter_commission_not_hesitation_answer");
                }else if (isHesitation && allowInternetProductPromotion && !isVisit){
                    dispatcher.utterTemplate("utter_commission_not_visit_answer");
                }else if (!allowInternetProductPromotion && !isHesitation && !isVisit){
                    dispatcher.utterTemplate("utter_commission_all_not_answer");
                }else if (allowInternetProductPromotion && !isHesitation && !isVisit){
                    dispatcher.utterTemplate("utter_commission_not_hesitation_visit_answer");
                }else if (!allowInternetProductPromotion && isHesitation && !isVisit){
                    dispatcher.utterTemplate("utter_commission_not_allow_visit_answer");
                }else if (!allowInternetProductPromotion && !isHesitation && isVisit){
                    dispatcher.utterTemplate("utter_commission_not_allow_hesitation_answer");
                }else {
                    dispatcher.utterTemplate("utter_transfer_labor");
                    return false;
                }
            }
        }
        return true;
    }

    private void setCreateOrderSlots(CollectingDispatcher dispatcher,Map<String,String> slotsMap,String userId,String tenantId) {
        UserInfoResp userInfoResp = kbcUcsService.getUserInfo(userId, tenantId);
        if (EmptyUtils.isEmpty(userInfoResp)){
            dispatcher.utterMessage("提交信息失败，失败原因：未查询到用户信息！请转人工处理");
            return;
        }
        String user = userInfoResp.getAgentName()+"-"+userInfoResp.getAgentPhone();
        String workContent = MessageFormat.format(WorkOrderConstant.COMMISSION_MESSAGE, user,slotsMap.get("orderId"),
                slotsMap.get("policyNo"),slotsMap.get("riskName"),slotsMap.get("insuredName"),"资金支付问题");
        slotsMap.put("workBigCategory","23");
        slotsMap.put("workSmallCagetory","2302");
        slotsMap.put("workActheadline","咨询佣金未收到");
        slotsMap.put("workAppel",workContent);
        slotsMap.put("workPhone",userInfoResp.getAgentPhone());
        slotsMap.put("workName",userInfoResp.getAgentName());
        slotsMap.put("workUserId",userInfoResp.getId());
    }
}
