package com.kbao.kbcchatbot.action.queryordervisit;

import com.kbao.common.enums.YesNoEnum;
import com.kbao.kbc.olpt.order.model.ChitChatOrderVo;
import com.kbao.kbcchatbot.constants.WorkOrderConstant;
import com.kbao.kbcchatbot.externalapi.work.ExternalApiService;
import com.kbao.kbcchatbot.discard.insurancecompany.entity.InsuranceCompany;
import com.kbao.kbcchatbot.discard.insurancecompany.service.InsuranceCompanyService;
import com.kbao.kbcchatbot.kbc.enums.OrderTypeEnum;
import com.kbao.kbcchatbot.kbc.olpt.KbcOlptService;
import com.kbao.kbcchatbot.kbc.ucs.KbcUcsService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AllSlotsReset;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;

/**
 * @program: kbc-chatbot
 * @description: 询问是否回访成功
 * @author: husw
 * @create: 2023-10-25 10:12
 **/
@Component
@Slf4j
public class ActionQueryVisitStatus implements Action {

    @Autowired
    private KbcOlptService kbcOlptService;
    @Autowired
    private KbcUcsService kbcUcsService;
    @Autowired
    private ExternalApiService externalApiService;
    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    @Override
    public String name() {
        return "action_query_return_visit_status";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_query_return_visit_status>>>>:询问是否回访成功");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        String tenantId = tracker.getSlotValue("tenant_id", String.class);
        String userId = tracker.getSlotValue("user_id", String.class);
        String visitStatus = slotsMap.get("visitStatus");
        String orderType = slotsMap.get("orderType");
        List<AbstractEvent> abstractEvents = new ArrayList<>();
        //订单回访状态
        boolean isVisited = YesNoEnum.YES.getCodeString().equals(visitStatus);
        //查询订单  订单类型枚举 uoc.dic.order.type
        if (OrderTypeEnum.LPT.getType().equals(orderType) || OrderTypeEnum.ONLINE.getType().equals(orderType)){
            //寿险 3-线下寿险 4-电投寿险
            if (isVisited){
                dispatcher.utterTemplate("utter_visited_answer");
            }else {
                //查询客服热线
                InsuranceCompany insuranceCompany = insuranceCompanyService.getInsuranceCompanyByName(slotsMap.get("companyName"));
                String tel = "";
                if (EmptyUtils.isNotEmpty(insuranceCompany)){
                    tel = insuranceCompany.getPhone();
                }
                Map<String, Object> kwargs = new HashMap<>();
                kwargs.put("tel",tel);
                dispatcher.utterTemplate("utter_life_not_visit_answer",kwargs);
                abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
                abstractEvents.add(new SlotSet("is_evaluate",true));
                return abstractEvents;
            }

        }else if (OrderTypeEnum.SHORT.getType().equals(orderType)){
            //短险
            ChitChatOrderVo orderInfo = kbcOlptService.findChitchatOrderInfo(slotsMap.get("orderId"),tenantId);
            if (YesNoEnum.YES.getCodeString().equals(orderInfo.getLongMedical())){
                if (YesNoEnum.YES.getCodeString().equals(visitStatus)){
                    dispatcher.utterTemplate("utter_visited_answer");
                    abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
                    abstractEvents.add(new SlotSet("is_evaluate",true));
                    return abstractEvents;
                }else {
                    //创建工单
                    setCreateOrderSlots(dispatcher,slotsMap,userId,tenantId);
                    Map<String, Object> kwargs = new HashMap<>();
                    kwargs.put("orderId",slotsMap.get("orderId"));
                    kwargs.put("question","回访");
                    dispatcher.utterTemplate("utter_create_service_verify",kwargs);
                    return Collections.singletonList(new SlotSet("slots_map",slotsMap));
                }
            }else {
                dispatcher.utterTemplate("utter_short_answer");
            }
        } else if (OrderTypeEnum.NMI.getType().equals(orderType)) {
            //期缴
            if (isVisited){
                dispatcher.utterTemplate("utter_visited_answer");
                abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
                abstractEvents.add(new SlotSet("is_evaluate",true));
                return abstractEvents;
            }else {
                String companyName = slotsMap.get("companyName");
                if (companyName.contains("平安健康")){
                    dispatcher.utterTemplate("utter_nmi_pingan_answer");
                }else if (companyName.contains("太平洋健康")){
                    dispatcher.utterTemplate("utter_nmi_tpy_answer");
                } else if (companyName.contains("和泰人寿")) {
                    dispatcher.utterTemplate("utter_nmi_htrs_answer");
                }else {
                    //创建工单
                    setCreateOrderSlots(dispatcher,slotsMap,userId,tenantId);
                    Map<String, Object> kwargs = new HashMap<>();
                    kwargs.put("orderId",slotsMap.get("orderId"));
                    kwargs.put("question","回访");
                    dispatcher.utterTemplate("utter_create_service_verify",kwargs);
                    return Collections.singletonList(new SlotSet("slots_map",slotsMap));
                }
                abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
                abstractEvents.add(new SlotSet("is_evaluate",true));
                return abstractEvents;
            }
        } else {
            dispatcher.utterMessage("此订单暂时无法查询回访信息！");
        }
        return Collections.emptyList();
    }

    private void setCreateOrderSlots(CollectingDispatcher dispatcher,Map<String,String> slotsMap,String userId,String tenantId) {
        UserInfoResp userInfoResp = kbcUcsService.getUserInfo(userId, tenantId);
        if (EmptyUtils.isEmpty(userInfoResp)){
            dispatcher.utterMessage("提交信息失败，失败原因：未查询到用户信息！请转人工处理");
            return;
        }
        String user = userInfoResp.getAgentName()+"-"+userInfoResp.getAgentPhone();
        String workContent = MessageFormat.format(WorkOrderConstant.ORDER_STATUS_MESSAGE, user,slotsMap.get("orderId"),
                slotsMap.get("policyNo"),slotsMap.get("companyName"),slotsMap.get("riskName"),slotsMap.get("insuredName"),slotsMap.get("insuredCardNo"),"顾问询问回访问题");
        slotsMap.put("workBigCategory","22");
        slotsMap.put("workSmallCagetory","2204");
        slotsMap.put("workActheadline","顾问询问是否回访");
        slotsMap.put("workAppel",workContent);
        slotsMap.put("workPhone",userInfoResp.getAgentPhone());
        slotsMap.put("workName",userInfoResp.getAgentName());
        slotsMap.put("workUserId",userId);
    }
}
