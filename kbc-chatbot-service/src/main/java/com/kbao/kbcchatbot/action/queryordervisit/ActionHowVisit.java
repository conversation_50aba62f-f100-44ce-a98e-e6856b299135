package com.kbao.kbcchatbot.action.queryordervisit;

import com.kbao.kbcchatbot.discard.insurancecompanyrule.entity.InsuranceCompanyRule;
import com.kbao.kbcchatbot.discard.insurancecompanyrule.service.InsuranceCompanyRuleService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description: 询问如何回访
 * @author: husw
 * @create: 2023-10-25 10:24
 **/
@Component
@Slf4j
public class ActionHowVisit implements Action {

    @Autowired
    private InsuranceCompanyRuleService insuranceCompanyRuleService;

    @Override
    public String name() {
        return "action_query_how_return_visit";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_query_how_return_visit>>>>:询问如何回访");
        //需要查询产品表  附件
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        List<InsuranceCompanyRule> companyRules = insuranceCompanyRuleService.getInsuranceCompanyRuleByName(slotsMap.get("companyName"));
        if (EmptyUtils.isEmpty(companyRules)){
            dispatcher.utterTemplate("utter_visit_transfer_labor");
            return Collections.emptyList();
        }
        Map<String, Object> kwargs = new HashMap<>();
        StringBuffer channel = new StringBuffer();
        StringBuffer rule = new StringBuffer();
        for (InsuranceCompanyRule companyRule:companyRules){
            channel.append(companyRule.getFollowUpType());
            rule.append(companyRule.getFollowUpRule());
        }
        kwargs.put("channel",channel);
        kwargs.put("address",rule);
        dispatcher.utterTemplate("utter_how_visit_answer",kwargs);
        return Collections.singletonList(new SlotSet("is_evaluate",true));
    }
}
