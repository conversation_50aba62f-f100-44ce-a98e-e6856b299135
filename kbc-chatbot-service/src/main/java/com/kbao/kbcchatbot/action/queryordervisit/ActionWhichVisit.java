package com.kbao.kbcchatbot.action.queryordervisit;

import com.kbao.common.enums.YesNoEnum;
import com.kbao.kbc.olpt.order.model.ChitChatOrderVo;
import com.kbao.kbcchatbot.kbc.enums.OrderTypeEnum;
import com.kbao.kbcchatbot.kbc.olpt.KbcOlptService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description: 什么产品需要回访
 * @author: husw
 * @create: 2023-10-25 10:24
 **/
@Component
@Slf4j
public class ActionWhichVisit implements Action {

    @Autowired
    private KbcOlptService kbcOlptService;

    @Override
    public String name() {
        return "action_query_which_return_visit";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_query_which_return_visit>>>>:什么产品需要回访");
        //需要查询
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        String tenantId = tracker.getSlotValue("tenant_id", String.class);
        if (EmptyUtils.isEmpty(slotsMap)){
            dispatcher.utterTemplate("utter_no_answer");
            return Collections.emptyList();
        }
        //查询订单  订单类型枚举 uoc.dic.order.type
        if (OrderTypeEnum.NMI.getType().equals(slotsMap.get("orderType"))){
            //期缴
            dispatcher.utterTemplate("utter_nmi_visit_answer");
            return Collections.singletonList(new SlotSet("is_evaluate",true));
        } else if (OrderTypeEnum.SHORT.getType().equals(slotsMap.get("orderType"))){
            boolean isVisited = false;
            ChitChatOrderVo orderInfo = kbcOlptService.findChitchatOrderInfo(slotsMap.get("orderId"),tenantId);
            if (YesNoEnum.YES.getCodeString().equals(orderInfo.getLongMedical())){
                isVisited = true;
            }
            if (isVisited){
                dispatcher.utterTemplate("utter_short_visit_answer");
            }else {
                dispatcher.utterTemplate("utter_short_not_visit_answer");
            }
            return Collections.singletonList(new SlotSet("is_evaluate",true));
        }else {
            dispatcher.utterTemplate("utter_short_visit_answer");
        }
        return Collections.emptyList();
    }
}
