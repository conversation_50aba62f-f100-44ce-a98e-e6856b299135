package com.kbao.kbcchatbot.action.common;

import com.kbao.kbcchatbot.customer.WorkOrderReq;
import com.kbao.kbcchatbot.customer.WorkOrderVO;
import com.kbao.kbcchatbot.externalapi.work.ExternalApiService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description: 创建工单
 * @author: husw
 * @create: 2023-10-25 09:25
 **/
@Component
@Slf4j
public class ActionCreateWorkOrder implements Action {

    @Autowired
    private ExternalApiService externalApiService;


    @Override
    public String name() {
        return "action_create_work_order";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_create_work_order>>>>:创建工单");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        if (EmptyUtils.isEmpty(slotsMap)){
            dispatcher.utterTemplate("utter_no_answer");
            return Collections.emptyList();
        }
        String bigCategory = slotsMap.get("workBigCategory");
        String smallCagetory = slotsMap.get("workSmallCagetory");
        String actheadline = slotsMap.get("workActheadline");
        String appel = slotsMap.get("workAppel");
        String phone = slotsMap.get("workPhone");
        String name = slotsMap.get("workName");
        String userId = slotsMap.get("workUserId");
        WorkOrderReq workOrderReq = new WorkOrderReq();
        workOrderReq.setDtUserId(userId);
        workOrderReq.setBigCategory(bigCategory);
        workOrderReq.setSmallCagetory(smallCagetory);
        workOrderReq.setActheadline(actheadline);
        workOrderReq.setAppel(appel);
        workOrderReq.setPhone(phone);
        workOrderReq.setName(name);
        workOrderReq.setFlowMarker("DT_FLOW_01");
        try{
            WorkOrderVO workOrder = externalApiService.createWorkOrder(workOrderReq);
            if (EmptyUtils.isNotEmpty(workOrder)){
                Map<String, Object> param = new HashMap<>();
                param.put("workNo",workOrder.getId());
                dispatcher.utterTemplate("utter_create_service_order",param);
                return Collections.singletonList(new SlotSet("is_evaluate",true));
            }else {
                dispatcher.utterMessage("提交信息失败，失败原因：创建工单失败。请转人工处理");
            }
        }catch (Exception e){
            log.info("创建工单失败：",e);
            dispatcher.utterMessage("提交信息失败，失败原因：创建工单失败。请转人工处理");
        }
        return Collections.emptyList();
    }
}
