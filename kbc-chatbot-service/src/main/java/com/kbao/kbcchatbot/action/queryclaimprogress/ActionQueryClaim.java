package com.kbao.kbcchatbot.action.queryclaimprogress;

import com.kbao.kbcchatbot.kbc.claim.KbcClaimService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcclaim.order.model.ClaimAgingReq;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-10-24 14:00
 **/
@Component
@Slf4j
public class ActionQueryClaim implements Action {


    @Autowired
    private KbcClaimService kbcClaimService;

    @Override
    public String name() {
        return "action_query_claim";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_query_claim:>>查询理赔进度");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        String tenantId = tracker.getSlotValue("tenant_id", String.class);
        String userId = tracker.getSlotValue("user_id", String.class);
        String riskName = slotsMap.get("riskName");
        List<AbstractEvent> abstractEvents = new ArrayList<>();
        if (EmptyUtils.isNotEmpty(riskName) && riskName.contains("人保大护甲")){
            //人保大护甲
            ClaimAgingReq claimAgingReq = new ClaimAgingReq();
            claimAgingReq.setpId(slotsMap.get("pId"));
            claimAgingReq.setCompanyId(slotsMap.get("commonCompanyCode"));
            claimAgingReq.setUserId(userId);
            Integer status = kbcClaimService.getClaimAging(claimAgingReq,tenantId);
            Boolean isOverTime = null;
            if (EmptyUtils.isEmpty(status)){
                dispatcher.utterTemplate("utter_not_claim_labor");
            }else {
                //查询是否超出失效 0-已超时  1-未超时
                isOverTime = Integer.valueOf(0).equals(status);
                if (isOverTime){
                    dispatcher.utterTemplate("utter_provider_dhj_claim_info");
                }else {
                    dispatcher.utterTemplate("utter_not_over_time");
                    abstractEvents.add(new SlotSet("is_evaluate",true));
                }
            }
            abstractEvents.add(new SlotSet("is_product_dhj", true));
            abstractEvents.add(new SlotSet("is_over_time", isOverTime));
            return abstractEvents;
        }else {
            //其他
            dispatcher.utterTemplate("utter_ask_apply_type");
            abstractEvents.add(new SlotSet("is_product_dhj",false));
            return abstractEvents;
        }
    }
}
