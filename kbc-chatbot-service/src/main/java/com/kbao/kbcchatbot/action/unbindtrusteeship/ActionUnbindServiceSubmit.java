package com.kbao.kbcchatbot.action.unbindtrusteeship;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.*;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @program: kbc-chatbot
 * @description: 提交表单
 * @author: husw
 * @create: 2023-11-28 11:36
 **/
@Component
public class ActionUnbindServiceSubmit implements Action {
    @Override
    public String name() {
        return "action_unbind_service_submit";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        String cusId = tracker.getSlotValue("cus_id", String.class);
        if (EmptyUtils.isNotEmpty(cusId)){
            //解绑
            String customerName = tracker.getSlotValue("customer_name", String.class);
            Map<String, Object> kwargs = new HashMap<>();
            kwargs.put("customerName",customerName);
            kwargs.put("cusCardId",cusId);
            dispatcher.utterTemplate("utter_comfirm_unbind_relation",kwargs);

        }
        else {
            dispatcher.utterTemplate("utter_info_not_equal_answer");
//            //非同一个人给出提示
//            dispatcher.utterTemplate("utter_info_not_equal_answer");
//            //停用表单
//            List<AbstractEvent> events = new ArrayList<>();
//            events.add(new ActionExecuted("action_deactivate_loop"));
//            events.add(new AllSlotsReset());
//            return events;
        }
        return Collections.emptyList();
    }
}
