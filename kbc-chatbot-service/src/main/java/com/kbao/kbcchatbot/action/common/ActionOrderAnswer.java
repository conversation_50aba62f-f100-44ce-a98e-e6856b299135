package com.kbao.kbcchatbot.action.common;

import com.kbao.kbcchatbot.maas.channel.channelguessquestion.enums.GuessQuestionSourceEnum;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 创建工单
 * @author: husw
 * @create: 2023-10-25 09:25
 **/
@Component
@Slf4j
public class ActionOrderAnswer implements Action {

    @Override
    public String name() {
        return "action_order_answer";
    }



    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_order_answer>>>>:订单");
        String sourceType = tracker.getSlotValue("source_type", String.class);
        if (GuessQuestionSourceEnum.ORDER.getKey().equals(sourceType)){
            //订单
            dispatcher.utterMessage("guess_answer");
        }else {
            dispatcher.utterTemplate("utter_greet_answer");
        }
        return Collections.emptyList();
    }
}
