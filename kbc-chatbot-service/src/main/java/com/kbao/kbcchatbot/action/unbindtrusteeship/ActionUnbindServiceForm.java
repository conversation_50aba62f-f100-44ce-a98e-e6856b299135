package com.kbao.kbcchatbot.action.unbindtrusteeship;

import com.kbao.common.enums.YesNoEnum;
import com.kbao.kbcchatbot.kbc.pm.KbcPmService;
import com.kbao.kbcchatbot.kbc.ucs.KbcUcsService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.form.FormValidationAction;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcpm.common.PmCommonVO;
import com.kbao.kbcpm.customer.vo.host.CustomerBindInfoVO;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-11-28 11:30
 **/
@Component
@Slf4j
public class ActionUnbindServiceForm extends FormValidationAction {

    @Autowired
    private KbcUcsService kbcUcsService;

    @Autowired
    private KbcPmService kbcPmService;

    @Override
    public String name() {
        return "validate_unbind_service_form";
    }

    private boolean isSameAgent = false;

    @Override
    protected List<String> requiredSlots(List<String> slots, CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("validate_unbind_service_form>>>>:解除托管服务关系表单");
        String agentCardId = tracker.getSlotValue("agent_card_id", String.class);
        String customerName = tracker.getSlotValue("customer_name", String.class);
        if (EmptyUtils.isNotEmpty(agentCardId)){
            slots.add("customer_name");
            slots.add("cus_id");
        }


//        boolean isCheckUser = false;
//        //只需要填充agentCardId的时候才需要查询用户值
//        if (EmptyUtils.isNotEmpty(agentCardId) && EmptyUtils.isEmpty(customerName)) {
//             isCheckUser = true;
//        }
//        boolean isSameInfo = false;
//        if (isCheckUser){
//            String tenantId = tracker.getSlotValue("tenant_id", String.class);
//            String userId = tracker.getSlotValue("user_id", String.class);
//            UserInfoResp userInfo = kbcUcsService.getUserInfo(userId, tenantId);
//            if (EmptyUtils.isEmpty(userInfo)){
//                return slots;
//            }
//            String identityCode = userInfo.getIdentityCode();
//            String phone = userInfo.getPhone();
//            if (identityCode.equals(agentCardId) || phone.equals(agentCardId)) {
//                //信息一致
//                isSameInfo = true;
//
//            }
//        }
//        if ((!isCheckUser && EmptyUtils.isNotEmpty(agentCardId)) || (isCheckUser && isSameInfo)){
//            slots.add("customer_name");
//            slots.add("cus_id");
//        }
        return slots;
    }

    public Map<String, Object> validateAgentCardId(Object slotValue, CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        Map<String, Object> result = new HashMap<>();
        log.info("sltValue:{}", slotValue);
        //校验顾问证件号
        if (EmptyUtils.isEmpty(slotValue)) {
            result.put("agent_card_id", null);
        } else {
            String tenantId = tracker.getSlotValue("tenant_id", String.class);
            String userId = tracker.getSlotValue("user_id", String.class);
            UserInfoResp userInfo = kbcUcsService.getUserInfo(userId, tenantId);
            if (EmptyUtils.isEmpty(userInfo)){
                result.put("agent_card_id", null);
            }
            String identityCode = userInfo.getIdentityCode();
            String phone = userInfo.getPhone();
            if (identityCode.equals(slotValue) || phone.equals(slotValue)) {
                //信息一致
                result.put("agent_card_id", slotValue);
            }else {
                dispatcher.utterTemplate("utter_info_not_equal_answer");
                result.put("agent_card_id", null);
            }
        }
        //查询订单信息
        return result;
    }

    public Map<String, Object> validateCustomerName(Object slotValue, CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        Map<String, Object> result = new HashMap<>();
        log.info("sltValue:{}", slotValue);
        //校验客户姓名
        if (EmptyUtils.isEmpty(slotValue)) {
            result.put("customer_name", null);
        } else {
            result.put("customer_name", slotValue);
        }
        //查询订单信息
        return result;
    }
//
    public Map<String, Object> validateCusId(Object slotValue, CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        Map<String, Object> result = new HashMap<>();
        log.info("sltValue:{}", slotValue);
        //校验客户姓名
        if (EmptyUtils.isEmpty(slotValue)) {
            result.put("cus_id", null);
        } else {
            //校验用户是否是托管服务用户
            String cusCardId = tracker.getSlotValue("cus_id",String.class);
            String customerName = tracker.getSlotValue("customer_name",String.class);
            String tenantId = tracker.getSlotValue("tenant_id", String.class);
            String userId = tracker.getSlotValue("user_id", String.class);
            PmCommonVO commonVO = new PmCommonVO();
            commonVO.setUserId(userId);
            commonVO.setCustomerName(customerName);
            commonVO.setCertificatesNum(cusCardId);
            CustomerBindInfoVO bindInfo = kbcPmService.getBindInfo(commonVO,tenantId);
            if (EmptyUtils.isNotEmpty(bindInfo) && YesNoEnum.YES.getCode().equals(bindInfo.getHostingStatus())
                    && YesNoEnum.YES.getCode().equals(bindInfo.getServiceFlag())){
                result.put("cus_id", slotValue);
            }else {
                dispatcher.utterTemplate("utter_not_relation");
                result.put("customer_name", null);
                result.put("cus_id", null);
            }
        }
        //查询订单信息
        return result;
    }
}
