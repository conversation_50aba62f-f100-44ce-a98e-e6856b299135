package com.kbao.kbcchatbot.action.queryrenewal;

import com.kbao.kbc.olpt.order.model.ChitChatOrderVo;
import com.kbao.kbcchatbot.constants.WorkOrderConstant;
import com.kbao.kbcchatbot.externalapi.work.ExternalApiService;
import com.kbao.kbcchatbot.kbc.enums.OrderTypeEnum;
import com.kbao.kbcchatbot.kbc.olpt.KbcOlptService;
import com.kbao.kbcchatbot.kbc.ucs.KbcUcsService;
import com.kbao.kbcchatbot.kbc.uoc.KbcUocService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AllSlotsReset;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;

/**
 * @program: kbc-chatbot
 * @description: 订单续期扣费
 * @author: husw
 * @create: 2023-10-25 09:25
 **/
@Component
@Slf4j
public class ActionQueryRenewalDeduction implements Action {

    @Autowired
    private ExternalApiService externalApiService;

    @Autowired
    private KbcUcsService kbcUcsService;

    @Autowired
    private KbcUocService kbcUocService;

    @Autowired
    private KbcOlptService kbcOlptService;

    @Override
    public String name() {
        return "action_query_renewal_deduction";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_query_renewal_deduction>>>>:查询订单续期扣费");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        String tenantId = tracker.getSlotValue("tenant_id", String.class);
        String userId = tracker.getSlotValue("user_id", String.class);
        String orderType = slotsMap.get("orderType");
        //查询订单  订单类型枚举 uoc.dic.order.type
        if (OrderTypeEnum.ONLINE.getType().equals(orderType) || OrderTypeEnum.LPT.getType().equals(orderType)){
            //寿险-创建工单处理  3-线下寿险 4-电投寿险
            setCreateOrderSlots(dispatcher,slotsMap,userId,tenantId);
            Map<String, Object> kwargs = new HashMap<>();
            kwargs.put("orderId",slotsMap.get("orderId"));
            kwargs.put("question","续期扣费");
            dispatcher.utterTemplate("utter_create_service_verify",kwargs);
            return Collections.singletonList(new SlotSet("slots_map",slotsMap));
        }else {
            List<AbstractEvent> abstractEvents = new ArrayList<>();
            abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
            //查询订单续期信息
            ChitChatOrderVo chitchatOrderInfo = kbcOlptService.findChitchatOrderInfo(slotsMap.get("orderId"),tenantId);
            if (EmptyUtils.isEmpty(chitchatOrderInfo)){
                //查询失败创建工单
                setCreateOrderSlots(dispatcher,slotsMap,userId,tenantId);
                Map<String, Object> kwargs = new HashMap<>();
                kwargs.put("orderId",slotsMap.get("orderId"));
                kwargs.put("question","续期扣费");
                dispatcher.utterTemplate("utter_create_service_verify",kwargs);
                return Collections.singletonList(new SlotSet("slots_map",slotsMap));
            }
            if (OrderTypeEnum.SHORT.getType().equals(orderType)){
                //短险
                if ("1".equals(chitchatOrderInfo.getLongMedical())){
                    //长期医疗险
                    if ("1".equals(chitchatOrderInfo.getRenewalStatus())){
                        //已续购续期
                        Map<String, Object> kwargs = new HashMap<>();
                        kwargs.put("productName",slotsMap.get("riskName"));
                        dispatcher.utterTemplate("utter_renewal_exist_answer",kwargs);
                    }else {
                        //未续费或续费失败
                        dispatcher.utterTemplate("utter_renewal_not_exist_answer");
                    }
                }else {
                    //其他-创建工单
                    setCreateOrderSlots(dispatcher,slotsMap,userId,tenantId);
                    Map<String, Object> kwargs = new HashMap<>();
                    kwargs.put("orderId",slotsMap.get("orderId"));
                    kwargs.put("question","续期扣费");
                    dispatcher.utterTemplate("utter_create_service_verify",kwargs);
                    return Collections.singletonList(new SlotSet("slots_map",slotsMap));
                }
            } else if (OrderTypeEnum.NMI.getType().equals(orderType)) {
                //期缴
                if ("1".equals(chitchatOrderInfo.getRenewalStatus())){
                    //已续购续期
                    Map<String, Object> kwargs = new HashMap<>();
                    kwargs.put("date",chitchatOrderInfo.getDeductionDate());
                    dispatcher.utterTemplate("utter_renewal_success_answer",kwargs);
                    abstractEvents.add(new SlotSet("is_evaluate",true));
                }else {
                    //未续费或续费失败
                    String reasonsForFailure = chitchatOrderInfo.getReasonsForFailure();
                    if (EmptyUtils.isNotEmpty(reasonsForFailure) && reasonsForFailure.contains("余额不足")){
                        Map<String, Object> kwargs = new HashMap<>();
                        kwargs.put("date",chitchatOrderInfo.getDeductionDate());
                        dispatcher.utterTemplate("utter_renewal_fail_answer",kwargs);
                        abstractEvents.add(new SlotSet("is_evaluate",true));
                    }else {
                        //其他原因-创建工单
                        setCreateOrderSlots(dispatcher,slotsMap,userId,tenantId);
                        Map<String, Object> kwargs = new HashMap<>();
                        kwargs.put("orderId",slotsMap.get("orderId"));
                        kwargs.put("question","续期扣费");
                        dispatcher.utterTemplate("utter_create_service_verify",kwargs);
                        return Collections.singletonList(new SlotSet("slots_map",slotsMap));
                    }
                }
            } else {
                dispatcher.utterMessage("此订单暂时无法查询续期扣费信息！");
            }
            return abstractEvents;
        }
    }

    private void setCreateOrderSlots(CollectingDispatcher dispatcher,Map<String,String> slotsMap,String userId,String tenantId) {
        UserInfoResp userInfoResp = kbcUcsService.getUserInfo(userId, tenantId);
        if (EmptyUtils.isEmpty(userInfoResp)){
            dispatcher.utterMessage("提交信息失败，失败原因：未查询到用户信息！请转人工处理");
            return;
        }
        String user = userInfoResp.getAgentName()+"-"+userInfoResp.getAgentPhone();
        String workContent = MessageFormat.format(WorkOrderConstant.RENEWAL_DEDUCTION_MESSAGE, user,slotsMap.get("orderId"),
                slotsMap.get("policyNo"),slotsMap.get("companyName"),slotsMap.get("insuredName"),slotsMap.get("insuredCardNo"),"续期扣费问题");
        slotsMap.put("workBigCategory","5");
        slotsMap.put("workSmallCagetory","501");
        slotsMap.put("workActheadline","顾问询问订单续期扣费是否成功");
        slotsMap.put("workAppel",workContent);
        slotsMap.put("workPhone",userInfoResp.getAgentPhone());
        slotsMap.put("workName",userInfoResp.getAgentName());
        slotsMap.put("workUserId",userId);
    }
}
