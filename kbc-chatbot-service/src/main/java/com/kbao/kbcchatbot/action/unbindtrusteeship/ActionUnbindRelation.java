package com.kbao.kbcchatbot.action.unbindtrusteeship;

import com.kbao.kbcchatbot.kbc.pm.KbcPmService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.ActionExecuted;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AllSlotsReset;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcpm.customer.vo.customer.CustomerUnbindVO;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @program: kbc-chatbot
 * @description: 解除绑定
 * @author: husw
 * @create: 2023-10-25 10:24
 **/
@Component
@Slf4j
public class ActionUnbindRelation implements Action {

    @Autowired
    private KbcPmService kbcPmService;

    @Override
    public String name() {
        return "action_unbind_relation";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        String cusCardId = tracker.getSlotValue("cus_id",String.class);
        String customerName = tracker.getSlotValue("customer_name",String.class);
        String tenantId = tracker.getSlotValue("tenant_id", String.class);
        String userId = tracker.getSlotValue("user_id", String.class);
        List<AbstractEvent> events = new ArrayList<>();
        if (EmptyUtils.isEmpty(cusCardId) || EmptyUtils.isEmpty(customerName)){
            dispatcher.utterTemplate("utter_no_answer");
            //停用表单
            events.add(new ActionExecuted("action_deactivate_loop"));
            events.add(new AllSlotsReset());
            return events;
        }
        CustomerUnbindVO customerUnbindVO = new CustomerUnbindVO();
        customerUnbindVO.setUserId(userId);
        customerUnbindVO.setCustomerName(customerName);
        customerUnbindVO.setCertificatesNum(cusCardId);
        customerUnbindVO.setUnBindReason("顾问:"+userId+"通过智聊解除托管/服务关系");
        Boolean flag = kbcPmService.unbindAll(customerUnbindVO, tenantId);
        if (!flag){
            dispatcher.utterTemplate("utter_unbind_fail_answer");
            events.add(new ActionExecuted("action_deactivate_loop"));
            return events;
        }
        Map<String, Object> kwargs = new HashMap<>();
        kwargs.put("customerName",customerName);
        kwargs.put("cusCardId",cusCardId);
        dispatcher.utterTemplate("utter_unbind_relation_answer",kwargs);
        return Collections.singletonList(new SlotSet("is_evaluate",true));
    }
}
