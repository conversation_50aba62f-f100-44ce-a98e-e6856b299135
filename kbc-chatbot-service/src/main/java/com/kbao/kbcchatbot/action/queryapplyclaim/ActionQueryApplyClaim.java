package com.kbao.kbcchatbot.action.queryapplyclaim;

import com.kbao.kbcbsc.dicitems.entity.DicItems;
import com.kbao.kbcchatbot.discard.insurancecompany.entity.InsuranceCompany;
import com.kbao.kbcchatbot.discard.insurancecompany.service.InsuranceCompanyService;
import com.kbao.kbcchatbot.kbc.bsc.KbcBscService;
import com.kbao.kbcchatbot.kbc.enums.OrderTypeEnum;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-10-25 09:40
 **/
@Component
@Slf4j
public class ActionQueryApplyClaim implements Action {

    @Autowired
    private KbcBscService kbcBscService;

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    private static final String MESSAGE = "此订单为{0}订单，不属于互联网订单，需要转人工处理";

    @Override
    public String name() {
        return "action_query_apply_claim";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_query_apply_claim>>>>>查询理赔申请回复");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        //寿险订单转人工处理
        Map<String, Object> kwargs = new HashMap<>();
        String orderType = slotsMap.get("orderType");
        if(OrderTypeEnum.LPT.getType().equals(orderType) || OrderTypeEnum.ONLINE.getType().equals(orderType)){
            String content = MessageFormat.format(MESSAGE, OrderTypeEnum.getValueByType(orderType));
            kwargs.put("message",content);
            dispatcher.utterTemplate("utter_custom_labor",kwargs);
            return Collections.singletonList(new SlotSet("is_evaluate",false));
        }else{
            if (slotsMap.get("riskName").contains("人保大护甲")){
                dispatcher.utterTemplate("utter_rb_answer");
            }else {
                //查询是否支持自助理赔
                List<DicItems> selfClaimCompanys = kbcBscService.getDicItems("claim.self.company.code");
                //查询客服热线
                InsuranceCompany insuranceCompany = insuranceCompanyService.getInsuranceCompanyByName(slotsMap.get("companyName"));
                String tel = "";
                if (EmptyUtils.isNotEmpty(insuranceCompany)){
                    tel = insuranceCompany.getPhone();
                }
                kwargs.put("tel",tel);
                boolean isSupport = false;
                if (EmptyUtils.isNotEmpty(selfClaimCompanys)){
                    for (DicItems dicItems:selfClaimCompanys){
                        if (dicItems.getDicItemCode().equals(slotsMap.get("commonCompanyCode"))){
                            isSupport = true;
                            break;
                        }
                    }
                }
                if (isSupport){
                    dispatcher.utterTemplate("utter_support_answer",kwargs);
                }else {
                    dispatcher.utterTemplate("utter_nonsupport_answer",kwargs);
                }
            }
        }
        return Collections.singletonList(new SlotSet("is_evaluate",true));
    }
}
