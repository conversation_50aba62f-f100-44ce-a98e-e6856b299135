package com.kbao.kbcchatbot.action.queryclaimprogress;

import com.alibaba.fastjson.JSONObject;
import com.kbao.kbcchatbot.customer.WorkOrderReq;
import com.kbao.kbcchatbot.customer.WorkOrderVO;
import com.kbao.kbcchatbot.externalapi.work.ExternalApiService;
import com.kbao.kbcchatbot.kbc.ucs.KbcUcsService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kbao.kbcchatbot.constants.WorkOrderConstant.QUERY_CLAIM_PROGRESS;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-10-24 14:01
 **/
@Component
@Slf4j
public class ActionSubmitWork implements Action {

    @Autowired
    private ExternalApiService externalApiService;

    @Autowired
    private KbcUcsService kbcUcsService;

    @Override
    public String name() {
        return "action_submit_work";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_submit_work:>>>>>查询理赔进度提交工单");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        String tenantId = tracker.getSlotValue("tenant_id", String.class);
        String userId = tracker.getSlotValue("user_id", String.class);
        log.info("slotsMap数据:{}", JSONObject.toJSONString(slotsMap));
        Object isOverTimeObj = tracker.getSlotValue("is_over_time");
        boolean isOverTime = EmptyUtils.isNotEmpty(isOverTimeObj) && (boolean) isOverTimeObj;
        String applyType = tracker.getSlotValue("apply_type",String.class);
        if (isOverTime || "company".equals(applyType)){
            String dateOfOccurrence = slotsMap.get("dateOfOccurrence");
            String submitInformationData = slotsMap.get("submitInformationData");
            String submitInformationType = slotsMap.get("submitInformationType");
            String phone = slotsMap.get("phone");
            String orderCode = slotsMap.get("orderId");
            String policyCode = slotsMap.get("policyNo");
            String companyName = slotsMap.get("companyName");
            String email = slotsMap.get("email");

            UserInfoResp userInfoResp = kbcUcsService.getUserInfo(userId, tenantId);
            if (EmptyUtils.isEmpty(userInfoResp)){
                dispatcher.utterMessage("提交信息失败，失败原因：未查询到用户信息！请转人工处理");
                return Collections.emptyList();
            }
            try{
                WorkOrderVO workOrder = createWorkOrder(dateOfOccurrence, submitInformationData, submitInformationType,
                        phone, email, orderCode, policyCode, companyName, userInfoResp);
                if (EmptyUtils.isNotEmpty(workOrder)){
                    Map<String, Object> kwargs = new HashMap<>();
                    kwargs.put("workNo",workOrder.getId());
                    dispatcher.utterTemplate("utter_create_service_order",kwargs);
                    return Collections.singletonList(new SlotSet("is_evaluate",true));
                }else {
                    dispatcher.utterMessage("提交信息失败，失败原因：创建工单失败。请转人工处理");
                }
            }catch (Exception e){
                log.info("创建工单失败：",e);
                dispatcher.utterMessage("提交信息失败，失败原因：创建工单失败。请转人工处理");
            }
        }

        return Collections.emptyList();
    }

    private WorkOrderVO createWorkOrder(String dateOfOccurrence, String submitInformationData, String submitInformationType,
                                        String phone, String email, String orderCode, String policyCode, String companyName,
                                        UserInfoResp userInfoResp) {
        WorkOrderReq workOrderReq = new WorkOrderReq();
        workOrderReq.setDtUserId(ChatUserUtil.getUserId());
        workOrderReq.setBigCategory("5");
        workOrderReq.setSmallCagetory("501");
        workOrderReq.setActheadline("顾问催理赔工单");
        String content = MessageFormat.format(QUERY_CLAIM_PROGRESS, orderCode, policyCode, companyName, dateOfOccurrence, submitInformationData,
                submitInformationType, phone, email);
        workOrderReq.setAppel(content);
        workOrderReq.setPhone(userInfoResp.getAgentPhone());
        workOrderReq.setName(userInfoResp.getAgentName());
        workOrderReq.setFlowMarker("DT_FLOW_01");
        return externalApiService.createWorkOrder(workOrderReq);
    }
}
