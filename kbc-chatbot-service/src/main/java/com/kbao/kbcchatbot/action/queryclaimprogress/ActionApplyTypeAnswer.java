package com.kbao.kbcchatbot.action.queryclaimprogress;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-10-24 14:01
 **/
@Component
@Slf4j
public class ActionApplyTypeAnswer implements Action {
    @Override
    public String name() {
        return "action_apply_type_answer";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_query_claim_progress:>>>>>查询理赔进度自助理赔和好赔代办回复");
        String applyType = tracker.getSlotValue("apply_type",String.class);
        if ("self".equals(applyType)){
            dispatcher.utterTemplate("utter_self_claim");
        }else if ("agent".equals(applyType)){
            dispatcher.utterTemplate("utter_agent_claim");
        }else {
            dispatcher.utterTemplate("utter_provider_other_claim_info");
            return Collections.emptyList();
        }

        Map<String,Object> map = new HashMap<>();
        map.put("is_evaluate",true);
        tracker.setSlots(map);
        return Collections.singletonList(new SlotSet("is_evaluate",true));
    }
}
