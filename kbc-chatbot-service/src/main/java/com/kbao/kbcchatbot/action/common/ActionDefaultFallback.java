package com.kbao.kbcchatbot.action.common;

import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 默认动作
 * @author: husw
 * @create: 2023-11-15 11:37
 **/
@Component
@Slf4j
public class ActionDefaultFallback implements Action {
    @Override
    public String name() {
        return "action_default_fallback";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("进入默认动作");
        dispatcher.utterTemplate("utter_no_answer");
        List<AbstractEvent> abstractEvents = new ArrayList<>();
        abstractEvents.add(new UserUtteranceReverted());
        abstractEvents.add(new SlotSet("is_evaluate",false));
        return abstractEvents;
    }
}
