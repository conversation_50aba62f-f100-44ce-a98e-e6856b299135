package com.kbao.kbcchatbot.action.invoice;

import com.kbao.common.enums.YesNoEnum;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcchatbot.utils.BaseUtil;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.*;

import static com.kbao.tool.util.DateUtils.DEFAULT_DATE_TIME_FORMAT;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-10-25 10:24
 **/
@Component
@Slf4j
public class ActionCheckOrder implements Action {

    @Override
    public String name() {
        return "action_check_order";
    }

    private static final String INVOICE_URL_TEST = "https://www-sta.kbao123.com/ospt/invoiceApply?linkType=1";
    private static final String INVOICE_URL_PROD = "https://kbc-prod.dtinsure.com/ospt/invoiceApply?linkType=1";

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_check_order>>>>:开票校验订单");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        Date insureStartDate = DateUtils.str2Date(slotsMap.get("insureStartDate"),DEFAULT_DATE_TIME_FORMAT);
        String effectStatus = slotsMap.get("effectStatus");
        if (EmptyUtils.isEmpty(insureStartDate) || EmptyUtils.isEmpty(effectStatus)){
            dispatcher.utterTemplate("utter_order_not_effective_answer");
            return Collections.singletonList(new SlotSet("is_evaluate",true));
        }
        if (insureStartDate.before(new Date()) && YesNoEnum.YES.getCodeString().equals(effectStatus)){
            //订单生效跳转发票申请页
            Map<String, Object> kwargs = new HashMap<>();
            String url = BaseUtil.isTestEnvironment() ? INVOICE_URL_TEST : INVOICE_URL_PROD;
            kwargs.put("invoiceAddress", url);
            dispatcher.utterTemplate("utter_apply_invoice",kwargs);
        }else {
            dispatcher.utterTemplate("utter_order_not_effective_answer");
            return Collections.singletonList(new SlotSet("is_evaluate",true));
        }
        return Collections.emptyList();
    }
}
