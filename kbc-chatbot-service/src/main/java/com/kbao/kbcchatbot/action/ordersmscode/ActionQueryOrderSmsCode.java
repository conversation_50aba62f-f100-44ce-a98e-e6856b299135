package com.kbao.kbcchatbot.action.ordersmscode;

import com.kbao.kbcchatbot.constants.WorkOrderConstant;
import com.kbao.kbcchatbot.externalapi.work.ExternalApiService;
import com.kbao.kbcchatbot.kbc.enums.OrderTypeEnum;
import com.kbao.kbcchatbot.kbc.ucs.KbcUcsService;
import com.kbao.kbcchatbot.kbc.uoc.KbcUocService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AllSlotsReset;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.SlotSet;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;

/**
 * @program: kbc-chatbot
 * @description: 订单查询验证码
 * @author: husw
 * @create: 2023-10-17 11:37
 **/
@Slf4j
@Component
public class ActionQueryOrderSmsCode  implements Action {
    @Autowired
    private ExternalApiService externalApiService;

    @Autowired
    private KbcUocService kbcUocService;

    @Autowired
    private KbcUcsService kbcUcsService;

    @Override
    public String name() {
        return "action_query_sms";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info(">>>>>>>>>action_query_sms");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        String tenantId = tracker.getSlotValue("tenant_id", String.class);
        String userId = tracker.getSlotValue("user_id", String.class);
        UserInfoResp userInfoResp = kbcUcsService.getUserInfo(userId, tenantId);
        List<AbstractEvent> abstractEvents = new ArrayList<>();
        if (EmptyUtils.isEmpty(userInfoResp)){
            dispatcher.utterMessage("提交信息失败，失败原因：未查询到用户信息！请转人工处理");
            abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
            return abstractEvents;
        }
        //查询订单信息  查询登录人信息
        if (OrderTypeEnum.LPT.getType().equals(slotsMap.get("orderType"))){
            //寿险电投订单
            dispatcher.utterTemplate("utter_life_sms_answer");
            abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
            abstractEvents.add(new SlotSet("is_evaluate",true));
            return abstractEvents;
        }else if (OrderTypeEnum.ONLINE.getType().equals(slotsMap.get("orderType"))){
            //线下寿险订单创建工单
            setCreateOrderSlots(userInfoResp,slotsMap);
            Map<String, Object> kwargs = new HashMap<>();
            kwargs.put("orderId",slotsMap.get("orderId"));
            kwargs.put("question","收不到短信验证码");
            dispatcher.utterTemplate("utter_create_service_verify",kwargs);
            return Collections.singletonList(new SlotSet("slots_map",slotsMap));
        }else {
            dispatcher.utterTemplate("utter_not_life_transfer_labor");
        }
        abstractEvents.add(new AllSlotsReset(Timestamp.valueOf(DateUtils.thisDateTime())));
        return abstractEvents;
    }

    private void setCreateOrderSlots(UserInfoResp userInfoResp, Map<String,String> slotsMap) {
        String workContent = MessageFormat.format(WorkOrderConstant.SMS_CODE_QUESTION, slotsMap.get("orderId"),
                slotsMap.get("policyNo"),slotsMap.get("companyName"),"寿险电投收不到短信验证码");
        slotsMap.put("workBigCategory","3");
        slotsMap.put("workSmallCagetory","301");
        slotsMap.put("workActheadline","寿险电投收不到短信验证码");
        slotsMap.put("workAppel",workContent);
        slotsMap.put("workPhone",userInfoResp.getAgentPhone());
        slotsMap.put("workName",userInfoResp.getAgentName());
        slotsMap.put("workUserId",userInfoResp.getId());
    }
}
