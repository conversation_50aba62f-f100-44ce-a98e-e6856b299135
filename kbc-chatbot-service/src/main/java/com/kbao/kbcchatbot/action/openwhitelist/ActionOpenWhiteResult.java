package com.kbao.kbcchatbot.action.openwhitelist;

import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.constants.EmailConstant;
import com.kbao.kbcchatbot.kbc.ums.KbcUmsService;
import com.kbao.kbcchatbot.rasa3x.sdk.CollectingDispatcher;
import com.kbao.kbcchatbot.rasa3x.sdk.action.Action;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Domain;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.Tracker;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.event.AbstractEvent;
import com.kbao.kbcchatbot.utils.BaseUtil;
import com.kbao.kbcums.common.bean.CommonApiResponse;
import com.kbao.kbcums.mailout.vo.MailApiRequestParamDetail;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.*;


/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-10-25 10:24
 **/
@Component
@Slf4j
public class ActionOpenWhiteResult implements Action {

    @Autowired
    private KbcUmsService kbcUmsService;

    private static final String SEPARATOR = "\\?";

    private static final String IMG_URL = "<img src=\"{0}\"/>";
    private static final String MESSAGE = "此订单为{0}产品，不属于人保大护甲5号产品，需要转人工处理";

    @Override
    public String name() {
        return "action_open_white_result";
    }

    @Override
    public List<AbstractEvent> run(CollectingDispatcher dispatcher, Tracker tracker, Domain domain) {
        log.info("action_open_white_result>>>>:开白结果");
        Map<String,String> slotsMap = tracker.getSlotValue("slots_map", Map.class);
        String questionType = tracker.getSlotValue("question_type",String.class);
        String tenantId = tracker.getSlotValue("tenant_id", String.class);
        if(EmptyUtils.isEmpty(questionType)){
            dispatcher.utterTemplate("utter_no_answer");
            return Collections.emptyList();
        }
        //处理非人保大护甲5号产品
        String riskName = slotsMap.get("riskName");
        if(EmptyUtils.isEmpty(riskName) || !riskName.contains("人保大护甲5号")){
            Map<String, Object> kwargs = new HashMap<>();
            String content = MessageFormat.format(MESSAGE, riskName);
            kwargs.put("message",content);
            dispatcher.utterTemplate("utter_custom_labor",kwargs);
            return Collections.emptyList();
        }
        //人保大护甲5号产品
        String questionName = null;
        if ("occupation".equals(questionType)){
            questionName = "职业类别问题";
        } else if ("actual_name".equals(questionType)) {
            questionName = "实名认证问题";
        }
        List<MailApiRequestParamDetail> detailList = new ArrayList<>();
        MailApiRequestParamDetail mailApiRequest = new MailApiRequestParamDetail();
        if (BaseUtil.isTestEnvironment()){
            mailApiRequest.setMailTo("<EMAIL>");
            mailApiRequest.setMailCopy("<EMAIL>");
        }else {
            mailApiRequest.setMailTo(EmailConstant.WHITE_WORK_RECEIVED);
            mailApiRequest.setMailCopy(EmailConstant.WHITE_WORK_COPY);
        }
        StringBuilder img = new StringBuilder();
        String sendImages = slotsMap.get("send_image");
        if (EmptyUtils.isNotEmpty(sendImages) && sendImages.contains(",")){
            String[] sendImageList = sendImages.split(",");
            for (String sendImage:sendImageList){
                img.append(MessageFormat.format(IMG_URL,sendImage));
            }
        }else {
            img.append(MessageFormat.format(IMG_URL,sendImages));
        }
        mailApiRequest.setTitle(MessageFormat.format(EmailConstant.WHITE_WORK_TITLE,slotsMap.get("insuredName")));
        String insuredPhone = slotsMap.get("insuredPhone");
        String content = MessageFormat.format(EmailConstant.WHITE_WORK_MESSAGE, slotsMap.get("orderId"), slotsMap.get("riskName"), slotsMap.get("insuredName"),
                slotsMap.get("insuredCardNo"), EmptyUtils.isEmpty(insuredPhone)?"":insuredPhone, questionName, img);
        mailApiRequest.setContent(content);
        detailList.add(mailApiRequest);
        Result<CommonApiResponse> result = kbcUmsService.sendEmail(detailList, tenantId, "");
        if (EmptyUtils.isNotEmpty(result) && EmptyUtils.isNotEmpty(result.getDatas())){
            log.info("已发送开白申请邮件，邮件批次号:{}",result.getDatas().getBatchNo());
            dispatcher.utterTemplate("utter_white_result_answer");
        }else {
            //处理失败  请重试或转人工
            dispatcher.utterTemplate("utter_send_email_fail_answer");
        }
        return Collections.emptyList();
    }
}
