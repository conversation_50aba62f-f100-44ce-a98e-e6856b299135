package com.kbao.kbcchatbot.series.service;

import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcchatbot.series.dao.SeriesDataDao;
import com.kbao.kbcchatbot.series.model.SeriesDataMo;
import org.springframework.stereotype.Service;

/**
 * 主键序列生成服务
 * <AUTHOR>
 * @version V1.0
 * @Description
 * @date 2020/5/6 16:51
 * @since V1.0
 */
@Service
public class SeriesDataService extends BaseMongoServiceImpl<SeriesDataMo, String, SeriesDataDao> {

    /**
     * 取自增长序列值
     * @param seriesName 序列名
     * @param num 补0位数
     * @return
     */
    public String getNextId(String seriesName, String preFix, int num) {
        return this.dao.getNextId(seriesName, preFix, num);
    }
}
