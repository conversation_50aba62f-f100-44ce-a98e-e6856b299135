package com.kbao.kbcchatbot.series.dao;

import com.kbao.kbcbsc.dao.nosql.BaseMongoDaoImpl;
import com.kbao.kbcchatbot.series.model.SeriesDataMo;
import com.kbao.tool.util.NullProcessUtil;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version V1.0
 * @Description
 * @date 2020/5/6 16:48
 * @since V1.0
 */
@Repository
public class SeriesDataDao extends BaseMongoDaoImpl<SeriesDataMo, String> {

    /**
     * 取自增长序列值
     * @param seriesName 序列名
     * @param num 补0位数
     * @return
     */
    public String getNextId(String seriesName, String preFix, int num) {
        Query query = new Query(Criteria.where("seriesName").is(seriesName));
        Update update = new Update();
        update.set("preFix", preFix);
        update.inc("incId", 1);
        FindAndModifyOptions options = new FindAndModifyOptions();
        options.upsert(true);
        options.returnNew(true);
        SeriesDataMo inc = this.mongoTemplate.findAndModify(query, update, options, SeriesDataMo.class);
        return fomatId(inc, num);
    }

    public String fomatId(SeriesDataMo data, int num) {
        StringBuffer idstr = new StringBuffer();
        if (data.getIncId() != null) {
            idstr.append(NullProcessUtil.nvlToString(data.getPreFix(), ""));
            int addNums = num - data.getIncId().toString().length();
            if (addNums > 0) {
                for (int i = 0; i < addNums; i++) {
                    idstr.append("0");
                }
            }
            idstr.append(data.getIncId());
        }
        return idstr.toString();
    }
}
