package com.kbao.kbcchatbot.common.redis.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcchatbot.common.redis.LockReturnHandle;
import com.kbao.kbcchatbot.common.redis.LockVoidHandle;
import com.kbao.kbcchatbot.constants.Constant;
import com.kbao.kbcchatbot.utils.BaseUtil;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: 分布式锁
 * @time: 2024/8/13 9:30
 */
@Service
@Slf4j
public class RedisLockService {


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisUtil redisUtil;

    public static final long WAIT_TIME = 1000L;

    public String getBizKey(String format, String... param) {
        return MessageFormat.format(format, param);
    }

    /**
     * 获取分布式锁，如果获取不到，则等待直至获取到锁
     *
     * @param lockName 锁名称
     * @param handle   业务处理
     */
    public void lock(String lockName, LockVoidHandle handle) {
        RLock rLock = getLock(lockName);
        try {
            // 加锁，默认过期时间30s，剩余20s后自动续期
            rLock.lock();
            log.info("获取分布式锁[{}]成功", rLock.getName());
            handle.execute();
        } finally {
            rLock.unlock();
            log.info("释放分布式锁[{}]成功", rLock.getName());
        }
    }

    /**
     * 获取分布式锁带返回值，如果获取不到，则等待直至获取到锁
     *
     * @param lockName 锁名称
     * @param handle   业务处理
     * @param <T>      返回值
     * @return
     */
    public <T> T lock(String lockName, LockReturnHandle<T> handle) {
        RLock rLock = getLock(lockName);
        try {
            rLock.lock();
            log.info("获取分布式锁[{}]成功", rLock.getName());
            return handle.execute();
        } finally {
            rLock.unlock();
            log.info("释放分布式锁[{}]成功", rLock.getName());
        }
    }

    /**
     * 获取分布式锁，最长等待1000毫秒 {@link RedisLockService#WAIT_TIME}
     *
     * @param lockName 锁名称
     * @param handle   业务处理
     */
    public void tryLock(String lockName, LockVoidHandle handle) {
        RLock rLock = getLock(lockName);
        boolean b = false;
        try {
            // 加锁，默认过期时间30s，剩余20s后自动续期
            b = rLock.tryLock(RedisLockService.WAIT_TIME, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.error("中断获取分布式锁[{}]", rLock.getName(), e);
            throw new BusinessException("中断获取分布式锁");
        }
        if (!b) {
            log.error("获取分布式锁[{}]超时", rLock.getName());
            throw new BusinessException("获取分布式锁超时");
        } else {
            log.info("获取分布式锁[{}]成功", rLock.getName());
        }

        try {
            handle.execute();
        } finally {
            rLock.unlock();
            log.info("释放分布式锁[{}]成功", rLock.getName());
        }
    }


    /**
     * 获取分布式锁带返回值 最长等待1000毫秒 {@link RedisLockService#WAIT_TIME}
     *
     * @param lockName 锁名称
     * @param handle   业务处理
     * @param <T>      返回值
     * @return
     */
    public <T> T tryLock(String lockName, LockReturnHandle<T> handle) {
        RLock rLock = getLock(lockName);
        boolean b = false;
        try {
            b = rLock.tryLock(RedisLockService.WAIT_TIME, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.error("中断获取分布式锁[{}]", rLock.getName(), e);
            throw new BusinessException("中断获取分布式锁");
        }
        if (!b) {
            log.error("获取分布式锁[{}]超时", rLock.getName());
            throw new BusinessException("获取分布式锁超时");
        } else {
            log.info("获取分布式锁[{}]成功", rLock.getName());
        }

        try {
            return handle.execute();
        } finally {
            rLock.unlock();
            log.info("释放分布式锁[{}]成功", rLock.getName());
        }
    }

    /**
     * 获取分布式锁
     *
     * @param lockName 锁名称
     * @param handle   业务处理
     * @return
     */
    public void tryLockNoWait(String lockName, LockVoidHandle handle) {
        RLock rLock = getLock(lockName);
        boolean b = rLock.tryLock();
        if (b) {
            log.info("获取分布式锁[{}]成功", rLock.getName());
            try {
                handle.execute();
            } finally {
                rLock.unlock();
                log.info("释放分布式锁[{}]成功", rLock.getName());
            }
        }
    }

    /**
     * 分布式锁
     *
     * @param handle
     * @param keyList
     */
    public void lock(LockVoidHandle handle, List<String> keyList) {
        if (EmptyUtils.isEmpty(keyList)) {
            return;
        }
        TreeSet<String> treeSet = new TreeSet<>(keyList);
        List<RLock> lockList = new ArrayList<>(keyList.size());
        for (String s : treeSet) {
            RLock rLock = getLock(s);
            lockList.add(rLock);
        }

        try {
            // 加锁，默认过期时间30s，剩余20s后自动续期
            for (RLock rLock : lockList) {
                rLock.lock();
                log.info("获取分布式锁[{}]成功", rLock.getName());
            }

            handle.execute();
        } finally {
            for (RLock rLock : lockList) {
                rLock.unlock();
                log.info("释放分布式锁[{}]成功", rLock.getName());
            }
        }
    }

    /**
     * 分布式锁
     *
     * @param handle
     * @param keyList
     */
    public <T> T lock(LockReturnHandle<T> handle, List<String> keyList) {
        if (EmptyUtils.isEmpty(keyList)) {
            return null;
        }
        TreeSet<String> treeSet = new TreeSet<>(keyList);
        List<RLock> lockList = new ArrayList<>(keyList.size());
        for (String s : treeSet) {
            RLock rLock = getLock(s);
            lockList.add(rLock);
        }

        try {
            // 加锁，默认过期时间30s，剩余20s后自动续期
            for (RLock rLock : lockList) {
                rLock.lock();
                log.info("获取分布式锁[{}]成功", rLock.getName());
            }

            return handle.execute();
        } finally {
            for (RLock rLock : lockList) {
                rLock.unlock();
                log.info("释放分布式锁[{}]成功", rLock.getName());
            }
        }
    }

    /**
     * 获取锁
     *
     * @param lockName
     * @return
     */
    private RLock getLock(String lockName) {
        if (EmptyUtils.isEmpty(lockName)) {
            throw new BusinessException("分布式锁KEY为空");
        }
        return redissonClient.getLock(getKey(lockName));
    }

    private String getKey(String key) {
        return redisUtil.generateKey(BaseUtil.getProfileActive() + Constant.COLON + key);
    }

}
