package com.kbao.kbcchatbot.common.mqtt.service;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.kbcchatbot.common.mqtt.config.MqttConfig;
import com.kbao.kbcchatbot.common.redis.service.RedisLockService;
import com.kbao.kbcchatbot.constants.CacheConstant;
import com.kbao.kbcchatbot.externalapi.model.LargeModelMqttTokenVO;
import com.kbao.kbcchatbot.externalapi.model.LargeModelMsgVO;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.tool.util.EmptyUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.eclipse.paho.client.mqttv3.MqttConnectOptions.MQTT_VERSION_3_1_1;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2024-08-20 13:49
 **/
@Service
@Slf4j
public class MqttClientService {

    public static final String UPLOAD_TOKEN = "$SYS/uploadToken";
    private static final long TIME_TO_WAITE = 5000;
    private static final int CONNECTION_TIMEOUT = 5000;
    public static final String RECEIVERID = "/zl_qa_message";
    public static final String TOKEN_EXPIRE_NOTICE = "$SYS/tokenExpireNotice";
    public static final String Q = "0";
    public static final String A = "1";
    public static final Integer QOS_LEVEL = 1;

    @Autowired
    private MqttConfig mqttConfig;

    @Autowired
    private MaasHttpService maasHttpService;

    @Autowired
    private ThreadPoolTaskExecutor asyncThreadPool;

    @Autowired
    private ChatSessionRecordService chatSessionRecordService;

    @Autowired
    private RedisLockService redisLockService;


    /**
     * 内部连接参数
     */
    @Getter
    private MqttConnectOptions mqttConnectOptions;

    /**
     * 客户端使用的 Token 参数，仅在 Token 鉴权模式下需要设置，Key 为 token 类型，一个客户端最多存在三种类型，R，W，RW，Value 是 token内容。
     * 应用需要保证 token 在过期及时更新。否则会导致连接异常。
     */
    private final Map<String, String> tokenData = new ConcurrentHashMap<String, String>();
    /**
    * @Description: mqtt消息订阅
    * @Param: []
    * @return: void
    * @Author: husw
    * @Date: 2024/8/22 14:36
    */
    public void subscribe(){
        log.info("MQTT connect begin...");
        String clientId = mqttConfig.getGroupId() + "@@@" + "chatbotWeb";
        final String topic = mqttConfig.getTopic();
        Map<String, String> tokenData = new HashMap<>();
        //获取RW Token
        String mqttToken = getMqttToken();
        if (EmptyUtils.isEmpty(mqttToken)){
            throw new BusinessException("获取MaasToken失败");
        }
        tokenData.put("RW", mqttToken);
        connectionOptionWrapper(mqttConfig.getInstanceId(), mqttConfig.getAccessKey(), tokenData);
        final MemoryPersistence memoryPersistence = new MemoryPersistence();
        MqttClient mqttClient  = getMqttClient(clientId, memoryPersistence);
        if (EmptyUtils.isEmpty(mqttClient)){
            throw new BusinessException("获取MQTT连接失败");
        }
        try {
            mqttClient.setTimeToWait(TIME_TO_WAITE);
            mqttClient.setCallback(new MqttCallbackExtended() {
                @Override
                public void connectComplete(boolean reconnect, String serverURI) {
                    //客户端连接成功后就需要尽快订阅需要的 topic
                    log.info("connect success-{},{}",serverURI,reconnect);
                    asyncThreadPool.execute(()-> {
                        try {
                            final String topicFilter[] = {topic + RECEIVERID};
                            final int[] qos = {QOS_LEVEL};
                            mqttClient.subscribe(topicFilter, qos);
                            log.info("subscribe success");
                        } catch (Exception e) {
                            log.error("connect fail:连接失败",e);
                        }
                    });
                }

                @Override
                public void connectionLost(Throwable throwable) {
                    log.info("connectionLost:连接断开",throwable);
                    int reconnectDelay = 1000; // 重连延迟，单位毫秒
                    int maxReconnectAttempts = 10; // 最大重连尝试次数
                    int attempt = 0;

                    while (attempt < maxReconnectAttempts) {
                        try {
                            log.info("尝试重连...");
                            String mqttToken = getMqttToken();
                            tokenData.put("RW", mqttToken);
                            connectionOptionWrapper(mqttConfig.getInstanceId(), mqttConfig.getAccessKey(), tokenData);
                            mqttClient.connect(mqttConnectOptions);
                            log.info("重连成功");
                            break;
                        } catch (MqttException e) {
                            attempt++;
                            ThreadUtil.sleep(reconnectDelay);
                            log.error("重连尝试失败，剩余尝试次数: " + (maxReconnectAttempts - attempt));
                        }
                    }
                    if (attempt == maxReconnectAttempts) {
                        log.error("已达到最大重连尝试次数，放弃重连");
                    }
                }

                @Override
                public void messageArrived(String topic, MqttMessage mqttMessage) throws MqttException {
                    log.info("messageArrived:接收到消息-to:{},msg:{}",topic,new String(mqttMessage.getPayload()));
                    if (TOKEN_EXPIRE_NOTICE.equals(topic)){
//                        MqttMessage updateToken = new MqttMessage();
//                        String newToken = largeModelService.getMqttToken(CODE).getToken();
//                        String content = "{\"token\":"+  newToken +",type\":\"RW\"}";
//                        updateToken.setPayload(content.getBytes(StandardCharsets.UTF_8));
//                        mqttClient.publish(UPLOAD_TOKEN, updateToken);
                    }else {
                        try {
                            JSONObject jsonObject = JSON.parseObject(new String(mqttMessage.getPayload()));
                            LargeModelMsgVO msgVO = jsonObject.toJavaObject(LargeModelMsgVO.class);
                            String key = redisLockService.getBizKey(CacheConstant.CHAT_RECORD, msgVO.getQuestionId());
                            redisLockService.lock(key,()->{
                                if (Q.equals(msgVO.getMessageType())){
                                    chatSessionRecordService.saveLargeMsgQRecord(msgVO);
                                }else if (A.equals(msgVO.getMessageType())){
                                    chatSessionRecordService.saveLargeMsgARecord(msgVO);
                                }else {
                                    log.error("消息类型错误");
                                }
                            });
                        } catch (Exception e) {
                            log.error("MQTT 消息解析异常",e);
                        }
                    }
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
                    log.info("send msg succeed topic is : {}", iMqttDeliveryToken.getTopics()[0]);
                }
            });
            mqttClient.connect(mqttConnectOptions);
        }catch (Exception e){
            log.error("MQTT 消息订阅异常",e);
        }
    }
    /**
    * @Description: 获取mqtt平台Token
    * @Param: []
    * @return: java.lang.String
    * @Author: husw
    * @Date: 2024/10/17 15:29
    */
    private String getMqttToken() {
        for (int i=0;i<10;i++){
            try {
                LargeModelMqttTokenVO mqttToken = maasHttpService.getMqttToken();
                if (EmptyUtils.isEmpty(mqttToken) || EmptyUtils.isEmpty(mqttToken.getToken())){
                    log.error("获取MQTT Token失败,error:{}",mqttToken);
                    ThreadUtil.sleep(60000);
                    continue;
                }
                return mqttToken.getToken();
            }catch (Exception e){
                log.info("查询MQTT失败重试第【{}】次",i+1,e);
                ThreadUtil.sleep(60000);
            }
        }
        return null;
    }

    private MqttClient getMqttClient(String clientId, MemoryPersistence memoryPersistence) {
        for (int i=0;i<10;i++){
            try {
                MqttClient mqttClient;
                mqttClient = new MqttClient(mqttConfig.getEndPoint(), clientId, memoryPersistence);
                return mqttClient;
            }catch (Exception e){
                log.info("创建MQTT连接失败重试第【{}】次",i+1,e);
                ThreadUtil.sleep(60000);
            }
        }
        return null;
    }
    /**
     * 包装连接选项
     *
     * @param instanceId 实例ID
     * @param accessKey 访问密钥
     * @param tokenData 令牌数据，可以为null
     */
    public void connectionOptionWrapper(String instanceId, String accessKey, Map<String, String> tokenData) {
        if (tokenData != null) {
            this.tokenData.putAll(tokenData);
        }
        mqttConnectOptions = new MqttConnectOptions();
        mqttConnectOptions.setUserName("Token|" + accessKey + "|" + instanceId);
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> entry : tokenData.entrySet()) {
            builder.append(entry.getKey()).append("|").append(entry.getValue()).append("|");
        }
        if (builder.length() > 0) {
            builder.setLength(builder.length() - 1);
        }
        mqttConnectOptions.setPassword(builder.toString().toCharArray());
        mqttConnectOptions.setCleanSession(false);
        mqttConnectOptions.setKeepAliveInterval(90);
        mqttConnectOptions.setAutomaticReconnect(true);
        mqttConnectOptions.setMqttVersion(MQTT_VERSION_3_1_1);
        mqttConnectOptions.setConnectionTimeout(CONNECTION_TIMEOUT);
    }


}
