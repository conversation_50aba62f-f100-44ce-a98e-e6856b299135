package com.kbao.kbcchatbot.common.mqtt.listener;

import com.kbao.kbcchatbot.common.mqtt.service.MqttClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2024-09-05 11:19
 **/
@Component
@ConditionalOnProperty(value = "mqtt.mass.enabled", havingValue = "true")
public class MqttListenerImpl implements ApplicationListener<ApplicationStartedEvent> {

    @Autowired
    private MqttClientService mqttClientService;

    @Autowired
    private ThreadPoolTaskExecutor asyncThreadPool;


    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        // 启动时订阅
        asyncThreadPool.execute(() -> mqttClientService.subscribe());
    }
}
