package com.kbao.kbcchatbot.common.convert;

import com.kbao.kbcchatbot.common.bean.ChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean.ChannelCommonCardToVOChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean.ChannelCommonCardVO;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean.ChannelCommonCardVOToChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean.ChannelCommonPhraseToVOChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean.ChannelCommonPhraseVO;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean.ChannelCommonPhraseVOToChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.entity.ChannelCommonPhrase;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionToVOChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVOToChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.entity.ChannelGuessQuestion;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> qiuzb
 * @Description: 转换服务
 * @create 2023/6/15 11:51
 */
@Slf4j
public class ConvertService {


    /**
     * 定义一个泛型方法，用来复制和转换对象
     * 这个方法有两个类型参数，T和R，分别表示源对象和目标对象的类型
     * 这个方法有两个参数，source和targetClass，分别表示源对象和目标类的Class对象
     * 这个方法的返回类型是R，表示返回一个目标对象
     *
     * @param source      源对象
     * @param targetClass 目标类的Class对象
     * @param <T>         源对象的类型
     * @param <R>         目标对象的类型
     * @return 目标对象
     */
    public static <T, R> R copyAndConvert(T source, Class<R> targetClass) {
        try {
            // 创建一个Map，用来存储不同类型的转换器对象
            Map<String, ChatBotBeanConverter<?, ?>> converterMap = new HashMap<>();
            // 把需要的转换器对象放入Map中，根据源类和目标类的类型作为键
            converterMap.put(ChannelCommonPhrase.class.getName() + ChannelCommonPhraseVO.class.getName(), new ChannelCommonPhraseToVOChatBotBeanConverter());
            converterMap.put(ChannelCommonPhraseVO.class.getName() + ChannelCommonPhrase.class.getName(), new ChannelCommonPhraseVOToChatBotBeanConverter());
            // 常用卡片转换器
            converterMap.put(ChannelCommonCard.class.getName() + ChannelCommonCardVO.class.getName(), new ChannelCommonCardToVOChatBotBeanConverter());
            converterMap.put(ChannelCommonCardVO.class.getName() + ChannelCommonCard.class.getName(), new ChannelCommonCardVOToChatBotBeanConverter());
            // 猜你想问转换类
            converterMap.put(ChannelGuessQuestion.class.getName() + ChannelGuessQuestionVO.class.getName(), new ChannelGuessQuestionToVOChatBotBeanConverter());
            converterMap.put(ChannelGuessQuestionVO.class.getName() + ChannelGuessQuestion.class.getName(), new ChannelGuessQuestionVOToChatBotBeanConverter());
            // 根据源类和目标类的类型，从Map中获取对应的转换器对象，并强制类型转换成Converter<T, R>
            ChatBotBeanConverter<T, R> chatBotBeanConverter = (ChatBotBeanConverter<T, R>) converterMap.get(source.getClass().getName() + targetClass.getName());
            // 如果找不到对应的转换器，抛出异常
            if (chatBotBeanConverter == null) {
                throw new IllegalArgumentException("No converter found for " + source.getClass().getName() + " to " + targetClass.getName());
            }
            // 调用转换器的convert方法，把源对象转换成目标对象，并返回
            return chatBotBeanConverter.convert(source);
        } catch (Exception e) {
            log.error("copyAndConvert() error: {}", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    // 定义一个泛型方法，用来根据一个函数和一个id来获取一个列表，并把它转换成另一个类型的列表
    public static <T, R> List<R> getList(Function<Map<String,Object>, List<T>> function, Map<String,Object> map, Class<R> targetClass) {
        List<T> list = Optional.ofNullable(function.apply(map))
                .orElse(Collections.emptyList());

        return list.stream()
                .map(source -> copyAndConvert(source, targetClass))
                .collect(Collectors.toList());
    }

}
