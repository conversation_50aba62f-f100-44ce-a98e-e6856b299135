package com.kbao.kbcchatbot.common.mqtt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

@Component
@PropertySource(value = "classpath:/application.yml")
@ConfigurationProperties(prefix = "mqtt.mass")
@Data
public class MqttConfig {

    private String instanceId;

    private String topic;

    private String groupId;

    private String accessKey;

    private String endPoint;

}
