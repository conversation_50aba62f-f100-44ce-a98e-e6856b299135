package com.kbao.kbcchatbot.common.convert;

import com.kbao.kbcchatbot.common.bean.Contentable;
import com.kbao.kbcchatbot.utils.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> qiuzb
 * @Description: 转换Byte服务
 * @create 2023/6/9 9:38
 */
@Slf4j
public class ConvertByteService {

    /**
     * 根据对象的content属性转换为String或Byte[]
     *
     * @param source      源对象
     * @param targetClass 目标类
     * @param <T>         源对象类型
     * @param <R>         目标对象类型
     * @return 目标类对象
     */
    public static <T extends Contentable, R extends Contentable> R copyAndConvert(T source, Class<R> targetClass) {
        try {
            // 创建一个目标类的对象
            R target = targetClass.newInstance();
            // 复制源对象的属性到目标对象
            BeanUtils.copyProperties(source, target);
            // 通过反射获取源对象和目标对象的content属性
            Field sourceField = source.getClass().getDeclaredField("content");
            Field targetField = targetClass.getDeclaredField("content");
            // 设置可访问性
            sourceField.setAccessible(true);
            targetField.setAccessible(true);
            // 判断源对象和目标对象的属性类型是否相同
            if (sourceField.getType().equals(targetField.getType())) {
                // 如果相同，就直接调用源对象的getContent()方法，并设置到目标对象
                Object content = source.getContent();
                targetField.set(target, content);
            } else {
                // 如果不同，就根据源对象和目标对象的属性类型来调用不同的getContent()方法，并进行相应的转换
                if (sourceField.getType().equals(String.class) && targetField.getType().equals(byte[].class)) {
                    // 如果源对象是String类型，目标对象是byte[]类型，就调用源对象的getContent()方法，并用UTF-8编码转换成byte[]类型
                    String content = source.getContent();
                    if (content != null) {
                        byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
                        targetField.set(target, bytes);
                    }
                } else if (sourceField.getType().equals(byte[].class) && targetField.getType().equals(String.class)) {
                    // 如果源对象是byte[]类型，目标对象是String类型，就调用源对象的getContent(StandardCharsets.UTF_8)方法，并用UTF-8解码转换成String类型
                    byte[] bytes = source.getContent(StandardCharsets.UTF_8);
                    String content = new String(bytes, StandardCharsets.UTF_8);
                    targetField.set(target, content);
                }
            }
            // 返回目标对象
            return target;
        } catch (InstantiationException | IllegalAccessException | NoSuchFieldException e) {
            log.error("ConvertByteService.copyAndConvert() error: {}", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 转换Contentable对象的List
     * @param function    一个函数，接收一个Long类型的参数，返回一个List<T>类型的结果
     * @param id          Long类型的参数
     * @param targetClass 目标类的Class对象
     * @param <T>         源对象类型
     * @param <R>         目标对象类型
     * @return 返回一个List<R>类型的结果
     */
    public static <T extends Contentable, R extends Contentable> List<R> getList(Function<Long, List<T>> function, Long id, Class<R> targetClass) {
        List<T> list = Optional.ofNullable(function.apply(id))
                .orElse(Collections.emptyList());

        return list.stream()
                .map(source -> copyAndConvert(source, targetClass))
                .collect(Collectors.toList());
    }


    public static <T, R> List<R> getVOList(List<T> entityList, Class<T> entityClass, Class<R> voClass) {
        return entityList.stream()
                .map(entity -> copyAndConvert(entity, entityClass, voClass))
                .collect(Collectors.toList());
    }
    private static <T, R> R copyAndConvert(T entity, Class<T> entityClass, Class<R> voClass) {
        R vo = BeanCopyUtil.copy(entity, voClass);
        try {
            Field contentField = entityClass.getDeclaredField("content");
            contentField.setAccessible(true);
            byte[] content = (byte[]) contentField.get(entity);
            Field contentVOField = voClass.getDeclaredField("content");
            contentVOField.setAccessible(true);
            contentVOField.set(vo, new String(content, StandardCharsets.UTF_8));
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return vo;
    }

}
