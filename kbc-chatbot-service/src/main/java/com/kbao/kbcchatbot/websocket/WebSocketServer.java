package com.kbao.kbcchatbot.websocket;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerReqVO;
import com.kbao.kbcchatbot.maas.chatsession.service.ChatSessionApiService;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.ConfigCacheVO;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.bean.ChatUser;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static com.kbao.kbcchatbot.constants.CacheConstant.CHAT_TOKEN;


/**
 * <AUTHOR>
 */
//@ServerEndpoint(value = "/api/nontenant/websocket/{token}", configurator = MySpringConfigurator.class)
//@Service
//@Scope("prototype")
@Slf4j
public class WebSocketServer {

    /**
     * concurrent包的线程安全map，用来存放每个客户端对应的MyWebSocket对象。
     */
    private static final ConcurrentMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();
    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    /**
     * 接收token
     */
    private String token;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ChatSessionApiService chatSessionApiService;


    /**
     * 连接建立成功调用的方法
     **/
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        //认证
        if (!isTokenValid(token)){
            sendAuthFailed(session);
            return;
        }

        this.session = session;
        this.token = ChatUserUtil.getUser().getToken();

        // 在线人数
        webSocketMap.put(token, this);
        int onlineNum = webSocketMap.size();

        log.info("【窗口开始监听: {} ,当前在线人数为 {} 】", token, onlineNum);
    }

    /**
     * 验证token是否有效
     *
     * @param token  token
     * @return boolean
     */
    protected boolean isTokenValid(String token) {
        if(StringUtil.isEmpty(token)) {
            return false;
        }
        String redisKey = redisUtil.generateKey(MessageFormat.format(CHAT_TOKEN, token));
        ConfigCacheVO cacheVO = (ConfigCacheVO)redisUtil.get(redisKey);
        if (EmptyUtils.isEmpty(cacheVO)){
            log.error("ws 认证失败");
            return false;
        }
        //封装ChatUser对象
        ChatUser chatUser = getChatUser(token, cacheVO);
        //设置通用参数
        ChatUserUtil.setUser(chatUser);
        return true;
    }

    /**
     * 缓存基本数据
     * @param token
     * @param cacheVO
     * @return
     */
    private ChatUser getChatUser(String token, ConfigCacheVO cacheVO) {
        ChatUser chatUser = new ChatUser();
        chatUser.setUserId(cacheVO.getUserId());
        chatUser.setUserName(cacheVO.getUserName());
        chatUser.setTenantId(cacheVO.getTenantId());
        chatUser.setChannelId(cacheVO.getChannelId());
        chatUser.setRobotId(cacheVO.getRobotId());
        chatUser.setRobotCode(cacheVO.getRobotCode());
        chatUser.setRobotVersion(cacheVO.getVersion());
        chatUser.setSessionId(cacheVO.getSessionId());
        chatUser.setToken(token);
        chatUser.setSourceType(cacheVO.getSourceType());
        chatUser.setChatSessionCache(cacheVO.getChatSessionCache());
        return chatUser;
    }

    /**
     * 认证失败，断开连接
     *
     * @param session session
     */
    protected void sendAuthFailed(Session session) {
        try {
            session.getBasicRemote().sendText("认证失败");
            session.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        // 从map中删除
        webSocketMap.remove(token);
        // 在线人数
        int onlineNum = webSocketMap.size();
        log.info("【窗口：{} 连接关闭！, 当前在线人数为: {}", token, onlineNum);
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     **/
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到来自窗口 {} 的信息: {}", token, message);
        if (!isTokenValid(token)){
            sendAuthFailed(session);
            return;
        }
        // 群发消息
        webSocketMap.forEach((k, v) -> {
            try {
                // 只响应对应的客户端
                if (token.equals(k)) {
                    if ("ping".equals(message)) {
                        v.sendMessage("pong");
                    } else {
//                        v.sendMessage(message);
                        ChatQueryAnswerReqVO reqVO = JSONObject.toJavaObject(JSONObject.parseObject(message), ChatQueryAnswerReqVO.class);
                        /*chatSessionApiService.queryAnswer(reqVO,true);*/
                    }
                }
            } catch (Exception e){
                log.error("接受消息处理失败，{}",e.getMessage(),e);
                throw new BusinessException("消息处理失败!");
            }
        });
    }

    /**
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("发生错误", error);
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) throws IOException {
        synchronized (this.session) {
            this.session.getBasicRemote().sendText(message);
        }
    }


    /**
     * 自定义消息发送
     */
    public static void sendInfo(String message, @PathParam("token") String token) {
        log.info("推送消息到窗口 {}，推送内容: {}", token, message);
        webSocketMap.forEach((k, v) -> {
            try {
                // 这里可以设定只推送给这个sid的，为null则全部推送
                if (StringUtil.isBlank(token)) {
                    v.sendMessage(message);
                } else if (v.token.equals(token)) {
                    v.sendMessage(message);
                }
            } catch (IOException e) {
                log.error("WebSocketServer sendInfo ex...", e);
            }
        });
    }
}