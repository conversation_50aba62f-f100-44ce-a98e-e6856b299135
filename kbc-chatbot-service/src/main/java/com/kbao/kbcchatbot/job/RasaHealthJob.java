package com.kbao.kbcchatbot.job;

import com.kbao.kbcchatbot.constants.RasaConstant;
import com.kbao.kbcchatbot.rasa3x.config.RasaApiConfig;
import com.kbao.kbcchatbot.rasa3x.rasa.RasaClient;
import com.kbao.kbcchatbot.rasa3x.utils.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @program: kbc-chatbot
 * @description: rasa心跳检测
 * @author: husw
 * @create: 2023-12-18 09:08
 **/
@Service
@Slf4j
public class RasaHealthJob {

    @Autowired
    private RasaApiConfig rasaApiConfig;

//    @Scheduled(fixedDelay = 10000)
    public void getHealth(){
        RasaClient rasaClient = getRasaClientInstance();
        try {
            String health = rasaClient.getHealth();
            log.info("rasa心跳检测：{}",health);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }
    private RasaClient getRasaClientInstance() {
        RasaClient rasaClient = new RasaClient(rasaApiConfig.getRestUrl()).setConnectionTimeout(10*60*1000).withJwtToken(rasaApiConfig.getJwtSecretKey(), getUsername(), RasaConstant.API_PAYLOAD_ROLE_USER);
        return rasaClient;
    }

    private static String getUsername() {
        return "admin";
    }

}
