{
  "compilerOptions": {
    "allowJs": true,
    "sourceMap": true,
    "baseUrl": ".",
    "types": ["element-plus/global", "unplugin-vue-define-options","vite/client"],
    "paths": {
      "@/*": ["src/*"]
    },
    "lib": ["esnext", "dom", "dom.iterable"],
    "module": "esnext",
    "moduleResolution": "Node",
    "allowSyntheticDefaultImports": true,
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "components.d.ts",
    "auto-imports.d.ts"
  ],
  "exclude": ["node_modules"],
}
