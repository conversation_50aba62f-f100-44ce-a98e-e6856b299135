package com.kbao.kbcchatbot.spring;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * spring bean帮助类
 */
@Component
public class ApplicationContextHelper implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    public ApplicationContextHelper() {
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationContextHelper.applicationContext = applicationContext;
    }

    public static Object getBean(String beanName) {
        return applicationContext != null ? applicationContext.getBean(beanName) : null;
    }

    public static <T> T getBean(Class<T> requiredType) {
        return applicationContext != null ? applicationContext.getBean(requiredType) : null;
    }

    public static <T> T getBean(String beanName, Class<T> requiredType) {
        return applicationContext != null ? applicationContext.getBean(beanName, requiredType) : null;
    }


    /**
     * 获取配置属性
     *
     * @param key 属性KEY
     * @return 属性值
     */
    public static String getProperties(String key) {
        Environment evn = getBean(Environment.class);
        if (evn != null) {
            return evn.getProperty(key);
        }
        return null;
    }

    /**
     * 获取配置属性
     *
     * @param key 属性KEY
     * @return 属性值
     */
    public static String getProperties(String key, String defalut) {
        Environment evn = getBean(Environment.class);
        if (evn != null) {
            return evn.getProperty(key, defalut);
        }
        return null;
    }

}
