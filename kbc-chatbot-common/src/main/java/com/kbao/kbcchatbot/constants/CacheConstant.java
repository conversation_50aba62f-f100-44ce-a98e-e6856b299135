package com.kbao.kbcchatbot.constants;

/**
* @Description: 缓存常量
* @Param:
* @return:
* @Author: husw
* @Date: 2023/6/8 9:27
*/
public class CacheConstant {
    /**
     * 会话缓存时长
     */
    public static final long SESSION_MAX_TIME = 24*60*60;
    /**
     * 会话配置缓存时长
     */
    public static final long CHAT_TOKEN_TIME = 2*60*60;
    /**
     * 渠道会话缓存
     */
    public static final String CHAT_TOKEN = "chat_token:{0}:{1}";
    /**
     * 会话记录缓存
     */
    public static final String CHAT_RECORD = "chat_record:{0}";
    /**
     * 会话统计
     */
    public static final String CHAT_SESSION = "chat_session:{0}";

    public static final String LARGE_MODEL_TOKEN = "tenant:large_model:token:{0}";
    public static final String LARGE_MODEL_MQTT_TOKEN = "tenant:large_model:mqtttoken";
    /**
     * 模型token配置信息
     */
    public static final String LARGE_MODEL_CONFIG = "tenant:large_model:config:{0}";

    public static final String SYNC_DEFAULT_ROBOT_CODE = "sync_default_robot_code";
    /**
     * 授权token缓存时长
     */
    public static final long AUTH_MAX_TIME = 5 * 60;

    /**
     * 机器人基础配置
     */
    public static final String ROBOT_BASIC_CONFIG = "tenant:robot_basic_config:{0}";

    public static final String GUESS_QUESTION_RECORD = "tenant:guess_question_record:{0}";
    /**
     * 消息推送锁
     */
    public static final String LOCK_CHAT_MSG = "lock:chatMsg:{0}";

}
