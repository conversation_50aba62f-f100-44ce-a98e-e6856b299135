package com.kbao.kbcchatbot.constants;

/**
 * @program: kbc-chatbot
 * @description: 邮件
 * @author: husw
 * @create: 2023-10-26 17:33
 **/
public class EmailConstant {

    public static final String WHITE_WORK_RECEIVED = "<EMAIL>";

    public static final String WHITE_WORK_COPY = "<EMAIL>";

    public static final String WHITE_WORK_TITLE = "大童开白申请+被保人姓名（{0}）+人保大护甲5号成人意外险（互联网专属）";

    public static final String WHITE_WORK_MESSAGE = "订单号：{0}<br/>" +
            "产品名称：{1}<br/>" +
            "被保人姓名：{2}<br/>" +
            "被保人证件号：{3}<br/>" +
            "被保人手机号：{4}<br/>" +
            "报错类型：{5}<br/>" +
            "报错类型的截图：{6}<br/>";
}
