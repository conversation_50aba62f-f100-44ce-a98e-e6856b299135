package com.kbao.kbcchatbot.constants;

/**
 * @Description 公共实体类
 * <AUTHOR>
 * @Date 2023-5-22
 */
public class ElasticsearchConstants {

    //查询词解析功能
    public static final String QUERY_ANALYSIS_STOP_WORD = "01";//停用词
    public static final String QUERY_ANALYSIS_CORRECT = "02";//词纠错
    public static final String QUERY_ANALYSIS_SYNONYM_WORD = "03";//同义词
    public static final String QUERY_ANALYSIS_STATUS_NORMAL = "1";//启用
    public static final String QUERY_ANALYSIS_STATUS_INVALID = "3";//停用

    public static final Integer STATUS_NOT_DELETE = 0;
    public static final Integer STATUS_DELETE = 1;

    //实体识别
    public static final int ENTITY_RECOGNITION_LEVEL_HIGH_WEIGHT = 10;
    public static final int ENTITY_RECOGNITION_LEVEL_MEDIUM_WEIGHT = 5;
    public static final int ENTITY_RECOGNITION_LEVEL_LOW_WEIGHT = 2;
    public static final String ENTITY_RECOGNITION_LEVEL_HIGH = "3";
    public static final String ENTITY_RECOGNITION_LEVEL_MEDIUM = "2";
    public static final String ENTITY_RECOGNITION_LEVEL_LOW = "1";
    public enum EntityRecognitionLevelEnum {
        HIGH(ElasticsearchConstants.ENTITY_RECOGNITION_LEVEL_HIGH, ElasticsearchConstants.ENTITY_RECOGNITION_LEVEL_HIGH_WEIGHT),
        MEDIUM(ElasticsearchConstants.ENTITY_RECOGNITION_LEVEL_MEDIUM, ElasticsearchConstants.ENTITY_RECOGNITION_LEVEL_MEDIUM_WEIGHT),
        LOW(ElasticsearchConstants.ENTITY_RECOGNITION_LEVEL_LOW, ElasticsearchConstants.ENTITY_RECOGNITION_LEVEL_LOW_WEIGHT);

        private String level;
        private int weight;

        EntityRecognitionLevelEnum(String level, int weight) {
            this.level = level;
            this.weight = weight;
        }

        public static Integer getWeight(String level) {
            for (EntityRecognitionLevelEnum levelEnum : EntityRecognitionLevelEnum.values()) {
                if (levelEnum.getLevel().equals(level)) {
                    return levelEnum.getWeight();
                }
            }
            return null;
        }

        public String getLevel() {
            return level;
        }
        public void setLevel(String level) {
            this.level = level;
        }
        public int getWeight() {
            return weight;
        }
        public void setWeight(int weight) {
            this.weight = weight;
        }
    }

    //索引字段类型
    //关键字
    public static final String COLUMN_TYPE_KEYWORD = "keyword";
    //文本
    public static final String COLUMN_TYPE_TEXT = "text";
    //日期
    public static final String COLUMN_TYPE_DATE = "date";

    //分词器
    //ik细粒度分词器
    public static final String ANALYZER_IK_MAX_WORD = "ik-index";
    //ik细粒度分词器+同义词过滤器
    public static final String ANALYZER_IK_MAX_WORD_WITH_SYNONYM = "ik-index-synonym";
    //ik粗粒度分词器
    public static final String ANALYZER_IK_SMART = "ik-smart";
    //ik粗粒度分词器+同义词过滤器
    public static final String ANALYZER_IK_SMART_WITH_SYNONYM = "ik-smart-synonym";
    //自定义分号分词器
    public static final String ANALYZER_SEMICOLON = "semicolon";

    //查询词使用分词器的最低长度
    public static final int QUERY_ANALYZER_MIN_LENGTH = 1;

    //搜索策略
    //搜索策略状态
    public static final String STRATEGY_STATUS_NORMAL = "1";
    public static final String STRATEGY_STATUS_INVALID = "3";
    //搜索策略类型
    public static final String STRATEGY_TYPE_LABEL = "1";
    public static final String STRATEGY_TYPE_PRODUCT = "2";
    public static final String STRATEGY_TYPE_URL = "3";

    //字典KEY
    public static final String DIC_ENTITY_TYPE = "search.ner.type";
    public static final String DIC_STRATEGY_TYPE = "search.strategy.type";
    public static final String DIC_USER_ROLE = "search.app.user.type";
    //字典VALUE
    public static final String DICITEM_YORN_YES = "Y";
    public static final String DICITEM_YORN_NO = "N";
    public static final String DICITEM_YORN_NUM_YES = "1";
    public static final String DICITEM_YORN_NUM_NO = "0";

    //REDIS缓存KEY
    public static final String REDIS_CACHE_KEY_DIC = "DIC_CACHE";
    public static final String REDIS_CACHE_KEY_HOT_WORDS = "HOT_WORDS_CACHE";
    public static final String REDIS_CACHE_KEY_HOT_SEARCH = "HOT_SEARCH_CACHE";
    public static final String REDIS_CACHE_RECENT_HISTORY = "RECENT_HISTORY";
    //REDIS字典数据时长5分钟
    public static final int REDIS_CACHE_TIME_BSC_DICITEMS_DATA = 5 * 60;
    //底纹词缓存七天
    public static final int REDIS_CACHE_TIME_HOT_WORDS = 60 * 60 * 24 * 7;
    //热搜词缓存12小时
    public static final int REDIS_CACHE_TIME_HOT_SEARCH = 60 * 60 * 12;
    //近期搜索记录缓存12小时
    public static final int REDIS_CACHE_TIME_RECENT_HISTORY = 60 * 60 * 12;

    //业务查询参数运算符
    public static final String BUSINESS_QUERY_OPERATOR_EQUALS = "eq";
    public static final String BUSINESS_QUERY_OPERATOR_NOT_EQUALS = "neq";
    //索引ID
    public static final String MATERIALS_KNOWLEDGE_PACKAGE_CLOUD_INDEX_ID = "888888";
    public static final String MATERIALS_KNOWLEDGE_PACKAGE_QA_INDEX_ID = "999999";


    //搜索结果
    public static final String SEARCH_RESULT_CACHE_KEY = "SEARCH_RESULT";
    public static final int SEARCH_RESULT_RECALL_NUM = 5;
    public static final int SEARCH_RESULT_REDIS_CACHE_TIME = 5 * 60;
    public static final String SEARCH_RESULT_URL_STRATEGY_SIGN = "urlStrategy";


    //搜索日志
    public static final String SEARCH_LOG_QUERY_TYPE_NORMAL = "1";
    public static final String SEARCH_LOG_QUERY_TYPE_STRATEGY = "2";
    public static final int SEARCH_LOG_QUERY_MAX_SIZE = 10;
    public static final int SEARCH_LOG_QUERY_HISTORY_DATE_INTERVAL = 30;
    public static final int SEARCH_LOG_QUERY_HISTORY_MAX_SIZE = 10;

    //REDIS分词词库缓存
    public static final String REDIS_CACHE_ANALYZER= "KBC_SEARCH_ANALYZER";
    public static final String EXPAND_CACHE = "EXPAND";//拓展词
    public static final String SYNONYM_CACHE = "SYNONYM";//同义词
    public static final String STOP_CACHE = "STOP";//停用词
    public static final Long REDIS_CACHE_ANALYZER_EXPIRATION = 180L;//过期时间

    //REDIS重建索引缓存
    public static final String REDIS_CACHE_REINDEX= "KBC_SEARCH_REINDEX";
    public static final Long REDIS_CACHE_REINDEX_EXPIRATION = 300L;//过期时间

    //APP用户身份
    public static final String APP_USER_ROLE_ALL = "0";
    public static final String APP_USER_ROLE_EMPLOYEE = "1";
    public static final String APP_USER_ROLE_USER = "2";
    public static final String APP_USER_ROLE_TOURIST = "3";

    public static final String APP_USER_SHOW_OFFLINE_OFF = "0";

    //索引分析配置
    public static final String IS_ANALYSIS = "Y";
    public static final String IS_NOT_ANALYSIS = "N";

    //下拉提示
    public static final int DROP_DOWN_SEARCH_RECORD_LIMIT= 20;

    //热搜
    public static final Integer BASE_SORT = 1;


    public static final String IK_TOKEN_TYPE_SYNONYM = "SYNONYM";
}
