package com.kbao.kbcchatbot.constants;

/**
 * @program: kbc-chatbot
 * @description: 工单信息
 * @author: husw
 * @create: 2023-10-25 17:35
 **/
public class WorkOrderConstant {
    /**
     * 续期问题工单
     */
    public static final String RENEWAL_DEDUCTION_MESSAGE = "服务顾问账户信息（姓名和手机号）：{0}\r\n" +
            "订单号：{1}\r\n" +
            "保单号：{2}\r\n" +
            "保险公司：{3}\r\n" +
            "被保人：{4}\r\n" +
            "被保人身份证号：{5}\r\n" +
            "问题描述：{6}\r\n" +
            "工单类别：通知型工单-续期问题-续期问题";
    /**
     * 订单状态问题
     */
    public static final String ORDER_STATUS_MESSAGE = "服务顾问账户信息（姓名和手机号）：{0}\r\n" +
            "订单号：{1}\r\n" +
            "保单号：{2}\r\n" +
            "保险公司：{3}\r\n" +
            "产品全称：{4}\r\n" +
            "被保人姓名：{5}\r\n" +
            "被保人身份证号：{6}\r\n" +
            "问题描述：{7}\r\n" +
            "工单类别：通知型工单-订单问题-订单状态处理";

    /**
     * 好赔进度查询工单
     */
    public static final String QUERY_CLAIM_PROGRESS = "订单号：{0}\r\n" +
            "保单号：{1}\r\n" +
            "保险公司：{2}\r\n" +
            "事故出险日期：{3}\r\n" +
            "理赔递交资料日期：{4}\r\n" +
            "理赔递交资料方式：{5}\r\n" +
            "本次理赔联系人电话：{6}\r\n" +
            "本次理赔人邮箱：{7}\r\n" +
            "工单类别：通知型工单-理赔问题-理赔查询";
    /**
     * 寿险电投收不到短信验证码
     */
    public static final String SMS_CODE_QUESTION = "订单号：{0}\r\n" +
            "保单号：{1}\r\n" +
            "保险公司：{2}\r\n" +
            "问题描述：{3}\r\n" +
            "工单类别：通知型工单-计划书/首期电投-首期电投寿险";

    /**
     * 童管家实名信息错误
     */
    public static final String ACTUAL_NAME_ERROR = "客户实名注册童管家的姓名：{0}\r\n" +
            "客户证件号：{1}\r\n" +
            "修改信息：{2}\r\n" +
            "来电手机号：{3}\r\n" +
            "工单类别：通知型工单-计划书/首期电投-首期电投寿险";

    /**
     * 佣金问题
     */
    public static final String COMMISSION_MESSAGE = "服务顾问账户信息（姓名和手机号）：{0}\r\n" +
            "订单号：{1}\r\n" +
            "保单号：{2}\r\n" +
            "保险公司：{3}\r\n" +
            "被保人：{4}\r\n" +
            "问题描述：{5}\r\n" +
            "工单类别：通知型工单-资金支付-佣金问题";
}
