package com.kbao.kbcchatbot.utils.easyexcel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcchatbot.utils.easyexcel.callback.EasyExcelCallBack;
import com.kbao.kbcchatbot.utils.easyexcel.handler.CustomHorizontalCellStyleHandler;
import com.kbao.kbcchatbot.utils.easyexcel.handler.ExcelCellWriteHandler;
import com.kbao.kbcchatbot.utils.easyexcel.listener.ExcelDataListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;


/**
 * @Description EasyExcel 工具类
 * <AUTHOR>
 * @Date 2022/12/30 9:49
 **/
@Slf4j
public class EasyExcelUtil {
    /*
     * @Description: 导出
     * @Author: SuiXinYang
     * @Date: 2023/2/10 16:45
     **/
    public static HttpServletResponse export(Object data, String fileName, Class clazz, HttpServletResponse response) throws IOException {
        List<T> list = (List)data;
        OutputStream outputStream = response.getOutputStream();
        response.setContentType("application/x-msdownload");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName + ".xlsx", "utf-8"));
        CustomHorizontalCellStyleHandler customHorizontalCellStyleHandler = new CustomHorizontalCellStyleHandler(getHeadStyle(),getContentStyle());
        EasyExcel.write(outputStream,clazz)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .registerWriteHandler(customHorizontalCellStyleHandler)
                .registerWriteHandler(new ExcelCellWriteHandler())
                .sheet(0).doWrite(list);
        outputStream.flush();
        outputStream.close();
        return response;
    }
    public static WriteCellStyle getHeadStyle() {
        // 头的样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 下边是具体实现
        headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        headWriteCellStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(true);
        headWriteFont.setFontHeightInPoints((short) 11);
        headWriteFont.setColor(IndexedColors.BROWN.getIndex());
        headWriteCellStyle.setWriteFont(headWriteFont);
        return headWriteCellStyle;
    }
    public static WriteCellStyle getContentStyle() {
        // 内容的样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 下边是具体实现
        return contentWriteCellStyle;
    }
    /*
     * @Description:将excel文件转化成javaList集合
     * 需要在传入的class的字段中加入EasyExcel的专用注解来指定表名
     * @Author: SuiXinYang
     * @Date: 2023/1/3 11:11
     **/
    public static <T> List<T> read(MultipartFile file, Class<T> clazz){
        List<T> objects = null;
        try{
            InputStream inputStream = file.getInputStream();
            objects = EasyExcel.read(inputStream).head(clazz).sheet().doReadSync();
            System.out.println("累计处理数据：" + objects.size());
        }catch (Exception e) {
            log.error("excelToList-exception",e);
        }finally {
            return objects;
        }
    }
    /*
     * @Description: 导入excel
     * @Author: SuiXinYang
     * @Date: 2023/1/3 11:20
     **/
    public static void read(MultipartFile excelFile, Class clazz, EasyExcelCallBack callBack){
        try{
            InputStream inputStream = excelFile.getInputStream();
            EasyExcel.read(inputStream, clazz, new ExcelDataListener(callBack)).sheet().doRead();
        }catch (BusinessException businessException) {
            throw businessException;
        }catch (Exception e) {
            log.error("excelToList-readExcelHighPerformance", e);
        }
    }
}
