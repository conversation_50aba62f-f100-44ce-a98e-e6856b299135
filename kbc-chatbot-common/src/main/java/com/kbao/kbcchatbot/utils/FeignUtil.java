package com.kbao.kbcchatbot.utils;

import com.kbao.feign.config.FeignRequestHeader;

import java.util.HashMap;
import java.util.Map;

public class FeignUtil {
    public static void setBaseFeignHeader(String tenantId) {
        Map<String, String> headerMap = new HashMap();
        headerMap.put("tenantId", tenantId);
        FeignRequestHeader.Header.set(headerMap);
    }

    public static void setBaseFeignHeader(String tenantId, String token) {
        Map<String, String> headerMap = new HashMap();
        headerMap.put("tenantId", tenantId);
        headerMap.put("token", token);
        FeignRequestHeader.Header.set(headerMap);
    }

    public static void setUserFeignHeader(String tenantId, String userId) {
        Map<String, String> headerMap = new HashMap();
        headerMap.put("tenantId", tenantId);
        headerMap.put("userId", userId);
        FeignRequestHeader.Header.set(headerMap);
    }
}
