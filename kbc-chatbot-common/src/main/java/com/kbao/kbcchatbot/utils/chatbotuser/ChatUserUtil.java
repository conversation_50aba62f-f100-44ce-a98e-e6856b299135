package com.kbao.kbcchatbot.utils.chatbotuser;

import com.kbao.kbcchatbot.utils.chatbotuser.bean.ChatUser;

/**
 * <AUTHOR>
 * @Desctiption 聊天机器人API端用户工具类
 * @Date 2023-6-6
 */
public class ChatUserUtil {

    private static ThreadLocal<ChatUser> local = new ThreadLocal();

    public ChatUserUtil() {
    }

    public static void setUser(ChatUser chatUser) {
        local.set(chatUser);
    }

    public static ChatUser getUser() {
        return local.get();
    }

    public static String getUserId() {
        return getUser() != null ? getUser().getUserId() : null;
    }

    public static void remove() {
        local.remove();
    }

}
