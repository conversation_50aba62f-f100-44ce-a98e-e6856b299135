package com.kbao.kbcchatbot.utils;


import com.kbao.kbcchatbot.spring.ApplicationContextHelper;

/**
* @Description: 区分环境
* @Param:
* @return:
* @Author: husw
* @Date: 2023/11/14 16:25
*/
public class BaseUtil {

    /**
     * 环境
     */
    public static final String SPRING_PROFILES_ACTIVE = "spring.profiles.active";
    public static final String DEV = "dev";
    public static final String STA = "sta";
    public static final String UAT = "uat";
    public static final String PROD = "prod";

    public static final String UNDERSCORE = "_";

    /**
     * @Description: 获取环境
     * <AUTHOR>
     * @Date 2020/11/2 15:53
     * @Return java.lang.String
     */
    public static String getProfileActive() {
        return ApplicationContextHelper.getProperties(SPRING_PROFILES_ACTIVE);
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description //区分生产环境和测试环境
     * @Date 15:49 2019/11/27
     * @Method isTestEnvironment
     * @Param []
     **/
    public static boolean isTestEnvironment() {
        String active = getProfileActive();
        if (PROD.equals(active)) {
            return false;
        }
        return true;
    }

    public static String getMqTag(String tagName) {
        String active = getProfileActive();
        if (DEV.equals(active) || STA.equals(active) || UAT.equals(active)) {
            return tagName + UNDERSCORE + active;
        }
        return tagName;
    }

}
