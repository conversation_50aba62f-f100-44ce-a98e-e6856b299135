package com.kbao.kbcchatbot.utils;

import com.kbao.commons.exception.BusinessException;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public  class RSAUtil {
    private static final String RSA_ALGORITHM = "RSA";
    private static final int RSA_2048 = 2048;

    public void generateSecret() throws Exception {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGORITHM);
        keyPairGenerator.initialize(RSA_2048);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();

        String publicKeyString = Base64.getEncoder().encodeToString(publicKey.getEncoded());
        System.out.println("public key string: " + publicKeyString);

        String privateKeyString = Base64.getEncoder().encodeToString(privateKey.getEncoded());
        System.out.println("private key String: " + privateKeyString);
    }

    public static String encrypt(String plaintext, String key) {
        try {
            return publicKeyEncrypted(plaintext, key);
        } catch (Exception e) {
            throw new BusinessException("加密失败", e);
        }
    }

    public static String decrypt(String ciphertext, String key) throws Exception {
        try {
            return privateDecrypt(ciphertext, key);
        } catch (Exception e) {
            throw new BusinessException("解密失败", e);
        }
    }

    public static String publicKeyEncrypted(String plaintext, String publicKeyString) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidKeySpecException {
        RSAPublicKey publicKey = getPublicKey(publicKeyString);
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return Base64.getEncoder().encodeToString(rsaCodec(cipher, plaintext.getBytes(StandardCharsets.UTF_8), publicKey.getModulus().bitLength() / 8 - 11));
    }

    private static RSAPublicKey getPublicKey(String publicKeyString) throws NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        KeySpec keySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKeyString));
        return (RSAPublicKey) keyFactory.generatePublic(keySpec);
    }

    private static RSAPrivateKey getPrivateKey(String privateKeyString) throws NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        KeySpec pkcS8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKeyString));
        return (RSAPrivateKey) keyFactory.generatePrivate(pkcS8EncodedKeySpec);
    }

    public static String privateDecrypt(String ciphertext, String privateKeyString) throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException, UnsupportedEncodingException {
        RSAPrivateKey privateKey = getPrivateKey(privateKeyString);

        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return new String(
                rsaCodec(cipher, Base64.getDecoder().decode(ciphertext), privateKey.getModulus().bitLength() / 8), StandardCharsets.UTF_8
        );
    }

    private static byte[] rsaCodec(Cipher cipher, byte[] data, int maxBlock) {
        int offset = 0;
        byte[] buffer;
        int i = 0;

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            while (data.length > offset) {
                if (data.length - offset > maxBlock) {
                    buffer = cipher.doFinal(data, offset, maxBlock);
                } else {
                    buffer = cipher.doFinal(data, offset, data.length - offset);
                }
                byteArrayOutputStream.write(buffer, 0, buffer.length);
                i++;
                offset = i * maxBlock;
            }
            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws Exception {
        RSAUtil rsa = new RSAUtil();
        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDnAC0GH0r6QPxYWPppYpIiDfMgO91w80ejnkb54H3+/dpUznEiiDKe8DcAWmJs6eQuw3VsDUdLHqpaAHzCWywngT4/Y/Gv8uoAgq2k+pD/njSEo0woCDw5GC/M7yplSXUqWeToXovuju2OCUM3hVsoQtejQZP7LactM1bxwyeAtQIDAQAB";
        String s = rsa.publicKeyEncrypted("hello world", publicKey);
        System.out.println(s);
    }

}

