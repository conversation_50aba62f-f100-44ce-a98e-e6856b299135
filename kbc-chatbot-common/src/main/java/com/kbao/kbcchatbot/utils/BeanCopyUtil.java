package com.kbao.kbcchatbot.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> qiuzb
 * @Description: 基于spring的beanUtils工具, 对于基础类型转换不了
 * @create 2023/6/8 13:59
 */
@Slf4j
public class BeanCopyUtil {

    /**
     * 拷贝实体
     * @param source 源实体
     * @param clz 目标实体类型
     * @param <T> 目标实体类型
     * @param <K> 源实体类型
     * @return 目标实体
     */
    public static <T, K> K copy(T source, Class<K> clz) {
        if (source == null) {
            return null;
        }
        try {
            K target = clz.newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Bean copy error-> source={} clz={}", source, clz, e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 拷贝实体
     * @param source 源实体
     * @param clz 目标实体类型
     * @param properties 忽略的字段名称
     * @param <T> 源实体类型
     * @param <K> 目标实体类型
     * @return 目标实体
     */
    public static <T, K> K copy(T source, Class<K> clz, String... properties) {
        if (source == null) {
            return null;
        }
        try {
            K target = clz.newInstance();
            BeanUtils.copyProperties(source, target, properties);
            return target;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Bean copy error-> source={} clz={} properties={}", source, clz, properties, e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 拷贝list
     * @param sourceList 源list
     * @param clz 目标类型
     * @param <T> 源类型
     * @param <K> 目标类型
     * @return 目标list
     */
    public static <T, K> List<K> copy(List<T> sourceList, Class<K> clz) {
        try {
            if (sourceList == null) {
                return null;
            }
            List<K> list = new ArrayList<K>();
            for (T source : sourceList) {
                K target = copy(source, clz);
                if (target != null) {
                    list.add(target);
                }
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Bean copy error-> source={} clz={}", sourceList, clz, e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 拷贝list
     * @param sourceList 源list
     * @param clz 目标类型
     * @param properties 忽略的字段名称
     * @return 目标list
     */
    public static <T, K> List<K> copy(List<T> sourceList, Class<K> clz, String... properties) {
        try {
            if (sourceList == null) {
                return null;
            }
            List<K> list = new ArrayList<K>();
            for (T source : sourceList) {
                K target = copy(source, clz, properties);
                if (target != null) {
                    list.add(target);
                }
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Bean copy error-> source={} clz={} properties={}", sourceList, clz, properties, e);
            throw new RuntimeException(e.getMessage());
        }
    }

    public static void copyPropertiesIgnoreNull(Object src, Object target){
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    private static String[] getNullPropertyNames (Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for(PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }
}
