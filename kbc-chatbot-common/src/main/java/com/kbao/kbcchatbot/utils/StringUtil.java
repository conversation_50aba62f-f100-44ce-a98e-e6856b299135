package com.kbao.kbcchatbot.utils;

import com.kbao.tool.util.EmptyUtils;

import java.text.MessageFormat;
import java.util.regex.Pattern;

/**
 * @program: kbc-chatbot
 * @description: 字符串util
 * @author: husw
 * @create: 2023-06-20 15:30
 **/
public class StringUtil {
    /**
     * @Description: 字符串转义
     * @Param: [s]
     * @return: java.lang.String
     * @Author: husw
     * @Date: 2023/6/20 15:31
     */
    public static String escape(String s) {
        if (EmptyUtils.isEmpty(s)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (c == '\\' || c == '+' || c == '-' || c == '!' || c == '(' || c == ')' || c == ':'
                    || c == '^' || c == '[' || c == ']' || c == '\"' || c == '{' || c == '}' || c == '~'
                    || c == '*' || c == '?' || c == '|' || c == '&' || c == '/') {
                sb.append('\\');
            }
            sb.append(c);
        }
        return sb.toString();
    }

    /**
     * mongo模糊查询条件拼接
     *
     * @param param
     * @return
     */
    public static Pattern getMongoPattern(String param) {
        return Pattern.compile(MessageFormat.format("^.*{0}.*$", escapeExprSpecialWord(param)), Pattern.CASE_INSENSITIVE);
    }

    private static String escapeExprSpecialWord(String keyword) {
        String[] fbsArr = {"\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|"};
        for (String key : fbsArr) {
            if (keyword.contains(key)) {
                keyword = keyword.replace(key, "\\" + key);
            }
        }
        return keyword;
    }
}
