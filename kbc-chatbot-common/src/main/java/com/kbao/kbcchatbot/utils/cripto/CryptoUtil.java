package com.kbao.kbcchatbot.utils.cripto;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import com.kbao.kbcchatbot.utils.cripto.bean.CryptoData;
import com.kbao.kbcchatbot.utils.cripto.bean.CryptoRSAKeyPair;
import org.apache.commons.codec.binary.Base64;

import java.security.KeyPair;

public class CryptoUtil {

    public static CryptoData encrypt(String content, String privateKey, String publicKey) {
        //随机生成AES密钥
        byte[] aesSecretKey = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();
        //AES加密内容
        AES aes = SecureUtil.aes(aesSecretKey);
        String encryptContent = aes.encryptBase64(content);

        //RSA加密AES密钥
        RSA rsa = SecureUtil.rsa(privateKey, publicKey);
        String encryptAESKey = rsa.encryptBase64(aesSecretKey, KeyType.PublicKey);
        CryptoData cryptoData = new CryptoData();
        cryptoData.setKey(encryptAESKey);
        cryptoData.setContent(encryptContent);

        //返回数据
        return cryptoData;
    }

    public static String decrypt(CryptoData cryptoData, String privateKey) {
        //RSA解密AES密钥
        RSA rsa = SecureUtil.rsa(privateKey, null);
        byte[] aesSecretKey = rsa.decryptFromBase64(cryptoData.getKey(), KeyType.PrivateKey);

        //用AES密钥解密内容
        AES aes = SecureUtil.aes(aesSecretKey);
        String decryptContent = aes.decryptStrFromBase64(cryptoData.getContent());

        return decryptContent;
    }

    public static CryptoRSAKeyPair generateKeyPair() {
        KeyPair keyPair = SecureUtil.generateKeyPair("RSA");
        CryptoRSAKeyPair cryptoRSAKeyPair = new CryptoRSAKeyPair();
        cryptoRSAKeyPair.setPrivateKey(Base64.encodeBase64String(keyPair.getPrivate().getEncoded()));
        cryptoRSAKeyPair.setPublicKey(Base64.encodeBase64String(keyPair.getPublic().getEncoded()));
        return cryptoRSAKeyPair;
    }

    public static void main(String[] args) {
        KeyPair keyPair = SecureUtil.generateKeyPair("RSA");
        String privateKey = Base64.encodeBase64String(keyPair.getPrivate().getEncoded());
        String publicKey = Base64.encodeBase64String(keyPair.getPublic().getEncoded());
        System.out.println(">>>>privateKey:" + privateKey);
        System.out.println(">>>>publicKey:" + publicKey);

        String content = "测试加密算法382193219321jfsdjasjdkjsa";
        CryptoData cryptoData = encrypt(content, privateKey, publicKey);
        System.out.println(">>>>encryptKey:" + cryptoData.getKey());
        System.out.println(">>>>encryptContent:" + cryptoData.getContent());

        String decryptContent = decrypt(cryptoData, privateKey);
        System.out.println(">>>>decryptContent:" + decryptContent);
    }

}
