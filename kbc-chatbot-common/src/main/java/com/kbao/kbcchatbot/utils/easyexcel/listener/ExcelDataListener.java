package com.kbao.kbcchatbot.utils.easyexcel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.kbao.kbcchatbot.utils.easyexcel.callback.EasyExcelCallBack;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedList;
import java.util.List;

/**
 * @Description excel 读取监听器
 * <AUTHOR>
 * @Date 2023/1/3 14:18
 **/
@Slf4j
public class ExcelDataListener extends AnalysisEventListener {

    //定义一个保存Excel所有记录的集合
    private List<Object> list = new LinkedList<>();
    //回调接口
    private EasyExcelCallBack callBack;

    public ExcelDataListener(EasyExcelCallBack callBack){
        this.callBack = callBack;
    }

    @Override
    public void invoke(Object o, AnalysisContext analysisContext) {
        this.callBack.doAnalysedValidate(o,analysisContext.readRowHolder().getRowIndex());
        list.add(o);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        this.callBack.doAfterAllAnalysed(this.list);
    }
}
