package com.kbao.kbcchatbot.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
public class SpringUtil {

    @Value("${spring.profiles.active:sta}")
    private String env;

    public boolean isProd() {
        if("prod".equals(env)) {
            return true;
        }else {
            return false;
        }
    }

}
