package com.kbao.kbcchatbot.utils.chatbotuser.bean;

import com.kbao.kbcchatbot.maas.chatsession.bean.ChatSessionCache;
import lombok.Data;

@Data
public class ChatUser {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    private String agentCode;

    /**
     * 机器人ID
     */
    private Long robotId;

    /**
     * 机器人编码
     */
    private String robotCode;

    /**
     * 机器人版本
     */
    private String robotVersion;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 聊天会话缓存
     */
    private ChatSessionCache chatSessionCache;

    /**
     * 租户ID
     */
    private String tenantId;

    private String token;
    /**
     * 来源类型 01是通用 02是订单
     */
    private String sourceType;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 设备ID 0-APP 1-web
     */
    private String deviceType;

}
