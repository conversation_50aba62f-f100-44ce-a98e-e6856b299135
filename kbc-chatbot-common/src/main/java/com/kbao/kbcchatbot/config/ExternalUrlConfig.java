package com.kbao.kbcchatbot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> qiuzb
 * @Description: 外部接口请求URL
 * @create 2023/10/23 15:10
 */
@Component
@PropertySource(value = "classpath:/application.yml")
@ConfigurationProperties(prefix = "external.url")
@Data
public class ExternalUrlConfig {

    /**
     * 创建工单接口Url
     */
    private String createWorkOrderUrl;

    /**
     * 获取租户配置接口Url
     */
    private String skillTenantConfigUrl;

    /**
     * 创建用户接口Url
     */
    private String dtUserInfoUrl;

    /**
     * 获取技能组信息接口Url
     */
    private String skillConfigUrl;

    /**
     *初始化接口Url
     */
    private String initUrl;

    /**
     * 工作时间URL
     */
    private String workTimeUrl;

    /**
     * 创建留言接口Url
     */
    private String createLeavingMessageUrl;

    /**
     * 获取客服登录信息url
     */
    private String loginInfoUrl;

    /**
     * 服务评价url
     */
    private String surveyUrl;

    /**
     * maas域名
     */
    private String maasBaseUrl;

    /**
     * 模型平台数据新增url
     */
    private String embeddingUrl;
    /**
     * 模型平台数据删除
     */
    private String embeddingDelUrl;
    /**
     * 模型平台数据查询
     */
    private String qaSearch;

    /**
     * 模型平台数据查询(流式推送数据)
     */
    private String qaSearchStream;
    /**
     * 查询token
     */
    private String queryToken;

    /**
     * 查询Mqtt的token
     */
    private String queryMqttToken;

    /**
     * 查询用户的SessionId
     */
    private String querySessionId;

    /**
     * 查询用户聊天记录
     */
    private String queryChatRecord;

    /**
     * 获取问答记录分页数据
     */
    private String queryQaRecordPage;

    /**
     * 获取问答记录报文
     */
    private String queryQaRecordInfo;

    /**
     * 获取问答记录引用数据
     */
    private String queryQaRecordReferences;

    /**
     * 获取知识库切片列表
     */
    private String getSliceList;

    /**
     * 更新知识数据状态
     */
    private String knowledgeDataStatusUpdate;

    /**
     * 删除知识数据状态
     */
    private String knowledgeDataDel;

    /**
     * 添加知识切片
     */
    private String knowledgeSliceAdd;

    /**
     * 更新知识库切片内容
     */
    private String knowledgeSliceContentUpdate;

    /**
     * 删除知识切片
     */
    private String knowledgeSliceDel;

    /**
     * 更新知识库切片状态
     */
    private String knowledgeSliceStatusUpdate;

    /**
     * 同步产品数据
     */
    private String syncProductData;

    private String syncProductFile;

    private String syncCompanyFile;

    private String productLabel;

    private String speakToText;

    private String trainRecordList;

    private String trainAddCache;

    /**
     * 查询纠错词
     */
    private String searchCorrection;

    /**
     * 查询推荐产品
     */
    private String productRecommend;
    /**
     * 清理回话
     */
    private String cleanSession;
    /**
     * 更新消息记录(ES)
     */
    private String updateChatRecord;
    /**
     * 语音流
     */
    private String textToVoice;

}
