package com.kbao.kbcchatbot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * @program: kbc-chatbot
 * @description: 大模型配置
 * @author: husw
 * @create: 2024-06-14 09:16
 **/
@Component
@PropertySource(value = "classpath:/application.yml")
@ConfigurationProperties(prefix = "largemodel")
@Data
public class LargeModelConfig {
    /**
     * 授权口令
     */
    private String secretKey;
    /**
     * 项目code
     */
    private String code;
    /**
     * 公钥
     */
    private String publicKey;
}
