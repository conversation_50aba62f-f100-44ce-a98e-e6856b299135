package com.kbao.kbcchatbot.config;

import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * @program: kbc-chatbot
 * @description: ES配置
 * @author: husw
 * @create: 2024-01-11 09:27
 **/
@Configuration
@RefreshScope
public class ElasticsearchConfig {


    @Value("${spring.elasticsearch.rest.username}")
    private String username;

    @Value("${spring.elasticsearch.rest.password}")
    private String password;

    @Autowired
    private RestClientBuilder restClientBuilder;

    @Bean
    public RestHighLevelClient restHighLevelClient(){

        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
        return new RestHighLevelClient(restClientBuilder.setHttpClientConfigCallback(requestConfig ->{
            requestConfig.setKeepAliveStrategy((response, context) -> TimeUnit.MINUTES.toMillis(10));
            requestConfig.setDefaultCredentialsProvider(credentialsProvider);
            return requestConfig;
        }));
    }

}
