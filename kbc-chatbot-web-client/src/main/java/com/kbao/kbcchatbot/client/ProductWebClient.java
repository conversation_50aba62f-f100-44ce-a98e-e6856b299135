package com.kbao.kbcchatbot.client;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.externalapi.model.MaasBdtgDelReq;
import com.kbao.kbcchatbot.externalapi.model.MaasPolicyExtractReq;
import com.kbao.kbcchatbot.maas.product.bean.ProductLabelVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "kbc-chatbot-web")
public interface ProductWebClient {

    @PostMapping(value = "/api/noauth/client/product/label")
    Result<String> productLabel(@RequestBody ProductLabelVO req);

    @PostMapping("/api/noauth/client/search/policyExtract")
    Result<JSONObject> policyExtract(@RequestBody MaasPolicyExtractReq req);

    @PostMapping("/api/noauth/client/bdtg/saveProducts")
    Result<JSONObject> saveProducts(@RequestBody JSONObject req);

    @PostMapping("/api/noauth/client/bdtg/delProduct")
    Result<JSONObject> delProduct(@RequestBody MaasBdtgDelReq req);
}
