package com.kbao.kbcchatbot.client;

import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.externalapi.model.LargeModelMqttTokenVO;
import com.kbao.kbcchatbot.externalapi.model.MaasChatFileVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(name = "kbc-chatbot-web")
public interface MaasWebClientService {

    @PostMapping("/api/noauth/client/mass/mtoken")
    Result<LargeModelMqttTokenVO> getMqttToken(@RequestParam("code") String code, @RequestParam("secretKey") String secretKey);

    @PostMapping("/api/noauth/client/mass/uploadChatFile")
    Result<MaasChatFileVO> uploadChatFile(@RequestParam("file") MultipartFile file, @RequestParam("projectCode") String projectCode,
                                          @RequestParam("channelCode") String channelCode, @RequestParam("userId") String userId);
}
