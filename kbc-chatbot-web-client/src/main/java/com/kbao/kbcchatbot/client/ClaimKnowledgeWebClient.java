package com.kbao.kbcchatbot.client;

import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeClaimSyncVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "kbc-chatbot-web")
public interface ClaimKnowledgeWebClient {

    @PostMapping(value = "/api/noauth/client/knowledge/syncClaim")
    Result syncClaimKnowledge(@RequestBody @Valid List<RobotKnowledgeClaimSyncVO> claimSyncVOS);

}
