package com.kbao.kbcchatbot.client;

import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeRemoveReqVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "kbc-chatbot-web")
public interface RobotKnowledgeWebClient {

    @PostMapping(value = "/api/noauth/client/knowledge/removedirectory")
    Result removeDirectory(@RequestBody @Valid RobotKnowledgeRemoveReqVO reqVO);
}
