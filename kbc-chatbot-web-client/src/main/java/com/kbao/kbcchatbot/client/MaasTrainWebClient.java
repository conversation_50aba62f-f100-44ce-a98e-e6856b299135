package com.kbao.kbcchatbot.client;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitReqVO;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeRemoveReqVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "kbc-chatbot-web")
public interface MaasTrainWebClient {

    @PostMapping(value = "/api/noauth/train/scene/record/page")
    Result<PageInfo<JSONObject>> recordPage(@RequestBody PageRequest<TrainSceneInitReqVO> pageRequest);
}
