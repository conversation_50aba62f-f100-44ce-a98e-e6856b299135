<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>kbc-common</artifactId>
        <groupId>com.kbao</groupId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>
    <groupId>com.kbao</groupId>
    <artifactId>kbc-chatbot</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>kbc-chatbot</name>
    <description>快报云服智聊机器人</description>
    <packaging>pom</packaging>

    <modules>
        <module>kbc-chatbot-entity</module>
        <module>kbc-chatbot-common</module>
        <module>kbc-chatbot-service</module>
        <module>kbc-chatbot-web</module>
        <module>kbc-chatbot-api</module>
        <module>kbc-chatbot-web-client</module>
        <module>kbc-chatbot-api-client</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <elasticsearch.version>7.9.3</elasticsearch.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <easypoi.version>4.1.3</easypoi.version>
        <socket.version>5.1.13.RELEASE</socket.version>
        <mqtt.version>1.2.5</mqtt.version>
        <kbc-tsc-ospt.version>1.0.0${env.version}SNAPSHOT</kbc-tsc-ospt.version>
        <kbc-tsc-olpt.version>1.0.0${env.version}SNAPSHOT</kbc-tsc-olpt.version>
        <spring-boot-maven-plugin.version>2.4.1</spring-boot-maven-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <kbc-pm.version>1.0.0${env.version}SNAPSHOT</kbc-pm.version>
        <kbc-claim.version>1.0.0${env.version}SNAPSHOT</kbc-claim.version>
        <kbc-uoc.version>1.0.0${env.version}SNAPSHOT</kbc-uoc.version>
        <kbc-ccs-dc.version>1.0.0${env.version}SNAPSHOT</kbc-ccs-dc.version>
        <ums-api-client.version>1.0.0${env.version}SNAPSHOT</ums-api-client.version>
        <ums-web-client.version>1.0.0${env.version}SNAPSHOT</ums-web-client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- sprint boot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <!-- elasticsearch -->
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!-- easy excel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel-core</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- easy poi  -->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-base</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-annotation</artifactId>
                <version>${easypoi.version}</version>
            </dependency>

            <!-- 项目内依赖 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-chatbot-entity</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-chatbot-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-chatbot-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-chatbot-web-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-chatbot-api-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 基础平台 接口服务包 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-bsc-web-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-bsc-api-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>

            <!--云知识库 web-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-km-web-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>
            <!--云知识库 api-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-km-api-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>
            <!--UCS-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ucs-web-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>
            <!-- 用户 api client包-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ucs-api-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>

            <!-- 云核心网销短期险管理 接口服务包 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-tsc-ospt-api-client</artifactId>
                <version>${kbc-tsc-ospt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-tsc-olpt-api-client</artifactId>
                <version>${kbc-tsc-olpt.version}</version>
            </dependency>


            <!-- 文件平台 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ufs-web-client</artifactId>
                <version>1.1.0${env.version}SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ufs-api-client</artifactId>
                <version>1.1.0${env.version}SNAPSHOT</version>
            </dependency>
            <!--调度平台-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-job-client-starter</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>

            <!--数据回流服务-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-dataflow-api-client</artifactId>
                <version>1.0.0${env.version}SNAPSHOT</version>
            </dependency>


            <!-- 根据客户信息和顾问信息查询托管/服务关系 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-pm-api-client</artifactId>
                <version>${kbc-pm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-pm-web-client</artifactId>
                <version>${kbc-pm.version}</version>
            </dependency>

            <!-- 好赔 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-claim-api-client</artifactId>
                <version>${kbc-claim.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-claim-web-client </artifactId>
                <version>${kbc-claim.version}</version>
            </dependency>

            <!-- UOC 统一订单 -->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-uoc-api-client</artifactId>
                <version>${kbc-uoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-uoc-web-client</artifactId>
                <version>${kbc-uoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-uoc-entity</artifactId>
                <version>${kbc-uoc.version}</version>
            </dependency>

            <!--CCS 童管家-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ccs-dc-api-client</artifactId>
                <version>${kbc-ccs-dc.version}</version>
            </dependency>

            <!--统一消息平台api client包-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ums-api-client</artifactId>
                <version>${ums-api-client.version}</version>
            </dependency>

            <!--统一消息平台web client包-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ums-web-client</artifactId>
                <version>${ums-web-client.version}</version>
            </dependency>

            <!--CCS 童管家-->
            <dependency>
                <groupId>com.kbao</groupId>
                <artifactId>kbc-ccs-dc-api-client</artifactId>
                <version>${kbc-ccs-dc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-websocket</artifactId>
                <version>${socket.version}</version>
            </dependency>
            <!--mqtt-->
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${mqtt.version}</version>
            </dependency>

            <!-- spring 模板引擎 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.threeten</groupId>
                <artifactId>threetenbp</artifactId>
                <version>1.6.8</version>
            </dependency>
            <dependency>
                <groupId>io.gsonfire</groupId>
                <artifactId>gson-fire</artifactId>
                <version>1.8.5</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>4.4.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>nexus</id>
            <name>nexus</name>
            <url>http://10.176.1.3:8081/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://10.176.1.3:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://10.176.1.3:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <target>${java.version}</target>
                    <source>${java.version}</source>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <addMavenDescriptor>false</addMavenDescriptor>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
                <env.version>-sta-</env.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <profiles.active>local</profiles.active>
                <env.version>-sta-</env.version>
            </properties>
        </profile>
        <profile>
            <id>sta</id>
            <properties>
                <profiles.active>sta</profiles.active>
                <env.version>-sta-</env.version>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <profiles.active>uat</profiles.active>
                <env.version>-uat-</env.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <env.version>-</env.version>
            </properties>
            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <url>http://10.176.1.3:8082/nexus/content/repositories/releases</url>
                </repository>
                <snapshotRepository>
                    <id>snapshots</id>
                    <url>http://10.176.1.3:8082/nexus/content/repositories/snapshots</url>
                </snapshotRepository>
            </distributionManagement>

            <repositories>
                <repository>
                    <id>nexus</id>
                    <name>nexus</name>
                    <url>http://10.176.1.3:8082/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>

</project>
