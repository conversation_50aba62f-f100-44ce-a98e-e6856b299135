@echo off
set env=uat

cd ..

for /f "delims=" %%i in ('git rev-parse --abbrev-ref head') do set result=%%i
echo.
echo ��ǰ�����֧Ϊ��%result%
echo.
if %result% == %env% ( goto A  ) else ( goto B )
pause 
exit

:A
echo 1�����ð汾��Ϊ %env%�汾
call mvn versions:set -DnewVersion=1.0.0-%env%-SNAPSHOT
call mvn versions:update-child-modules
echo 2����װ���
call mvn clean deploy -P %env% -DskipTests
echo 3���ع��汾��
call mvn versions:revert
echo 4������������˳�
pause>nul
exit

:B
echo ���⵱ǰ�����֧���������л���%env%�����󷢰�
pause>nul
exit