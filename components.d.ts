/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    Area: typeof import('./src/components/Area.vue')['default']
    DoubleDate: typeof import('./src/components/DoubleDate.vue')['default']
    DtEntrustList: typeof import('./src/components/DtEntrustList.vue')['default']
    DtIcon: typeof import('./src/components/DtIcon.vue')['default']
    DtNav: typeof import('./src/components/DtNav.vue')['default']
    DtPopup: typeof import('./src/components/DtPopup.vue')['default']
    DtProductList: typeof import('./src/components/DtProductList.vue')['default']
    DtTextItem: typeof import('./src/components/DtTextItem.vue')['default']
    DtTextTable: typeof import('./src/components/DtTextTable.vue')['default']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    NotFound: typeof import('./src/components/NotFound.vue')['default']
    PageTable: typeof import('./src/components/PageTable.vue')['default']
    Pagination: typeof import('./src/components/Pagination.vue')['default']
    RemoteSelect: typeof import('./src/components/RemoteSelect.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchForm: typeof import('./src/components/SearchForm.vue')['default']
    TableToolTemp: typeof import('./src/components/TableToolTemp.vue')['default']
    UpdateFormTemp: typeof import('./src/components/UpdateFormTemp.vue')['default']
    UploadCard: typeof import('./src/components/UploadCard.vue')['default']
    UploadFileBtn: typeof import('./src/components/UploadFileBtn.vue')['default']
    UploadFileMultiple: typeof import('./src/components/UploadFileMultiple.vue')['default']
    Editor: typeof import('./src/components/Editor.vue')['default']
    DtRobotSelect: typeof import('./src/components/DtRobotSelect.vue')['default']
    DtprojectList: typeof import('./src/components/DtprojectList.vue')['default']
    DtNumber: typeof import('./src/components/DtNumber.vue')['default']
    SearchFormUp:typeof import('./src/components/SearchFormUp.vue')['default']
    UpdateForm: typeof import('./src/components/UpdateFormTemp.vue')['default']
    NewUpdateForm: typeof import('./src/components/NewUpdateFormTemp.vue')['default']
    SecondaryPageHeader: typeof import('./src/components/SecondaryPageHeader.vue')['default']
    UniversalTable: typeof import('./src/components/UniversalTable.vue')['default']
    UniversalFormDialog: typeof import('./src/components/UniversalFormDialog.vue')['default']
    ConfirmDialog: typeof import('./src/components/ConfirmDialog.vue')['default']
    NewSearch: typeof import('./src/components/NewSearch.vue')['default']
  }
}
