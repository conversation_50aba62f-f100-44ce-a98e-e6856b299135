<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>kbc-chatbot</artifactId>
        <groupId>com.kbao</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.kbao</groupId>
    <artifactId>kbc-chatbot-entity</artifactId>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <!-- API文档配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>db-spring-boot-starter</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- API文档配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>tool-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-elasticsearch</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.microsoft.spring.data.gremlin</groupId>-->
<!--            <artifactId>spring-data-gremlin</artifactId>-->
<!--        </dependency>-->

        <!-- neo4j -->
<!--        <dependency>-->
<!--            <groupId>org.neo4j</groupId>-->
<!--            <artifactId>neo4j-ogm-core</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-data-neo4j</artifactId>-->
<!--        </dependency>-->

        <!-- easy excel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
