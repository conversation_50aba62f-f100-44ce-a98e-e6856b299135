package com.kbao.kbcchatbot.common.bean;

import java.nio.charset.Charset;

/**
 * <AUTHOR> qiuzb
 * @Description: 转换Content接口
 * @create 2023/6/9 9:11
 */
public interface Contentable {

    /**
     * 这个方法返回一个String类型的内容
     * @return String
     */
    String getContent();

    /**
     * 这个方法设置一个Object类型的内容
     * @param content 内容
     */
    void setContent(Object content);

    // 这个方法设置一个Object类型的内容
    void setContent(String content);

    void setContent(byte[] content);

    /**
     * 这个方法返回一个byte[]类型的内容
     * @param utf8 编码
     * @return byte[]
     */
    byte[] getContent(Charset utf8);
}
