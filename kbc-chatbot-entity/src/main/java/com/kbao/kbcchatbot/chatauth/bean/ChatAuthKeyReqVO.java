package com.kbao.kbcchatbot.chatauth.bean;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @program: kbc-chatbot
 * @description: 渠道查询授信息
 * @author: husw
 * @create: 2023-09-04 11:29
 **/
@Data
public class ChatAuthKeyReqVO {

    @NotBlank(message = "渠道编码不能为空！")
    private String channelCode;

    /**
     * timeStamp+channelCode
     */
    @NotBlank(message = "key不能为空！")
    private String key;
}
