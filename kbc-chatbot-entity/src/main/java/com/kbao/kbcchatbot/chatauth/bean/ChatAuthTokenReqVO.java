package com.kbao.kbcchatbot.chatauth.bean;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-06-09 10:58
 **/
@Data
public class ChatAuthTokenReqVO {

    @NotBlank(message = "渠道编码不能为空！")
    private String channelCode;
    /**
     * 来源类型 01是通用 02是订单
     */
    private String sourceType;
    /**
     * RSA加密AES密钥
     */
    private String key;
    /**
     * 授权码（userId,userName,timestamp）
     */
    private String content;
}
