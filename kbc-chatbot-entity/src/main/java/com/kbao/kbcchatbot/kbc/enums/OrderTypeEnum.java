package com.kbao.kbcchatbot.kbc.enums;

import lombok.Getter;

@Getter
public enum OrderTypeEnum {

    SHORT("1","互联网短险"),
    NMI("2","互联网期缴"),
    ONLINE("3","线下寿险"),
    LPT("4","电投寿险");

    OrderTypeEnum(String type,String value){
        this.type = type;
        this.value = value;
    }

    private String type;
    private String value;

    public static String getValueByType(String type){
        for (OrderTypeEnum emum:OrderTypeEnum.values()){
            if (emum.getType().equals(type)){
                return emum.getValue();
            }
        }
        return null;
    }
}
