package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgeChapter;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 知识详情
 * @author: husw
 * @create: 2023-06-15 16:39
 **/
@Data
public class RobotKnowledgeDetailRespVO {
    @Id
    private String id;
    /**
     * 知识ID
     */
    private String knowledgeId;

    /**
     * 机器人ID
     */
    private Long robotId;
    /**
     * 版本类型 0-测试版 1-正式版
     */
    private Integer environment;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 标题
     */
    private String title;
    /**
     * 类型: 1知识，2略树
     * 字典:km.knowledge.type
     */
    private String type;
    /**
     * 所属项目不能为空
     */
    private String projectId;
    /**
     * 所属项目名称
     */
    private String projectName;
    /**
     * 所属知识库夹不能为空
     */
    private String folderId;
    /**
     * 所属知识库夹名称
     */
    private String folderName;
    /**
     * 一级目录
     */
    private String firstDirect;
    /**
     * 一级目录名称
     */
    private String firstDirectName;
    /**
     * 二级目录
     */
    private String secondDirect;
    /**
     * 二级目录名称
     */
    private String secondDirectName;
    /**
     * FAQ类目id
     */
    private Long faqCategoryId;
    /**
     * 所属类型: 1公司，2产品
     * 字典：km.knowledge.belong.type
     */
    private String belongType;
    /**
     * 所属名称
     */
    private String belongName;
    /**
     * 关联公司
     */
    private String companyId;
    /**
     * 关联公司名称
     */
    private String companyName;
    /**
     * 关联产品
     */
    private String productId;
    /**
     * 关联产品名称
     */
    private String productName;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 关键字数量
     */
    private Integer keywordNum;
    /**
     * 互斥文章
     */
    private String mutexArticle;
    /**
     * 互斥文章
     */
    private String mutexArticleName;
    /**
     * 相似问题
     */
    private String similarQuestion;
    /**
     * 关联文章
     */
    private String relatedArticles;
    /**
     * 内容
     */
    private String subContent;
    /**
     * 历程图章节
     */
    private List<KnowledgeChapter> chapters;
    /**
     * 封面
     */
    private String cover;
    /**
     * 状态：1待发布，2已发布
     * 字典：km.knowledge.state
     */
    private String state;
    /**
     * 浏览量
     */
    private Integer readingVolume;
    /**
     * 点赞数
     */
    private Integer praiseVolume;
    /**
     * 评论数
     */
    private Integer commentVolume;
    /**
     * 点赞状态,1已点赞，0未点赞
     */
    private Integer praiseState;
    /**
     * 置顶：0未置顶，1已置顶
     */
    private Integer top;
    /**
     * 置顶时间
     */
    private Date topTime;
    /**
     * 是否可编辑，1为可编辑，0为不可编辑
     */
    private Integer editable;
    /**
     * 允许分享，1可分享，0不可享
     */
    private Integer shareable;
    /**
     * "是共享，1是，0否
     */
    private Integer isShare;
    /**
     * 来源知识库
     */
    private String sourceFolder;
    /**
     * FAQ知识id
     */
    private Long faqKnowledgeId;

    /**
     * 删除状态：0未删除，1删除
     */
    private Integer isDeleted;

    /**
     * 获取云知识QA答案
     * @return
     */
    @Transient
    public String getAnswer() {
        if(CollectionUtils.isEmpty(chapters)) {
            return subContent;
        }else {
            return chapters.get(0).getChapterContent();
        }
    }
}
