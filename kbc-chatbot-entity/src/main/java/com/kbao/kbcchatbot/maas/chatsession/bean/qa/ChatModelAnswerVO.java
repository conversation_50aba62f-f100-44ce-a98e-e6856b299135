package com.kbao.kbcchatbot.maas.chatsession.bean.qa;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 智聊模型回复对象
 * @author: husw
 * @create: 2024-07-23 10:35
 **/
@Data
public class ChatModelAnswerVO {
    /**
     * 文本
     */
    private String text;
    /**
     * 链接
     */
    private List<String> urls;

    /**
     * 来源对象
     */
    private List<Reference> references;

    /**
     * 是否开启评价  0-否 1-是
     */
    private String vote;

    /**
     * 块状态 0-部分内容  1-完整内容
     */
    private Integer chunkStatus;


    @Data
    public static class Reference{
        /**
         * 类型 0-文本 1-图文，2视频，3文件
         */
        private String type;
        /**
         * 知识ID
         */
        private String knowledgeId;
        /**
         * 章节id
         */
        private String itemId;
        /**
         * 知识名称
         */
        private String title;

        /**
         * 分数
         */
        private BigDecimal score;
    }
}
