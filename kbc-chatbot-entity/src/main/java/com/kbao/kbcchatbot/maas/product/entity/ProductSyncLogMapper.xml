<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.product.dao.ProductSyncLogMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.product.entity.ProductSyncLog">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="syncType" jdbcType="CHAR"  column="sync_type" />  
        <result property="productNum" jdbcType="INTEGER"  column="product_num" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		sync_type,  
		product_num,  
		create_time  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.sync_type, 
		t.product_num, 
		t.create_time 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="syncType != null">
	   		and t.sync_type = #{syncType,jdbcType=CHAR}
	    </if>
	    <if test="productNum != null">
	   		and t.product_num = #{productNum,jdbcType=INTEGER}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_product_sync_log t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_product_sync_log
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_product_sync_log
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.product.entity.ProductSyncLog">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_product_sync_log(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{syncType,jdbcType=CHAR}, 
                 
                #{productNum,jdbcType=INTEGER}, 
                 
                #{createTime,jdbcType=TIMESTAMP} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.product.entity.ProductSyncLog">
		update t_product_sync_log
		<set>
	        <if test="syncType != null ">  
	        	sync_type = #{syncType,jdbcType=CHAR},  
	        </if>  
	        <if test="productNum != null ">  
	        	product_num = #{productNum,jdbcType=INTEGER},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
	<select id="getLastSyncLog" resultMap="BaseResultMap">
		select * from t_product_sync_log where sync_type = '0'
		order by create_time desc limit 1
	</select>
</mapper>
