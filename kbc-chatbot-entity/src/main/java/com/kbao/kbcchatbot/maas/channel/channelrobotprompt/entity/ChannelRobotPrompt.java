package com.kbao.kbcchatbot.maas.channel.channelrobotprompt.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR>
* @Description maas机器人提示词表实体
* @Date 2024-12-17
*/
@Data
public class ChannelRobotPrompt implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中channel_robot_id
     * 渠道机器人id
     */  
	private Integer channelRobotId;

    /**
     * 对应表中prompt_type
     * 提示词类型: 1-知识问答，2-闲聊
     */  
	private String promptType;

    /**
     * 对应表中prompt
     * 提示词
     */  
	private String prompt;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_by
     * 更新人
     */  
	private String updateBy;

}   