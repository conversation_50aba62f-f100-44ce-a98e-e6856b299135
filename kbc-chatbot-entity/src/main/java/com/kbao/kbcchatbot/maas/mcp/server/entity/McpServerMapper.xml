<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.mcp.server.dao.McpServerMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.mcp.server.entity.McpServer">
    	<id column="server_id" jdbcType="BIGINT"  property="serverId"  />
        <result property="serverCode" jdbcType="VARCHAR"  column="server_code" />
        <result property="serverName" jdbcType="VARCHAR"  column="server_name" />  
        <result property="serverUrl" jdbcType="VARCHAR"  column="server_url" />  
        <result property="serverPort" jdbcType="INTEGER"  column="server_port" />  
        <result property="serverStatus" jdbcType="INTEGER"  column="server_status" />  
        <result property="description" jdbcType="VARCHAR"  column="description" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="isDeleted" jdbcType="INTEGER"  column="is_deleted" />  
	</resultMap>

	<sql id="Base_Column_List">
		server_id,  
		server_code,  
		server_name,  
		server_url,  
		server_port,  
		server_status,  
		description,  
		tenant_id,  
		create_time,  
		create_id,  
		update_id,  
		update_time,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.server_id, 
		t.server_code, 
		t.server_name, 
		t.server_url, 
		t.server_port, 
		t.server_status, 
		t.description, 
		t.tenant_id, 
		t.create_time, 
		t.create_id, 
		t.update_id, 
		t.update_time, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0 
	    <if test="serverId != null and serverId != ''"> 
	   		and t.server_id = #{serverId,jdbcType=VARCHAR}  
	    </if>
	    <if test="serverName != null and serverName != ''"> 
	   		and t.server_name like CONCAT('%', #{serverName,jdbcType=VARCHAR}, '%')  
	    </if>
	    <if test="serverUrl != null and serverUrl != ''"> 
	   		and t.server_url = #{serverUrl,jdbcType=VARCHAR}  
	    </if>
	    <if test="serverPort != null"> 
	   		and t.server_port = #{serverPort,jdbcType=INTEGER}  
	    </if>
	    <if test="serverStatus != null"> 
	   		and t.server_status = #{serverStatus,jdbcType=INTEGER}  
	    </if>
	    <if test="description != null and description != ''"> 
	   		and t.description = #{description,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null and tenantId != ''"> 
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null"> 
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="createId != null and createId != ''"> 
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateId != null and updateId != ''"> 
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null"> 
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="isDeleted != null"> 
	   		and t.is_deleted = #{isDeleted,jdbcType=INTEGER}  
	    </if>
		<!-- 自定义条件-->
		<if test="serverIds != null and serverIds.size()>0">
			and t.server_id in
			 <foreach collection="serverIds" index="index" item="item" open="(" separator="," close=")"> #{item}</foreach>
		</if>
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_mcp_server t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_mcp_server t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_mcp_server
		where  is_deleted = 0 and server_id = #{serverId,jdbcType=BIGINT}
	</select>


	<!-- 根据主键删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
		update t_mcp_server set is_deleted = 1
		where server_id = #{serverId,jdbcType=BIGINT} and is_deleted = 0
	</update>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.mcp.server.entity.McpServer">
		insert into t_mcp_server(
			<include refid="Base_Column_List" />
		)
		values(  
                #{serverId,jdbcType=BIGINT}, 
                 
                #{serverCode,jdbcType=VARCHAR}, 
                 
                #{serverName,jdbcType=VARCHAR}, 
                 
                #{serverUrl,jdbcType=VARCHAR}, 
                 
                #{serverPort,jdbcType=INTEGER}, 
                 
                #{serverStatus,jdbcType=INTEGER}, 
                 
                #{description,jdbcType=VARCHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{isDeleted,jdbcType=INTEGER} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.mcp.server.entity.McpServer">
		insert into t_mcp_server
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="serverId != null ">  
	       		server_id,
	        </if>  
	        <if test="serverCode != null ">  
	       		server_code,
	        </if>  
	        <if test="serverName != null ">  
	       		server_name,
	        </if>  
	        <if test="serverUrl != null ">  
	       		server_url,
	        </if>  
	        <if test="serverPort != null ">  
	       		server_port,
	        </if>  
	        <if test="serverStatus != null ">  
	       		server_status,
	        </if>  
	        <if test="description != null ">  
	       		description,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="isDeleted != null ">  
	       		is_deleted,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serverId != null">  
            	#{serverId,jdbcType=BIGINT},
            </if>  
            <if test="serverCode != null">  
            	#{serverCode,jdbcType=VARCHAR},
            </if>  
            <if test="serverName != null">  
            	#{serverName,jdbcType=VARCHAR},
            </if>  
            <if test="serverUrl != null">  
            	#{serverUrl,jdbcType=VARCHAR},
            </if>  
            <if test="serverPort != null">  
            	#{serverPort,jdbcType=INTEGER},
            </if>  
            <if test="serverStatus != null">  
            	#{serverStatus,jdbcType=INTEGER},
            </if>  
            <if test="description != null">  
            	#{description,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="isDeleted != null">  
            	#{isDeleted,jdbcType=INTEGER},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.mcp.server.entity.McpServer">
		update t_mcp_server
		<set>
	        <if test="serverCode != null ">  
	        	server_code = #{serverCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="serverName != null ">  
	        	server_name = #{serverName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="serverUrl != null ">  
	        	server_url = #{serverUrl,jdbcType=VARCHAR},  
	        </if>  
	        <if test="serverPort != null ">  
	        	server_port = #{serverPort,jdbcType=INTEGER},  
	        </if>  
	        <if test="serverStatus != null ">  
	        	server_status = #{serverStatus,jdbcType=INTEGER},  
	        </if>  
	        <if test="description != null ">  
	        	description = #{description,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="isDeleted != null ">  
	        	is_deleted = #{isDeleted,jdbcType=INTEGER},  
	        </if>  
		</set>
		where server_id = #{serverId,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.mcp.server.entity.McpServer">
		update t_mcp_server
		set
           server_code = #{serverCode,jdbcType=VARCHAR},
           server_name = #{serverName,jdbcType=VARCHAR},
           server_url = #{serverUrl,jdbcType=VARCHAR},
           server_port = #{serverPort,jdbcType=INTEGER},
           server_status = #{serverStatus,jdbcType=INTEGER},
           description = #{description,jdbcType=VARCHAR},
           tenant_id = #{tenantId,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           create_id = #{createId,jdbcType=VARCHAR},
           update_id = #{updateId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           is_deleted = #{isDeleted,jdbcType=INTEGER}
		where server_id = #{serverId,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_mcp_server(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.serverId}
	        <choose>
	            <when test="item.serverCode != null">,#{item.serverCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.serverName != null">,#{item.serverName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.serverUrl != null">,#{item.serverUrl}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.serverPort != null">,#{item.serverPort}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.serverStatus != null">,#{item.serverStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.description != null">,#{item.description}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量删除 -->
	<update id="batchDelete" parameterType="java.util.List">
		update t_mcp_server set is_deleted = 1
		where server_id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}</foreach>
		and is_deleted = 0
	</update>

</mapper>