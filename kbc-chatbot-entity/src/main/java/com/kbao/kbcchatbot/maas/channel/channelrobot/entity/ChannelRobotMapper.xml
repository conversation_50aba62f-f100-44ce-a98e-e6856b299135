<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.channel.channelrobot.dao.ChannelRobotMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.channel.channelrobot.entity.ChannelRobot">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="channelId" jdbcType="INTEGER"  column="channel_id" />  
        <result property="robotId" jdbcType="INTEGER"  column="robot_id" />  
        <result property="status" jdbcType="CHAR"  column="status" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		channel_id,  
		robot_id,  
		status,  
		create_time,  
		create_by
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.channel_id, 
		t.robot_id, 
		t.status, 
		t.create_time, 
		t.create_by
	</sql>

	<sql id="Base_Condition">
		<where>
	    and t.is_delete = '0'
	    <if test="channelId != null">
	   		and t.channel_id = #{channelId,jdbcType=INTEGER}  
	    </if>
	    <if test="robotId != null">
	   		and t.robot_id = #{robotId,jdbcType=INTEGER}  
	    </if>
	    <if test="status != null">
	   		and t.status = #{status,jdbcType=CHAR}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_channel_robot t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_channel_robot t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_channel_robot
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_channel_robot set is_delete = '1'
		where id = #{id,jdbcType=INTEGER}
	</update>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.channel.channelrobot.entity.ChannelRobot">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_channel_robot(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{channelId,jdbcType=INTEGER}, 
                 
                #{robotId,jdbcType=INTEGER}, 
                 
                #{status,jdbcType=CHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{createBy,jdbcType=VARCHAR}
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelrobot.entity.ChannelRobot">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_channel_robot
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="channelId != null ">  
	       		channel_id,
	        </if>  
	        <if test="robotId != null ">  
	       		robot_id,
	        </if>  
	        <if test="status != null ">  
	       		status,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createBy != null ">  
	       		create_by,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="channelId != null">  
            	#{channelId,jdbcType=INTEGER},
            </if>  
            <if test="robotId != null">  
            	#{robotId,jdbcType=INTEGER},
            </if>  
            <if test="status != null">  
            	#{status,jdbcType=CHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="createBy != null">  
            	#{createBy,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelrobot.entity.ChannelRobot">
		update t_channel_robot
		<set>
	        <if test="channelId != null ">  
	        	channel_id = #{channelId,jdbcType=INTEGER},  
	        </if>  
	        <if test="robotId != null ">  
	        	robot_id = #{robotId,jdbcType=INTEGER},  
	        </if>  
	        <if test="status != null ">  
	        	status = #{status,jdbcType=CHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createBy != null ">  
	        	create_by = #{createBy,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.channel.channelrobot.entity.ChannelRobot">
		update t_channel_robot
		set
           channel_id = #{channelId,jdbcType=INTEGER},
           robot_id = #{robotId,jdbcType=INTEGER},
           status = #{status,jdbcType=CHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           create_by = #{createBy,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_channel_robot(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.robotId != null">,#{item.robotId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createBy != null">,#{item.createBy}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_channel_robot(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.robotId != null">,#{item.robotId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createBy != null">,#{item.createBy}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   channel_id=values(channel_id), 
		   robot_id=values(robot_id), 
		   status=values(status), 
		   create_time=values(create_time), 
		   create_by=values(create_by)
	</update>

	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_channel_robot where id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>

	<!-- 自定义查询 -->
	<select id="isExistChannelRobot" resultType="int">
		select count(1)
		from t_channel_robot c
		left join t_robot r on c.robot_id = r.id
		where c.is_delete = '0'
		<if test="channelId != null">
			and c.channel_id = #{channelId}
		</if>
		<if test="robotType != null">
			and r.robot_type = #{robotType}
		</if>
	</select>

	<select id="getChannelRobotList" resultType="com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotListVo">
		select  t.id id,t.channel_id channel,t.robot_id robot,r.robot_code robotCode,
		        r.robot_name robotName,r.robot_type robotType,t.status status,t.create_by createBy,t.create_time createTime
		from t_channel_robot t
		left join t_robot r on t.robot_id = r.id
		where t.channel_id = #{channelId}
		and t.is_delete = '0'
		order by t.create_time desc
	</select>

	<select id="getChannelRobotPage" resultType="com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotListVo">
		select  t.id id,t.channel_id channel,t.robot_id robot,r.robot_code robotCode,
		        r.robot_name robotName,r.robot_type robotType,t.status status,t.create_by createBy,t.create_time createTime
		from t_channel_robot t
		left join t_robot r on t.robot_id = r.id
		<where>
			t.channel_id = #{channelId}
			and t.is_delete = '0'
			<if test="robotName != null and robotName != ''">
				and r.robot_name like concat("%",#{robotName},"%")
			</if>
			<if test="robotCode != null and robotCode != ''">
				and r.robot_code = #{robotCode}
			</if>
		</where>
	</select>
</mapper>
