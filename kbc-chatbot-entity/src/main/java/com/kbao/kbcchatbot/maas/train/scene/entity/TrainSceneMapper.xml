<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.train.scene.dao.TrainSceneMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.train.scene.entity.TrainScene">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="sceneCode" jdbcType="VARCHAR"  column="scene_code" />
		<result property="sceneName" jdbcType="VARCHAR"  column="scene_name" />
        <result property="robotName" jdbcType="VARCHAR"  column="robot_name" />  
        <result property="robotImage" jdbcType="VARCHAR"  column="robot_image" />  
        <result property="robotInfo" jdbcType="VARCHAR"  column="robot_info" />  
        <result property="scenePrompt" jdbcType="LONGVARCHAR"  column="scene_prompt" />  
        <result property="scorePrompt" jdbcType="LONGVARCHAR"  column="score_prompt" />  
        <result property="memoryCacheNum" jdbcType="INTEGER"  column="memory_cache_num" />  
        <result property="modelId" jdbcType="INTEGER"  column="model_id" />
		<result property="scoreModelId" jdbcType="INTEGER"  column="score_model_id" />
        <result property="passScore" jdbcType="INTEGER"  column="pass_score" />
		<result property="standardCaseImage" jdbcType="VARCHAR"  column="standard_case_image" />
		<result property="voiceStatus" jdbcType="CHAR"  column="voice_status" />
		<result property="voiceSynthesis" jdbcType="CHAR"  column="voice_synthesis" />
		<result property="voiceSpeaker" jdbcType="VARCHAR"  column="voice_speaker" />
		<result property="uploadFileStatus" jdbcType="CHAR"  column="upload_file_status" />
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateBy" jdbcType="VARCHAR"  column="update_by" />  
        <result property="isDelete" jdbcType="CHAR"  column="is_delete" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		scene_code,
		scene_name,
		robot_name,  
		robot_image,  
		robot_info,  
		scene_prompt,  
		score_prompt,  
		memory_cache_num,  
		model_id,
		score_model_id,
		pass_score,
		standard_case_image,
		voice_status,
		voice_synthesis,
		voice_speaker,
		upload_file_status,
		create_time,  
		create_by,  
		update_time,  
		update_by,  
		is_delete,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.scene_code,
		t.scene_name,
		t.robot_name, 
		t.robot_image, 
		t.robot_info, 
		t.scene_prompt, 
		t.score_prompt, 
		t.memory_cache_num, 
		t.model_id,
		t.score_model_id,
		t.pass_score,
		t.standard_case_image,
		t.voice_status,
		t.voice_synthesis,
		t.voice_speaker,
		t.upload_file_status,
		t.create_time, 
		t.create_by, 
		t.update_time, 
		t.update_by, 
		t.is_delete, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
		is_delete = '0'
	    <if test="sceneCode != null and sceneCode != ''">
	   		and t.scene_code = #{sceneCode,jdbcType=VARCHAR}
	    </if>
		<if test="sceneName != null and sceneName != ''">
			and instr(t.scene_name,#{sceneName,jdbcType=VARCHAR}) > 0
		</if>
	    <if test="robotName != null and robotName != ''">
	   		and t.robot_name = #{robotName,jdbcType=VARCHAR}
	    </if>
	    <if test="robotImage != null and robotImage != ''">
	   		and t.robot_image = #{robotImage,jdbcType=VARCHAR}
	    </if>
	    <if test="robotInfo != null and robotInfo != ''">
	   		and t.robot_info = #{robotInfo,jdbcType=VARCHAR}
	    </if>
	    <if test="scenePrompt != null">
	   		and t.scene_prompt = #{scenePrompt,jdbcType=LONGVARCHAR}
	    </if>
	    <if test="scorePrompt != null">
	   		and t.score_prompt = #{scorePrompt,jdbcType=LONGVARCHAR}
	    </if>
	    <if test="memoryCacheNum != null">
	   		and t.memory_cache_num = #{memoryCacheNum,jdbcType=INTEGER}
	    </if>
	    <if test="modelId != null">
	   		and t.model_id = #{modelId,jdbcType=INTEGER}
	    </if>
	    <if test="passScore != null">
	   		and t.pass_score = #{passScore,jdbcType=INTEGER}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateBy != null and updateBy != ''">
	   		and t.update_by = #{updateBy,jdbcType=VARCHAR}
	    </if>
	    <if test="isDelete != null">
	   		and t.is_delete = #{isDelete,jdbcType=CHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_train_scene t
		<include refid="Base_Condition" />
		order by t.update_time desc
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_train_scene
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_train_scene set is_delete = '1'
		where id = #{id,jdbcType=INTEGER}
	</update>

	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.train.scene.entity.TrainScene">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_train_scene(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{sceneCode,jdbcType=VARCHAR}, 

		       	#{sceneName,jdbcType=VARCHAR},

                #{robotName,jdbcType=VARCHAR}, 
                 
                #{robotImage,jdbcType=VARCHAR}, 
                 
                #{robotInfo,jdbcType=VARCHAR}, 
                 
                #{scenePrompt,jdbcType=LONGVARCHAR}, 
                 
                #{scorePrompt,jdbcType=LONGVARCHAR}, 
                 
                #{memoryCacheNum,jdbcType=INTEGER}, 
                 
                #{modelId,jdbcType=INTEGER},

		       	#{scoreModelId,jdbcType=INTEGER},
                 
                #{passScore,jdbcType=INTEGER}, 

		       	#{standardCaseImage,jdbcType=VARCHAR},

				#{voiceStatus,jdbcType=VARCHAR},

				#{voiceSynthesis,jdbcType=VARCHAR},

				#{voiceSpeaker,jdbcType=VARCHAR},

				#{uploadFileStatus,jdbcType=VARCHAR},

                now(),
                 
                #{createBy,jdbcType=VARCHAR}, 
                 
                now(),

				#{createBy,jdbcType=VARCHAR},

				'0',
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.train.scene.entity.TrainScene">
		update t_train_scene
		<set>
	        <if test="sceneCode != null ">  
	        	scene_code = #{sceneCode,jdbcType=VARCHAR},  
	        </if>
		    <if test="sceneName != null ">
		    	scene_name = #{sceneName,jdbcType=VARCHAR},
		    </if>
	        <if test="robotName != null ">  
	        	robot_name = #{robotName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="robotImage != null ">  
	        	robot_image = #{robotImage,jdbcType=VARCHAR},  
	        </if>  
	        <if test="robotInfo != null ">  
	        	robot_info = #{robotInfo,jdbcType=VARCHAR},  
	        </if>  
	        <if test="scenePrompt != null ">  
	        	scene_prompt = #{scenePrompt,jdbcType=LONGVARCHAR},  
	        </if>  
	        <if test="scorePrompt != null ">  
	        	score_prompt = #{scorePrompt,jdbcType=LONGVARCHAR},  
	        </if>  
	        <if test="memoryCacheNum != null ">  
	        	memory_cache_num = #{memoryCacheNum,jdbcType=INTEGER},  
	        </if>  
	        <if test="modelId != null ">  
	        	model_id = #{modelId,jdbcType=INTEGER},  
	        </if>
		    <if test="scoreModelId != null ">
		    	score_model_id = #{scoreModelId,jdbcType=INTEGER},
		    </if>
	        <if test="passScore != null ">  
	        	pass_score = #{passScore,jdbcType=INTEGER},  
	        </if>
		    <if test="standardCaseImage != null ">
		    	standard_case_image = #{standardCaseImage,jdbcType=VARCHAR},
		    </if>
		    <if test="voiceStatus != null ">
		    	voice_status = #{voiceStatus,jdbcType=VARCHAR},
		    </if>
			<if test="voiceSynthesis != null ">
				voice_synthesis = #{voiceSynthesis,jdbcType=VARCHAR},
		    </if>
			<if test="voiceSpeaker != null ">
				voice_speaker = #{voiceSpeaker,jdbcType=VARCHAR},
			</if>
		    <if test="uploadFileStatus != null ">
		    	upload_file_status = #{uploadFileStatus,jdbcType=VARCHAR},
		    </if>
			update_time = now(),
			update_by = #{updateBy,jdbcType=VARCHAR}
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
	<select id="selectByCode" resultMap="BaseResultMap">
		select * from t_train_scene where scene_code = #{sceneCode} and is_delete = '0'limit 1
	</select>

	<select id="isExistSceneCode" resultType="java.lang.Integer">
		select count(1) from t_train_scene where scene_code = #{sceneCode} and is_delete = '0'
		<if test="id != null">
			and id != #{id}
		</if>
	</select>
</mapper>
