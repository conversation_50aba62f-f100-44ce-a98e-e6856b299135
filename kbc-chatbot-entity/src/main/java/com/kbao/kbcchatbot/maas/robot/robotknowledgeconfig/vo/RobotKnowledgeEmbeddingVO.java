package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import com.kbao.kbcchatbot.maas.chatsession.enums.ChatKnowledgeTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 向量化对象
 * @author: husw
 * @create: 2024-06-04 10:59
 **/
@Data
public class RobotKnowledgeEmbeddingVO {

    private List<Embedding> datas;

    @Data
    public static class Embedding{

        /**
         * 知识目录编码
         */
        private String directoryCode;

        /**
         * 知识ID
         */
        private String knowledgeId;
        /**
         * 机器人编码
         */
        private String robotCode;

        /**
         * 标题
         */
        private String title;
        /**
         * 全路径标题
         */
        private String fullTitle;
        /**
         * 章节内容
         */
        private List<Item> itemList;

        /**
         * 来源渠道 1-云知识库   2-好赔
         */
        private String source;

        /**
         * 知识原文
         */
        private String link;

    }

    @Data
    public static class Item{
        /**
         * 类型 0-文本 1-图文，2视频，3文件
         * @see ChatKnowledgeTypeEnum
         */
        private String type;
        /**
         * 章节标题
         */
        private String chapterTitle;
        /**
         * 样本数据
         */
        private String content;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 文件链接
         */
        private String fileUrl;
    }
}
