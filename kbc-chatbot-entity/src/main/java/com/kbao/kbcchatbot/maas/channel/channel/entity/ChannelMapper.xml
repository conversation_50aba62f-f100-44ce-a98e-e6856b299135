<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.channel.channel.dao.ChannelMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.channel.channel.entity.Channel">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="channelCode" jdbcType="VARCHAR"  column="channel_code" />  
        <result property="channelName" jdbcType="VARCHAR"  column="channel_name" />
		<result property="channelType" jdbcType="CHAR"  column="channel_type" />
		<result property="executeType" jdbcType="CHAR"  column="execute_type" />
		<result property="flowId" jdbcType="CHAR"  column="flow_id" />
        <result property="projectId" jdbcType="INTEGER"  column="project_id" />
        <result property="defaultAnswer" jdbcType="VARCHAR"  column="default_answer" />  
        <result property="finalWords" jdbcType="VARCHAR"  column="final_words" />  
        <result property="status" jdbcType="CHAR"  column="status" />  
        <result property="sensitiveWordsReply" jdbcType="VARCHAR"  column="sensitive_words_reply" />  
        <result property="noAnswerReply" jdbcType="VARCHAR"  column="no_answer_reply" />
		<result property="transferHumanStatus" jdbcType="CHAR"  column="transfer_human_status" />
		<result property="contextualizeQuestionStatus" jdbcType="CHAR"  column="contextualize_question_status" />
		<result property="uploadFileStatus" jdbcType="CHAR"  column="upload_file_status" />
		<result property="memoryCacheNum" jdbcType="INTEGER"  column="memory_cache_num" />
		<result property="voiceStatus" jdbcType="CHAR"  column="voice_status" />
		<result property="voiceSynthesis" jdbcType="CHAR"  column="voice_synthesis" />
		<result property="voiceSpeaker" jdbcType="VARCHAR"  column="voice_speaker" />
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateBy" jdbcType="VARCHAR"  column="update_by" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		channel_code,  
		channel_name,
		execute_type,
		flow_id,
		channel_type,
		project_id,  
		default_answer,  
		final_words,  
		status,  
		sensitive_words_reply,  
		no_answer_reply,
		transfer_human_status,
		contextualize_question_status,
		upload_file_status,
		memory_cache_num,
		voice_status,
		voice_synthesis,
		voice_speaker,
		create_time,
		create_by,  
		update_time,  
		update_by,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.channel_code, 
		t.channel_name,
		t.execute_type,
		t.flow_id,
		t.channel_type,
		t.project_id, 
		t.default_answer, 
		t.final_words, 
		t.status, 
		t.sensitive_words_reply, 
		t.no_answer_reply,
		t.transfer_human_status,
		t.contextualize_question_status,
		t.upload_file_status,
		t.memory_cache_num,
		t.voice_status,
		t.voice_synthesis,
		t.voice_speaker,
		t.create_time,
		t.create_by, 
		t.update_time, 
		t.update_by, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
		is_delete = '0'
	    <if test="channelCode != null and channelCode != ''">
	   		and t.channel_code = #{channelCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="channelName != null and channelName != ''">
	   		and t.channel_name = #{channelName,jdbcType=VARCHAR}  
	    </if>
	    <if test="channelType != null and channelType != ''">
	    	and t.channel_type = #{channelType,jdbcType=CHAR}
	    </if>
	    <if test="projectId != null">
	   		and t.project_id = #{projectId,jdbcType=INTEGER}  
	    </if>
	    <if test="defaultAnswer != null and defaultAnswer != ''">
	   		and t.default_answer = #{defaultAnswer,jdbcType=VARCHAR}  
	    </if>
	    <if test="finalWords != null and finalWords != ''">
	   		and t.final_words = #{finalWords,jdbcType=VARCHAR}  
	    </if>
	    <if test="status != null">
	   		and t.status = #{status,jdbcType=CHAR}  
	    </if>
	    <if test="sensitiveWordsReply != null and sensitiveWordsReply != ''">
	   		and t.sensitive_words_reply = #{sensitiveWordsReply,jdbcType=VARCHAR}  
	    </if>
	    <if test="noAnswerReply != null and noAnswerReply != ''">
	   		and t.no_answer_reply = #{noAnswerReply,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateBy != null and updateBy != ''">
	   		and t.update_by = #{updateBy,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_channel t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_channel t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_channel
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_channel set is_delete = '1'
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.channel.channel.entity.Channel">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_channel(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{channelCode,jdbcType=VARCHAR}, 
                 
                #{channelName,jdbcType=VARCHAR},

				#{executeType,jdbcType=CHAR},

				#{flowId,jdbcType=CHAR},

		       	#{channelType,jdbcType=CHAR},

                #{projectId,jdbcType=INTEGER}, 
                 
                #{defaultAnswer,jdbcType=VARCHAR}, 
                 
                #{finalWords,jdbcType=VARCHAR}, 
                 
                #{status,jdbcType=CHAR}, 
                 
                #{sensitiveWordsReply,jdbcType=VARCHAR}, 
                 
                #{noAnswerReply,jdbcType=VARCHAR},

				#{transferHumanStatus,jdbcType=VARCHAR},

				#{contextualizeQuestionStatus,jdbcType=VARCHAR},

		       	#{uploadFileStatus,jdbcType=VARCHAR},

		       	#{memoryCacheNum,jdbcType=INTEGER},

		       	#{voiceStatus,jdbcType=CHAR},
		       	#{voiceSynthesis,jdbcType=CHAR},
		       	#{voiceSpeaker,jdbcType=VARCHAR},

                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{createBy,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{updateBy,jdbcType=VARCHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.channel.channel.entity.Channel">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_channel
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="channelCode != null ">  
	       		channel_code,
	        </if>  
	        <if test="channelName != null ">  
	       		channel_name,
	        </if>
			<if test="executeType != null ">
				execute_type,
			</if>
			<if test="flowId != null ">
				flow_id,
			</if>
			<if test="channelType != null ">
				channel_type,
			</if>
		    <if test="channelType != null ">
		    	channel_type,
		    </if>
	        <if test="projectId != null ">  
	       		project_id,
	        </if>  
	        <if test="defaultAnswer != null ">  
	       		default_answer,
	        </if>  
	        <if test="finalWords != null ">  
	       		final_words,
	        </if>  
	        <if test="status != null ">  
	       		status,
	        </if>  
	        <if test="sensitiveWordsReply != null ">  
	       		sensitive_words_reply,
	        </if>  
	        <if test="noAnswerReply != null ">  
	       		no_answer_reply,
	        </if>
			<if test="transferHumanStatus != null ">
				transfer_human_status,
			</if>
			<if test="contextualizeQuestionStatus != null ">
				contextualize_question_status,
			</if>
		    <if test="uploadFileStatus != null ">
		    	upload_file_status,
			</if>
		    <if test="memoryCacheNum != null ">
		    	memory_cache_num,
		    </if>
			<if test="voiceStatus != null ">
				voice_status,
			</if>
			<if test="voiceSynthesis != null ">
				voice_synthesis,
			</if>
			<if test="voiceSpeaker != null ">
				voice_speaker,
			</if>
			<if test="createTime != null ">
	       		create_time,
	        </if>  
	        <if test="createBy != null ">  
	       		create_by,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="updateBy != null ">  
	       		update_by,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="channelCode != null">  
            	#{channelCode,jdbcType=VARCHAR},
            </if>  
            <if test="channelName != null">  
            	#{channelName,jdbcType=VARCHAR},
            </if>
			<if test="executeType != null">
				#{executeType,jdbcType=CHAR},
			</if>
			<if test="flowId != null">
				#{flowId,jdbcType=CHAR},
			</if>
		    <if test="channelType != null">
		    	#{channelType,jdbcType=CHAR},
		    </if>
            <if test="projectId != null">  
            	#{projectId,jdbcType=INTEGER},
            </if>  
            <if test="defaultAnswer != null">  
            	#{defaultAnswer,jdbcType=VARCHAR},
            </if>  
            <if test="finalWords != null">  
            	#{finalWords,jdbcType=VARCHAR},
            </if>  
            <if test="status != null">  
            	#{status,jdbcType=CHAR},
            </if>  
            <if test="sensitiveWordsReply != null">  
            	#{sensitiveWordsReply,jdbcType=VARCHAR},
            </if>  
            <if test="noAnswerReply != null">  
            	#{noAnswerReply,jdbcType=VARCHAR},
            </if>
			<if test="transferHumanStatus != null ">
				#{transferHumanStatus,jdbcType=VARCHAR},
			</if>
			<if test="contextualizeQuestionStatus != null ">
				#{contextualizeQuestionStatus,jdbcType=VARCHAR},
			</if>
		    <if test="uploadFileStatus != null ">
		    	#{uploadFileStatus,jdbcType=VARCHAR},
		    </if>
		    <if test="memoryCacheNum != null ">
		    	#{memoryCacheNum,jdbcType=INTEGER},
		    </if>
			<if test="voiceStatus != null ">
				#{voiceStatus,jdbcType=CHAR},
			</if>
			<if test="voiceSynthesis != null ">
				#{voiceSynthesis,jdbcType=CHAR},
			</if>
			<if test="voiceSpeaker != null ">
				#{voiceSpeaker,jdbcType=VARCHAR},
			</if>
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="createBy != null">  
            	#{createBy,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateBy != null">  
            	#{updateBy,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.channel.channel.entity.Channel">
		update t_channel
		<set>
	        <if test="channelCode != null ">  
	        	channel_code = #{channelCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="channelName != null ">  
	        	channel_name = #{channelName,jdbcType=VARCHAR},  
	        </if>
			<if test="executeType != null ">
				execute_type = #{executeType,jdbcType=CHAR},
			</if>
			<if test="flowId != null ">
				flow_id = #{flowId,jdbcType=CHAR},
			</if>
		    <if test="channelType != null ">
		    	channel_type = #{channelType,jdbcType=CHAR},
		    </if>
	        <if test="projectId != null ">  
	        	project_id = #{projectId,jdbcType=INTEGER},  
	        </if>  
	        <if test="defaultAnswer != null ">  
	        	default_answer = #{defaultAnswer,jdbcType=VARCHAR},  
	        </if>  
	        <if test="finalWords != null ">  
	        	final_words = #{finalWords,jdbcType=VARCHAR},  
	        </if>  
	        <if test="status != null ">  
	        	status = #{status,jdbcType=CHAR},  
	        </if>  
	        <if test="sensitiveWordsReply != null ">  
	        	sensitive_words_reply = #{sensitiveWordsReply,jdbcType=VARCHAR},  
	        </if>  
	        <if test="noAnswerReply != null ">  
	        	no_answer_reply = #{noAnswerReply,jdbcType=VARCHAR},  
	        </if>
			<if test="transferHumanStatus != null ">
				transfer_human_status = #{transferHumanStatus,jdbcType=VARCHAR},
			</if>
			<if test="contextualizeQuestionStatus != null ">
				contextualize_question_status = #{contextualizeQuestionStatus,jdbcType=VARCHAR},
			</if>
		    <if test="uploadFileStatus != null ">
		    	upload_file_status = #{uploadFileStatus,jdbcType=VARCHAR},
		    </if>
		    <if test="memoryCacheNum != null ">
		    	memory_cache_num = #{memoryCacheNum,jdbcType=INTEGER},
		    </if>
			<if test="voiceStatus != null ">
				voice_status = #{voiceStatus,jdbcType=CHAR},
			</if>
			<if test="voiceSynthesis != null ">
				voice_synthesis = #{voiceSynthesis,jdbcType=CHAR},
			</if>
			<if test="voiceSpeaker != null ">
				voice_speaker = #{voiceSpeaker,jdbcType=VARCHAR},
			</if>
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createBy != null ">  
	        	create_by = #{createBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateBy != null ">  
	        	update_by = #{updateBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_channel where id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>

	<!-- 自定义查询 -->
	<select id="isExistChannel" resultType="int">
		select count(1)
		from t_channel
		where channel_code = #{channelCode,jdbcType=VARCHAR}
		<if test="id != null">
			and id != #{id,jdbcType=INTEGER}
		</if>
		and is_delete = '0'
	</select>

	<select id="getChannelList" resultType="com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelPageRespVO">
		select t.id channelId,t.channel_code channelCode,t.channel_name channelName,t.project_id projectId,b.remark remark,
		       p.name projectName,t.status status,t.create_time createTime,t.update_time updateTime,t.update_by updateBy,
		       t.execute_type executeType, t.flow_id flowId, f.flow_name flowName
		from t_channel t
		left join t_project p on t.project_id = p.id
		left join t_channel_basic_config b on t.channel_code = b.channel_code
		left join t_flow f on t.flow_id = f.flow_id
		<where>
			t.is_delete = '0'
			<if test="channelCode != null and channelCode != ''">
				and t.channel_code = #{channelCode,jdbcType=VARCHAR}
			</if>
			<if test="status != null and status != ''">
				and t.status = #{status,jdbcType=VARCHAR}
			</if>
			<if test="name != null and name != ''">
				and t.channel_name like concat("%",#{name},"%")
			</if>
			<if test="tenantId != null and tenantId != ''">
				and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
			</if>
		</where>
	</select>

	<select id="selectByChannelCode" resultType="com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelUpdateVO" parameterType="java.lang.String">
		select t.id channelId,t.project_id projectId,t.channel_name channelName,t.channel_code channelCode,t.default_answer defaultAnswer
			,t.final_words finalWords,t.status status,t.sensitive_words_reply sensitiveWordsReply,t.no_answer_reply noAnswerReply
			,t.transfer_human_status transferHumanStatus, t.contextualize_question_status contextualizeQuestionStatus, t.memory_cache_num memoryCacheNum
		    ,t.upload_file_status uploadFileStatus, t.channel_type channelType ,t.voice_status voiceStatus,t.voice_synthesis voiceSynthesis,t.voice_speaker voiceSpeaker,b.pass_id passId
			,b.welcome_reply welcomeReply,b.input_place_holder inputPlaceHolder,b.common_card_status commonCardStatus,b.answer_evaluate_status answerEvaluateStatus
			,b.common_phrase_status commonPhraseStatus,b.guess_question_status guessQuestionStatus,b.remark remark,t.execute_type executeType, t.flow_id flowId
		from t_channel t
		left join t_channel_basic_config b on t.channel_code = b.channel_code
		left join t_flow f on t.flow_id = f.flow_id
		where t.channel_code = #{channelCode,jdbcType=VARCHAR}
		and t.is_delete = '0'
	</select>

	<select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_channel
		where channel_code = #{channelCode,jdbcType=VARCHAR}
		and is_delete = '0'
	</select>

</mapper>
