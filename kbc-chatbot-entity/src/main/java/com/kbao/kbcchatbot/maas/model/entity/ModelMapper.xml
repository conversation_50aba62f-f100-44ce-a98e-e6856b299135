<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.model.dao.ModelMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.model.entity.Model">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="modelName" jdbcType="VARCHAR"  column="model_name" />  
        <result property="platform" jdbcType="VARCHAR"  column="platform" />
        <result property="modelType" jdbcType="CHAR"  column="model_type" />  
        <result property="modelSetting" jdbcType="VARCHAR"  column="model_setting" />  
        <result property="modelDesc" jdbcType="VARCHAR"  column="model_desc" />  
        <result property="isDelete" jdbcType="TINYINT"  column="is_delete" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateBy" jdbcType="VARCHAR"  column="update_by" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		model_name,  
		platform,
		model_type,  
		model_setting,  
		model_desc,  
		is_delete,  
		create_time,  
		create_by,  
		update_time,  
		update_by,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.model_name, 
		t.platform,
		t.model_type, 
		t.model_setting, 
		t.model_desc, 
		t.is_delete, 
		t.create_time, 
		t.create_by, 
		t.update_time, 
		t.update_by, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="modelName != null and modelName != ''">
	   		and instr(t.model_name, #{modelName,jdbcType=VARCHAR}) > 0
	    </if>
	    <if test="platform != null and platform != ''">
	   		and t.platform = #{platform,jdbcType=VARCHAR}
	    </if>
	    <if test="modelType != null">
	   		and t.model_type = #{modelType,jdbcType=CHAR}
	    </if>
	    <if test="modelSetting != null and modelSetting != ''">
	   		and t.model_setting = #{modelSetting,jdbcType=VARCHAR}
	    </if>
	    <if test="modelDesc != null and modelDesc != ''">
	   		and t.model_desc = #{modelDesc,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateBy != null and updateBy != ''">
	   		and t.update_by = #{updateBy,jdbcType=VARCHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
			and t.is_delete = 0
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_model t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_model
		where  id = #{id,jdbcType=INTEGER} and is_delete = 0
	</select>


	<!-- 根据主键删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_model set is_delete = 1, update_time = now()
		where id = #{id,jdbcType=INTEGER}
	</update>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.model.entity.Model">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_model(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                #{modelName,jdbcType=VARCHAR},
                #{platform,jdbcType=VARCHAR},
                #{modelType,jdbcType=CHAR},
                #{modelSetting,jdbcType=VARCHAR},
                #{modelDesc,jdbcType=VARCHAR},
                0,
                now(),
                #{createBy,jdbcType=VARCHAR},
                now(),
				#{createBy,jdbcType=VARCHAR},
				#{tenantId,jdbcType=VARCHAR}
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.model.entity.Model">
		update t_model
		<set>
	        <if test="modelName != null ">  
	        	model_name = #{modelName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="platform != null ">
	        	platform = #{platform,jdbcType=VARCHAR},  
	        </if>  
	        <if test="modelType != null ">  
	        	model_type = #{modelType,jdbcType=CHAR},  
	        </if>  
	        <if test="modelSetting != null ">  
	        	model_setting = #{modelSetting,jdbcType=VARCHAR},  
	        </if>  
	        <if test="modelDesc != null ">  
	        	model_desc = #{modelDesc,jdbcType=VARCHAR},  
	        </if>
	        <if test="updateBy != null ">
	        	update_by = #{updateBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>
			update_time = now()
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<select id="getModelList" resultMap="BaseResultMap">
		select m.id, m.model_name, m.model_type, m.platform
		from t_model m
		where m.is_delete = 0
		order by m.model_type, m.model_name
	</select>

	<select id="getModelPage" resultType="com.kbao.kbcchatbot.maas.model.bean.ModelPageResVo">
		select * from (
			  select m.id, m.model_name modelName, m.model_type modelType, m.platform,
			         m.update_by updateBy, m.update_time updateTime,
					 if(s.id is null, '0', '1') as isCommonModel
			  from t_model m left join t_common_setting s on m.model_name = s.value and s.code = 'default_common_llm'
			  where m.is_delete = 0
				<if test="modelName != null and modelName != ''">
					and instr(m.model_name, #{modelName,jdbcType=VARCHAR}) > 0
				</if>
				<if test="platform != null and platform != ''">
					and m.platform = #{platform,jdbcType=VARCHAR}
				</if>
		  ) t
		order by isCommonModel desc, updateTime desc
	</select>
</mapper>
