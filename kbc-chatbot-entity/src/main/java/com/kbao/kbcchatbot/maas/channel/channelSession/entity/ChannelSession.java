package com.kbao.kbcchatbot.maas.channel.channelSession.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-05-22
*/
@Data
public class ChannelSession implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中session_id
     * sessionId
     */  
	private String sessionId;

    /**
     * 对应表中user_id
     * 用户ID
     */  
	private String userId;

    /**
     * 对应表中channel_code
     * 渠道编码
     */  
	private String channelCode;

    /**
     * 对应表中title
     * 标题
     */  
	private String title;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中is_delete
     * 是否删除，0-否，1-是
     */  
	private String isDelete;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   