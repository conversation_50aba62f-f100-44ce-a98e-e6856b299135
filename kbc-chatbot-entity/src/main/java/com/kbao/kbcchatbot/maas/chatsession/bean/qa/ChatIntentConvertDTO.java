package com.kbao.kbcchatbot.maas.chatsession.bean.qa;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description: 意图转换
 * @author: husw
 * @create: 2024-01-24 09:56
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatIntentConvertDTO {

    private String question;

    private Map<String, Object> slots;
}
