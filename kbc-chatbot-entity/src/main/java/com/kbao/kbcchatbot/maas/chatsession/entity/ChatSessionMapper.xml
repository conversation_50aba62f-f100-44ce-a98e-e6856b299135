<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.chatsession.dao.ChatSessionMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.chatsession.entity.ChatSession">
    	<id column="id" jdbcType="BIGINT"  property="id"  />
    	<result column="session_id" jdbcType="VARCHAR"  property="sessionId"  />
        <result property="robotId" jdbcType="BIGINT"  column="robot_id" />
        <result property="robotCode" jdbcType="VARCHAR"  column="robot_code" />
        <result property="robertVersion" jdbcType="VARCHAR"  column="robert_version" />
        <result property="channelId" jdbcType="BIGINT"  column="channel_id" />
        <result property="channelCode" jdbcType="VARCHAR"  column="channel_code" />
        <result property="userId" jdbcType="VARCHAR"  column="user_id" />
        <result property="userName" jdbcType="VARCHAR"  column="user_name" />  
        <result property="totalRound" jdbcType="SMALLINT"  column="total_round" />  
        <result property="knowledgeRound" jdbcType="SMALLINT"  column="knowledge_round" />  
        <result property="chatRound" jdbcType="SMALLINT"  column="chat_round" />  
        <result property="manMachineRound" jdbcType="SMALLINT"  column="man_machine_round" />
        <result property="productAnalyzeRound" jdbcType="SMALLINT"  column="product_analyze_round" />
        <result property="transformRound" jdbcType="SMALLINT"  column="transform_round" />
        <result property="resolveCount" jdbcType="SMALLINT"  column="resolve_count" />  
        <result property="upVoteRoundCount" jdbcType="SMALLINT"  column="up_vote_round_count" />  
        <result property="downVoteRoundCount" jdbcType="SMALLINT"  column="down_vote_round_count" />  
        <result property="resolveStatus" jdbcType="VARCHAR"  column="resolve_status" />  
        <result property="startTime" jdbcType="VARCHAR"  column="start_time" />  
        <result property="endTime" jdbcType="VARCHAR"  column="end_time" />  
        <result property="updateStatus" jdbcType="INTEGER"  column="update_status" />
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />
	</resultMap>

	<sql id="Base_Column_List">
		id,
		session_id,  
		robot_id,
		robot_code,
		robert_version,  
		channel_id,
		channel_code,
		user_id,  
		user_name,  
		total_round,  
		knowledge_round,  
		chat_round,  
		transform_round,  
		resolve_count,  
		up_vote_round_count,  
		down_vote_round_count,  
		resolve_status,  
		start_time,  
		end_time,  
		update_status,
		tenant_id
	</sql>

	<sql id="Alias_Column_List">
		t.id,
		t.session_id,
		t.robot_id,
		t.robot_code,
		t.robert_version, 
		t.channel_id,
		t.channel_code,
		t.user_id, 
		t.user_name, 
		t.total_round, 
		t.knowledge_round, 
		t.chat_round, 
		t.transform_round, 
		t.resolve_count, 
		t.up_vote_round_count, 
		t.down_vote_round_count, 
		t.resolve_status, 
		t.start_time, 
		t.end_time, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="sessionId != null and sessionId != ''">
	   		and t.session_id = #{sessionId,jdbcType=VARCHAR}
	    </if>
	    <if test="robotId != null">
	   		and t.robot_id = #{robotId,jdbcType=BIGINT}
	    </if>
	    <if test="robotCode != null and robotCode != ''">
	   		and t.robot_code = #{robotCode,jdbcType=VARCHAR}
	    </if>
	    <if test="robertVersion != null and robertVersion != ''">
	   		and t.robert_version = #{robertVersion,jdbcType=VARCHAR}
	    </if>
	    <if test="channelId != null">
	   		and t.channel_id = #{channelId,jdbcType=BIGINT}
	    </if>
		<if test="channelCode != null and channelCode != ''">
			and t.channel_code = #{channelCode,jdbcType=VARCHAR}
		</if>
	    <if test="userId != null and userId != ''">
	   		and t.user_id = #{userId,jdbcType=VARCHAR}  
	    </if>
	    <if test="userName != null and userName != ''">
	   		and t.user_name = #{userName,jdbcType=VARCHAR}  
	    </if>
	    <if test="totalRound != null">
	   		and t.total_round = #{totalRound,jdbcType=SMALLINT}  
	    </if>
	    <if test="knowledgeRound != null">
	   		and t.knowledge_round = #{knowledgeRound,jdbcType=SMALLINT}  
	    </if>
	    <if test="chatRound != null">
	   		and t.chat_round = #{chatRound,jdbcType=SMALLINT}  
	    </if>
	    <if test="transformRound != null">
	   		and t.transform_round = #{transformRound,jdbcType=SMALLINT}  
	    </if>
	    <if test="resolveCount != null">
	   		and t.resolve_count = #{resolveCount,jdbcType=SMALLINT}  
	    </if>
	    <if test="upVoteRoundCount != null">
	   		and t.up_vote_round_count = #{upVoteRoundCount,jdbcType=SMALLINT}  
	    </if>
	    <if test="downVoteRoundCount != null">
	   		and t.down_vote_round_count = #{downVoteRoundCount,jdbcType=SMALLINT}  
	    </if>
	    <if test="resolveStatus != null and resolveStatus != ''">
	   		and t.resolve_status = #{resolveStatus,jdbcType=VARCHAR}  
	    </if>
	    <if test="startTime != null and startTime != ''">
	   		and t.start_time = #{startTime,jdbcType=VARCHAR}  
	    </if>
	    <if test="endTime != null and endTime != ''">
	   		and t.end_time = #{endTime,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		<if test="endTimeEqNull != null and endTimeEqNull != ''">
			and t.end_time is null
		</if>
		<if test="startDate != null and startDate != ''">
			and DATE_FORMAT(t.start_time,'%Y-%m-%d') >= #{startDate,jdbcType=VARCHAR}
		</if>
		<if test="endDate != null and endDate != ''">
			and DATE_FORMAT(t.start_time,'%Y-%m-%d') &lt;= #{endDate,jdbcType=VARCHAR}
		</if>
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_chat_session t
		<include refid="Base_Condition" />
	</select>
	<!--查询待更新会话-->
	<select id="selectUpdateSession" resultMap="BaseResultMap">
		SELECT <include refid="Alias_Column_List" />
		FROM t_chat_session t
		where t.update_status = 0
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_chat_session t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_chat_session
		where  id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 查询客户当天会话-->
	<select id="selectByUserIdCurrentDay" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_chat_session
		where session_id = #{sessionId,jdbcType=VARCHAR}
		and channel_code = #{channelCode,jdbcType=VARCHAR}
		and robot_code = #{robotCode,jdbcType=VARCHAR}
		and DATE(start_time) = CURDATE()
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from t_chat_session
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<!-- 查询客户当天会话-->
	<update id="updateStatusCurrentDay" parameterType="java.lang.String">
		update  t_chat_session
		set update_status = 0
		where session_id = #{sessionId,jdbcType=VARCHAR}
		and channel_code = #{channelCode,jdbcType=VARCHAR}
		and robot_code = #{robotCode,jdbcType=VARCHAR}
		and DATE(start_time) = CURDATE()
	</update>
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.chatsession.entity.ChatSession">
		insert into t_chat_session(
			<include refid="Base_Column_List" />
		)
		values(
		       #{id,jdbcType=BIGINT},
                #{sessionId,jdbcType=VARCHAR}, 
                 
                #{robotId,jdbcType=BIGINT},

		       	#{robotCode,jdbcType=VARCHAR},
                 
                #{robertVersion,jdbcType=VARCHAR}, 
                 
                #{channelId,jdbcType=BIGINT},

				#{channelCode,jdbcType=VARCHAR},

                #{userId,jdbcType=VARCHAR},
                 
                #{userName,jdbcType=VARCHAR}, 
                 
                #{totalRound,jdbcType=SMALLINT}, 
                 
                #{knowledgeRound,jdbcType=SMALLINT}, 
                 
                #{chatRound,jdbcType=SMALLINT}, 
                 
                #{transformRound,jdbcType=SMALLINT}, 
                 
                #{resolveCount,jdbcType=SMALLINT}, 
                 
                #{upVoteRoundCount,jdbcType=SMALLINT}, 
                 
                #{downVoteRoundCount,jdbcType=SMALLINT}, 
                 
                #{resolveStatus,jdbcType=VARCHAR}, 
                 
                #{startTime,jdbcType=VARCHAR}, 
                 
                #{endTime,jdbcType=VARCHAR},

				#{updateStatus,jdbcType=INTEGER},

				#{tenantId,jdbcType=VARCHAR}
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.chatsession.entity.ChatSession">
		insert into t_chat_session
		<trim prefix="(" suffix=")" suffixOverrides=",">
		    <if test="id != null">
	       		id,
	        </if>
	        <if test="sessionId != null ">  
	       		session_id,
	        </if>  
	        <if test="robotId != null ">  
	       		robot_id,
	        </if>
		    <if test="robotCode != null ">
		    	robot_code,
		    </if>
	        <if test="robertVersion != null ">  
	       		robert_version,
	        </if>  
	        <if test="channelId != null ">  
	       		channel_id,
	        </if>
			<if test="channelCode != null ">
				channel_code,
			</if>
			<if test="userId != null ">
	       		user_id,
	        </if>  
	        <if test="userName != null ">  
	       		user_name,
	        </if>  
	        <if test="totalRound != null ">  
	       		total_round,
	        </if>  
	        <if test="knowledgeRound != null ">  
	       		knowledge_round,
	        </if>  
	        <if test="chatRound != null ">  
	       		chat_round,
	        </if>  
	        <if test="transformRound != null ">  
	       		transform_round,
	        </if>  
	        <if test="resolveCount != null ">  
	       		resolve_count,
	        </if>  
	        <if test="upVoteRoundCount != null ">  
	       		up_vote_round_count,
	        </if>  
	        <if test="downVoteRoundCount != null ">  
	       		down_vote_round_count,
	        </if>  
	        <if test="resolveStatus != null ">  
	       		resolve_status,
	        </if>  
	        <if test="startTime != null ">  
	       		start_time,
	        </if>  
	        <if test="endTime != null ">  
	       		end_time,
	        </if>
			<if test="updateStatus != null ">
				update_status,
			</if>
			<if test="tenantId != null ">
	       		tenant_id,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
		    <if test="id != null">
            	#{id,jdbcType=BIGINT},
            </if>
            <if test="sessionId != null">  
            	#{sessionId,jdbcType=VARCHAR},
            </if>  
            <if test="robotId != null">  
            	#{robotId,jdbcType=BIGINT},
            </if>
			<if test="robotCode != null">
				#{robotCode,jdbcType=VARCHAR},
			</if>
			<if test="robertVersion != null">
            	#{robertVersion,jdbcType=VARCHAR},
            </if>  
            <if test="channelId != null">  
            	#{channelId,jdbcType=BIGINT},
            </if>
			<if test="channelCode != null">
				#{channelCode,jdbcType=VARCHAR},
			</if>
			<if test="userId != null">
            	#{userId,jdbcType=VARCHAR},
            </if>  
            <if test="userName != null">  
            	#{userName,jdbcType=VARCHAR},
            </if>  
            <if test="totalRound != null">  
            	#{totalRound,jdbcType=SMALLINT},
            </if>  
            <if test="knowledgeRound != null">  
            	#{knowledgeRound,jdbcType=SMALLINT},
            </if>  
            <if test="chatRound != null">  
            	#{chatRound,jdbcType=SMALLINT},
            </if>  
            <if test="transformRound != null">  
            	#{transformRound,jdbcType=SMALLINT},
            </if>  
            <if test="resolveCount != null">  
            	#{resolveCount,jdbcType=SMALLINT},
            </if>  
            <if test="upVoteRoundCount != null">  
            	#{upVoteRoundCount,jdbcType=SMALLINT},
            </if>  
            <if test="downVoteRoundCount != null">  
            	#{downVoteRoundCount,jdbcType=SMALLINT},
            </if>  
            <if test="resolveStatus != null">  
            	#{resolveStatus,jdbcType=VARCHAR},
            </if>  
            <if test="startTime != null">  
            	#{startTime,jdbcType=VARCHAR},
            </if>  
            <if test="endTime != null">  
            	#{endTime,jdbcType=VARCHAR},
            </if>
		    <if test="updateStatus != null">
		    	#{updateStatus,jdbcType=INTEGER},
		    </if>
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.chatsession.entity.ChatSession">
		update t_chat_session
		<set>
		    <if test="id != null">
	        	id = #{id,jdbcType=BIGINT},
	        </if>
	        <if test="robotId != null ">  
	        	robot_id = #{robotId,jdbcType=BIGINT},
	        </if>
			<if test="robotCode != null ">
				robot_code = #{robotCode,jdbcType=VARCHAR},
			</if>
			<if test="robertVersion != null ">
	        	robert_version = #{robertVersion,jdbcType=VARCHAR},  
	        </if>  
	        <if test="channelId != null ">  
	        	channel_id = #{channelId,jdbcType=BIGINT},
	        </if>
			<if test="channelCode != null ">
				channel_code = #{channelCode,jdbcType=VARCHAR},
			</if>
	        <if test="userId != null ">  
	        	user_id = #{userId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="userName != null ">  
	        	user_name = #{userName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="totalRound != null ">  
	        	total_round = #{totalRound,jdbcType=SMALLINT},  
	        </if>  
	        <if test="knowledgeRound != null ">  
	        	knowledge_round = #{knowledgeRound,jdbcType=SMALLINT},  
	        </if>  
	        <if test="chatRound != null ">  
	        	chat_round = #{chatRound,jdbcType=SMALLINT},  
	        </if>
			<if test="manMachineRound != null ">
				man_machine_round = #{manMachineRound,jdbcType=SMALLINT},
			</if>
			<if test="productAnalyzeRound != null ">
				product_analyze_round = #{productAnalyzeRound,jdbcType=SMALLINT},
			</if>
			<if test="transformRound != null ">
	        	transform_round = #{transformRound,jdbcType=SMALLINT},  
	        </if>  
	        <if test="resolveCount != null ">  
	        	resolve_count = #{resolveCount,jdbcType=SMALLINT},  
	        </if>  
	        <if test="upVoteRoundCount != null ">  
	        	up_vote_round_count = #{upVoteRoundCount,jdbcType=SMALLINT},  
	        </if>  
	        <if test="downVoteRoundCount != null ">  
	        	down_vote_round_count = #{downVoteRoundCount,jdbcType=SMALLINT},  
	        </if>  
	        <if test="resolveStatus != null ">  
	        	resolve_status = #{resolveStatus,jdbcType=VARCHAR},  
	        </if>  
	        <if test="startTime != null ">  
	        	start_time = #{startTime,jdbcType=VARCHAR},  
	        </if>  
	        <if test="endTime != null ">  
	        	end_time = #{endTime,jdbcType=VARCHAR},  
	        </if>
		    <if test="updateStatus != null">
		    	update_status = #{updateStatus,jdbcType=INTEGER},
		    </if>
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.chatsession.entity.ChatSession">
		update t_chat_session
		set
           robot_id = #{robotId,jdbcType=BIGINT},
		   robot_code = #{robotCode,jdbcType=VARCHAR},
           robert_version = #{robertVersion,jdbcType=VARCHAR},
           channel_id = #{channelId,jdbcType=BIGINT},
           channel_code = #{channelCode,jdbcType=VARCHAR},
           user_id = #{userId,jdbcType=VARCHAR},
           user_name = #{userName,jdbcType=VARCHAR},
           total_round = #{totalRound,jdbcType=SMALLINT},
           knowledge_round = #{knowledgeRound,jdbcType=SMALLINT},
           chat_round = #{chatRound,jdbcType=SMALLINT},
           transform_round = #{transformRound,jdbcType=SMALLINT},
           resolve_count = #{resolveCount,jdbcType=SMALLINT},
           up_vote_round_count = #{upVoteRoundCount,jdbcType=SMALLINT},
           down_vote_round_count = #{downVoteRoundCount,jdbcType=SMALLINT},
           resolve_status = #{resolveStatus,jdbcType=VARCHAR},
           start_time = #{startTime,jdbcType=VARCHAR},
           end_time = #{endTime,jdbcType=VARCHAR},
           update_status = #{updateStatus,jdbcType=INTEGER},
           tenant_id = #{tenantId,jdbcType=VARCHAR}
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_chat_session(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.sessionId}
	        <choose>
	            <when test="item.robotId != null">,#{item.robotId}</when><otherwise>,default</otherwise>
	        </choose>
			<choose>
				<when test="item.robotCode != null">,#{item.robotCode}</when><otherwise>,default</otherwise>
			</choose>
	        <choose>
	            <when test="item.robertVersion != null">,#{item.robertVersion}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise>
	        </choose>
			<choose>
				<when test="item.channelCode != null">,#{item.channelCode}</when><otherwise>,default</otherwise>
			</choose>
	        <choose>
	            <when test="item.userId != null">,#{item.userId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.userName != null">,#{item.userName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.totalRound != null">,#{item.totalRound}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.knowledgeRound != null">,#{item.knowledgeRound}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.chatRound != null">,#{item.chatRound}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.transformRound != null">,#{item.transformRound}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.resolveCount != null">,#{item.resolveCount}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.upVoteRoundCount != null">,#{item.upVoteRoundCount}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.downVoteRoundCount != null">,#{item.downVoteRoundCount}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.resolveStatus != null">,#{item.resolveStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.startTime != null">,#{item.startTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.endTime != null">,#{item.endTime}</when><otherwise>,default</otherwise>
	        </choose>
			<choose>
	            <when test="item.updateStatus != null">,#{item.updateStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_chat_session(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.sessionId}
			<choose><when test="item.robotId != null">,#{item.robotId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.robotCode != null">,#{item.robotCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.robertVersion != null">,#{item.robertVersion}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.channelCode != null">,#{item.channelCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.userId != null">,#{item.userId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.userName != null">,#{item.userName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.totalRound != null">,#{item.totalRound}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.knowledgeRound != null">,#{item.knowledgeRound}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.chatRound != null">,#{item.chatRound}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.transformRound != null">,#{item.transformRound}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.resolveCount != null">,#{item.resolveCount}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.upVoteRoundCount != null">,#{item.upVoteRoundCount}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.downVoteRoundCount != null">,#{item.downVoteRoundCount}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.resolveStatus != null">,#{item.resolveStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.startTime != null">,#{item.startTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.endTime != null">,#{item.endTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateStatus != null">,#{item.updateStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   robot_id=values(robot_id),
		   robot_code=values(robot_code),
		   robert_version=values(robert_version), 
		   channel_id=values(channel_id),
		   channel_code=values(channel_code),
		   user_id=values(user_id), 
		   user_name=values(user_name), 
		   total_round=values(total_round), 
		   knowledge_round=values(knowledge_round), 
		   chat_round=values(chat_round), 
		   transform_round=values(transform_round), 
		   resolve_count=values(resolve_count), 
		   up_vote_round_count=values(up_vote_round_count), 
		   down_vote_round_count=values(down_vote_round_count), 
		   resolve_status=values(resolve_status), 
		   start_time=values(start_time), 
		   end_time=values(end_time),
		   update_status=values(update_status),
		   tenant_id=values(tenant_id) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_chat_session where session_id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>
	
	<!-- 自定义查询 -->
	<!--统计接待明细-->
	<select id="selectDataBoard" resultType="com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotReceptionRespVO" parameterType="java.util.HashMap">
		select
		DATE_FORMAT(t.start_time,'%Y-%m-%d') date,
		count(distinct t.session_id) receptionNum,
		sum(t.total_round) totalRound,
		sum(t.knowledge_round) knowledgeRound,
		sum(t.chat_round) chatRound,
		sum(t.man_machine_round) manMachineRound,
		sum(t.product_analyze_round) productAnalyzeRound,
		sum(t.transform_round) transformCount,
		sum(t.resolve_count) resolveNum,
		sum(t.up_vote_round_count) upVoteRoundCount,
		sum(t.down_vote_round_count) downVoteRoundCount
		from t_chat_session t
		<include refid="Base_Condition"/>
		group by DATE_FORMAT(t.start_time,'%Y-%m-%d')
		order by date desc
	</select>
</mapper>
