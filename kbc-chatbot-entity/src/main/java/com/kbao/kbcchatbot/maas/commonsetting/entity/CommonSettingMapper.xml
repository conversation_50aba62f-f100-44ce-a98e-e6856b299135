<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.commonsetting.dao.CommonSettingMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.commonsetting.entity.CommonSetting">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="code" jdbcType="VARCHAR"  column="code" />  
        <result property="value" jdbcType="VARCHAR"  column="value" />  
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="updateBy" jdbcType="VARCHAR"  column="update_by" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		code,  
		value,  
		create_by,  
		create_time,  
		update_by,  
		update_time,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.code, 
		t.value, 
		t.create_by, 
		t.create_time, 
		t.update_by, 
		t.update_time, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="code != null and code != ''">
	   		and t.code = #{code,jdbcType=VARCHAR}
	    </if>
	    <if test="value != null and value != ''">
	   		and t.value = #{value,jdbcType=VARCHAR}
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateBy != null and updateBy != ''">
	   		and t.update_by = #{updateBy,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_common_setting t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_common_setting
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_common_setting
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.commonsetting.entity.CommonSetting">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_common_setting(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{code,jdbcType=VARCHAR}, 
                 
                #{value,jdbcType=VARCHAR}, 
                 
                #{createBy,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{updateBy,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.commonsetting.entity.CommonSetting">
		update t_common_setting
		<set>
	        <if test="code != null ">  
	        	code = #{code,jdbcType=VARCHAR},  
	        </if>  
	        <if test="value != null ">  
	        	value = #{value,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createBy != null ">  
	        	create_by = #{createBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateBy != null ">  
	        	update_by = #{updateBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
	<insert id="saveSetting" parameterType="com.kbao.kbcchatbot.maas.commonsetting.entity.CommonSetting">
		insert into t_common_setting (code,value,create_by,create_time,update_by,update_time,tenant_id)
		values
			(#{code}, #{value}, #{updateBy}, now(), #{updateBy}, now(), #{tenantId})
		ON DUPLICATE KEY UPDATE
							 value =#{value,jdbcType=VARCHAR},
							 update_by=#{updateBy},
							 update_time=now()
	</insert>

	<select id="getSettingValue" resultType="string">
		select value from t_common_setting where code = #{code}
	</select>
</mapper>
