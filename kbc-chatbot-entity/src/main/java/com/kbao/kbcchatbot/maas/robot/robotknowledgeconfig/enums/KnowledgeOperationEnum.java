package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums;

import lombok.Getter;

/**
 * @program: kbc-chatbot
 * @description: 测试版操作记录
 * @author: husw
 * @create: 2023-06-02 15:47
 **/
@Getter
public enum KnowledgeOperationEnum {

    INIT(0,"初始化"),
    BIND(1,"绑定"),
    UNBIND(2,"解绑"),
    REFRESH(3,"同步");

    private Integer code;

    private String value;

    KnowledgeOperationEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }
}
