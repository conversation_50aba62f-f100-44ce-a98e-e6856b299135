package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @program: kbc-chatbot
 * @description: 刷新
 * @author: husw
 * @create: 2023-05-17 11:26
 **/
@Data
public class RobotKnowledgeConfigRefreshReqVO {
    private Long robotId;

    @NotNull(message = "机器人CODE不能为空！")
    private String robotCode;
     /**
     * 知识目录名称
     */
    private String directoryName;

    /**
     * 知识目录ID
     */
    @NotNull(message = "知识目录ID不能为空！")
    private String directoryId;
}
