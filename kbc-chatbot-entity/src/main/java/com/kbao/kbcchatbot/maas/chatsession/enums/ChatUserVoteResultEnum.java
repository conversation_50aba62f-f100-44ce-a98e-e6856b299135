package com.kbao.kbcchatbot.maas.chatsession.enums;

public enum ChatUserVoteResultEnum {

    VOTE_GOOD("0","点踩"),

    VOTE_BAD("1","点赞"),
    TRANSFER_LABOR("2","转人工");

    private String key;

    private String value;

    ChatUserVoteResultEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String findNameByKey(String key) {
        for (ChatUserVoteResultEnum e : ChatUserVoteResultEnum.values()) {
            if (e.getKey().equals(key)) {
                return e.getValue();
            }
        }
        return null;
    }

    public String getKey() {
        return this.key;
    }

    public String getValue() {
        return this.value;
    }

}
