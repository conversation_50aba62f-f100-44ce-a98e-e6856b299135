package com.kbao.kbcchatbot.maas.chatsession.bean;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 聊天会话缓存
 * @Date 2023-6-12
 */
@Data
public class ChatSessionCache implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 直接回复阈值
     */
    private BigDecimal directReplyThreshold;

    /**
     * 澄清回复阈值
     */
    private BigDecimal clearReplyThreshold;

    /**
     * 澄清最大条数
     */
    private Integer clearMaxCount;

    /**
     * 联想最大条数
     */
    private Integer associateMaxCount;

    /**
     * 问题澄清文案
     */
    private String clearContent;

    /**
     * 无回复文案
     */
    private String noAnswerContent;

    /**
     * 敏感词文案
     */
    private String sensitiveWordsReply;

    /**
     * 连续无回复多少次后转人工
     */
    private Integer noReplyTransferTimes;

    /**
     * 澄清重复多少次转人工
     */
    private Integer clearTransferTimes;

    /**
     * 用户重复提问多少次转人工
     */
    private Integer sameQuestionTransferTimes;

    /**
     * 客服平台接入方式 1-网页跳转 2-系统集成
     */
    private Integer custServPlatAccessType;

    /**
     * 客服平台跳转地址
     */
    private String custServPlatAddr;

    /**
     * 关键词
     */
    private List<String> keywords;

    /**
     * 关键词
     */
    private List<String> sensitiveWords;

}
