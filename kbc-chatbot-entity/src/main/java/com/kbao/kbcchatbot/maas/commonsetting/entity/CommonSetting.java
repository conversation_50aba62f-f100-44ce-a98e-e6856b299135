package com.kbao.kbcchatbot.maas.commonsetting.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-01-09
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonSetting implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中code
     * code
     */  
	private String code;

    /**
     * 对应表中value
     * 值
     */  
	private String value;

    /**
     * 对应表中create_by
     * 创建人
     */  
	private String createBy;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中update_by
     * 更新人
     */  
	private String updateBy;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   