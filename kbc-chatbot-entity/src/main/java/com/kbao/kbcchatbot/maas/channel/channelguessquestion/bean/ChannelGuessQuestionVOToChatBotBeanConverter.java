package com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean;

import com.kbao.kbcchatbot.common.bean.ChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.entity.ChannelGuessQuestion;
import org.springframework.beans.BeanUtils;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> qiuzb
 * @Description: VO转Mapper类
 * @create 2023/6/15 14:15
 */
public class ChannelGuessQuestionVOToChatBotBeanConverter implements ChatBotBeanConverter<ChannelGuessQuestionVO, ChannelGuessQuestion> {
    @Override
    public ChannelGuessQuestion convert(ChannelGuessQuestionVO source) {
        ChannelGuessQuestion target = new ChannelGuessQuestion();
        // 复制源对象的属性到目标对象
        BeanUtils.copyProperties(source, target);
        // 把源对象的content属性从String类型转换成byte[]类型，并设置到目标对象
        if (source.getContent() != null && !source.getContent().isEmpty()){
            target.setContent(source.getContent().getBytes(StandardCharsets.UTF_8));
        }
        // 返回目标对象
        return target;
    }
}
