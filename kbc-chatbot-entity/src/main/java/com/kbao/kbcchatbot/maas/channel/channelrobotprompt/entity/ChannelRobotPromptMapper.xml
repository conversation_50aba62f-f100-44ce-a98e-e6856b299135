<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.channel.channelrobotprompt.dao.ChannelRobotPromptMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.channel.channelrobotprompt.entity.ChannelRobotPrompt">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="channelRobotId" jdbcType="INTEGER"  column="channel_robot_id" />  
        <result property="promptType" jdbcType="CHAR"  column="prompt_type" />  
        <result property="prompt" jdbcType="VARCHAR"  column="prompt" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateBy" jdbcType="VARCHAR"  column="update_by" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		channel_robot_id,  
		prompt_type,  
		prompt,  
		update_time,  
		update_by
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.channel_robot_id, 
		t.prompt_type, 
		t.prompt, 
		t.update_time, 
		t.update_by
	</sql>

	<sql id="Base_Condition">
		<where>
		and t.is_delete = '0'
	    <if test="channelRobotId != null">
	   		and t.channel_robot_id = #{channelRobotId,jdbcType=INTEGER}  
	    </if>
	    <if test="promptType != null">
	   		and t.prompt_type = #{promptType,jdbcType=CHAR}  
	    </if>
	    <if test="prompt != null and prompt != ''">
	   		and t.prompt = #{prompt,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateBy != null and updateBy != ''">
	   		and t.update_by = #{updateBy,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_channel_robot_prompt t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_channel_robot_prompt t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_channel_robot_prompt
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_channel_robot_prompt set is_delete = '1'
		where id = #{id,jdbcType=INTEGER}
	</update>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.channel.channelrobotprompt.entity.ChannelRobotPrompt">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_channel_robot_prompt(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{channelRobotId,jdbcType=INTEGER}, 
                 
                #{promptType,jdbcType=CHAR}, 
                 
                #{prompt,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{updateBy,jdbcType=VARCHAR}
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelrobotprompt.entity.ChannelRobotPrompt">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_channel_robot_prompt
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="channelRobotId != null ">  
	       		channel_robot_id,
	        </if>  
	        <if test="promptType != null ">  
	       		prompt_type,
	        </if>  
	        <if test="prompt != null ">  
	       		prompt,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="updateBy != null ">  
	       		update_by,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="channelRobotId != null">  
            	#{channelRobotId,jdbcType=INTEGER},
            </if>  
            <if test="promptType != null">  
            	#{promptType,jdbcType=CHAR},
            </if>  
            <if test="prompt != null">  
            	#{prompt,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateBy != null">  
            	#{updateBy,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelrobotprompt.entity.ChannelRobotPrompt">
		update t_channel_robot_prompt
		<set>
	        <if test="channelRobotId != null ">  
	        	channel_robot_id = #{channelRobotId,jdbcType=INTEGER},  
	        </if>  
	        <if test="promptType != null ">  
	        	prompt_type = #{promptType,jdbcType=CHAR},  
	        </if>  
	        <if test="prompt != null ">  
	        	prompt = #{prompt,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateBy != null ">  
	        	update_by = #{updateBy,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.channel.channelrobotprompt.entity.ChannelRobotPrompt">
		update t_channel_robot_prompt
		set
           channel_robot_id = #{channelRobotId,jdbcType=INTEGER},
           prompt_type = #{promptType,jdbcType=CHAR},
           prompt = #{prompt,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           update_by = #{updateBy,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_channel_robot_prompt(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.channelRobotId != null">,#{item.channelRobotId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.promptType != null">,#{item.promptType}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.prompt != null">,#{item.prompt}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateBy != null">,#{item.updateBy}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_channel_robot_prompt(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.channelRobotId != null">,#{item.channelRobotId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.promptType != null">,#{item.promptType}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.prompt != null">,#{item.prompt}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateBy != null">,#{item.updateBy}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   channel_robot_id=values(channel_robot_id), 
		   prompt_type=values(prompt_type), 
		   prompt=values(prompt), 
		   update_time=values(update_time), 
		   update_by=values(update_by)
	</update>

	<!-- 自定义查询 -->
	<select id="isExistChannelRobotPrompt" resultType="int">
		select count(1)
		from t_channel_robot_prompt t
		where channel_robot_id = #{channelRobotId,jdbcType=INTEGER} and prompt_type = #{promptType,jdbcType=CHAR}
		<if test="id != null">
			and id != #{id,jdbcType=INTEGER}
		</if>
		and t.is_delete = '0'
	</select>

	<select id="getChannelRobotPromptList" resultMap="BaseResultMap">
		select
		<include refid="Alias_Column_List" />
		from t_channel_robot_prompt t
		<where>
			and t.is_delete = '0'
			<if test="channelRobotId != null">
				and t.channel_robot_id = #{channelRobotId}
			</if>
			<if test="prompt != null and prompt != ''">
				and t.prompt like concat("%",#{prompt},"%")
			</if>
			<if test="promptType != null and promptType != ''">
				and t.prompt_type = #{promptType,jdbcType=CHAR}
			</if>
		</where>
	</select>

	<delete id="delByChannelRobotId">
		delete from t_channel_robot_prompt
		       where channel_robot_id = #{channelRobotId,jdbcType=INTEGER}
	</delete>
</mapper>
