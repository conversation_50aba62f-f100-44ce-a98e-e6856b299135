package com.kbao.kbcchatbot.maas.mcp.appserver.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 对应表 t_mcp_app_server
 * MCP应用服务器表
 */
@Data
public class McpAppServer implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应表中app_server_id
     * 应用服务器编号（自增长主键）
     */
    private Long appServerId;

    /**
     * 对应表中app_server_code
     * 应用服务器编码（序列生成，唯一标识）
     */
    private String appServerCode;

    /**
     * 对应表中app_server_name
     * 应用服务器名称
     */
    @NotNull(message = "appServerName不能为空")
    private String appServerName;

    /**
     * 对应表中domain_url
     * 域名地址
     */
    @NotNull(message = "domainUrl不能为空")
    private String domainUrl;

    /**
     * 对应表中app_server_status
     * 应用服务器状态 1启用，0禁用
     */
    private Integer appServerStatus;

    /**
     * 对应表中description
     * 应用服务器描述
     */
    private String description;

    /**
     * 对应表中tenant_id
     * 租户编号
     */
    private String tenantId;

    /**
     * 对应表中create_time
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 对应表中create_id
     * 创建人编号
     */
    private String createId;

    /**
     * 对应表中update_id
     * 更新人编号
     */
    private String updateId;

    /**
     * 对应表中update_time
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 对应表中is_deleted
     * 是否删除 0 未删除  1已删除
     */
    private Integer isDeleted;

}