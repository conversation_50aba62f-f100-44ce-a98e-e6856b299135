<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.channel.channelbasicconfig.dao.ChannelBasicConfigMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig">
    	<id column="id" jdbcType="BIGINT"  property="id"  />
        <result property="channelId" jdbcType="BIGINT"  column="channel_id" />
        <result property="channelCode" jdbcType="VARCHAR"  column="channel_code" />
        <result property="passId" jdbcType="VARCHAR"  column="pass_id" />
        <result property="welcomeReply" jdbcType="VARCHAR"  column="welcome_reply" />
        <result property="inputPlaceHolder" jdbcType="VARCHAR"  column="input_place_holder" />  
        <result property="guessQuestionStatus" jdbcType="VARCHAR"  column="guess_question_status" />  
        <result property="commonCardStatus" jdbcType="VARCHAR"  column="common_card_status" />  
        <result property="commonPhraseStatus" jdbcType="VARCHAR"  column="common_phrase_status" />  
        <result property="answerEvaluateStatus" jdbcType="VARCHAR"  column="answer_evaluate_status" />
		<result property="custServPlatAccessType" jdbcType="INTEGER" column="cust_serv_plat_access_type"/>
		<result property="custServPlatAddr" jdbcType="VARCHAR" column="cust_serv_plat_addr"/>
        <result property="remark" jdbcType="VARCHAR"  column="remark" />
        <result property="updateTime" jdbcType="VARCHAR"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		channel_id,
		channel_code,
		pass_id,
		welcome_reply,
		input_place_holder,  
		guess_question_status,  
		common_card_status,  
		common_phrase_status,  
		answer_evaluate_status,
		cust_serv_plat_access_type,
		cust_serv_plat_addr,
		remark,
		update_time,  
		update_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.channel_id,
		t.channel_code,
		t.pass_id,
		t.welcome_reply,
		t.input_place_holder, 
		t.guess_question_status, 
		t.common_card_status, 
		t.common_phrase_status, 
		t.answer_evaluate_status,
		t.cust_serv_plat_access_type,
		t.cust_serv_plat_addr,
		t.remark,
		t.update_time, 
		t.update_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="channelId != null">
	   		and t.channel_id = #{channelId,jdbcType=BIGINT}
	    </if>
	    <if test="channelCode != null and channelCode != ''">
	   		and t.channel_code = #{channelCode,jdbcType=VARCHAR}
	    </if>
	    <if test="welcomeReply != null and welcomeReply != ''">
	   		and t.welcome_reply = #{welcomeReply,jdbcType=VARCHAR}  
	    </if>
	    <if test="inputPlaceHolder != null and inputPlaceHolder != ''">
	   		and t.input_place_holder = #{inputPlaceHolder,jdbcType=VARCHAR}  
	    </if>
	    <if test="guessQuestionStatus != null and guessQuestionStatus != ''">
	   		and t.guess_question_status = #{guessQuestionStatus,jdbcType=VARCHAR}  
	    </if>
	    <if test="commonCardStatus != null and commonCardStatus != ''">
	   		and t.common_card_status = #{commonCardStatus,jdbcType=VARCHAR}  
	    </if>
	    <if test="commonPhraseStatus != null and commonPhraseStatus != ''">
	   		and t.common_phrase_status = #{commonPhraseStatus,jdbcType=VARCHAR}  
	    </if>
	    <if test="answerEvaluateStatus != null and answerEvaluateStatus != ''">
	   		and t.answer_evaluate_status = #{answerEvaluateStatus,jdbcType=VARCHAR}  
	    </if>
		<if test="custServPlatAccessType != null">
			and t.cust_serv_plat_access_type = #{custServPlatAccessType,jdbcType=INTEGER}
		</if>
		<if test="custServPlatAddr != null and custServPlatAddr != ''">
			and t.cust_serv_plat_addr = #{custServPlatAddr,jdbcType=VARCHAR}
		</if>
	    <if test="remark != null and remark != ''">
	   		and t.remark = #{remark,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null and updateTime != ''">
	   		and t.update_time = #{updateTime,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_channel_basic_config t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Long" parameterType="java.util.HashMap">
		select count(0)
		from t_channel_basic_config t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_channel_basic_config
		where  id = #{id,jdbcType=BIGINT}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from t_channel_basic_config
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig">
		insert into t_channel_basic_config(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=BIGINT},
                 
                #{channelId,jdbcType=BIGINT},

                #{channelCode,jdbcType=VARCHAR},

				#{passId,jdbcType=VARCHAR},
                 
                #{welcomeReply,jdbcType=VARCHAR}, 
                 
                #{inputPlaceHolder,jdbcType=VARCHAR}, 
                 
                #{guessQuestionStatus,jdbcType=VARCHAR}, 
                 
                #{commonCardStatus,jdbcType=VARCHAR}, 
                 
                #{commonPhraseStatus,jdbcType=VARCHAR}, 
                 
                #{answerEvaluateStatus,jdbcType=VARCHAR},

				#{custServPlatAccessType,jdbcType=INTEGER},

				#{custServPlatAddr,jdbcType=VARCHAR},

				#{remark,jdbcType=VARCHAR},
                 
                #{updateTime,jdbcType=VARCHAR}, 
                 
                #{updateId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig">
		<selectKey resultType="Long" order="AFTER" keyProperty="id">
			SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_channel_basic_config
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="channelId != null ">  
	       		channel_id,
	        </if>
	        <if test="channelCode != null ">
	       		channel_code,
	        </if>
			<if test="passId != null ">
				pass_id,
			</if>
			<if test="welcomeReply != null ">
	       		welcome_reply,
	        </if>  
	        <if test="inputPlaceHolder != null ">  
	       		input_place_holder,
	        </if>  
	        <if test="guessQuestionStatus != null ">  
	       		guess_question_status,
	        </if>  
	        <if test="commonCardStatus != null ">  
	       		common_card_status,
	        </if>  
	        <if test="commonPhraseStatus != null ">  
	       		common_phrase_status,
	        </if>  
	        <if test="answerEvaluateStatus != null ">  
	       		answer_evaluate_status,
	        </if>
			<if test="custServPlatAccessType != null ">
				cust_serv_plat_access_type,
			</if>
			<if test="custServPlatAddr != null ">
				cust_serv_plat_addr,
			</if>
			<if test="remark != null ">
	       		remark,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=BIGINT},
            </if>  
            <if test="channelId != null">  
            	#{channelId,jdbcType=BIGINT},
            </if>
            <if test="channelCode != null">
            	#{channelCode,jdbcType=VARCHAR},
            </if>
			<if test="passId != null">
				#{passId,jdbcType=VARCHAR},
			</if>
            <if test="welcomeReply != null">  
            	#{welcomeReply,jdbcType=VARCHAR},
            </if>  
            <if test="inputPlaceHolder != null">  
            	#{inputPlaceHolder,jdbcType=VARCHAR},
            </if>  
            <if test="guessQuestionStatus != null">  
            	#{guessQuestionStatus,jdbcType=VARCHAR},
            </if>  
            <if test="commonCardStatus != null">  
            	#{commonCardStatus,jdbcType=VARCHAR},
            </if>  
            <if test="commonPhraseStatus != null">  
            	#{commonPhraseStatus,jdbcType=VARCHAR},
            </if>  
            <if test="answerEvaluateStatus != null">  
            	#{answerEvaluateStatus,jdbcType=VARCHAR},
            </if>
			<if test="custServPlatAccessType != null">
				#{custServPlatAccessType,jdbcType=INTEGER},
			</if>
			<if test="custServPlatAddr != null">
				#{custServPlatAddr,jdbcType=VARCHAR},
			</if>
            <if test="remark != null">
            	#{remark,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=VARCHAR},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig">
		update t_channel_basic_config
		<set>
	        <if test="channelId != null ">  
	        	channel_id = #{channelId,jdbcType=BIGINT},
	        </if>
	        <if test="channelCode != null ">
	        	channel_code = #{channelCode,jdbcType=VARCHAR},
	        </if>
			<if test="passId != null ">
				pass_id = #{passId,jdbcType=BIGINT},
			</if>
			<if test="welcomeReply != null ">
	        	welcome_reply = #{welcomeReply,jdbcType=VARCHAR},  
	        </if>  
	        <if test="inputPlaceHolder != null ">  
	        	input_place_holder = #{inputPlaceHolder,jdbcType=VARCHAR},  
	        </if>  
	        <if test="guessQuestionStatus != null ">  
	        	guess_question_status = #{guessQuestionStatus,jdbcType=VARCHAR},  
	        </if>  
	        <if test="commonCardStatus != null ">  
	        	common_card_status = #{commonCardStatus,jdbcType=VARCHAR},  
	        </if>  
	        <if test="commonPhraseStatus != null ">  
	        	common_phrase_status = #{commonPhraseStatus,jdbcType=VARCHAR},  
	        </if>  
	        <if test="answerEvaluateStatus != null ">  
	        	answer_evaluate_status = #{answerEvaluateStatus,jdbcType=VARCHAR},  
	        </if>
			<if test="custServPlatAccessType != null ">
				cust_serv_plat_access_type = #{custServPlatAccessType,jdbcType=INTEGER},
			</if>
			<if test="custServPlatAddr != null ">
				cust_serv_plat_addr = #{custServPlatAddr,jdbcType=VARCHAR},
			</if>
	        <if test="remark != null ">
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig">
		update t_channel_basic_config
		set
           channel_id = #{channelId,jdbcType=BIGINT},
           channel_code = #{channelCode,jdbcType=VARCHAR},
           pass_id = #{passId,jdbcType=VARCHAR},
           welcome_reply = #{welcomeReply,jdbcType=VARCHAR},
           input_place_holder = #{inputPlaceHolder,jdbcType=VARCHAR},
           guess_question_status = #{guessQuestionStatus,jdbcType=VARCHAR},
           common_card_status = #{commonCardStatus,jdbcType=VARCHAR},
           common_phrase_status = #{commonPhraseStatus,jdbcType=VARCHAR},
           answer_evaluate_status = #{answerEvaluateStatus,jdbcType=VARCHAR},
		   cust_serv_plat_access_type = #{custServPlatAccessType,jdbcType=INTEGER},
		   cust_serv_plat_addr = #{custServPlatAddr,jdbcType=VARCHAR},
           remark = #{remark,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=VARCHAR},
           update_id = #{updateId,jdbcType=VARCHAR}
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_channel_basic_config(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.channelCode != null">,#{item.channelCode}</when><otherwise>,default</otherwise>
	        </choose>
			<choose>
				<when test="item.passId != null">,#{item.passId}</when><otherwise>,default</otherwise>
			</choose>
	        <choose>
	            <when test="item.welcomeReply != null">,#{item.welcomeReply}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.inputPlaceHolder != null">,#{item.inputPlaceHolder}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.guessQuestionStatus != null">,#{item.guessQuestionStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.commonCardStatus != null">,#{item.commonCardStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.commonPhraseStatus != null">,#{item.commonPhraseStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.answerEvaluateStatus != null">,#{item.answerEvaluateStatus}</when><otherwise>,default</otherwise>
	        </choose>
            <choose>
                <when test="item.custServPlatAccessType != null">,#{item.custServPlatAccessType}</when><otherwise>,default</otherwise>
            </choose>
            <choose>
                <when test="item.custServPlatAddr != null">,#{item.custServPlatAddr}</when><otherwise>,default</otherwise>
            </choose>
	        <choose>
	            <when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_channel_basic_config(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.channelCode != null">,#{item.channelCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.passId != null">,#{item.passId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.welcomeReply != null">,#{item.welcomeReply}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.inputPlaceHolder != null">,#{item.inputPlaceHolder}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.guessQuestionStatus != null">,#{item.guessQuestionStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.commonCardStatus != null">,#{item.commonCardStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.commonPhraseStatus != null">,#{item.commonPhraseStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.answerEvaluateStatus != null">,#{item.answerEvaluateStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.custServPlatAccessType != null">,#{item.custServPlatAccessType}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.custServPlatAddr != null">,#{item.custServPlatAddr}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   channel_id=values(channel_id), 
		   channel_code=values(channel_code),
		   pass_id=values(pass_id),
		   welcome_reply=values(welcome_reply),
		   input_place_holder=values(input_place_holder), 
		   guess_question_status=values(guess_question_status), 
		   common_card_status=values(common_card_status), 
		   common_phrase_status=values(common_phrase_status), 
		   answer_evaluate_status=values(answer_evaluate_status),
		   cust_serv_plat_access_type=values(cust_serv_plat_access_type),
		   cust_serv_plat_addr=values(cust_serv_plat_addr),
		   remark=values(remark),
		   update_time=values(update_time), 
		   update_id=values(update_id) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_channel_basic_config where id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>

    <!-- 自定义查询 -->

<!--根据渠道ids获取渠道基础配置List-->
	<select id="selectBatchByPrimaryKey" parameterType="java.util.List" resultMap="BaseResultMap">
		SELECT id,channel_id
		FROM t_channel_basic_config
		WHERE channel_id IN
		<foreach item="item" index="index" collection="channelIds" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectByChannelCode" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_channel_basic_config
		where channel_code = #{channelCode,jdbcType=VARCHAR}
	</select>
</mapper>
