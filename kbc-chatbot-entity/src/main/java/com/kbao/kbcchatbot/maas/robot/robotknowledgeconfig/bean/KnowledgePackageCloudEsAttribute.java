package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.bean;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @program: kbc-chatbot
 * @description: 知识包QA内容索引属性
 * @author: husw
 * @create: 2023-05-26 13:58
 **/
@Data
@Component
@RefreshScope
public class KnowledgePackageCloudEsAttribute {

    @Value("${es.indexName.knowledgePackageCloud:sta-kbcs-search_knowledge_cloud_latest}")
    private String indexName;
}
