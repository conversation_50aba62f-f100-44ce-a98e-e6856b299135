package com.kbao.kbcchatbot.maas.chatsession.bean.qa;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ChatKnowledgeAnswerVoteReqVO {

    /**
     * 聊天会话记录ID
     */
    @NotBlank(message = "recordId不能为空")
    private String recordId;

    /**
     * 反馈结果 0-点踩 1-点赞 2-转人工
     */
    private String result;

    /**
     * 点踩原因
     */
    private String remark;

}
