<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.recordShareLog.dao.RecordShareLogMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.recordShareLog.entity.RecordShareLog">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
		<result property="shareId" jdbcType="VARCHAR"  column="share_id" />
        <result property="userId" jdbcType="VARCHAR"  column="user_id" />  
        <result property="userName" jdbcType="VARCHAR"  column="user_name" />  
        <result property="headImage" jdbcType="VARCHAR"  column="head_image" />  
        <result property="recordId" jdbcType="VARCHAR"  column="record_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,
		share_id,
		user_id,  
		user_name,  
		head_image,  
		record_id,  
		create_time,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id,
		t.share_id,
		t.user_id, 
		t.user_name, 
		t.head_image, 
		t.record_id, 
		t.create_time, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="userId != null and userId != ''">
	   		and t.user_id = #{userId,jdbcType=VARCHAR}
	    </if>
	    <if test="userName != null and userName != ''">
	   		and t.user_name = #{userName,jdbcType=VARCHAR}
	    </if>
	    <if test="headImage != null and headImage != ''">
	   		and t.head_image = #{headImage,jdbcType=VARCHAR}
	    </if>
	    <if test="recordId != null and recordId != ''">
	   		and t.record_id = #{recordId,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_record_share_log t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_record_share_log
		where  share_id = #{shareId,jdbcType=VARCHAR}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from t_record_share_log
		where share_id = #{shareId,jdbcType=VARCHAR}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.recordShareLog.entity.RecordShareLog">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_record_share_log(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER},

		       	#{shareId,jdbcType=VARCHAR},
                 
                #{userId,jdbcType=VARCHAR}, 
                 
                #{userName,jdbcType=VARCHAR}, 
                 
                #{headImage,jdbcType=VARCHAR}, 
                 
                #{recordId,jdbcType=VARCHAR}, 
                 
                now(),
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.recordShareLog.entity.RecordShareLog">
		update t_record_share_log
		<set>
	        <if test="userId != null ">  
	        	user_id = #{userId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="userName != null ">  
	        	user_name = #{userName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="headImage != null ">  
	        	head_image = #{headImage,jdbcType=VARCHAR},  
	        </if>  
	        <if test="recordId != null ">  
	        	record_id = #{recordId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
</mapper>
