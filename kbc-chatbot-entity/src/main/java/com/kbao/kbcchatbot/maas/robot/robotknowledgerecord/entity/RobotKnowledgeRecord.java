package com.kbao.kbcchatbot.maas.robot.robotknowledgerecord.entity;

import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums.KnowledgeOperationEnum;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * @program: kbc-chatbot
 * @description: 只是配置记录
 * @author: husw
 * @create: 2023-05-17 15:34
 **/
@Data
@Document(collection = "RobotKnowledgeRecord")
public class RobotKnowledgeRecord {
    @Id
    private String id;

    private Long robotId;

    private String robotCode;

    /**
     * 知识目录名称
     */
    private String directoryName;
    /**
     * 一级知识目录ID
     */
    private String firstDirectId;

    /**
     * 一级知识目录名称
     */
    private String firstDirectName;

    /**
     * 知识目录ID
     */
    private String directoryId;
    /**
     * 项目
     */
    private String projectId;
    /**
     * 项目
     */
    private String projectName;
    /**
     * 文件夹ID
     */
    private String folderId;
    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 类型 1-绑定 2-解绑 3-刷新
     * @see KnowledgeOperationEnum
     */
    private Integer type;

    /**
     * 绑定人
     */
    private String bindId;

    /**
     * 绑定时间
     */
    private Date bindTime;

    private String operateStatus;
}
