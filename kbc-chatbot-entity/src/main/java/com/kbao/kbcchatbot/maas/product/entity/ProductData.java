package com.kbao.kbcchatbot.maas.product.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-04-24
*/
@Data
public class ProductData implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中product_id
     * 产品ID
     */  
	private String productId;

    /**
     * 对应表中product_name
     * 产品名称
     */  
	private String productName;

    /**
     * 对应表中content_length
     * 知识内容长度
     */  
	private Integer contentLength;

    /**
     * 对应表中status
     * 状态:0-上架，1-下架，2-废弃
     */  
	private String status;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中list_show
     * 列表是否显示 0-不显示 1-显示
     */  
	private Integer listShow;

    /**
     * 对应表中for_sale
     * 销售状态 0-停售 1-可售 2-预售 3-维护
     */  
	private Integer forSale;

    /**
     * 对应表中online
     * 电投状态 0-未开启 1-开启
     */  
	private Integer online;

    /**
     * 对应表中product_status
     * 上架状态 0-下架 1-上架
     */  
	private Integer productStatus;

    /**
     * 对应表中insurance_category
     * 险种分类
     */  
	private String insuranceCategory;

    /**
     * 对应表中primary_insurance_name
     * 险种类型
     */  
	private String primaryInsuranceName;

    /**
     * 对应表中secondary_insurance_name
     * 二级险种类型
     */  
	private String secondaryInsuranceName;

    /**
     * 对应表中sync_status
     * 同步状态:0-同步中，1-同步成功，2-同步失败
     */  
	private String syncStatus;

}   