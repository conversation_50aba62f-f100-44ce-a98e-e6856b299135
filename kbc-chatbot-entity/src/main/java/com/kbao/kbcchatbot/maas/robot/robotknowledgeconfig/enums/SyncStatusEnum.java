package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums;

import lombok.Getter;

/**
 * @program: kbc-chatbot
 * @description: 知识同步状态
 * @author: husw
 * @create: 2023-06-06 14:45
 **/
@Getter
public enum SyncStatusEnum {

    SYNCING(1,"执行中"),
    SUCCESS(2,"已完成"),
    FAIL(3,"同步失败");

    private Integer code;

    private String value;

    SyncStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

}
