package com.kbao.kbcchatbot.maas.chatsession.enums;

/**
 * @Description 聊天回复类型枚举
 * <AUTHOR>
 * @Date 2023-6-2
 */
public enum ChatContentTypeEnum {

    JUMP("1","跳转页面"),
    CONTENT("2","发送文本"),
    REL("3","关联知识库");

    private String key;

    private String value;

    ChatContentTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return this.key;
    }

    public String getValue() {
        return this.value;
    }

}
