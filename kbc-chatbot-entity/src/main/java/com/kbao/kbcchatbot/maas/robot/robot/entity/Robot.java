package com.kbao.kbcchatbot.maas.robot.robot.entity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR>
* @Description maas机器人实体
* @Date 2024-12-20
*/
@Data
public class Robot implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中robot_code
     * 机器人编码
     */  
	private String robotCode;

    /**
     * 对应表中robot_name
     * 机器人名称
     */  
	private String robotName;

    /**
     * 对应表中robot_type
     * 机器人类型: 1-闲聊，2-云知识库问答，3-产品检索
     */  
	private String robotType;

    /**
     * 对应表中similarity_threshold
     * 相似度阈值
     */  
	private BigDecimal similarityThreshold;

    /**
     * 对应表中retrieval_threshold
     * 检索量阈值
     */  
	private Integer retrievalThreshold;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_by
     * 创建人
     */  
	private String createBy;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_by
     * 更新人
     */  
	private String updateBy;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   