package com.kbao.kbcchatbot.maas.channel.channelrobot.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR>
* @Description maas渠道机器人关联表实体
* @Date 2024-12-17
*/
@Data
public class ChannelRobot implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中channel_id
     * 渠道id
     */  
	private Integer channelId;

    /**
     * 对应表中robot_id
     * 机器人id
     */  
	private Integer robotId;

    /**
     * 对应表中status
     * 状态:0-启用，1-禁用
     */  
	private String status;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_by
     * 创建人
     */  
	private String createBy;

}   