package com.kbao.kbcchatbot.maas.model.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-01-08
*/
@Data
public class Model implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中model_name
     * 模型名称
     */  
	private String modelName;

    /**
     * 对应表中platform
     * 所属平台
     */  
	private String platform;

    /**
     * 对应表中model_type
     * 1-短文本模型，2-长文本模型
     */  
	private String modelType;

    /**
     * 对应表中model_setting
     * 模型配置
     */  
	private String modelSetting;

    /**
     * 对应表中model_desc
     * 模型描述
     */  
	private String modelDesc;

    /**
     * 对应表中is_delete
     * 0-正常，1-删除
     */  
	private Integer isDelete;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_by
     * 创建人
     */  
	private String createBy;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_by
     * 更新人
     */  
	private String updateBy;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   