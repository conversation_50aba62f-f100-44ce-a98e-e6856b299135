package com.kbao.kbcchatbot.maas.flow.entity;

import lombok.Data;

import java.util.Date;

/**
 * 流程配置表
 * @TableName t_flow
 */
@Data
public class Flow {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 流程ID
     */
    private String flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 流程类型:0-workflow，1-chatflow
     */
    private String flowType;

    /**
     * api秘钥
     */
    private String apiKey;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;
}