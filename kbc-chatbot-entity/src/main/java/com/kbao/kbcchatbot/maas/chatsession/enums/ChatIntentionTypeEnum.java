package com.kbao.kbcchatbot.maas.chatsession.enums;

public enum ChatIntentionTypeEnum {

    QUERY_PRODUCT("query_product","产品咨询"),
    RECOMMEND_PRODUCT("recommend_product","产品推荐"),
    COMPARE_PRODUCT("compare_product","产品对比"),
    RECOMMEND_SOLUTION("recommend_solution","方案推荐"),
    EMOTION_EXPRESSION("emotion_expression","情感表达"),
    TRANSFER_TO_HUMAN("transfer_to_human","转人工"),
    OTHER("other","其它");

    private String key;

    private String value;

    ChatIntentionTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String findNameByKey(String key) {
        for (ChatIntentionTypeEnum e : ChatIntentionTypeEnum.values()) {
            if (e.getKey().equals(key)) {
                return e.getValue();
            }
        }
        return null;
    }

    public String getKey() {
        return this.key;
    }

    public String getValue() {
        return this.value;
    }

}
