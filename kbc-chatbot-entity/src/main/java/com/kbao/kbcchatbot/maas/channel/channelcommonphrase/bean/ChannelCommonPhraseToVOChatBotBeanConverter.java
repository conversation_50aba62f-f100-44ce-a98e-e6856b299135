package com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean;

import com.kbao.kbcchatbot.common.bean.ChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.entity.ChannelCommonPhrase;
import org.springframework.beans.BeanUtils;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> qiuzb
 * @Description: 转换器类
 * @create 2023/6/15 11:45
 */
public class ChannelCommonPhraseToVOChatBotBeanConverter implements ChatBotBeanConverter<ChannelCommonPhrase, ChannelCommonPhraseVO> {

    /**
     * 定义一个转换器类，用来把ChannelCommonPhraseVO对象转换成ChannelCommonPhrase对象
     * 这个类实现了Converter接口，并指定了泛型参数为ChannelCommonPhraseVO和ChannelCommonPhrase
     * 这个类重写了convert方法，并实现了具体的转换逻辑
     *
     * @param source 源对象
     * @return 目标对象
     */
    @Override
    public ChannelCommonPhraseVO convert(ChannelCommonPhrase source) {
        // 创建一个ChannelCommonPhraseVO对象
        ChannelCommonPhraseVO target = new ChannelCommonPhraseVO();
        // 复制源对象的属性到目标对象
        BeanUtils.copyProperties(source, target);
        // 把源对象的content属性从byte[]类型转换成String类型，并设置到目标对象
        if (source.getContent() != null){
            target.setContent(new String(source.getContent(), StandardCharsets.UTF_8));
        }
        // 返回目标对象
        return target;
    }

}
