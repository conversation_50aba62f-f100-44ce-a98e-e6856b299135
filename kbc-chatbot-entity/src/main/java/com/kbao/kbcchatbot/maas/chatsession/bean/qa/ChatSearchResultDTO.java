package com.kbao.kbcchatbot.maas.chatsession.bean.qa;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ChatSearchResultDTO {

    private String qaId;

    private String knowledgeId;

    private String secondDirectId;

    private String relatedArticles;

    private String qaSource;

    private String answer;

    private List<String> similarQuestions;

    private String question;
    /**
     * 匹配度
     */
    private BigDecimal matchPercent;

}
