package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: kbc-chatbot
 * @description: 更新知识
 * @author: husw
 * @create: 2024-09-05 16:25
 **/
@Data
public class RobotKnowledgeUpdateReqVO {
    /**
     * 机器人ID
     */
    private Long robotId;
    /**
     * 机器人CODE
     */
    @NotNull(message = "机器人CODE不能为空")
    private String robotCode;
    /**
     * 知识ID
     */
    @NotBlank(message = "知识ID不能为空")
    private String knowledgeId;
    /**
     * 目录编码
     */
    @NotBlank(message = "目录编码不能为空")
    private String directoryCode;
}
