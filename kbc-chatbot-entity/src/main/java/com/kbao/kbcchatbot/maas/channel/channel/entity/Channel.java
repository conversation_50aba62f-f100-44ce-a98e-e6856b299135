package com.kbao.kbcchatbot.maas.channel.channel.entity;

import com.kbao.kbcchatbot.maas.robot.robot.entity.Robot;
import lombok.Data;
import org.springframework.data.annotation.Transient;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @Description maas渠道实体
* @Date 2024-12-20
*/
@Data
public class Channel implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中channel_code
     * 渠道编码
     */  
	private String channelCode;

    /**
     * 对应表中channel_name
     * 渠道名称
     */  
	private String channelName;

	private String channelType;
	/**
	 * 执行类型 0-自定义  1-flow流程
	 */
	private String executeType;
	/**
	 * 关联流程ID
	 */
	private String flowId;

    /**
     * 对应表中project_id
     * 项目id
     */  
	private Integer projectId;

    /**
     * 对应表中default_answer
     * 关闭闲聊时的默认回复
     */  
	private String defaultAnswer;

    /**
     * 对应表中final_words
     * 结束语
     */  
	private String finalWords;

    /**
     * 对应表中status
     * 状态:0-启用，1-禁用
     */  
	private String status;

    /**
     * 对应表中sensitive_words_reply
     * 敏感词回复文案
     */  
	private String sensitiveWordsReply;

    /**
     * 对应表中no_answer_reply
     * 无答案回复文案
     */  
	private String noAnswerReply;

	private String transferHumanStatus;

	private String uploadFileStatus;

	private String contextualizeQuestionStatus;
	// 聊天缓存条数
	private Integer memoryCacheNum;
	/**
	 * 是否开启语音识别
	 */
	private String voiceStatus;

	/**
	 * 是否开启语音播报
	 */
	private String voiceSynthesis;
	/**
	 * 语音播报音色
	 */
	private String voiceSpeaker;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_by
     * 创建人
     */  
	private String createBy;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_by
     * 更新人
     */  
	private String updateBy;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

	/**
	 * 项目名称
	 */
	@Transient
	private String projectName;

	/**
	 * 机器人列表
	 */
	@Transient
	private List<Robot> robotList;
}   