<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.project.dao.ProjectMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.project.entity.Project">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="name" jdbcType="VARCHAR"  column="name" />  
        <result property="code" jdbcType="VARCHAR"  column="code" />  
        <result property="secretKey" jdbcType="VARCHAR"  column="secret_key" />  
        <result property="status" jdbcType="CHAR"  column="status" />  
        <result property="chatEnable" jdbcType="CHAR"  column="chat_enable" />  
        <result property="defaultAnswer" jdbcType="VARCHAR"  column="default_answer" />  
        <result property="finalWords" jdbcType="VARCHAR"  column="final_words" />  
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="updateBy" jdbcType="VARCHAR"  column="update_by" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		name,  
		code,  
		secret_key,  
		status,  
		chat_enable,  
		default_answer,  
		final_words,  
		create_by,  
		create_time,  
		update_by,  
		update_time,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.name, 
		t.code, 
		t.secret_key, 
		t.status, 
		t.chat_enable, 
		t.default_answer, 
		t.final_words, 
		t.create_by, 
		t.create_time, 
		t.update_by, 
		t.update_time, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="name != null and name != ''">
	   		and t.name = #{name,jdbcType=VARCHAR}  
	    </if>
	    <if test="code != null and code != ''">
	   		and t.code = #{code,jdbcType=VARCHAR}  
	    </if>
	    <if test="secretKey != null and secretKey != ''">
	   		and t.secret_key = #{secretKey,jdbcType=VARCHAR}  
	    </if>
	    <if test="status != null">
	   		and t.status = #{status,jdbcType=CHAR}  
	    </if>
	    <if test="chatEnable != null">
	   		and t.chat_enable = #{chatEnable,jdbcType=CHAR}  
	    </if>
	    <if test="defaultAnswer != null and defaultAnswer != ''">
	   		and t.default_answer = #{defaultAnswer,jdbcType=VARCHAR}  
	    </if>
	    <if test="finalWords != null and finalWords != ''">
	   		and t.final_words = #{finalWords,jdbcType=VARCHAR}  
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateBy != null and updateBy != ''">
	   		and t.update_by = #{updateBy,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_project t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_project t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_project
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_project
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.project.entity.Project">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_project(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{name,jdbcType=VARCHAR}, 
                 
                #{code,jdbcType=VARCHAR}, 
                 
                #{secretKey,jdbcType=VARCHAR}, 
                 
                #{status,jdbcType=CHAR}, 
                 
                #{chatEnable,jdbcType=CHAR}, 
                 
                #{defaultAnswer,jdbcType=VARCHAR}, 
                 
                #{finalWords,jdbcType=VARCHAR}, 
                 
                #{createBy,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{updateBy,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.project.entity.Project">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_project
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="name != null ">  
	       		name,
	        </if>  
	        <if test="code != null ">  
	       		code,
	        </if>  
	        <if test="secretKey != null ">  
	       		secret_key,
	        </if>  
	        <if test="status != null ">  
	       		status,
	        </if>  
	        <if test="chatEnable != null ">  
	       		chat_enable,
	        </if>  
	        <if test="defaultAnswer != null ">  
	       		default_answer,
	        </if>  
	        <if test="finalWords != null ">  
	       		final_words,
	        </if>  
	        <if test="createBy != null ">  
	       		create_by,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="updateBy != null ">  
	       		update_by,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="name != null">  
            	#{name,jdbcType=VARCHAR},
            </if>  
            <if test="code != null">  
            	#{code,jdbcType=VARCHAR},
            </if>  
            <if test="secretKey != null">  
            	#{secretKey,jdbcType=VARCHAR},
            </if>  
            <if test="status != null">  
            	#{status,jdbcType=CHAR},
            </if>  
            <if test="chatEnable != null">  
            	#{chatEnable,jdbcType=CHAR},
            </if>  
            <if test="defaultAnswer != null">  
            	#{defaultAnswer,jdbcType=VARCHAR},
            </if>  
            <if test="finalWords != null">  
            	#{finalWords,jdbcType=VARCHAR},
            </if>  
            <if test="createBy != null">  
            	#{createBy,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateBy != null">  
            	#{updateBy,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.project.entity.Project">
		update t_project
		<set>
	        <if test="name != null ">  
	        	name = #{name,jdbcType=VARCHAR},  
	        </if>  
	        <if test="code != null ">  
	        	code = #{code,jdbcType=VARCHAR},  
	        </if>  
	        <if test="secretKey != null ">  
	        	secret_key = #{secretKey,jdbcType=VARCHAR},  
	        </if>  
	        <if test="status != null ">  
	        	status = #{status,jdbcType=CHAR},  
	        </if>  
	        <if test="chatEnable != null ">  
	        	chat_enable = #{chatEnable,jdbcType=CHAR},  
	        </if>  
	        <if test="defaultAnswer != null ">  
	        	default_answer = #{defaultAnswer,jdbcType=VARCHAR},  
	        </if>  
	        <if test="finalWords != null ">  
	        	final_words = #{finalWords,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createBy != null ">  
	        	create_by = #{createBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateBy != null ">  
	        	update_by = #{updateBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.project.entity.Project">
		update t_project
		set
           name = #{name,jdbcType=VARCHAR},
           code = #{code,jdbcType=VARCHAR},
           secret_key = #{secretKey,jdbcType=VARCHAR},
           status = #{status,jdbcType=CHAR},
           chat_enable = #{chatEnable,jdbcType=CHAR},
           default_answer = #{defaultAnswer,jdbcType=VARCHAR},
           final_words = #{finalWords,jdbcType=VARCHAR},
           create_by = #{createBy,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           update_by = #{updateBy,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           tenant_id = #{tenantId,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_project(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.name != null">,#{item.name}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.code != null">,#{item.code}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.secretKey != null">,#{item.secretKey}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.chatEnable != null">,#{item.chatEnable}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.defaultAnswer != null">,#{item.defaultAnswer}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.finalWords != null">,#{item.finalWords}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createBy != null">,#{item.createBy}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateBy != null">,#{item.updateBy}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_project(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.name != null">,#{item.name}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.code != null">,#{item.code}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.secretKey != null">,#{item.secretKey}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.chatEnable != null">,#{item.chatEnable}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.defaultAnswer != null">,#{item.defaultAnswer}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.finalWords != null">,#{item.finalWords}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createBy != null">,#{item.createBy}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateBy != null">,#{item.updateBy}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   name=values(name), 
		   code=values(code), 
		   secret_key=values(secret_key), 
		   status=values(status), 
		   chat_enable=values(chat_enable), 
		   default_answer=values(default_answer), 
		   final_words=values(final_words), 
		   create_by=values(create_by), 
		   create_time=values(create_time), 
		   update_by=values(update_by), 
		   update_time=values(update_time), 
		   tenant_id=values(tenant_id) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_project where id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>
	
	<!-- 自定义查询 -->
	<select id="isExistProject" resultType="int">
		select count(1)
		from t_project
		where code = #{code,jdbcType=VARCHAR}
		<if test="id != null">
			and id != #{id,jdbcType=INTEGER}
		</if>
	</select>
	<select id="getProjectList" resultMap="BaseResultMap">
		select
		<include refid="Alias_Column_List" />
		from t_project t
		<where>
			<if test="code != null and code != ''">
				and t.code = #{code,jdbcType=VARCHAR}
			</if>
			<if test="name != null and name != ''">
				and t.name like concat("%",#{name},"%")
			</if>
			<if test="tenantId != null and tenantId != ''">
				and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
			</if>
		</where>
		order by t.create_time desc
	</select>
	<select id="getSimpleProjectList" resultType="com.kbao.kbcchatbot.maas.project.bean.ProjectSimpleVO">
		select  t.id id,t.name name from t_project t where t.tenant_id = #{tenantId,jdbcType=VARCHAR}
		order by t.create_time desc
	</select>
</mapper>
