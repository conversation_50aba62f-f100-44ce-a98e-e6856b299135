<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.channel.channelcommoncard.dao.ChannelCommonCardMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard">
    	<id column="id" jdbcType="BIGINT"  property="id"  />
        <result property="channelId" jdbcType="BIGINT"  column="channel_id" />
		<result property="channelCode" jdbcType="VARCHAR"  column="channel_code" />
		<result property="title" jdbcType="VARCHAR"  column="title" />
        <result property="status" jdbcType="VARCHAR"  column="status" />  
        <result property="sort" jdbcType="TINYINT"  column="sort" />  
        <result property="imgPath" jdbcType="VARCHAR"  column="img_path" />  
        <result property="type" jdbcType="VARCHAR"  column="type" />
		<result property="knowledgeType" jdbcType="VARCHAR"  column="knowledge_type" />
		<result property="knowledgeTitle" jdbcType="VARCHAR"  column="knowledge_title" />
        <result property="content" jdbcType="LONGVARBINARY"  column="content" />
        <result property="createTime" jdbcType="VARCHAR"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="VARCHAR"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="isDeleted" jdbcType="TINYINT"  column="is_deleted" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		channel_id,
		channel_code,
		title,  
		status,  
		sort,  
		img_path,  
		type,
		knowledge_type,
		knowledge_title,
		content,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.channel_id,
		t.channel_code,
		t.title, 
		t.status, 
		t.sort, 
		t.img_path, 
		t.type,
		t.knowledge_type,
		t.knowledge_title,
<!--		t.content, -->
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
		<where>
			is_deleted = 0
	    <if test="channelId != null">
	   		and t.channel_id = #{channelId,jdbcType=BIGINT}
	    </if>
		<if test="channelCode != null and channelCode != ''">
			and t.channel_code = #{channelCode,jdbcType=VARCHAR}
		</if>
		<if test="title != null and title != ''">
			and t.title LIKE CONCAT('%', #{title,jdbcType=VARCHAR}, '%')
		</if>
	    <if test="status != null and status != ''">
	   		and t.status = #{status,jdbcType=VARCHAR}
	    </if>
	    <if test="sort != null">
	   		and t.sort = #{sort,jdbcType=TINYINT}
	    </if>
	    <if test="imgPath != null and imgPath != ''">
	   		and t.img_path = #{imgPath,jdbcType=VARCHAR}
	    </if>
	    <if test="type != null and type != ''">
	   		and t.type = #{type,jdbcType=VARCHAR}
	    </if>
		<if test="knowledgeType != null and knowledgeType != ''">
			and t.knoeledge_type = #{knowledgeType,jdbcType=VARCHAR}
		</if>
		<if test="knowledgeTitle != null and knowledgeTitle != ''">
			and t.knowledge_title = #{knowledgeTitle,jdbcType=VARCHAR}
		</if>
	    <if test="content != null">
	   		and t.content = #{content,jdbcType=LONGVARBINARY}
	    </if>
	    <if test="createTime != null and createTime != ''">
	   		and t.create_time = #{createTime,jdbcType=VARCHAR}
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null and updateTime != ''">
	   		and t.update_time = #{updateTime,jdbcType=VARCHAR}
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}
	    </if>
	    <if test="isDeleted != null">
	   		and t.is_deleted = #{isDeleted,jdbcType=TINYINT}
	    </if>
		<!-- 自定义条件-->
		<if test="titleKey != null and titleKey != ''">
			and t.title = #{titleKey,jdbcType=VARCHAR}
		</if>
		</where>
		order by t.sort asc
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_channel_common_card t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_channel_common_card t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_channel_common_card
		where  id = #{id,jdbcType=BIGINT}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from t_channel_common_card
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard">
		insert into t_channel_common_card(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=BIGINT},
                 
                #{channelId,jdbcType=BIGINT},

		       	#{channelCode,jdbcType=VARCHAR},

                #{title,jdbcType=VARCHAR},
                 
                #{status,jdbcType=VARCHAR}, 
                 
                #{sort,jdbcType=TINYINT}, 
                 
                #{imgPath,jdbcType=VARCHAR}, 
                 
                #{type,jdbcType=VARCHAR},

				#{knowledgeType,jdbcType=VARCHAR},

				#{knowledgeTitle,jdbcType=VARCHAR},

				#{content,jdbcType=LONGVARBINARY},
                 
                #{createTime,jdbcType=VARCHAR}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=VARCHAR}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{isDeleted,jdbcType=TINYINT} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard">
		<selectKey resultType="Long" order="AFTER" keyProperty="id">
			SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_channel_common_card
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="channelId != null ">  
	       		channel_id,
	        </if>
		    <if test="channelCode != null ">
		    	channel_code,
		    </if>
	        <if test="title != null ">  
	       		title,
	        </if>  
	        <if test="status != null ">  
	       		status,
	        </if>  
	        <if test="sort != null ">  
	       		sort,
	        </if>  
	        <if test="imgPath != null ">  
	       		img_path,
	        </if>  
	        <if test="type != null ">  
	       		type,
	        </if>
			<if test="knowledgeType != null ">
				knowledge_type,
			</if>
			<if test="knowledgeTitle != null ">
				knowledge_title,
			</if>
	        <if test="content != null ">  
	       		content,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="isDeleted != null ">  
	       		is_deleted,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=BIGINT},
            </if>  
            <if test="channelId != null">  
            	#{channelId,jdbcType=BIGINT},
            </if>
		    <if test="channelCode != null">
		    	#{channelCode,jdbcType=VARCHAR},
		    </if>
            <if test="title != null">  
            	#{title,jdbcType=VARCHAR},
            </if>  
            <if test="status != null">  
            	#{status,jdbcType=VARCHAR},
            </if>  
            <if test="sort != null">  
            	#{sort,jdbcType=TINYINT},
            </if>  
            <if test="imgPath != null">  
            	#{imgPath,jdbcType=VARCHAR},
            </if>  
            <if test="type != null">  
            	#{type,jdbcType=VARCHAR},
            </if>
			<if test="knowledgeType != null">
				#{knowledgeType,jdbcType=VARCHAR},
			</if>
			<if test="knowledgeTitle != null">
				#{knowledgeTitle,jdbcType=VARCHAR},
			</if>
			<if test="content != null">
            	#{content,jdbcType=LONGVARBINARY},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=VARCHAR},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=VARCHAR},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="isDeleted != null">  
            	#{isDeleted,jdbcType=TINYINT},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard">
		update t_channel_common_card
		<set>
	        <if test="channelId != null ">  
	        	channel_id = #{channelId,jdbcType=BIGINT},
	        </if>
		    <if test="channelCode != null ">
		    	channel_code = #{channelCode,jdbcType=VARCHAR},
		    </if>
	        <if test="title != null ">  
	        	title = #{title,jdbcType=VARCHAR},  
	        </if>  
	        <if test="status != null ">  
	        	status = #{status,jdbcType=VARCHAR},  
	        </if>  
	        <if test="sort != null ">  
	        	sort = #{sort,jdbcType=TINYINT},  
	        </if>  
	        <if test="imgPath != null ">  
	        	img_path = #{imgPath,jdbcType=VARCHAR},  
	        </if>  
	        <if test="type != null ">  
	        	type = #{type,jdbcType=VARCHAR},  
	        </if>
			<if test="knowledgeType != null ">
				knowledge_type = #{knowledgeType,jdbcType=VARCHAR},
			</if>
			<if test="knowledgeTitle != null ">
				knowledge_title = #{knowledgeTitle,jdbcType=VARCHAR},
			</if>
	        <if test="content != null ">  
	        	content = #{content,jdbcType=LONGVARBINARY},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="isDeleted != null ">  
	        	is_deleted = #{isDeleted,jdbcType=TINYINT},  
	        </if>  
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard">
		update t_channel_common_card
		set
           channel_id = #{channelId,jdbcType=BIGINT},
           channel_code = #{channelCode,jdbcType=VARCHAR},
           title = #{title,jdbcType=VARCHAR},
           status = #{status,jdbcType=VARCHAR},
           sort = #{sort,jdbcType=TINYINT},
           img_path = #{imgPath,jdbcType=VARCHAR},
           type = #{type,jdbcType=VARCHAR},
		   knowledge_type = #{knowledgeType,jdbcType=VARCHAR},
		   knowledge_title = #{knowledgeTitle,jdbcType=VARCHAR},
           content = #{content,jdbcType=LONGVARBINARY},
           create_time = #{createTime,jdbcType=VARCHAR},
           create_id = #{createId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=VARCHAR},
           update_id = #{updateId,jdbcType=VARCHAR},
           is_deleted = #{isDeleted,jdbcType=TINYINT}
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_channel_common_card(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise>
	        </choose>
			<choose>
				<when test="item.channelCode != null">,#{item.channelCode}</when><otherwise>,default</otherwise>
			</choose>
	        <choose>
	            <when test="item.title != null">,#{item.title}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.sort != null">,#{item.sort}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.imgPath != null">,#{item.imgPath}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.type != null">,#{item.type}</when><otherwise>,default</otherwise>
	        </choose>
			<choose>
				<when test="item.knowledgeType != null">,#{item.knowledgeType}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.knowledgeTitle != null">,#{item.knowledgeTitle}</when><otherwise>,default</otherwise>
			</choose>
	        <choose>
	            <when test="item.content != null">,#{item.content}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_channel_common_card(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.channelCode != null">,#{item.channelCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.title != null">,#{item.title}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.sort != null">,#{item.sort}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.imgPath != null">,#{item.imgPath}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.type != null">,#{item.type}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.knowledgeType != null">,#{item.knowledgeType}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.knowledgeTitle != null">,#{item.knowledgeTitle}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.content != null">,#{item.content}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   channel_id=values(channel_id),
		   channel_code=values(channel_code),
		   title=values(title), 
		   status=values(status), 
		   sort=values(sort), 
		   img_path=values(img_path), 
		   type=values(type),
		   knowledgeType=values(knowledgeType),
		   knowledgeTitle=values(knowledgeTitle),
		   content=values(content),
		   create_time=values(create_time), 
		   create_id=values(create_id), 
		   update_time=values(update_time), 
		   update_id=values(update_id), 
		   is_deleted=values(is_deleted) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_channel_common_card where id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>

	<!-- 自定义查询 -->

<!--根据渠道ID获取渠道常用卡片列表(包含content)-->
	<select id="getChannelCommonCardListByChannelId" resultMap="BaseResultMap">
        select id,
               channel_id,
               channel_code,
               title,
               status,
               sort,
               img_path,
               type,
			   knowledge_type,
			   knowledge_title,
               content
        from t_channel_common_card
		<where>
			channel_id = #{channelId,jdbcType=BIGINT}
			and is_deleted = 0
			<if test="status != null">
				and status = #{status,jdbcType=VARCHAR}
			</if>
		</where>
		order by sort asc
    </select>

	<select id="getChannelCommonCardByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_channel_common_card
		where  channel_code = #{channelCode,jdbcType=VARCHAR}
	</select>

	<!--根据渠道CODE获取渠道常用卡片列表(包含content)-->
	<select id="getChannelCommonCardListByChannelCode" resultMap="BaseResultMap">
		select id,
		channel_id,
		channel_code,
		title,
		status,
		sort,
		img_path,
		type,
		knowledge_type,
		knowledge_title,
		content
		from t_channel_common_card
		<where>
			channel_code = #{channelCode,jdbcType=VARCHAR}
			and is_deleted = 0
			<if test="status != null">
				and status = #{status,jdbcType=VARCHAR}
			</if>
		</where>
		order by sort asc
	</select>
</mapper>
