package com.kbao.kbcchatbot.maas.mcp.server.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 对应表 t_mcp_server
 * MCP服务器表
 */
@Data
public class McpServer implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应表中server_id
     * 服务器编号（自增长主键）
     */
    private Long serverId;

    /**
     * 对应表中server_code
     * 服务器编码（序列生成，唯一标识）
     */
    private String serverCode;

    /**
     * 对应表中server_name
     * 服务器名称
     */
    @NotNull(message = "serverName不能为空")
    private String serverName;

    /**
     * 对应表中server_url
     * 服务器地址
     */
    private String serverUrl;

    /**
     * 对应表中server_port
     * 服务器端口
     */
    private Integer serverPort;

    /**
     * 对应表中server_status
     * 服务器状态 1启用，0禁用
     */
    private Integer serverStatus;

    /**
     * 对应表中description
     * 服务器描述
     */
    private String description;

    /**
     * 对应表中tenant_id
     * 租户编号
     */
    private String tenantId;

    /**
     * 对应表中create_time
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 对应表中create_id
     * 创建人编号
     */
    private String createId;

    /**
     * 对应表中update_id
     * 更新人编号
     */
    private String updateId;

    /**
     * 对应表中update_time
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 对应表中is_deleted
     * 是否删除 0 未删除  1已删除
     */
    private Integer isDeleted;


}