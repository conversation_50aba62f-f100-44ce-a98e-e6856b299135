<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcchatbot.maas.flow.dao.FlowMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.flow.entity.Flow">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="flowId" column="flow_id" jdbcType="VARCHAR"/>
            <result property="flowName" column="flow_name" jdbcType="VARCHAR"/>
            <result property="flowType" column="flow_type" jdbcType="CHAR"/>
            <result property="apiKey" column="api_key" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,flow_id,flow_name,flow_type,api_key,
        create_time,create_by,update_time,update_by,tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="Base_Condition">
        <where>
            and 1=1
            <if test="flowId != null and flowId != ''">
                AND flow_id = #{flowId}
            </if>
            <if test="flowName != null and flowName != ''">
                AND flow_name like concat('%',#{flowName},'%')
            </if>
            <if test="flowType != null and flowType != ''">
                AND flow_type = #{flowType}
            </if>
            <if test="apiKey != null and apiKey != ''">
                AND api_key = #{apiKey}
            </if>
            <if test="createTime != null">
                AND create_time = #{createTime}
            </if>
            <if test="createBy != null and createBy != ''">
                AND create_by = #{createBy}
            </if>
            <if test="updateTime != null">
                AND update_time = #{updateTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                AND update_by = #{updateBy}
            </if>
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </sql>

    <select id="selectList" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select flow_id, flow_name, flow_type
        from t_flow
        <include refid="Base_Condition" />
        order by update_time desc
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_flow
        where  id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 根据条件查询-->
    <select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Base_Column_List" />
        from t_flow
        <include refid="Base_Condition" />
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from t_flow
        where  id = #{id,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.maas.flow.entity.Flow" useGeneratedKeys="true">
        insert into t_flow
        ( id,flow_id,flow_name,flow_type,api_key,
        create_time,create_by,update_time,update_by,tenant_id)
        values (#{id,jdbcType=INTEGER},#{flowId,jdbcType=VARCHAR},#{flowName,jdbcType=VARCHAR},#{flowType,jdbcType=CHAR},#{apiKey,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR},#{tenantId,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.maas.flow.entity.Flow" useGeneratedKeys="true">
        insert into t_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="flowId != null">flow_id,</if>
                <if test="flowName != null">flow_name,</if>
                <if test="flowType != null">flow_type,</if>
                <if test="apiKey != null">api_key,</if>
                <if test="createTime != null">create_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="tenantId != null">tenant_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="flowId != null">#{flowId,jdbcType=VARCHAR},</if>
                <if test="flowName != null">#{flowName,jdbcType=VARCHAR},</if>
                <if test="flowType != null">#{flowType,jdbcType=CHAR},</if>
                <if test="apiKey != null">#{apiKey,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
                <if test="tenantId != null">#{tenantId,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.flow.entity.Flow">
        update t_flow
        <set>
                <if test="flowId != null">
                    flow_id = #{flowId,jdbcType=VARCHAR},
                </if>
                <if test="flowName != null">
                    flow_name = #{flowName,jdbcType=VARCHAR},
                </if>
                <if test="flowType != null">
                    flow_type = #{flowType,jdbcType=CHAR},
                </if>
                <if test="apiKey != null">
                    api_key = #{apiKey,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="tenantId != null">
                    tenant_id = #{tenantId,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.flow.entity.Flow">
        update t_flow
        set 
            flow_id =  #{flowId,jdbcType=VARCHAR},
            flow_name =  #{flowName,jdbcType=VARCHAR},
            flow_type =  #{flowType,jdbcType=CHAR},
            api_key =  #{apiKey,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            update_by =  #{updateBy,jdbcType=VARCHAR},
            tenant_id =  #{tenantId,jdbcType=VARCHAR}
        where   id = #{id,jdbcType=INTEGER} 
    </update>
</mapper>
