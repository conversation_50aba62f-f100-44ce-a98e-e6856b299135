package com.kbao.kbcchatbot.maas.channel.channel.bean;

import lombok.Data;

/**
 * @author: xia<PERSON><PERSON><PERSON><PERSON>
 * @time: 2024/12/20 10:31
 */
@Data
public class ChannelUpdateVO {
    private Integer channelId;
    private Integer projectId;
    private String channelName;
    private String channelCode;
    private String channelType;
    private String executeType;
    private String flowId;
    private String flowName;
    private String passId;
    private String defaultAnswer;
    private String finalWords;
    private String status;
    private String welcomeReply;
    private String inputPlaceHolder;
    private String sensitiveWordsReply;
    private String noAnswerReply;
    private String answerEvaluateStatus;
    private String commonCardStatus;
    private String commonPhraseStatus;
    private String guessQuestionStatus;
    private String voiceStatus;
    private String voiceSynthesis;
    private String voiceSpeaker;
    private String transferHumanStatus;
    private String contextualizeQuestionStatus;
    private String uploadFileStatus;
    private Integer memoryCacheNum;
    private String remark;
}
