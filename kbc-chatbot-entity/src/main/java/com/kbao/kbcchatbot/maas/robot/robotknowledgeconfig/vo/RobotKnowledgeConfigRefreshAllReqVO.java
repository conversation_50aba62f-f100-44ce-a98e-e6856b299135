package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 批量刷新
 * @author: husw
 * @create: 2023-12-18 15:06
 **/
@Data
public class RobotKnowledgeConfigRefreshAllReqVO {
    private Long robotId;

    private String robotCode;
    /**
     * 知识目录名称
     */
    private String directoryName;

    /**
     * 知识目录ID
     */
    private List<String> directoryIdList;

    private String onlyFile;
}
