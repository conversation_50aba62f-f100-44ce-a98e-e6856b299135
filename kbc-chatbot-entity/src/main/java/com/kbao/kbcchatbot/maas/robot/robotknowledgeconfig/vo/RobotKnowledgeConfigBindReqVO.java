package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 绑定解绑入参
 * @author: husw
 * @create: 2023-05-17 17:56
 **/
@Data
public class RobotKnowledgeConfigBindReqVO {

    private Long id;

    private Long robotId;

    @NotNull(message = "机器人CODE不能为空！")
    private String robotCode;
    /**
     * 项目
     */
    private String projectId;
    /**
     * 一级知识目录ID
     */
    private String firstDirectId;

    /**
     * 一级知识目录名称
     */
    private String firstDirectName;

    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 文件夹
     */
    private String folderId;
    /**
     * 文件夹名称
     */
    private String folderName;
    /**
     * 知识目录
     */
    @NotEmpty(message = "知识目录不能为空")
    private List<RobotKnowledgeDirectVO> directVOS;
}
