<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.mcp.tool.dao.McpToolMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.mcp.tool.entity.McpTool">
    	<id column="tool_id" jdbcType="BIGINT"  property="toolId"  />
        <result property="toolCode" jdbcType="VARCHAR"  column="tool_code" />
        <result property="toolName" jdbcType="VARCHAR"  column="tool_name" />  
        <result property="toolType" jdbcType="VARCHAR"  column="tool_type" />  
        <result property="serverId" jdbcType="BIGINT"  column="server_id" />  
        <result property="appServerId" jdbcType="BIGINT"  column="app_server_id" />  
        <result property="apiPath" jdbcType="VARCHAR"  column="api_path" />  
        <result property="toolDescription" jdbcType="VARCHAR"  column="tool_description" />  
        <result property="inputSchema" jdbcType="LONGVARCHAR"  column="input_schema" />  
        <result property="outputSchema" jdbcType="LONGVARCHAR"  column="output_schema" />  
        <result property="toolStatus" jdbcType="INTEGER"  column="tool_status" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="isDeleted" jdbcType="INTEGER"  column="is_deleted" />  
	</resultMap>

	<sql id="Base_Column_List">
		tool_id,  
		tool_code,  
		tool_name,  
		tool_type,  
		server_id,  
		app_server_id,  
		api_path,  
		tool_description,  
		input_schema,  
		output_schema,  
		tool_status,  
		tenant_id,  
		create_time,  
		create_id,  
		update_id,  
		update_time,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.tool_id, 
		t.tool_code, 
		t.tool_name, 
		t.tool_type, 
		t.server_id, 
		t.app_server_id, 
		t.api_path, 
		t.tool_description, 
		t.input_schema, 
		t.output_schema, 
		t.tool_status, 
		t.tenant_id, 
		t.create_time, 
		t.create_id, 
		t.update_id, 
		t.update_time, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0 
	    <if test="toolId != null and toolId != ''"> 
	   		and t.tool_id = #{toolId,jdbcType=VARCHAR}  
	    </if>
	    <if test="toolName != null and toolName != ''"> 
	   		and t.tool_name like CONCAT('%', #{toolName,jdbcType=VARCHAR}, '%')  
	    </if>
	    <if test="toolType != null and toolType != ''"> 
	   		and t.tool_type = #{toolType,jdbcType=VARCHAR}  
	    </if>
	    <if test="serverId != null and serverId != ''"> 
	   		and t.server_id = #{serverId,jdbcType=VARCHAR}  
	    </if>
	    <if test="appServerId != null and appServerId != ''"> 
	   		and t.app_server_id = #{appServerId,jdbcType=BIGINT}  
	    </if>
	    <if test="apiPath != null and apiPath != ''"> 
	   		and t.api_path like CONCAT('%', #{apiPath,jdbcType=VARCHAR}, '%')  
	    </if>
	    <if test="toolDescription != null and toolDescription != ''"> 
	   		and t.tool_description like CONCAT('%', #{toolDescription,jdbcType=VARCHAR}, '%')  
	    </if>
	    <if test="inputSchema != null and inputSchema != ''"> 
	   		and t.input_schema = #{inputSchema,jdbcType=LONGVARCHAR}  
	    </if>
	    <if test="outputSchema != null and outputSchema != ''"> 
	   		and t.output_schema = #{outputSchema,jdbcType=LONGVARCHAR}  
	    </if>
	    <if test="toolStatus != null"> 
	   		and t.tool_status = #{toolStatus,jdbcType=INTEGER}  
	    </if>
	    <if test="tenantId != null"> 
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null"> 
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="createId != null"> 
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateId != null"> 
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null"> 
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="isDeleted != null"> 
	   		and t.is_deleted = #{isDeleted,jdbcType=INTEGER}  
	    </if>
		<!-- 自定义条件-->
		<if test="toolIds != null and toolIds.size()>0">
			and t.tool_id in
			 <foreach collection="toolIds" index="index" item="item" open="(" separator="," close=")"> #{item}</foreach>
		</if>
		<if test="serverIds != null and serverIds.size()>0">
			and t.server_id in
			 <foreach collection="serverIds" index="index" item="item" open="(" separator="," close=")"> #{item}</foreach>
		</if>
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_mcp_tool t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_mcp_tool t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_mcp_tool
		where  is_deleted = 0 and tool_id = #{toolId,jdbcType=BIGINT}
	</select>


	<!-- 根据主键删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
		update t_mcp_tool set is_deleted = 1
		where tool_id = #{toolId,jdbcType=BIGINT} and is_deleted = 0
	</update>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.mcp.tool.entity.McpTool">
		insert into t_mcp_tool(
			<include refid="Base_Column_List" />
		)
		values(  
                #{toolId,jdbcType=BIGINT}, 
                 
                #{toolCode,jdbcType=VARCHAR}, 
                 
                #{toolName,jdbcType=VARCHAR}, 
                 
                #{toolType,jdbcType=VARCHAR}, 
                 
                #{serverId,jdbcType=BIGINT}, 
                 
                #{appServerId,jdbcType=BIGINT}, 
                 
                #{apiPath,jdbcType=VARCHAR}, 
                 
                #{toolDescription,jdbcType=VARCHAR}, 
                 
                #{inputSchema,jdbcType=LONGVARCHAR}, 
                 
                #{outputSchema,jdbcType=LONGVARCHAR}, 
                 
                #{toolStatus,jdbcType=INTEGER}, 
                 
                #{tenantId,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{isDeleted,jdbcType=INTEGER} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.mcp.tool.entity.McpTool">
		insert into t_mcp_tool
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="toolId != null ">  
	       		tool_id,
	        </if>  
	        <if test="toolCode != null ">  
	       		tool_code,
	        </if>  
	        <if test="toolName != null ">  
	       		tool_name,
	        </if>  
	        <if test="toolType != null ">  
	       		tool_type,
	        </if>  
	        <if test="serverId != null ">  
	       		server_id,
	        </if>  
	        <if test="appServerId != null ">  
	       		app_server_id,
	        </if>  
	        <if test="apiPath != null ">  
	       		api_path,
	        </if>  
	        <if test="toolDescription != null ">  
	       		tool_description,
	        </if>  
	        <if test="inputSchema != null ">  
	       		input_schema,
	        </if>  
	        <if test="outputSchema != null ">  
	       		output_schema,
	        </if>  
	        <if test="toolStatus != null ">  
	       		tool_status,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="isDeleted != null ">  
	       		is_deleted,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="toolId != null">  
            	#{toolId,jdbcType=BIGINT},
            </if>  
            <if test="toolCode != null">  
            	#{toolCode,jdbcType=VARCHAR},
            </if>  
            <if test="toolName != null">  
            	#{toolName,jdbcType=VARCHAR},
            </if>  
            <if test="toolType != null">  
            	#{toolType,jdbcType=VARCHAR},
            </if>  
            <if test="serverId != null">  
            	#{serverId,jdbcType=BIGINT},
            </if>  
            <if test="appServerId != null">  
            	#{appServerId,jdbcType=BIGINT},
            </if>  
            <if test="apiPath != null">  
            	#{apiPath,jdbcType=VARCHAR},
            </if>  
            <if test="toolDescription != null">  
            	#{toolDescription,jdbcType=VARCHAR},
            </if>  
            <if test="inputSchema != null">  
            	#{inputSchema,jdbcType=LONGVARCHAR},
            </if>  
            <if test="outputSchema != null">  
            	#{outputSchema,jdbcType=LONGVARCHAR},
            </if>  
            <if test="toolStatus != null">  
            	#{toolStatus,jdbcType=INTEGER},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="isDeleted != null">  
            	#{isDeleted,jdbcType=INTEGER},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.mcp.tool.entity.McpTool">
		update t_mcp_tool
		<set>
	        <if test="toolCode != null ">  
	        	tool_code = #{toolCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="toolName != null ">  
	        	tool_name = #{toolName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="toolType != null ">  
	        	tool_type = #{toolType,jdbcType=VARCHAR},  
	        </if>  
	        <if test="serverId != null"> 
                server_id = #{serverId,jdbcType=BIGINT}, 
            </if> 
            <if test="appServerId != null"> 
                app_server_id = #{appServerId,jdbcType=BIGINT}, 
            </if> 
            <if test="apiPath != null"> 
                api_path = #{apiPath,jdbcType=VARCHAR}, 
            </if> 
            <if test="toolDescription != null"> 
                tool_description = #{toolDescription,jdbcType=VARCHAR},  
	        </if>  
	        <if test="inputSchema != null ">  
	        	input_schema = #{inputSchema,jdbcType=LONGVARCHAR},  
	        </if>  
	        <if test="outputSchema != null ">  
	        	output_schema = #{outputSchema,jdbcType=LONGVARCHAR},  
	        </if>  
	        <if test="toolStatus != null ">  
	        	tool_status = #{toolStatus,jdbcType=INTEGER},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="isDeleted != null ">  
	        	is_deleted = #{isDeleted,jdbcType=INTEGER},  
	        </if>  
		</set>
		where tool_id = #{toolId,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.mcp.tool.entity.McpTool">
		update t_mcp_tool
		set
           tool_code = #{toolCode,jdbcType=VARCHAR},
           tool_name = #{toolName,jdbcType=VARCHAR},
           tool_type = #{toolType,jdbcType=VARCHAR},
           server_id = #{serverId,jdbcType=BIGINT}, 
            app_server_id = #{appServerId,jdbcType=BIGINT}, 
            api_path = #{apiPath,jdbcType=VARCHAR}, 
            tool_description = #{toolDescription,jdbcType=VARCHAR},
           input_schema = #{inputSchema,jdbcType=LONGVARCHAR},
           output_schema = #{outputSchema,jdbcType=LONGVARCHAR},
           tool_status = #{toolStatus,jdbcType=INTEGER},
           tenant_id = #{tenantId,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           create_id = #{createId,jdbcType=VARCHAR},
           update_id = #{updateId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           is_deleted = #{isDeleted,jdbcType=INTEGER}
		where tool_id = #{toolId,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_mcp_tool(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.toolId}
	        <choose>
	            <when test="item.toolCode != null">,#{item.toolCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.toolName != null">,#{item.toolName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.toolType != null">,#{item.toolType}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.serverId != null">,#{item.serverId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.appServerId != null">,#{item.appServerId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.apiPath != null">,#{item.apiPath}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.toolDescription != null">,#{item.toolDescription}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.inputSchema != null">,#{item.inputSchema}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.outputSchema != null">,#{item.outputSchema}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.toolStatus != null">,#{item.toolStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量删除 -->
	<update id="batchDelete" parameterType="java.util.List">
		update t_mcp_tool set is_deleted = 1
		where tool_id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}</foreach>
		and is_deleted = 0
	</update>

</mapper>