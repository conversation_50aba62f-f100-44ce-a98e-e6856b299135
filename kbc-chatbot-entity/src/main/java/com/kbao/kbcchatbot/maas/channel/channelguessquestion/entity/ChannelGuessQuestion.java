package com.kbao.kbcchatbot.maas.channel.channelguessquestion.entity;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description 渠道猜你想问实体
 * @Date 2023-05-24
 */
@Data
public class ChannelGuessQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应表中id
     * 主键
     */
    private Long id;

    /**
     * 对应表中channel_id
     * 渠道主键
     */
    private Long channelId;

    /**
     * 对应表中channel_code
     * 渠道编码
     */
    private String channelCode;

    /**
     * 对应表中title
     * 标题
     */
    private String title;

    /**
     * 对应表中status
     * 启用状态 0-未启用 1-启用
     */
    private String status;

    /**
     * 对应表中type
     * 类型 1-跳转页面 2-发送文本 3-关联知识库
     */
    private String type;

    /**
     * 来源类型 01是通用 02是订单
     */
    private String sourceType;

    /**
     * 关联知识类型 type=3时必填 1-QA 2-其他知识
     */
    private String knowledgeType;
    /**
     * type = 3有值 知识标题
     */
    private String knowledgeTitle;

    /**
     * 对应表中content
     * 内容
     */
    private byte[] content;

    /**
     * 对应表中create_time
     * 创建时间
     */
    private String createTime;

    /**
     * 对应表中create_id
     * 创建人
     */
    private String createId;

    /**
     * 对应表中update_time
     * 最后更新时间
     */
    private String updateTime;

    /**
     * 对应表中update_id
     * 最后更新人
     */
    private String updateId;

    /**
     * 对应表中is_deleted
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 来源类型模糊查询
     */
    private String sourceTypeFuzzy;

}