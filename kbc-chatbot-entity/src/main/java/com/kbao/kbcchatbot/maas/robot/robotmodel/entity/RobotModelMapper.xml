<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.robot.robotmodel.dao.RobotModelMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.robot.robotmodel.entity.RobotModel">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="robotId" jdbcType="INTEGER"  column="robot_id" />  
        <result property="modelId" jdbcType="INTEGER"  column="model_id" />
		<result property="weight" jdbcType="DOUBLE"  column="weight" />
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		robot_id,  
		model_id,
		weight,
		create_time,  
		create_by,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.robot_id, 
		t.model_id,
		t.weight,
		t.create_time, 
		t.create_by, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="robotId != null">
	   		and t.robot_id = #{robotId,jdbcType=INTEGER}
	    </if>
	    <if test="modelId != null">
	   		and t.model_id = #{modelId,jdbcType=INTEGER}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_robot_model t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_robot_model
		where  id = #{id,jdbcType=INTEGER}
	</select>

	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_robot_model
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.robot.robotmodel.entity.RobotModel">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_robot_model(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                #{robotId,jdbcType=INTEGER},
                #{modelId,jdbcType=INTEGER},
		        #{weight,jdbcType=DOUBLE},
                now(),
                #{createBy,jdbcType=VARCHAR},
                #{tenantId,jdbcType=VARCHAR}
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.robot.robotmodel.entity.RobotModel">
		update t_robot_model
		<set>
	        <if test="robotId != null ">  
	        	robot_id = #{robotId,jdbcType=INTEGER},  
	        </if>  
	        <if test="modelId != null ">  
	        	model_id = #{modelId,jdbcType=INTEGER},  
	        </if>
		    <if test="weight != null ">
		    	weight = #{weight,jdbcType=DOUBLE},
		    </if>
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createBy != null ">  
	        	create_by = #{createBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 自定义查询 -->
	<delete id="deleteByRobotId">
		delete from t_robot_model where robot_id = #{robotId}
	</delete>

	<select id="getModelByRobotId" resultType="com.kbao.kbcchatbot.maas.robot.robotmodel.bean.RobotModelResVo">
		select m.id modelId, m.model_name modelName, m.model_type modelType, m.platform, rm.weight
		from t_robot_model rm
				 join t_model m on rm.model_id = m.id and m.is_delete = 0
		where rm.robot_id = #{robotId}
		order by m.model_type, m.model_name
	</select>
</mapper>
