package com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity;
import java.io.Serializable;

import lombok.Data;

/**
* <AUTHOR>
* @Description 渠道常用卡片实体
* @Date 2023-05-22
*/
@Data
public class ChannelCommonCard implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Long id;

    /**
     * 对应表中channel_id
     * 渠道主键
     */  
	private Long channelId;

	/**
	 * 对应表中channel_code
	 * 渠道编码
	 */
	private String channelCode;

    /**
     * 对应表中title
     * 标题
     */  
	private String title;

    /**
     * 对应表中status
     * 启用状态 0-未启用 1-启用
     */  
	private String status;

    /**
     * 对应表中sort
     * 排序
     */  
	private Integer sort;

    /**
     * 对应表中img_path
     * 图片路径
     */  
	private String imgPath;

    /**
     * 对应表中type
     * 类型 1-跳转页面 2-发送文本 3-关联知识库
     */  
	private String type;

	/**
	 * 关联知识类型 type=3时必填 1-QA 2-其他知识
	 */
	private String knowledgeType;
	/**
	 * type = 3有值 知识标题
	 */
	private String knowledgeTitle;

    /**
     * 对应表中content
     * 内容
     */  
	private byte[] content;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	private String createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中update_time
     * 最后更新时间
     */  
	private String updateTime;

    /**
     * 对应表中update_id
     * 最后更新人
     */  
	private String updateId;

    /**
     * 对应表中is_deleted
     * 是否删除
     */  
	private Integer isDeleted;

}   