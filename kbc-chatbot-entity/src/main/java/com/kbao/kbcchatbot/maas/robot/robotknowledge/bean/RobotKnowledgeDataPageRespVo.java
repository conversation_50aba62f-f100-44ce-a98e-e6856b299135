package com.kbao.kbcchatbot.maas.robot.robotknowledge.bean;

import lombok.Data;

import java.util.Date;

/**
 * 知识库分页返参
 * @author: xia<PERSON><PERSON><PERSON><PERSON>
 * @time: 2024/12/12 16:57
 */
@Data
public class RobotKnowledgeDataPageRespVo {

    private Integer id;
    private String directoryCode;
    private String robotCode;
    private String knowledgeId;
    private String itemId;
    private String title;
    private String dataType;
    private String contentLength;
    private String status;
    private String syncStatus;
    private Date syncTime;
    private String robotName;
    private String message;
}
