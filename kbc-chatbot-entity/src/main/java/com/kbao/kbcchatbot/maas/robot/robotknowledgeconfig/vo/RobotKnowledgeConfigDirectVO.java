package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import lombok.Data;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 目录
 * @author: husw
 * @create: 2023-05-29 15:43
 **/
@Data
public class RobotKnowledgeConfigDirectVO {

    /**
     * 是否绑定 0-否  1-是
     */
    private Integer isBind;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 项目
     */
    private String projectName;
    /**
     * 知识库夹id
     */
    private String folderId;
    /**
     * 知识库夹
     */
    private String folderName;
    /**
     * 父级目录id
     */
    private String parentId;
    /**
     * 目录ID
     */
    private String directId;
    /**
     * 目录名称
     */
    private String directName;
    /**
     * 目录类型: 1.项目，2.知识库夹，3.一级目录，4.二级目录
     */
    private String directType;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 一级目录ID
     */
    private String firstDirectId;
    /**
     * 一级目录名称
     */
    private String firstDirectName;

    /**
     * 子级目录
     */
    private List<RobotKnowledgeConfigDirectVO> childs;
}
