package com.kbao.kbcchatbot.maas.product.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2024-12-23
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSyncLog implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中sync_type
     * 同步类型:0-自动增量同步，1-手动同步
     */  
	private String syncType;

    /**
     * 对应表中product_num
     * 同步产品数量
     */  
	private Integer productNum;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

}   