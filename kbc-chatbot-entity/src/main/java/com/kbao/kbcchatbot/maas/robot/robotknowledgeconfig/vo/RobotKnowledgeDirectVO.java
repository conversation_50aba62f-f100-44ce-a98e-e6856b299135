package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @program: kbc-chatbot
 * @description: 知识目录
 * @author: husw
 * @create: 2023-06-05 11:25
 **/
@Data
public class RobotKnowledgeDirectVO {

    /**
     * 知识目录编码
     */
    private String directoryCode;
    /**
     * 知识目录名称
     */
    @NotBlank(message = "知识目录名称不能为空")
    private String directoryName;

    /**
     * 知识目录ID
     */
    @NotBlank(message = "知识目录id不能为空")
    private String directoryId;
}
