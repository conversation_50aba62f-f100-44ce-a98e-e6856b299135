package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @program: kbc-chatbot
 * @description: 知识搜索入参
 * @author: husw
 * @create: 2023-06-29 09:38
 **/
@Data
public class RobotKnowledgeSearchReqVO {
    private Long robotId;

    @NotNull(message = "机器人CODE不能为空！")
    private String robotCode;

    /**
     * 类型 0-测试版 1-正式版
     */
    @NotNull(message = "版本类型不能为空！")
    private Integer type;

    /**
     * 知识类型 1-QA 2-其它知识
     */
    @NotNull(message = "知识类型不能为空！")
    private String knowledgeType;

    /**
     * 二级目录ID
     */
    private String directoryId;
    /**
     * 标题
     */
    private String title;
    /**
     * 知识ID
     */
    private String knowledgeId;
}
