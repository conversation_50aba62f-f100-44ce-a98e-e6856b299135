package com.kbao.kbcchatbot.maas.train.scene.bean;

import com.alibaba.fastjson.JSONObject;
import com.kbao.kbcchatbot.maas.train.scene.entity.TrainScene;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrainSceneInitResVo {
    private String sceneCode;
    private String sceneName;
    private String robotName;
    private String robotImage;
    private String robotInfo;
    private String voiceStatus;
    private String voiceSynthesis;
    private String uploadFileStatus;
    private String externalId;

    public static TrainSceneInitResVo fromTrainScene(TrainScene trainScene) {
        return TrainSceneInitResVo.builder()
                .sceneCode(trainScene.getSceneCode())
                .sceneName(trainScene.getSceneName())
                .robotName(trainScene.getRobotName())
                .robotImage(trainScene.getRobotImage())
                .robotInfo(trainScene.getRobotInfo())
                .voiceStatus(trainScene.getVoiceStatus())
                .voiceSynthesis(trainScene.getVoiceSynthesis())
                .uploadFileStatus(trainScene.getUploadFileStatus())
                .build();
    }
}
