package com.kbao.kbcchatbot.maas.chatsession.bean.qa;

import com.kbao.kbcchatbot.maas.chatsession.enums.ChatAnswerSourceEnum;
import lombok.Data;

@Data
public class ChatQueryAnswerRespVO {

    private String recordId;

    private String recordStartTime;

    private String type;

    private String title;
    /**
     * 答案ID
     */
    private String answerId;
    /**
     * 答案
     */
    private Object answer;
    /**
     * 知识来源
     * @see ChatAnswerSourceEnum
     */
    private String qaSource;
    /**
     * 关联知识ID
     */
    private String relatedArticles;
    /**
     * 关联知识标题
     */
    private String relatedArticlesName;

    /**
     * 对话场景ID
     */
    private String sceneId;

    /**
     * 场景对话展示类型
     */
    private Integer sceneShowType;

}
