package com.kbao.kbcchatbot.maas.chatsession.bean.qa;

import lombok.Data;

import java.util.Map;

@Data
public class ChatQueryAnswerReqVO {

    private String bizType;

    private String bizId;

    private String question;

    private String payload;

    private String clearRecordId;

    private String qaSource;

    private String qaId;
    /**
     * 消息附带参数
     */
    private Map<String,Object> slots;
    /**
     * 场景ID
     */
    private String sceneId;
    /**
     * 场景对话类型  0、普通文本 1、订单信息 2、图片 3、表情包
     */
    private Integer sceneShowType;
    /**
     * 消息结构数据
     */
    private Map<String,Object> fieldData;

}
