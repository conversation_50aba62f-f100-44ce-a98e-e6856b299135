<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.robot.robotknowledge.dao.RobotKnowledgeDataMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.robot.robotknowledge.entity.RobotKnowledgeData">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="robotCode" jdbcType="VARCHAR"  column="robot_code" />  
        <result property="itemId" jdbcType="VARCHAR"  column="item_id" />  
        <result property="projectCode" jdbcType="VARCHAR"  column="project_code" />  
        <result property="projectItemId" jdbcType="VARCHAR"  column="project_item_id" />  
        <result property="knowledgeId" jdbcType="VARCHAR"  column="knowledge_id" />  
        <result property="title" jdbcType="VARCHAR"  column="title" />  
        <result property="dataType" jdbcType="VARCHAR"  column="data_type" />  
        <result property="contentLength" jdbcType="INTEGER"  column="content_length" />  
        <result property="status" jdbcType="CHAR"  column="status" />  
        <result property="syncStatus" jdbcType="CHAR"  column="sync_status" />  
        <result property="isCheck" jdbcType="CHAR"  column="is_check" />  
        <result property="mergeItemId" jdbcType="VARCHAR"  column="merge_item_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="syncTime" jdbcType="TIMESTAMP"  column="sync_time" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="isDelete" jdbcType="CHAR"  column="is_delete" />
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		robot_code,  
		item_id,  
		project_code,  
		project_item_id,  
		knowledge_id,  
		title,  
		data_type,  
		content_length,  
		status,  
		sync_status,  
		is_check,  
		merge_item_id,  
		create_time,  
		sync_time,  
		update_time,  
		is_delete
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.robot_code, 
		t.item_id, 
		t.project_code, 
		t.project_item_id, 
		t.knowledge_id, 
		t.title, 
		t.data_type, 
		t.content_length, 
		t.status, 
		t.sync_status, 
		t.is_check, 
		t.merge_item_id, 
		t.create_time, 
		t.sync_time, 
		t.update_time, 
		t.is_delete
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="robotCode != null and robotCode != ''">
	   		and t.robot_code = #{robotCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="itemId != null and itemId != ''">
	   		and t.item_id = #{itemId,jdbcType=VARCHAR}  
	    </if>
	    <if test="projectCode != null and projectCode != ''">
	   		and t.project_code = #{projectCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="projectItemId != null and projectItemId != ''">
	   		and t.project_item_id = #{projectItemId,jdbcType=VARCHAR}  
	    </if>
	    <if test="knowledgeId != null and knowledgeId != ''">
	   		and t.knowledge_id = #{knowledgeId,jdbcType=VARCHAR}  
	    </if>
	    <if test="title != null and title != ''">
	   		and t.title = #{title,jdbcType=VARCHAR}  
	    </if>
	    <if test="dataType != null and dataType != ''">
	   		and t.data_type = #{dataType,jdbcType=VARCHAR}  
	    </if>
	    <if test="contentLength != null">
	   		and t.content_length = #{contentLength,jdbcType=INTEGER}  
	    </if>
	    <if test="status != null">
	   		and t.status = #{status,jdbcType=CHAR}  
	    </if>
	    <if test="syncStatus != null">
	   		and t.sync_status = #{syncStatus,jdbcType=CHAR}  
	    </if>
	    <if test="isCheck != null">
	   		and t.is_check = #{isCheck,jdbcType=CHAR}  
	    </if>
	    <if test="mergeItemId != null and mergeItemId != ''">
	   		and t.merge_item_id = #{mergeItemId,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="syncTime != null">
	   		and t.sync_time = #{syncTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="isDelete != null">
	   		and t.is_delete = #{isDelete,jdbcType=CHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_knowledge_data t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_knowledge_data t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_knowledge_data
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_knowledge_data
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.robot.robotknowledge.entity.RobotKnowledgeData">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_knowledge_data(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{robotCode,jdbcType=VARCHAR}, 
                 
                #{itemId,jdbcType=VARCHAR}, 
                 
                #{projectCode,jdbcType=VARCHAR}, 
                 
                #{projectItemId,jdbcType=VARCHAR}, 
                 
                #{knowledgeId,jdbcType=VARCHAR}, 
                 
                #{title,jdbcType=VARCHAR}, 
                 
                #{dataType,jdbcType=VARCHAR}, 
                 
                #{contentLength,jdbcType=INTEGER}, 
                 
                #{status,jdbcType=CHAR}, 
                 
                #{syncStatus,jdbcType=CHAR}, 
                 
                #{isCheck,jdbcType=CHAR}, 
                 
                #{mergeItemId,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{syncTime,jdbcType=TIMESTAMP}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{isDelete,jdbcType=CHAR}
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.robot.robotknowledge.entity.RobotKnowledgeData">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_knowledge_data
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="robotCode != null ">  
	       		robot_code,
	        </if>  
	        <if test="itemId != null ">  
	       		item_id,
	        </if>  
	        <if test="projectCode != null ">  
	       		project_code,
	        </if>  
	        <if test="projectItemId != null ">  
	       		project_item_id,
	        </if>  
	        <if test="knowledgeId != null ">  
	       		knowledge_id,
	        </if>  
	        <if test="title != null ">  
	       		title,
	        </if>  
	        <if test="dataType != null ">  
	       		data_type,
	        </if>  
	        <if test="contentLength != null ">  
	       		content_length,
	        </if>  
	        <if test="status != null ">  
	       		status,
	        </if>  
	        <if test="syncStatus != null ">  
	       		sync_status,
	        </if>  
	        <if test="isCheck != null ">  
	       		is_check,
	        </if>  
	        <if test="mergeItemId != null ">  
	       		merge_item_id,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="syncTime != null ">  
	       		sync_time,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="isDelete != null ">  
	       		is_delete,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="robotCode != null">  
            	#{robotCode,jdbcType=VARCHAR},
            </if>  
            <if test="itemId != null">  
            	#{itemId,jdbcType=VARCHAR},
            </if>  
            <if test="projectCode != null">  
            	#{projectCode,jdbcType=VARCHAR},
            </if>  
            <if test="projectItemId != null">  
            	#{projectItemId,jdbcType=VARCHAR},
            </if>  
            <if test="knowledgeId != null">  
            	#{knowledgeId,jdbcType=VARCHAR},
            </if>  
            <if test="title != null">  
            	#{title,jdbcType=VARCHAR},
            </if>  
            <if test="dataType != null">  
            	#{dataType,jdbcType=VARCHAR},
            </if>  
            <if test="contentLength != null">  
            	#{contentLength,jdbcType=INTEGER},
            </if>  
            <if test="status != null">  
            	#{status,jdbcType=CHAR},
            </if>  
            <if test="syncStatus != null">  
            	#{syncStatus,jdbcType=CHAR},
            </if>  
            <if test="isCheck != null">  
            	#{isCheck,jdbcType=CHAR},
            </if>  
            <if test="mergeItemId != null">  
            	#{mergeItemId,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="syncTime != null">  
            	#{syncTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="isDelete != null">  
            	#{isDelete,jdbcType=CHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.robot.robotknowledge.entity.RobotKnowledgeData">
		update t_knowledge_data
		<set>
	        <if test="robotCode != null ">  
	        	robot_code = #{robotCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="itemId != null ">  
	        	item_id = #{itemId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="projectCode != null ">  
	        	project_code = #{projectCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="projectItemId != null ">  
	        	project_item_id = #{projectItemId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="knowledgeId != null ">  
	        	knowledge_id = #{knowledgeId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="title != null ">  
	        	title = #{title,jdbcType=VARCHAR},  
	        </if>  
	        <if test="dataType != null ">  
	        	data_type = #{dataType,jdbcType=VARCHAR},  
	        </if>  
	        <if test="contentLength != null ">  
	        	content_length = #{contentLength,jdbcType=INTEGER},  
	        </if>  
	        <if test="status != null ">  
	        	status = #{status,jdbcType=CHAR},  
	        </if>  
	        <if test="syncStatus != null ">  
	        	sync_status = #{syncStatus,jdbcType=CHAR},  
	        </if>  
	        <if test="isCheck != null ">  
	        	is_check = #{isCheck,jdbcType=CHAR},  
	        </if>  
	        <if test="mergeItemId != null ">  
	        	merge_item_id = #{mergeItemId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="syncTime != null ">  
	        	sync_time = #{syncTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="isDelete != null ">  
	        	is_delete = #{isDelete,jdbcType=CHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.robot.robotknowledge.entity.RobotKnowledgeData">
		update t_knowledge_data
		set
           robot_code = #{robotCode,jdbcType=VARCHAR},
           item_id = #{itemId,jdbcType=VARCHAR},
           project_code = #{projectCode,jdbcType=VARCHAR},
           project_item_id = #{projectItemId,jdbcType=VARCHAR},
           knowledge_id = #{knowledgeId,jdbcType=VARCHAR},
           title = #{title,jdbcType=VARCHAR},
           data_type = #{dataType,jdbcType=VARCHAR},
           content_length = #{contentLength,jdbcType=INTEGER},
           status = #{status,jdbcType=CHAR},
           sync_status = #{syncStatus,jdbcType=CHAR},
           is_check = #{isCheck,jdbcType=CHAR},
           merge_item_id = #{mergeItemId,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           sync_time = #{syncTime,jdbcType=TIMESTAMP},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           is_delete = #{isDelete,jdbcType=CHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_knowledge_data(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.robotCode != null">,#{item.robotCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.itemId != null">,#{item.itemId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.projectCode != null">,#{item.projectCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.projectItemId != null">,#{item.projectItemId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.knowledgeId != null">,#{item.knowledgeId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.title != null">,#{item.title}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.dataType != null">,#{item.dataType}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.contentLength != null">,#{item.contentLength}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.syncStatus != null">,#{item.syncStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isCheck != null">,#{item.isCheck}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.mergeItemId != null">,#{item.mergeItemId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.syncTime != null">,#{item.syncTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isDelete != null">,#{item.isDelete}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_knowledge_data(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.robotCode != null">,#{item.robotCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.itemId != null">,#{item.itemId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.projectCode != null">,#{item.projectCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.projectItemId != null">,#{item.projectItemId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.knowledgeId != null">,#{item.knowledgeId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.title != null">,#{item.title}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.dataType != null">,#{item.dataType}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.contentLength != null">,#{item.contentLength}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.syncStatus != null">,#{item.syncStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isCheck != null">,#{item.isCheck}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.mergeItemId != null">,#{item.mergeItemId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.syncTime != null">,#{item.syncTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isDelete != null">,#{item.isDelete}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   robot_code=values(robot_code), 
		   item_id=values(item_id), 
		   project_code=values(project_code), 
		   project_item_id=values(project_item_id), 
		   knowledge_id=values(knowledge_id), 
		   title=values(title), 
		   data_type=values(data_type), 
		   content_length=values(content_length), 
		   status=values(status), 
		   sync_status=values(sync_status), 
		   is_check=values(is_check), 
		   merge_item_id=values(merge_item_id), 
		   create_time=values(create_time), 
		   sync_time=values(sync_time), 
		   update_time=values(update_time), 
		   is_delete=values(is_delete)
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_knowledge_data where id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>

	<!-- 自定义查询 -->
	<select id="getKnowledgeDataList" resultType="com.kbao.kbcchatbot.maas.robot.robotknowledge.bean.RobotKnowledgeDataPageRespVo">
		select * from (
			select d.directory_code directoryCode, d.robot_code robotCode, d.knowledge_id knowledgeId,
			d.title title, sum(d.content_length) contentLength,
			case when SUM(if(d.sync_status = 1, 1, 0)) > 0 THEN '1'
				when SUM(if(d.sync_status = 2, 1, 0)) > 0 THEN '2'
				else '0' end as syncStatus,
			max(d.create_time) syncTime
			from t_knowledge_data d
			where d.is_delete = '0' and d.status != '2'
			<if test="robotCode != null and robotCode != ''">
				and d.robot_code = #{robotCode}
			</if>
			<if test="dataType != null and dataType != ''">
				and d.data_type = #{dataType}
			</if>
			<if test="directoryCode != null and directoryCode != ''">
				and d.directory_code = #{directoryCode}
			</if>
			<if test="knowledgeId != null and knowledgeId != ''">
				and d.knowledge_id = #{knowledgeId}
			</if>
			<if test="title != null and title != ''">
				and instr(d.title, #{title})
			</if>
			group by d.knowledge_id
		) t
		order by t.syncTime desc
	</select>

	<!-- 自定义查询 -->
	<select id="getChapterDataList" resultType="com.kbao.kbcchatbot.maas.robot.robotknowledge.bean.RobotKnowledgeDataPageRespVo">
		select d.id id, d.knowledge_id knowledgeId, d.item_id itemId,
		       ifnull(d.file_name, d.title) title, d.data_type dataType, d.content_length contentLength, d.status status,
			   d.sync_status syncStatus, d.create_time syncTime, l.message message
		from t_knowledge_data d
		join t_robot r on d.robot_code = r.robot_code
		left join t_knowledge_sync_log l on l.item_id = d.item_id and d.robot_code = l.robot_code
		where d.is_delete = '0' and d.status != '2'
		<if test="robotCode != null and robotCode != ''">
			and d.robot_code = #{robotCode}
		</if>
		<if test="dataType != null and dataType != ''">
			and d.data_type = #{dataType}
		</if>
		<if test="directoryCode != null and directoryCode != ''">
		    and d.directory_code = #{directoryCode}
		</if>
		<if test="status != null and status != ''">
			and d.status = #{status}
		</if>
		<if test="syncStatus != null and syncStatus != ''">
			and d.sync_status = #{syncStatus}
		</if>
		<if test="knowledgeId != null and knowledgeId != ''">
			and d.knowledge_id = #{knowledgeId}
		</if>
		<if test="title != null and title != ''">
			and instr(d.title, #{title})
		</if>
		order by d.create_time desc
	</select>

	<!--更新目录编码-->
	<update id="updateDirectoryCodeByKnowledgeIds" parameterType="java.util.Map">
		update t_knowledge_data
		set
			directory_code = #{directoryCode,jdbcType=VARCHAR}
		where knowledge_id in(
		    <foreach collection="knowledgeIds" item="item" index="index" separator=",">
				#{item}
			</foreach>
			)
	</update>

	<select id="hasUnfinishedKnowledgeData" resultType="int">
		select count(1) from t_knowledge_data where knowledge_id = #{knowledgeId}
		                                        and robot_code = #{robotCode} and sync_status = '2'
	</select>
</mapper>
