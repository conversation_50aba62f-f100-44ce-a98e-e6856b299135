package com.kbao.kbcchatbot.maas.train.scene.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-03-19
*/
@Data
public class TrainScene implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中scene_code
     * 场景编码
     */  
	private String sceneCode;

	private String sceneName;

    /**
     * 对应表中robot_name
     * 智能体名称
     */  
	private String robotName;

    /**
     * 对应表中robot_image
     * 智能体头像
     */  
	private String robotImage;

    /**
     * 对应表中robot_info
     * 智能体身份信息
     */  
	private String robotInfo;

    /**
     * 对应表中scene_prompt
     * 场景提示词
     */  
	private String scenePrompt;

    /**
     * 对应表中score_prompt
     * 评分提示词
     */  
	private String scorePrompt;

    /**
     * 对应表中memory_cache_num
     * 历史记录缓存条数
     */  
	private Integer memoryCacheNum;

    /**
     * 对应表中model_id
     * 模型ID
     */  
	private Integer modelId;

	private Integer scoreModelId;

    /**
     * 对应表中pass_score
     * 及格分数
     */  
	private Integer passScore;

	private String standardCaseImage;

	private String voiceStatus;
	/**
	 * 是否开启语音播报 0-否  1-是
	 */
	private String voiceSynthesis;
	/**
	 * 语音播报音色
	 */
	private String voiceSpeaker;

	private String uploadFileStatus;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_by
     * 创建人
     */  
	private String createBy;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_by
     * 更新人
     */  
	private String updateBy;

    /**
     * 对应表中is_delete
     * 是否删除，0-否，1-是
     */  
	private String isDelete;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   