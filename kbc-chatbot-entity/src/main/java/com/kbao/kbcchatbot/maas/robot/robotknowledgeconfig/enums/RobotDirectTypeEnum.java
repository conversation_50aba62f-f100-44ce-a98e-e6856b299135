package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums;

import lombok.Getter;

/**
 * @program: kbc-chatbot
 * @description: 机器人绑定类型
 * @author: husw
 * @create: 2023-05-29 11:06
 **/
@Getter
public enum RobotDirectTypeEnum {

    PROJECT("1","项目"),
    FOLDER("2","知识库夹"),
    FIRST("3","一级目录"),
    SECOND("4","二级目录");

    private String code;

    private String value;

    RobotDirectTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }
}
