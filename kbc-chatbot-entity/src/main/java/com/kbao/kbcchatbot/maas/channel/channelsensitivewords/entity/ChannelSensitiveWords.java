package com.kbao.kbcchatbot.maas.channel.channelsensitivewords.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR>
* @Description maas渠道敏感词配置实体
* @Date 2024-12-25
*/
@Data
public class ChannelSensitiveWords implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Long id;

    /**
     * 对应表中channel_id
     * 渠道主键id
     */  
	private Long channelId;

    /**
     * 对应表中channel_code
     * 渠道主键id
     */  
	private String channelCode;

    /**
     * 对应表中words
     * 关键词
     */  
	private String words;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中update_id
     * 修改人
     */  
	private String updateId;

    /**
     * 对应表中update_time
     * 修改时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

}   