package com.kbao.kbcchatbot.maas.mcp.tool.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 对应表 t_mcp_tool
 * MCP工具表
 */
@Data
public class McpTool implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应表中tool_id
     * 工具编号（自增长主键）
     */
    private Long toolId;

    /**
     * 对应表中tool_code
     * 工具编码（序列生成，唯一标识）
     */
    private String toolCode;

    /**
     * 对应表中tool_name
     * 工具名称
     */
    @NotNull(message = "toolName不能为空")
    private String toolName;

    /**
     * 对应表中tool_type
     * 工具类型
     */
    private String toolType;

    /**
     * 对应表中server_id
     * 所属服务器编号
     */
    private Long serverId;

    /**
     * 对应表中app_server_id
     * 关联应用服务器编号
     */
    private Long appServerId;

    /**
     * 对应表中api_path
     * 接口路径
     */
    private String apiPath;

    /**
     * 对应表中tool_description
     * 工具描述
     */
    private String toolDescription;

    /**
     * 对应表中input_schema
     * 输入参数结构
     */
    private String inputSchema;

    /**
     * 对应表中output_schema
     * 输出参数结构
     */
    private String outputSchema;

    /**
     * 对应表中tool_status
     * 工具状态 1启用，0禁用
     */
    private Integer toolStatus;

    /**
     * 对应表中tenant_id
     * 租户编号
     */
    private String tenantId;

    /**
     * 对应表中create_time
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 对应表中create_id
     * 创建人编号
     */
    private String createId;

    /**
     * 对应表中update_id
     * 更新人编号
     */
    private String updateId;

    /**
     * 对应表中update_time
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 对应表中is_deleted
     * 是否删除 0 未删除  1已删除
     */
    private Integer isDeleted;

}