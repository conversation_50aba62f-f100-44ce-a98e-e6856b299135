<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.channel.channelsensitivewords.dao.ChannelSensitiveWordsMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.channel.channelsensitivewords.entity.ChannelSensitiveWords">
    	<id column="id" jdbcType="BIGINT"  property="id"  />
        <result property="channelId" jdbcType="BIGINT"  column="channel_id" />  
        <result property="channelCode" jdbcType="VARCHAR"  column="channel_code" />  
        <result property="words" jdbcType="VARCHAR"  column="words" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		channel_id,  
		channel_code,  
		words,  
		create_id,  
		create_time,  
		update_id,  
		update_time  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.channel_id, 
		t.channel_code, 
		t.words, 
		t.create_id, 
		t.create_time, 
		t.update_id, 
		t.update_time 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="channelId != null">
	   		and t.channel_id = #{channelId,jdbcType=BIGINT}  
	    </if>
	    <if test="channelCode != null and channelCode != ''">
	   		and t.channel_code = #{channelCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="words != null and words != ''">
	   		and t.words = #{words,jdbcType=VARCHAR}  
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_channel_sensitive_words t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_channel_sensitive_words t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_channel_sensitive_words
		where  id = #{id,jdbcType=BIGINT}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from t_channel_sensitive_words
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.channel.channelsensitivewords.entity.ChannelSensitiveWords">
		<!-- <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_channel_sensitive_words(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=BIGINT}, 
                 
                #{channelId,jdbcType=BIGINT}, 
                 
                #{channelCode,jdbcType=VARCHAR}, 
                 
                #{words,jdbcType=VARCHAR}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelsensitivewords.entity.ChannelSensitiveWords">
		<selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_channel_sensitive_words
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="channelId != null ">  
	       		channel_id,
	        </if>  
	        <if test="channelCode != null ">  
	       		channel_code,
	        </if>  
	        <if test="words != null ">  
	       		words,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=BIGINT},
            </if>  
            <if test="channelId != null">  
            	#{channelId,jdbcType=BIGINT},
            </if>  
            <if test="channelCode != null">  
            	#{channelCode,jdbcType=VARCHAR},
            </if>  
            <if test="words != null">  
            	#{words,jdbcType=VARCHAR},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelsensitivewords.entity.ChannelSensitiveWords">
		update t_channel_sensitive_words
		<set>
	        <if test="channelId != null ">  
	        	channel_id = #{channelId,jdbcType=BIGINT},  
	        </if>  
	        <if test="channelCode != null ">  
	        	channel_code = #{channelCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="words != null ">  
	        	words = #{words,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.channel.channelsensitivewords.entity.ChannelSensitiveWords">
		update t_channel_sensitive_words
		set
           channel_id = #{channelId,jdbcType=BIGINT},
           channel_code = #{channelCode,jdbcType=VARCHAR},
           words = #{words,jdbcType=VARCHAR},
           create_id = #{createId,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           update_id = #{updateId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_channel_sensitive_words(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.channelCode != null">,#{item.channelCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.words != null">,#{item.words}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_channel_sensitive_words(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.channelId != null">,#{item.channelId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.channelCode != null">,#{item.channelCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.words != null">,#{item.words}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   channel_id=values(channel_id), 
		   channel_code=values(channel_code), 
		   words=values(words), 
		   create_id=values(create_id), 
		   create_time=values(create_time), 
		   update_id=values(update_id), 
		   update_time=values(update_time) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_channel_sensitive_words where id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>
	
	<!-- 自定义查询 -->
	<select id="selectCountByWords" parameterType="java.lang.Object" resultType="java.lang.Integer">
		select count(*)
		from t_channel_sensitive_words
		where channel_code = #{channelCode,jdbcType=VARCHAR}
		  and words = #{words,jdbcType=VARCHAR}
	</select>
</mapper>
