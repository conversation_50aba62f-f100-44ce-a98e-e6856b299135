package com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean;

import com.kbao.kbcchatbot.common.bean.ChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard;
import org.springframework.beans.BeanUtils;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> qiuzb
 * @Description: 实体转VO
 * @create 2023/6/15 14:02
 */
public class ChannelCommonCardToVOChatBotBeanConverter implements ChatBotBeanConverter<ChannelCommonCard, ChannelCommonCardVO> {
    @Override
    public ChannelCommonCardVO convert(ChannelCommonCard source) {
        ChannelCommonCardVO target = new ChannelCommonCardVO();
        // 复制源对象的属性到目标对象
        BeanUtils.copyProperties(source, target);
        // 把源对象的content属性从byte[]类型转换成String类型，并设置到目标对象
        if (source.getContent() != null){
            target.setContent(new String(source.getContent(), StandardCharsets.UTF_8));
        }
        // 返回目标对象
        return target;
    }
}
