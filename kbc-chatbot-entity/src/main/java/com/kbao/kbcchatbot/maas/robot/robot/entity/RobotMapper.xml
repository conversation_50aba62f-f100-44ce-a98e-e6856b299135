<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.robot.robot.dao.RobotMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.robot.robot.entity.Robot">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="robotCode" jdbcType="VARCHAR"  column="robot_code" />  
        <result property="robotName" jdbcType="VARCHAR"  column="robot_name" />  
        <result property="robotType" jdbcType="CHAR"  column="robot_type" />  
        <result property="similarityThreshold" jdbcType="DECIMAL"  column="similarity_threshold" />  
        <result property="retrievalThreshold" jdbcType="INTEGER"  column="retrieval_threshold" />
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateBy" jdbcType="VARCHAR"  column="update_by" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		robot_code,  
		robot_name,  
		robot_type,  
		similarity_threshold,  
		retrieval_threshold,  
		create_time,  
		create_by,  
		update_time,  
		update_by,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.robot_code, 
		t.robot_name, 
		t.robot_type, 
		t.similarity_threshold, 
		t.retrieval_threshold, 
		t.create_time, 
		t.create_by, 
		t.update_time, 
		t.update_by, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
		t.is_delete = '0'
	    <if test="robotCode != null and robotCode != ''">
	   		and t.robot_code = #{robotCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="robotName != null and robotName != ''">
	   		and t.robot_name = #{robotName,jdbcType=VARCHAR}  
	    </if>
	    <if test="robotType != null">
	   		and t.robot_type = #{robotType,jdbcType=CHAR}  
	    </if>
	    <if test="similarityThreshold != null">
	   		and t.similarity_threshold = #{similarityThreshold,jdbcType=DECIMAL}  
	    </if>
	    <if test="retrievalThreshold != null">
	   		and t.retrieval_threshold = #{retrievalThreshold,jdbcType=INTEGER}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateBy != null and updateBy != ''">
	   		and t.update_by = #{updateBy,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_robot t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_robot t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_robot
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		update t_robot set is_delete = '1'
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.robot.robot.entity.Robot">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_robot(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{robotCode,jdbcType=VARCHAR}, 
                 
                #{robotName,jdbcType=VARCHAR}, 
                 
                #{robotType,jdbcType=CHAR}, 
                 
                #{similarityThreshold,jdbcType=DECIMAL}, 
                 
                #{retrievalThreshold,jdbcType=INTEGER},
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{createBy,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{updateBy,jdbcType=VARCHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.robot.robot.entity.Robot">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_robot
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="robotCode != null ">  
	       		robot_code,
	        </if>  
	        <if test="robotName != null ">  
	       		robot_name,
	        </if>  
	        <if test="robotType != null ">  
	       		robot_type,
	        </if>  
	        <if test="similarityThreshold != null ">  
	       		similarity_threshold,
	        </if>  
	        <if test="retrievalThreshold != null ">  
	       		retrieval_threshold,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createBy != null ">  
	       		create_by,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="updateBy != null ">  
	       		update_by,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=INTEGER},
            </if>  
            <if test="robotCode != null">  
            	#{robotCode,jdbcType=VARCHAR},
            </if>  
            <if test="robotName != null">  
            	#{robotName,jdbcType=VARCHAR},
            </if>  
            <if test="robotType != null">  
            	#{robotType,jdbcType=CHAR},
            </if>  
            <if test="similarityThreshold != null">  
            	#{similarityThreshold,jdbcType=DECIMAL},
            </if>  
            <if test="retrievalThreshold != null">  
            	#{retrievalThreshold,jdbcType=INTEGER},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="createBy != null">  
            	#{createBy,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateBy != null">  
            	#{updateBy,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.robot.robot.entity.Robot">
		update t_robot
		<set>
	        <if test="robotCode != null ">  
	        	robot_code = #{robotCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="robotName != null ">  
	        	robot_name = #{robotName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="robotType != null ">  
	        	robot_type = #{robotType,jdbcType=CHAR},  
	        </if>  
	        <if test="similarityThreshold != null ">  
	        	similarity_threshold = #{similarityThreshold,jdbcType=DECIMAL},  
	        </if>  
	        <if test="retrievalThreshold != null ">  
	        	retrieval_threshold = #{retrievalThreshold,jdbcType=INTEGER},
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createBy != null ">  
	        	create_by = #{createBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateBy != null ">  
	        	update_by = #{updateBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.robot.robot.entity.Robot">
		update t_robot
		set
           robot_code = #{robotCode,jdbcType=VARCHAR},
           robot_name = #{robotName,jdbcType=VARCHAR},
           robot_type = #{robotType,jdbcType=CHAR},
           similarity_threshold = #{similarityThreshold,jdbcType=DECIMAL},
           retrieval_threshold = #{retrievalThreshold,jdbcType=INTEGER},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           create_by = #{createBy,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           update_by = #{updateBy,jdbcType=VARCHAR},
           tenant_id = #{tenantId,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_robot(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.robotCode != null">,#{item.robotCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.robotName != null">,#{item.robotName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.robotType != null">,#{item.robotType}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.similarityThreshold != null">,#{item.similarityThreshold}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.retrievalThreshold != null">,#{item.retrievalThreshold}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createBy != null">,#{item.createBy}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateBy != null">,#{item.updateBy}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_robot(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.robotCode != null">,#{item.robotCode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.robotName != null">,#{item.robotName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.robotType != null">,#{item.robotType}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.similarityThreshold != null">,#{item.similarityThreshold}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.retrievalThreshold != null">,#{item.retrievalThreshold}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createBy != null">,#{item.createBy}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateBy != null">,#{item.updateBy}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   robot_code=values(robot_code), 
		   robot_name=values(robot_name), 
		   robot_type=values(robot_type), 
		   similarity_threshold=values(similarity_threshold), 
		   retrieval_threshold=values(retrieval_threshold), 
		   create_time=values(create_time), 
		   create_by=values(create_by), 
		   update_time=values(update_time), 
		   update_by=values(update_by), 
		   tenant_id=values(tenant_id) 
	</update>

	<!-- 自定义查询 -->
	<!-- 根据code查询-->
	<select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_robot
		where robot_code = #{robotCode,jdbcType=VARCHAR}
		and is_delete = '0'
	</select>

	<select id="getRobotList" resultMap="BaseResultMap">
		select
		<include refid="Alias_Column_List" />
		from t_robot t
		<where>
			t.is_delete = '0'
			<if test="robotName != null and robotName != ''">
				and t.robot_name like concat("%",#{robotName},"%")
			</if>
			<if test="robotCode != null and robotCode != ''">
				and t.robot_code = #{robotCode,jdbcType=VARCHAR}
			</if>
			<if test="robotType != null">
				and t.robot_type = #{robotType,jdbcType=CHAR}
			</if>
			<if test="tenantId != null and tenantId != ''">
				and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
			</if>
		</where>
	</select>

	<select id="getSimpleRobotList" resultType="com.kbao.kbcchatbot.maas.robot.robot.bean.RobotSimpleVO">
		select  t.id id,t.robot_type robotType,t.robot_code robotCode,t.robot_name robotName from t_robot t
		<where>
			t.is_delete = '0'
			<if test="robotType != null">
				and t.robot_type = #{robotType,jdbcType=CHAR}
			</if>
			<if test="tenantId != null and tenantId != ''">
				and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
			</if>
		</where>
		order by t.robot_type asc
	</select>

	<select id="getRobotBySessionId" resultType="com.kbao.kbcchatbot.maas.robot.robot.bean.RobotSimpleVO">
		select t.id id,t.robot_type robotType,t.robot_code robotCode,t.robot_name robotName
		from t_robot t
		left join t_chat_session s on t.robot_code = s.robot_code
		where s.session_id = #{sessionId} and t.is_delete = '0'
	</select>
</mapper>
