package com.kbao.kbcchatbot.maas.train.score.entity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.kbao.commons.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-03-19
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrainScore implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中scene_code
     * 场景编码
     */
	@Excel(name = "场景编码")
	private String sceneCode;

    /**
     * 对应表中user_id
     * 用户ID
     */
	@Excel(name = "用户ID")
	private String userId;

    /**
     * 对应表中user_name
     * 用户名
     */
	@Excel(name = "用户名")
	private String userName;

    /**
     * 对应表中source
     * 来源-dac
     */
	@Excel(name = "来源")
	private String source;

    /**
     * 对应表中score
     * 分数
     */
	@Excel(name = "分数")
	private BigDecimal score;

    /**
     * 对应表中is_pass
     * 是否及格，0-否，1-是
     */
	@Excel(name = "是否及格")
	private String isPass;

    /**
     * 对应表中external_id
     * 关联外部ID
     */
	@Excel(name = "关联外部ID")
	private String externalId;

	private String hasRecord;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中create_by
     * 创建人
     */  
	private String createBy;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_by
     * 更新人
     */  
	private String updateBy;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   