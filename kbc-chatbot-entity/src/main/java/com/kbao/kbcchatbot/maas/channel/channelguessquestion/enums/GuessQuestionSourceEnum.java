package com.kbao.kbcchatbot.maas.channel.channelguessquestion.enums;

import lombok.Getter;

/**
 * GuessQuestionSourceEnum
 * Created 2024-01/29
 * &#064;Description:  猜你想问来源类型
 * &#064;Version:  1.0
 */
@Getter
public enum GuessQuestionSourceEnum {

    /**
     * 通用 01
     */
    COMMON("01", "通用"),

    /**
     * 订单 02
     */
    ORDER("02", "订单");

    private final String key;

    private final String value;

    GuessQuestionSourceEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    /**
     * 根据key获取value
     */
    public static String getValueByKey(String key) {
        for (GuessQuestionSourceEnum e : GuessQuestionSourceEnum.values()) {
            if (e.getKey().equals(key)) {
                return e.getValue();
            }
        }
        return null;
    }

    /**
     * 根据value获取key
     */
    public static String getKeyByValue(String value) {
        for (GuessQuestionSourceEnum e : GuessQuestionSourceEnum.values()) {
            if (e.getValue().equals(value)) {
                return e.getKey();
            }
        }
        return null;
    }

}
