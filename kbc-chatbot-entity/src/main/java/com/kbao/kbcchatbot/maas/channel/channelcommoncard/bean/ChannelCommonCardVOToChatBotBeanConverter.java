package com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean;

import com.kbao.kbcchatbot.common.bean.ChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard;
import org.springframework.beans.BeanUtils;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> qiuzb
 * @Description: VO转Mapper类
 * @create 2023/6/15 13:55
 */
public class ChannelCommonCardVOToChatBotBeanConverter implements ChatBotBeanConverter<ChannelCommonCardVO, ChannelCommonCard> {

    @Override
    public ChannelCommonCard convert(ChannelCommonCardVO source) {
        // 创建一个ChannelCommonCard对象
        ChannelCommonCard target = new ChannelCommonCard();
        // 复制源对象的属性到目标对象
        BeanUtils.copyProperties(source, target);
        // 把源对象的content属性从String类型转换成byte[]类型，并设置到目标对象
        if (source.getContent() != null && !source.getContent().isEmpty()){
            target.setContent(source.getContent().getBytes(StandardCharsets.UTF_8));
        }
        // 返回目标对象
        return target;
    }
}
