package com.kbao.kbcchatbot.maas.mcp.log.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * MCP调用日志MongoDB实体类
 * 对应MongoDB集合 mcp_call_log
 */
@Data
@Document(collection = "mcp_call_log")
public class McpCallLogMo implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * MongoDB文档ID
     */
    @Id
    private String id;

    /**
     * 日志编号
     */
    @Field("log_id")
    private String logId;

    /**
     * 服务器编号
     */
    @Field("server_id")
    private String serverId;

    /**
     * 工具编号
     */
    @Field("tool_id")
    private String toolId;

    /**
     * 请求参数
     */
    @Field("request_params")
    private String requestParams;

    /**
     * 响应结果
     */
    @Field("response_result")
    private String responseResult;

    /**
     * 调用状态 1成功，0失败
     */
    @Field("call_status")
    private Integer callStatus;

    /**
     * 错误信息
     */
    @Field("error_message")
    private String errorMessage;

    /**
     * 调用耗时（毫秒）
     */
    @Field("call_duration")
    private Long callDuration;

    /**
     * 调用时间
     */
    @Field("call_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date callTime;

    /**
     * 用户编号
     */
    @Field("user_id")
    private String userId;

    /**
     * 租户编号
     */
    @Field("tenant_id")
    private String tenantId;

    /**
     * 用户类型
     */
    @Field("user_type")
    private String userType;

    /**
     * 映射时间
     */
    @Field("map_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mapTime;

    /**
     * 租户时间
     */
    @Field("tenant_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tenantTime;

    /**
     * 是否管理员
     */
    @Field("is_admin")
    private Boolean isAdmin;
}