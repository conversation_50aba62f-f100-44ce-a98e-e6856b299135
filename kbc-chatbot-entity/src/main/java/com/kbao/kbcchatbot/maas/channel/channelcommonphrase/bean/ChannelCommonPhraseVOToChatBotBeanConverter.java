package com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean;

import com.kbao.kbcchatbot.common.bean.ChatBotBeanConverter;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.entity.ChannelCommonPhrase;
import org.springframework.beans.BeanUtils;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> qiuzb
 * @Description: VO转Mapper
 * @create 2023/6/15 11:48
 */
public class ChannelCommonPhraseVOToChatBotBeanConverter implements ChatBotBeanConverter<ChannelCommonPhraseVO, ChannelCommonPhrase> {

    /**
     * 定义一个转换器类，用来把ChannelCommonPhrase对象转换成ChannelCommonPhraseVO对象
     * 这个类实现了Converter接口，并指定了泛型参数为ChannelCommonPhrase和ChannelCommonPhraseVO
     * 这个类重写了convert方法，并实现了具体的转换逻辑
     *
     * @param source 源对象
     * @return 目标对象
     */
    @Override
    public ChannelCommonPhrase convert(ChannelCommonPhraseVO source) {
        // 创建一个ChannelCommonPhrase对象
        ChannelCommonPhrase target = new ChannelCommonPhrase();
        // 复制源对象的属性到目标对象
        BeanUtils.copyProperties(source, target);
        // 把源对象的content属性从String类型转换成byte[]类型，并设置到目标对象
        if (source.getContent() != null && !source.getContent().isEmpty()){
            target.setContent(source.getContent().getBytes(StandardCharsets.UTF_8));
        }
        // 返回目标对象
        return target;
    }
}
