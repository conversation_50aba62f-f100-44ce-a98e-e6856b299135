package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 云知识库同步
 * @author: husw
 * @create: 2023-05-26 09:31
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = "#{@knowledgePackageCloudEsAttribute.indexName}", createIndex = false)
public class KnowledgePackageCloud implements Serializable {

    @Id
    private String id;
    /**
     * 知识ID
     */
    @Field(type = FieldType.Keyword)
    private String knowledgeId;

    /**
     * 机器人ID
     */
    @Field(type = FieldType.Keyword)
    private Long robotId;
    /**
     * 版本类型 0-测试版 1-正式版
     */
    @Field(type = FieldType.Integer)
    private Integer environment;
    /**
     * 租户id
     */
    @Field(type = FieldType.Keyword)
    private String tenantId;
    /**
     * 标题
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String title;
    /**
     * 类型: 1知识，2略树
     * 字典:km.knowledge.type
     */
    @Field(type = FieldType.Keyword)
    private String type;
    /**
     * 所属项目不能为空
     */
    @Field(type = FieldType.Keyword)
    private String projectId;
    /**
     * 所属项目名称
     */
    @Field(type = FieldType.Keyword)
    private String projectName;
    /**
     * 所属知识库夹不能为空
     */
    @Field(type = FieldType.Keyword)
    private String folderId;
    /**
     * 所属知识库夹名称
     */
    @Field(type = FieldType.Keyword)
    private String folderName;
    /**
     * 一级目录
     */
    @Field(type = FieldType.Keyword)
    private String firstDirect;
    /**
     * 一级目录名称
     */
    @Field(type = FieldType.Keyword)
    private String firstDirectName;
    /**
     * 二级目录
     */
    @Field(type = FieldType.Keyword)
    private String secondDirect;
    /**
     * 二级目录名称
     */
    @Field(type = FieldType.Keyword)
    private String secondDirectName;
    /**
     * FAQ类目id
     */
    @Field(type = FieldType.Long)
    private Long faqCategoryId;
    /**
     * 所属类型: 1公司，2产品
     * 字典：km.knowledge.belong.type
     */
    @Field(type = FieldType.Keyword)
    private String belongType;
    /**
     * 所属名称
     */
    @Field(type = FieldType.Keyword)
    private String belongName;
    /**
     * 关联公司
     */
    @Field(type = FieldType.Keyword)
    private String companyId;
    /**
     * 关联公司名称
     */
    @Field(type = FieldType.Keyword)
    private String companyName;
    /**
     * 关联产品
     */
    @Field(type = FieldType.Keyword)
    private String productId;
    /**
     * 关联产品名称
     */
    @Field(type = FieldType.Keyword)
    private String productName;
    /**
     * 关键字
     */
    @Field(type = FieldType.Keyword)
    private String keyword;
    /**
     * 关键字数量
     */
    @Field(type = FieldType.Integer)
    private Integer keywordNum;
    /**
     * 互斥文章
     */
    @Field(type = FieldType.Keyword)
    private String mutexArticle;
    /**
     * 互斥文章
     */
    @Field(type = FieldType.Keyword)
    private String mutexArticleName;
    /**
     * 相似问题
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String similarQuestion;
    /**
     * 相似问集合
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private List<String> similarQuestions;
    /**
     * 关联文章
     */
    @Field(type = FieldType.Keyword)
    private String relatedArticles;
    /**
     * 内容
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String subContent;
    /**
     * 历程图章节
     */
    @Field(type = FieldType.Nested)
    private List<KnowledgeChapter> chapters;
    /**
     * 封面
     */
    @Field(type = FieldType.Keyword)
    private String cover;
    /**
     * 状态：1待发布，2已发布
     * 字典：km.knowledge.state
     */
    @Field(type = FieldType.Keyword)
    private String state;
    /**
     * 浏览量
     */
    @Field(type = FieldType.Integer)
    private Integer readingVolume;
    /**
     * 点赞数
     */
    @Field(type = FieldType.Integer)
    private Integer praiseVolume;
    /**
     * 评论数
     */
    @Field(type = FieldType.Integer)
    private Integer commentVolume;
    /**
     * 点赞状态,1已点赞，0未点赞
     */
    @Field(type = FieldType.Integer)
    private Integer praiseState;
    /**
     * 置顶：0未置顶，1已置顶
     */
    @Field(type = FieldType.Integer)
    private Integer top;
    /**
     * 置顶时间
     */
    @Field(type = FieldType.Date)
    private Date topTime;
    /**
     * 是否可编辑，1为可编辑，0为不可编辑
     */
    @Field(type = FieldType.Integer)
    private Integer editable;
    /**
     * 允许分享，1可分享，0不可享
     */
    @Field(type = FieldType.Integer)
    private Integer shareable;
    /**
     * "是共享，1是，0否
     */
    @Field(type = FieldType.Integer)
    private Integer isShare;
    /**
     * 来源知识库
     */
    @Field(type = FieldType.Keyword)
    private String sourceFolder;
    /**
     * FAQ知识id
     */
    @Field(type = FieldType.Long)
    private Long faqKnowledgeId;
    /**
     * 知识通知配置id集合
     */
    private List<String> configIds;
    /**
     * 创建人id
     */
    @Field(type = FieldType.Keyword)
    private String createId;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Date)
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    /**
     * 更新人id
     */
    @Field(type = FieldType.Keyword)
    private String updateId;
    /**
     * 更新人姓名
     */
    @Field(type = FieldType.Keyword)
    private String updateName;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Date)
    private Date updateTime;
    /**
     * 删除状态：0未删除，1删除
     */
    @Field(type = FieldType.Integer)
    private Integer isDeleted;

    /**
     * 获取云知识QA答案
     * @return
     */
    @Transient
    public String getAnswer() {
        if(CollectionUtils.isEmpty(chapters)) {
            return subContent;
        }else {
            return chapters.get(0).getChapterContent();
        }
    }

}
