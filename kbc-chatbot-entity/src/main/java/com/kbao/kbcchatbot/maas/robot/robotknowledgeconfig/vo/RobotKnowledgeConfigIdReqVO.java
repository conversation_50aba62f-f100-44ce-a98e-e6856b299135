package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @program: kbc-chatbot
 * @description:
 * @author: husw
 * @create: 2023-05-17 11:43
 **/
@Data
public class RobotKnowledgeConfigIdReqVO {

    private Long robotId;

    @NotNull(message = "机器人CODE不能为空！")
    private String robotCode;
    /**
     * 类型 0-测试版 1-正式版
     */
    @NotNull(message = "版本类型不能为空！")
    private Integer type;

    /**
     * 知识类型 1-QA 2-其它知识
     */
//    @NotNull(message = "知识类型不能为空！")
    private String knowledgeType;

    /**
     * 一级目录ID
     */
    private String firstDirectId;
    /**
     * 二级目录ID
     */
    private String directoryId;

    private Integer operationNoEq;

    /**
     * 知识目录名称
     */
    private String directoryName;
    /**
     * 操作人
     */
    private String updateId;
}
