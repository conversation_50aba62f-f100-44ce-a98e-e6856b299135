package com.kbao.kbcchatbot.maas.chatsession.enums;

public enum ChatAnswerTypeEnum {

    KNOWLEDGE_ANSWER("0","QA直接回复"),

    DIALOGUE_ANSWER("2","场景对话"),

    WELCOME_ANSWER("1","欢迎语"),
    EMOJI_ANSWER("3","表情包回复"),

    CUSTOMER_SERVICE_ANSWER("4","人工客服回复"),
    QUESTION_CLEAR("9","问题澄清"),
    QUESTION_CLEAR_ANSWER("12","问题澄清回复"),
    NO_ANSWER("10","无回复"),
    SENSITIVE_WORD_ANSWER("11","敏感词"),
    TRANSFER_LABOR("13","转人工"),
    CUSTOMER_ANSWER("14","客户回复"),

    COMMON_CARD("15","常用卡片回复"),

    GUESS_QUESTION("16","猜你想问"),

    CUSTOMER_LEAVING_MESSAGE("17","客户留言"),
    MODEL_MESSAGE("18","大模型回复"),
    ;
    private String key;

    private String value;

    ChatAnswerTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String findNameByKey(String key) {
        for (ChatAnswerTypeEnum e : ChatAnswerTypeEnum.values()) {
            if (e.getKey().equals(key)) {
                return e.getValue();
            }
        }
        return null;
    }

    public String getKey() {
        return this.key;
    }

    public String getValue() {
        return this.value;
    }

}
