package com.kbao.kbcchatbot.maas.chatsession.entity;
import java.io.Serializable;
import lombok.Data;

/**
* <AUTHOR>
* @Description 聊天会话实体
* @Date 2023-06-06
*/
@Data
public class ChatSession implements Serializable{
	private static final long serialVersionUID = 1L;

	private Long id;
 
    /**
     * 对应表中session_id
     * 主键
     */  
	private String sessionId;

    /**
     * 对应表中robot_id
     * 机器人主键
     */  
	private Long robotId;

	/**
	 * 对应表中robot_code
	 */
	private String robotCode;

    /**
     * 对应表中robert_version
     * 机器人版本号
     */  
	private String robertVersion;

    /**
     * 对应表中channel_id
     * 渠道主键
     */  
	private Long channelId;

	/**
	 * 对应表中channel_code
	 * 渠道编码
	 */
	private String channelCode;
    /**
     * 对应表中user_id
     * 用户ID
     */  
	private String userId;

    /**
     * 对应表中user_name
     * 用户名称
     */  
	private String userName;

    /**
     * 对应表中total_round
     * 对话轮次
     */  
	private Integer totalRound;

    /**
     * 对应表中knowledge_round
     * 知识对话轮次
     */  
	private Integer knowledgeRound;

    /**
     * 闲聊对话轮次
     */  
	private Integer chatRound;

	/**
	 * 人机对练轮次
	 */
	private Integer manMachineRound;

	/**
	 * 产品解读轮次
	 */
	private Integer productAnalyzeRound;

    /**
     * 转人工人数
     */  
	private Integer transformRound;

    /**
     * 解决量
     */  
	private Integer resolveCount;

    /**
     * 对应表中up_vote_round_count
     * 点赞轮次
     */  
	private Integer upVoteRoundCount;

    /**
     * 对应表中down_vote_round_count
     * 点踩轮次
     */  
	private Integer downVoteRoundCount;

    /**
     * 对应表中resolve_status
     * 解决状态 0-未解决 1-已解决
     */  
	private String resolveStatus;

    /**
     * 对应表中start_time
     * 会话开始时间
     */  
	private String startTime;

    /**
     * 对应表中end_time
     * 会话结束时间
     */  
	private String endTime;
	/**
	 * 更新状态 0-未更新 1-已更新
	 */
	private Integer updateStatus;
    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   