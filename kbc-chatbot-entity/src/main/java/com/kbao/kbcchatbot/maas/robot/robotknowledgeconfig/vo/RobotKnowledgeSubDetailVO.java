package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgeChapter;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 知识明细
 * @author: husw
 * @create: 2023-06-06 10:04
 **/
@Data
public class RobotKnowledgeSubDetailVO {

    private String id;
    /**
     * 知识ID
     */
    private String knowledgeId;
    /**
     * 机器人ID
     */
    private Long robotId;
    /**
     * 版本类型 0-测试版 1-正式版
     */
    private Integer environment;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 标题
     */
    private String title;
    /**
     * 相似问题
     */
    private List<String> similarQuestions;
    /**
     * 类型
     */
    private String type;
    /**
     * 所属项目不能为空
     */
    private String projectId;
    /**
     * 所属项目名称
     */
    private String projectName;
    /**
     * 所属知识库夹不能为空
     */
    private String folderId;
    /**
     * 所属知识库夹名称
     */
    private String folderName;
    /**
     * 一级目录
     */
    private String firstDirect;
    /**
     * 一级目录名称
     */
    private String firstDirectName;
    /**
     * 二级目录
     */
    private String secondDirect;
    /**
     * 二级目录名称
     */
    private String secondDirectName;
    /**
     * 所属类型
     */
    private String belongType;
    /**
     * 所属名称
     */
    private String belongName;
    /**
     * 互斥文章
     */
    private String mutexArticle;
    /**
     * 互斥文章
     */
    private String mutexArticleName;
    /**
     * 状态：1待发布，2已发布
     */
    private String state;
    /**
     * 来源知识库
     */
    private String sourceFolder;
    /**
     * 历程图章节
     */
    private List<KnowledgeChapter> chapters;
    /**
     * 更新人id
     */
    private String updateId;
    /**
     * 更新人姓名
     */
    private String updateName;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 删除状态：0未删除，1删除
     */
    private Integer isDeleted;

    /**
     * 知识库类型
     */
    private String knowledgeType;
}
