package com.kbao.kbcchatbot.maas.robot.robot.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * maas机器人添加请求参数
 * @author: xia<PERSON><PERSON><PERSON><PERSON>
 * @time: 2024/12/11 17:08
 */
@Data
public class RobotAddReqVO {

    private Integer id;

    /**
     * 机器人编码
     */
    private String robotCode;

    /**
     * 机器人名称
     */
    private String robotName;

    /**
     * 机器人类型: 1-闲聊，2-云知识库问答，3-产品检索
     */
    private String robotType;

    /**
     * 相似度阈值
     */
    private BigDecimal similarityThreshold;

    /**
     * 检索量阈值
     */
    private Integer retrievalThreshold;
}
