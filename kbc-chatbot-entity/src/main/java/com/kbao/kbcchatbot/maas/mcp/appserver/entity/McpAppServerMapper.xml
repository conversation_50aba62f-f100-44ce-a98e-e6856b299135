<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.mcp.appserver.dao.McpAppServerMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.mcp.appserver.entity.McpAppServer">
    	<id column="app_server_id" jdbcType="BIGINT"  property="appServerId"  />
        <result property="appServerCode" jdbcType="VARCHAR"  column="app_server_code" />
        <result property="appServerName" jdbcType="VARCHAR"  column="app_server_name" />  
        <result property="domainUrl" jdbcType="VARCHAR"  column="domain_url" />  
        <result property="appServerStatus" jdbcType="INTEGER"  column="app_server_status" />  
        <result property="description" jdbcType="VARCHAR"  column="description" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="isDeleted" jdbcType="INTEGER"  column="is_deleted" />  
	</resultMap>

	<sql id="Base_Column_List">
		app_server_id,  
		app_server_code,  
		app_server_name,  
		domain_url,  
		app_server_status,  
		description,  
		tenant_id,  
		create_time,  
		create_id,  
		update_id,  
		update_time,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.app_server_id, 
		t.app_server_code, 
		t.app_server_name, 
		t.domain_url, 
		t.app_server_status, 
		t.description, 
		t.tenant_id, 
		t.create_time, 
		t.create_id, 
		t.update_id, 
		t.update_time, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0 
	    <if test="appServerId != null and appServerId != ''">
	   		and t.app_server_id = #{appServerId,jdbcType=BIGINT}  
	    </if>
	    <if test="appServerCode != null and appServerCode != ''">
	   		and t.app_server_code = #{appServerCode,jdbcType=VARCHAR}  
	    </if>
	    <if test="appServerName != null and appServerName != ''">
	   		and t.app_server_name like CONCAT('%', #{appServerName,jdbcType=VARCHAR}, '%')  
	    </if>
	    <if test="domainUrl != null and domainUrl != ''">
	   		and t.domain_url = #{domainUrl,jdbcType=VARCHAR}  
	    </if>
	    <if test="appServerStatus != null">
	   		and t.app_server_status = #{appServerStatus,jdbcType=INTEGER}  
	    </if>
	    <if test="description != null and description != ''">
	   		and t.description = #{description,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="isDeleted != null">
	   		and t.is_deleted = #{isDeleted,jdbcType=INTEGER}  
	    </if>
		<!-- 自定义条件-->
		<if test="appServerIds != null and appServerIds.size()>0">
			and t.app_server_id in
			 <foreach collection="appServerIds" index="index" item="item" open="(" separator="," close=")"> #{item}</foreach>
		</if>
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_mcp_app_server t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_mcp_app_server t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_mcp_app_server
		where  is_deleted = 0 and app_server_id = #{appServerId,jdbcType=BIGINT}
	</select>


	<!-- 根据主键删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
		update t_mcp_app_server set is_deleted = 1
		where app_server_id = #{appServerId,jdbcType=BIGINT} and is_deleted = 0
	</update>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.mcp.appserver.entity.McpAppServer">
		insert into t_mcp_app_server(
			<include refid="Base_Column_List" />
		)
		values(  
                #{appServerId,jdbcType=BIGINT}, 
                 
                #{appServerCode,jdbcType=VARCHAR}, 
                 
                #{appServerName,jdbcType=VARCHAR}, 
                 
                #{domainUrl,jdbcType=VARCHAR}, 
                 
                #{appServerStatus,jdbcType=INTEGER}, 
                 
                #{description,jdbcType=VARCHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{isDeleted,jdbcType=INTEGER} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.maas.mcp.appserver.entity.McpAppServer">
		insert into t_mcp_app_server
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="appServerId != null ">  
	       		app_server_id,
	        </if>  
	        <if test="appServerCode != null ">  
	       		app_server_code,
	        </if>  
	        <if test="appServerName != null ">  
	       		app_server_name,
	        </if>  
	        <if test="domainUrl != null ">  
	       		domain_url,
	        </if>  
	        <if test="appServerStatus != null ">  
	       		app_server_status,
	        </if>  
	        <if test="description != null ">  
	       		description,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="isDeleted != null ">  
	       		is_deleted,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appServerId != null">  
            	#{appServerId,jdbcType=BIGINT},
            </if>  
            <if test="appServerCode != null">  
            	#{appServerCode,jdbcType=VARCHAR},
            </if>  
            <if test="appServerName != null">  
            	#{appServerName,jdbcType=VARCHAR},
            </if>  
            <if test="domainUrl != null">  
            	#{domainUrl,jdbcType=VARCHAR},
            </if>  
            <if test="appServerStatus != null">  
            	#{appServerStatus,jdbcType=INTEGER},
            </if>  
            <if test="description != null">  
            	#{description,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="isDeleted != null">  
            	#{isDeleted,jdbcType=INTEGER},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.mcp.appserver.entity.McpAppServer">
		update t_mcp_app_server
		<set>
	        <if test="appServerCode != null ">  
	        	app_server_code = #{appServerCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="appServerName != null ">  
	        	app_server_name = #{appServerName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="domainUrl != null ">  
	        	domain_url = #{domainUrl,jdbcType=VARCHAR},  
	        </if>  
	        <if test="appServerStatus != null ">  
	        	app_server_status = #{appServerStatus,jdbcType=INTEGER},  
	        </if>  
	        <if test="description != null ">  
	        	description = #{description,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="isDeleted != null ">  
	        	is_deleted = #{isDeleted,jdbcType=INTEGER},  
	        </if>  
		</set>
		where app_server_id = #{appServerId,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.mcp.appserver.entity.McpAppServer">
		update t_mcp_app_server
		set
           app_server_code = #{appServerCode,jdbcType=VARCHAR},
           app_server_name = #{appServerName,jdbcType=VARCHAR},
           domain_url = #{domainUrl,jdbcType=VARCHAR},
           app_server_status = #{appServerStatus,jdbcType=INTEGER},
           description = #{description,jdbcType=VARCHAR},
           tenant_id = #{tenantId,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           create_id = #{createId,jdbcType=VARCHAR},
           update_id = #{updateId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           is_deleted = #{isDeleted,jdbcType=INTEGER}
		where app_server_id = #{appServerId,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_mcp_app_server(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.appServerId}
	        <choose>
	            <when test="item.appServerCode != null">,#{item.appServerCode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.appServerName != null">,#{item.appServerName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.domainUrl != null">,#{item.domainUrl}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.appServerStatus != null">,#{item.appServerStatus}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.description != null">,#{item.description}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量删除 -->
	<update id="batchDelete" parameterType="java.util.List">
		update t_mcp_app_server set is_deleted = 1
		where app_server_id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}</foreach>
		and is_deleted = 0
	</update>

</mapper>