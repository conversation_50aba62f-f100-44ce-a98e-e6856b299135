<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.product.dao.ProductDataMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.product.entity.ProductData">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="productId" jdbcType="VARCHAR"  column="product_id" />  
        <result property="productName" jdbcType="VARCHAR"  column="product_name" />  
        <result property="contentLength" jdbcType="INTEGER"  column="content_length" />  
        <result property="status" jdbcType="CHAR"  column="status" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="listShow" jdbcType="TINYINT"  column="list_show" />  
        <result property="forSale" jdbcType="INTEGER"  column="for_sale" />  
        <result property="online" jdbcType="TINYINT"  column="online" />  
        <result property="productStatus" jdbcType="INTEGER"  column="product_status" />  
        <result property="insuranceCategory" jdbcType="VARCHAR"  column="insurance_category" />  
        <result property="primaryInsuranceName" jdbcType="VARCHAR"  column="primary_insurance_name" />  
        <result property="secondaryInsuranceName" jdbcType="VARCHAR"  column="secondary_insurance_name" />  
        <result property="syncStatus" jdbcType="CHAR"  column="sync_status" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		product_id,  
		product_name,  
		content_length,  
		status,  
		create_time,  
		update_time,  
		list_show,  
		for_sale,  
		online,  
		product_status,  
		insurance_category,  
		primary_insurance_name,  
		secondary_insurance_name,  
		sync_status  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.product_id, 
		t.product_name, 
		t.content_length, 
		t.status, 
		t.create_time, 
		t.update_time, 
		t.list_show, 
		t.for_sale, 
		t.online, 
		t.product_status, 
		t.insurance_category, 
		t.primary_insurance_name, 
		t.secondary_insurance_name, 
		t.sync_status 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="productId != null and productId != ''">
	   		and t.product_id = #{productId,jdbcType=VARCHAR}
	    </if>
	    <if test="productName != null and productName != ''">
	   		and t.product_name = #{productName,jdbcType=VARCHAR}
	    </if>
	    <if test="contentLength != null">
	   		and t.content_length = #{contentLength,jdbcType=INTEGER}
	    </if>
	    <if test="status != null">
	   		and t.status = #{status,jdbcType=CHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="listShow != null">
	   		and t.list_show = #{listShow,jdbcType=TINYINT}
	    </if>
	    <if test="forSale != null">
	   		and t.for_sale = #{forSale,jdbcType=INTEGER}
	    </if>
	    <if test="online != null">
	   		and t.online = #{online,jdbcType=TINYINT}
	    </if>
	    <if test="productStatus != null">
	   		and t.product_status = #{productStatus,jdbcType=INTEGER}
	    </if>
	    <if test="insuranceCategory != null and insuranceCategory != ''">
	   		and t.insurance_category = #{insuranceCategory,jdbcType=VARCHAR}
	    </if>
	    <if test="primaryInsuranceName != null and primaryInsuranceName != ''">
	   		and t.primary_insurance_name = #{primaryInsuranceName,jdbcType=VARCHAR}
	    </if>
	    <if test="secondaryInsuranceName != null and secondaryInsuranceName != ''">
	   		and t.secondary_insurance_name = #{secondaryInsuranceName,jdbcType=VARCHAR}
	    </if>
	    <if test="syncStatus != null">
	   		and t.sync_status = #{syncStatus,jdbcType=CHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_product_data t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_product_data
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_product_data
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.product.entity.ProductData">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_product_data(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{productId,jdbcType=VARCHAR}, 
                 
                #{productName,jdbcType=VARCHAR}, 
                 
                #{contentLength,jdbcType=INTEGER}, 
                 
                #{status,jdbcType=CHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{listShow,jdbcType=TINYINT}, 
                 
                #{forSale,jdbcType=INTEGER}, 
                 
                #{online,jdbcType=TINYINT}, 
                 
                #{productStatus,jdbcType=INTEGER}, 
                 
                #{insuranceCategory,jdbcType=VARCHAR}, 
                 
                #{primaryInsuranceName,jdbcType=VARCHAR}, 
                 
                #{secondaryInsuranceName,jdbcType=VARCHAR}, 
                 
                #{syncStatus,jdbcType=CHAR} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.product.entity.ProductData">
		update t_product_data
		<set>
	        <if test="productId != null ">  
	        	product_id = #{productId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="productName != null ">  
	        	product_name = #{productName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="contentLength != null ">  
	        	content_length = #{contentLength,jdbcType=INTEGER},  
	        </if>  
	        <if test="status != null ">  
	        	status = #{status,jdbcType=CHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="listShow != null ">  
	        	list_show = #{listShow,jdbcType=TINYINT},  
	        </if>  
	        <if test="forSale != null ">  
	        	for_sale = #{forSale,jdbcType=INTEGER},  
	        </if>  
	        <if test="online != null ">  
	        	online = #{online,jdbcType=TINYINT},  
	        </if>  
	        <if test="productStatus != null ">  
	        	product_status = #{productStatus,jdbcType=INTEGER},  
	        </if>  
	        <if test="insuranceCategory != null ">  
	        	insurance_category = #{insuranceCategory,jdbcType=VARCHAR},  
	        </if>  
	        <if test="primaryInsuranceName != null ">  
	        	primary_insurance_name = #{primaryInsuranceName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="secondaryInsuranceName != null ">  
	        	secondary_insurance_name = #{secondaryInsuranceName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="syncStatus != null ">  
	        	sync_status = #{syncStatus,jdbcType=CHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
	<select id="getProductList" resultType="com.kbao.kbcchatbot.maas.product.bean.ProductListResVo">
		select * from (
			select pd.product_id productId, pd.product_name productName, count(pf.id) fileCount,
			sum(case when pf.id is null or pf.sync_status = '1' then 0 else 1 end) failFileCount, pd.create_time createTime
			from t_product_data pd
			left join t_product_file pf on pd.product_id = pf.product_id
			where 1=1
			<if test="productId != null and productId != ''">
				and pd.product_id = #{productId,jdbcType=VARCHAR}
			</if>
			<if test="productName != null and productName != ''">
				and instr(pd.product_name, #{productName,jdbcType=VARCHAR}) > 0
			</if>
			group by pd.product_id
		) t order by t.createTime desc
	</select>
</mapper>
