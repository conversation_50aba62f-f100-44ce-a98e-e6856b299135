package com.kbao.kbcchatbot.maas.channel.channelsensitivewords.bean;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: kbc-chatbot
 * @description: 敏感词
 * @author: husw
 * @create: 2023-07-04 09:01
 **/
@Data
public class ChannelSensitiveWordsSaveVO {

    @NotNull(message = "渠道ID不能为空！")
    private Long channelId;

    @NotNull(message = "渠道CODE不能为空！")
    private String channelCode;

    @NotBlank(message = "敏感词不能为空！")
    private String words;
}
