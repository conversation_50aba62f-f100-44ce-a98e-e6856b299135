package com.kbao.kbcchatbot.maas.product.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-04-24
*/
@Data
public class ProductFile implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

    /**
     * 对应表中file_id
     * 文件ID
     */  
	private String fileId;

    /**
     * 对应表中file_name
     * 文件名称
     */  
	private String fileName;

    /**
     * 对应表中file_type
     * 文件类型
     */  
	private String fileType;

    /**
     * 对应表中file_url
     * 文件地址
     */  
	private String fileUrl;

    /**
     * 对应表中content_length
     * 文件内容长度
     */  
	private Integer contentLength;

    /**
     * 对应表中product_id
     * 产品ID
     */  
	private String productId;

    /**
     * 对应表中product_name
     * 产品名称
     */  
	private String productName;

    /**
     * 对应表中sync_status
     * 同步状态:0-同步中，1-同步成功，2-同步失败
     */  
	private String syncStatus;

    /**
     * 对应表中source
     * 来源：mpls-营销库，upload-上传，apls-精算库
     */  
	private String source;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   