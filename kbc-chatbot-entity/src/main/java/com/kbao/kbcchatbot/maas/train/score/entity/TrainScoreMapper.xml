<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.train.score.dao.TrainScoreMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.train.score.entity.TrainScore">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="sceneCode" jdbcType="VARCHAR"  column="scene_code" />  
        <result property="userId" jdbcType="VARCHAR"  column="user_id" />  
        <result property="userName" jdbcType="VARCHAR"  column="user_name" />  
        <result property="source" jdbcType="CHAR"  column="source" />  
        <result property="score" jdbcType="DECIMAL"  column="score" />  
        <result property="isPass" jdbcType="CHAR"  column="is_pass" />  
        <result property="externalId" jdbcType="VARCHAR"  column="external_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createBy" jdbcType="VARCHAR"  column="create_by" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateBy" jdbcType="VARCHAR"  column="update_by" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		scene_code,  
		user_id,  
		user_name,  
		source,  
		score,  
		is_pass,  
		external_id,  
		create_time,  
		create_by,  
		update_time,  
		update_by,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.scene_code, 
		t.user_id, 
		t.user_name, 
		t.source, 
		t.score, 
		t.is_pass, 
		t.external_id, 
		t.create_time, 
		t.create_by, 
		t.update_time, 
		t.update_by, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="sceneCode != null and sceneCode != ''">
	   		and t.scene_code = #{sceneCode,jdbcType=VARCHAR}
	    </if>
	    <if test="userId != null and userId != ''">
	   		and t.user_id = #{userId,jdbcType=VARCHAR}
	    </if>
	    <if test="userName != null and userName != ''">
	   		and t.user_name = #{userName,jdbcType=VARCHAR}
	    </if>
	    <if test="source != null">
	   		and t.source = #{source,jdbcType=CHAR}
	    </if>
	    <if test="score != null">
	   		and t.score = #{score,jdbcType=DECIMAL}
	    </if>
	    <if test="isPass != null">
	   		and t.is_pass = #{isPass,jdbcType=CHAR}
	    </if>
	    <if test="externalId != null and externalId != ''">
	   		and t.external_id = #{externalId,jdbcType=VARCHAR}
	    </if>
	    <if test="hasRecord != null and hasRecord != ''">
	    	and t.has_record = #{hasRecord,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createBy != null and createBy != ''">
	   		and t.create_by = #{createBy,jdbcType=VARCHAR}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateBy != null and updateBy != ''">
	   		and t.update_by = #{updateBy,jdbcType=VARCHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_train_score t
		<include refid="Base_Condition" />
		order by t.update_time desc
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_train_score
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_train_score
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.train.score.entity.TrainScore">
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_train_score(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{sceneCode,jdbcType=VARCHAR}, 
                 
                #{userId,jdbcType=VARCHAR}, 
                 
                #{userName,jdbcType=VARCHAR}, 
                 
                #{source,jdbcType=CHAR}, 
                 
                #{score,jdbcType=DECIMAL}, 
                 
                #{isPass,jdbcType=CHAR}, 
                 
                #{externalId,jdbcType=VARCHAR},

				now(),
                 
                #{createBy,jdbcType=VARCHAR}, 
                 
                now(),

				#{createBy,jdbcType=VARCHAR},

				#{tenantId,jdbcType=VARCHAR}
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.train.score.entity.TrainScore">
		update t_train_score
		<set>
	        <if test="sceneCode != null ">  
	        	scene_code = #{sceneCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="userId != null ">  
	        	user_id = #{userId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="userName != null ">  
	        	user_name = #{userName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="source != null ">  
	        	source = #{source,jdbcType=CHAR},  
	        </if>  
	        <if test="score != null ">  
	        	score = #{score,jdbcType=DECIMAL},  
	        </if>  
	        <if test="isPass != null ">  
	        	is_pass = #{isPass,jdbcType=CHAR},  
	        </if>  
	        <if test="externalId != null ">  
	        	external_id = #{externalId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createBy != null ">  
	        	create_by = #{createBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateBy != null ">  
	        	update_by = #{updateBy,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
	<select id="getUnfinishedTrain" resultMap="BaseResultMap">
		select * from t_train_score
		where scene_code = #{sceneCode} and external_id = #{externalId}
		and source = #{source} and score is null
	</select>

	<select id="getTrainScoreByExternalId" resultMap="BaseResultMap">
		select * from t_train_score
		where external_id = #{externalId} and source = #{source} limit 1
	</select>

	<select id="getTrainScoreByUserId" resultMap="BaseResultMap">
		select * from t_train_score
		where scene_code = #{sceneCode} and user_id = #{userId} and source = #{source}
		and score is null
		order by create_time desc limit 1
	</select>
</mapper>
