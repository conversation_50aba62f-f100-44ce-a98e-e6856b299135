package com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity;
import java.io.Serializable;
import lombok.Data;
import org.springframework.data.annotation.Transient;

/**
* <AUTHOR>
* @Description 渠道基础配置实体
* @Date 2023-05-19
*/
@Data
public class ChannelBasicConfig implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Long id;

    /**
     * 对应表中channel_id
     * 渠道主键
     */  
	private Long channelId;

    /**
     * 对应表中channel_code
     * 渠道编码
     */
	private String channelCode;

	/**
	 * 通道ID(云知识库的通道)
	 */
	private String passId;

    /**
     * 对应表中welcome_reply
     * 欢迎语文案
     */  
	private String welcomeReply;

    /**
     * 对应表中input_place_holder
     * 输入框引导语
     */  
	private String inputPlaceHolder;

    /**
     * 对应表中guess_question_status
     * 猜你想问开启状态 0-未开启 1-开启
     */  
	private String guessQuestionStatus;

    /**
     * 对应表中common_card_status
     * 常用卡片开启状态 0-未开启 1-开启
     */  
	private String commonCardStatus;

    /**
     * 对应表中common_phrase_status
     * 常用短语开启状态 0-未开启 1-开启
     */  
	private String commonPhraseStatus;

    /**
     * 对应表中answer_evaluate_status
     * 答案评价开启状态 0-未开启 1-开启
     */  
	private String answerEvaluateStatus;

	/**
	 * 客服平台接入方式 1-网页跳转 2-系统集成
	 */
	private Integer custServPlatAccessType;

	/**
	 * 客服平台接入配置
	 */
	private String custServPlatAddr;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

    /**
     * 对应表中update_time
     * 最后更新时间
     */  
	private String updateTime;

    /**
     * 对应表中update_id
     * 最后更新人
     */  
	private String updateId;

	/**
	 * 渠道名称
	 */
	@Transient
	private String channelName;

	/**
	 * 启用状态 状态 0-未启用 1-启用
	 */
	@Transient
	private String status;

	/**
	 * 机器人id
	 */
	@Transient
	private Long robotId;

}   