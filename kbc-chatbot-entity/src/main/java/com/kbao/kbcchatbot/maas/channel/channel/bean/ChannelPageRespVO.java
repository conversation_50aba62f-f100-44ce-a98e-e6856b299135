package com.kbao.kbcchatbot.maas.channel.channel.bean;

import lombok.Data;

/**
 * maas渠道分页查询参数
 * @author: xia<PERSON><PERSON><PERSON><PERSON>
 * @time: 2024/12/11 17:08
 */
@Data
public class ChannelPageRespVO {

    private Integer channelId;

    private String channelCode;

    private String channelName;

    private Integer projectId;

    private String projectName;

    private String status;

    /**
     * 执行类型 0-自定义  1-flow流程
     */
    private String executeType;
    /**
     * 流程ID
     */
    private String flowId;
    /**
     * 流程名称
     */
    private String flowName;

    private String createTime;

    private String updateTime;

    private String updateBy;

    private String remark;

    private String bindRobots;

}
