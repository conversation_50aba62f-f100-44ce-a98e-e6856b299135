<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.product.dao.CompanyFileMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.product.entity.CompanyFile">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="fileId" jdbcType="VARCHAR"  column="file_id" />  
        <result property="fileName" jdbcType="VARCHAR"  column="file_name" />  
        <result property="fileType" jdbcType="VARCHAR"  column="file_type" />  
        <result property="fileUrl" jdbcType="VARCHAR"  column="file_url" />  
        <result property="contentLength" jdbcType="INTEGER"  column="content_length" />  
        <result property="companyId" jdbcType="VARCHAR"  column="company_id" />  
        <result property="companyName" jdbcType="VARCHAR"  column="company_name" />  
        <result property="syncStatus" jdbcType="CHAR"  column="sync_status" />  
        <result property="source" jdbcType="VARCHAR"  column="source" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		file_id,  
		file_name,  
		file_type,  
		file_url,  
		content_length,  
		company_id,  
		company_name,  
		sync_status,  
		source,  
		create_time,  
		update_time,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.file_id, 
		t.file_name, 
		t.file_type, 
		t.file_url, 
		t.content_length, 
		t.company_id, 
		t.company_name, 
		t.sync_status, 
		t.source, 
		t.create_time, 
		t.update_time, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="fileId != null and fileId != ''">
	   		and t.file_id = #{fileId,jdbcType=VARCHAR}
	    </if>
	    <if test="fileName != null and fileName != ''">
	   		and instr(t.file_name, #{fileName,jdbcType=VARCHAR}) > 0
	    </if>
	    <if test="fileType != null and fileType != ''">
	   		and t.file_type = #{fileType,jdbcType=VARCHAR}
	    </if>
	    <if test="fileUrl != null and fileUrl != ''">
	   		and t.file_url = #{fileUrl,jdbcType=VARCHAR}
	    </if>
	    <if test="contentLength != null">
	   		and t.content_length = #{contentLength,jdbcType=INTEGER}
	    </if>
	    <if test="companyId != null and companyId != ''">
	   		and t.company_id = #{companyId,jdbcType=VARCHAR}
	    </if>
	    <if test="companyName != null and companyName != ''">
	   		and t.company_name = #{companyName,jdbcType=VARCHAR}
	    </if>
		<if test="syncStatus != null and syncStatus != ''">
			and t.sync_status = #{syncStatus,jdbcType=CHAR}
		</if>
	    <if test="source != null and source != ''">
	   		and t.source = #{source,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_company_file t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_company_file
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_company_file
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.product.entity.CompanyFile">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_company_file(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{fileId,jdbcType=VARCHAR}, 
                 
                #{fileName,jdbcType=VARCHAR}, 
                 
                #{fileType,jdbcType=VARCHAR}, 
                 
                #{fileUrl,jdbcType=VARCHAR}, 
                 
                #{contentLength,jdbcType=INTEGER}, 
                 
                #{companyId,jdbcType=VARCHAR}, 
                 
                #{companyName,jdbcType=VARCHAR}, 
                 
                #{syncStatus,jdbcType=CHAR}, 
                 
                #{source,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.product.entity.CompanyFile">
		update t_company_file
		<set>
	        <if test="fileId != null ">  
	        	file_id = #{fileId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="fileName != null ">  
	        	file_name = #{fileName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="fileType != null ">  
	        	file_type = #{fileType,jdbcType=VARCHAR},  
	        </if>  
	        <if test="fileUrl != null ">  
	        	file_url = #{fileUrl,jdbcType=VARCHAR},  
	        </if>  
	        <if test="contentLength != null ">  
	        	content_length = #{contentLength,jdbcType=INTEGER},  
	        </if>  
	        <if test="companyId != null ">  
	        	company_id = #{companyId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="companyName != null ">  
	        	company_name = #{companyName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="syncStatus != null ">  
	        	sync_status = #{syncStatus,jdbcType=CHAR},  
	        </if>  
	        <if test="source != null ">  
	        	source = #{source,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
	<select id="getCompanyList" resultType="com.kbao.kbcchatbot.maas.product.bean.CompanyListResVo">
		select * from (
			select company_id companyId, company_name companyName, count(1) fileCount,
				 sum(case when sync_status = '1' then 0 else 1 end) failFileCount
			from t_company_file
			where 1=1
			<if test="companyId != null and companyId != ''">
				and company_id = #{companyId,jdbcType=VARCHAR}
			</if>
			<if test="companyName != null and companyName != ''">
				and instr(company_name, #{companyName,jdbcType=VARCHAR}) > 0
			</if>
		  group by company_id
		) t order by companyName
	</select>
</mapper>
