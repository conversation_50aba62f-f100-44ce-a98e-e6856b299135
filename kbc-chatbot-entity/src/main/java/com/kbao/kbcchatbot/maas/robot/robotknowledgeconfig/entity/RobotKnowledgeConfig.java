package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity;

import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums.KnowledgeOperationEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums.SyncStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天机器人知识配置表
 * @TableName t_robot_knowledge_config
 */
@Data
public class RobotKnowledgeConfig implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 目录编码
     */
    private String directoryCode;

    /**
     * 机器人主键id
     */
    private Long robotId;

    /**
     * 机器人code
     */
    private String robotCode;

    /**
     * 类型 0-测试版 1-正式版
     */
    private Integer type;
    /**
     * 操作 0-初始化 1-绑定  2-解绑 3-刷新
     * @see KnowledgeOperationEnum
     */
    private Integer operation;

    /**
     * 一级知识目录ID
     */
    private String firstDirectId;

    /**
     * 一级知识目录名称
     */
    private String firstDirectName;

    /**
     * 知识目录ID
     */
    private String directoryId;
    /**
     * 知识目录名称
     */
    private String directoryName;

    /**
     * 项目
     */
    private String projectId;

    /**
     * 项目
     */
    private String projectName;
    /**
     * 知识库ID
     */
    private String folderId;
    /**
     * 知识库
     */
    private String folderName;

    /**
     * 绑定人
     */
    private String bindId;

    /**
     * 绑定时间
     */
    private Date bindTime;

    /**
     * 是否在知识库中被删除
     */
    private Integer hasRemoved;

    /**
     * 在是知识库中被删除的时间
     */
    private Date removedTime;

    /**
     * 最后刷新人
     */
    private String refreshId;

    /**
     * 最后刷新时间
     */
    private Date refreshTime;
    /**
     * 更新人
     */
    private String updateId;

    /**
     * 更新时间
     */
    private Date updateTime;


    /**
     * 知识同步状态 1-同步中  2-同步成功  3-同步失败
     * @see SyncStatusEnum
     */
    private Integer syncStatus;

    /**
     * 失败原因
     */
    private String failMsg;

    private static final long serialVersionUID = 1L;
}