package com.kbao.kbcchatbot.maas.recordShareLog.entity;
import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR> jie
* @Description 实体
* @Date 2025-05-23
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecordShareLog implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Integer id;

	private String shareId;

    /**
     * 对应表中user_id
     * 用户ID
     */  
	private String userId;

    /**
     * 对应表中user_name
     * 用户名
     */  
	private String userName;

    /**
     * 对应表中head_image
     * 头像
     */  
	private String headImage;

    /**
     * 对应表中record_id
     * 消息记录ID
     */  
	private String recordId;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

	private JSONObject recordData;

}   