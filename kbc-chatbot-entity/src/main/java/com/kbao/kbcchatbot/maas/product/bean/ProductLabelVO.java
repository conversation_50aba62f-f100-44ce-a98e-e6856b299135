package com.kbao.kbcchatbot.maas.product.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 标签
 * @date 2025/4/1 13:48
 */
@Data
public class ProductLabelVO {
    /**
     * 产品ID
     */
    private String productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 关联产品
     */
    private String relatedProductId;
    /**
     * 险种类型
     */
    private String secondaryInsuranceName;
    /**
     * 计划名称
     */
    private List<String> planNames;
    /**
     * 责任名称
     */
    private List<String> liabNames;
    /**
     * 文件
     */
    private List<ProductFile> fileList;

    @Data
    public static class ProductFile{

        /**
         * 文件类型
         */
        private String fileType;

        /**
         * 文件ID
         */
        private String fileId;

        /**
         * 文件名称
         */
        private String fileName;
        /**
         * 文件链接
         */
        private String url;
    }
}
