<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.dao.RobotKnowledgeConfigMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="directoryCode" column="directory_code" jdbcType="VARCHAR"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="robotCode" column="robot_code" jdbcType="VARCHAR"/>
            <result property="operation" column="operation" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="firstDirectId" column="first_direct_id" jdbcType="VARCHAR"/>
            <result property="firstDirectName" column="first_direct_name" jdbcType="VARCHAR"/>
            <result property="directoryName" column="directory_name" jdbcType="VARCHAR"/>
            <result property="directoryId" column="directory_id" jdbcType="VARCHAR"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
            <result property="folderId" column="folder_id" jdbcType="VARCHAR"/>
            <result property="folderName" column="folder_name" jdbcType="VARCHAR"/>
            <result property="bindId" column="bind_id" jdbcType="VARCHAR"/>
            <result property="bindTime" column="bind_time" jdbcType="TIMESTAMP"/>
            <result property="hasRemoved" column="has_removed" jdbcType="INTEGER"/>
            <result property="syncStatus" column="sync_status" jdbcType="INTEGER"/>
            <result property="failMsg" column="fail_msg" jdbcType="VARCHAR"/>
            <result property="removedTime" column="removed_time" jdbcType="TIMESTAMP"/>
            <result property="refreshId" column="refresh_id" jdbcType="VARCHAR"/>
            <result property="refreshTime" column="refresh_time" jdbcType="TIMESTAMP"/>
            <result property="updateId" column="update_id" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="All_Column_List">
        id,directory_code,robot_id,robot_code,operation,type,first_direct_id,first_direct_name,
        directory_name,directory_id,project_id,project_name,folder_id,folder_name,
        bind_id,bind_time,has_removed,sync_status,fail_msg,
        removed_time,refresh_id,refresh_time,update_id,update_time
    </sql>

    <sql id="Base_Column_List">
        id,directory_code,robot_id,robot_code,operation,type,first_direct_id,first_direct_name,
        directory_name,directory_id,project_id,project_name,folder_id,folder_name,
        bind_id,bind_time,has_removed,sync_status,
        removed_time,refresh_id,refresh_time,update_id,update_time
    </sql>

    <sql id = "base_condition">
        <where>
            1 = 1
            <if test="robotId != null">
                and robot_id = #{robotId,jdbcType=BIGINT}
            </if>
            <if test="robotCode != null">
                and robot_code = #{robotCode,jdbcType=VARCHAR}
            </if>
            <if test="operation != null">
                and operation = #{operation,jdbcType=INTEGER}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="directoryName != null">
                and directory_name = #{directoryName,jdbcType=VARCHAR}
            </if>
            <if test="directoryId != null">
                and directory_id = #{directoryId,jdbcType=VARCHAR}
            </if>
            <if test="projectId != null">
                and project_id = #{projectId,jdbcType=VARCHAR}
            </if>
            <if test="projectName != null">
                and project_name = #{projectName,jdbcType=VARCHAR}
            </if>
            <if test="folderId != null">
                and folder_id = #{folderId,jdbcType=VARCHAR}
            </if>
            <if test="folderName != null">
                and folder_name = #{folderName,jdbcType=VARCHAR}
            </if>
            <if test="bindId != null">
                and bind_id = #{bindId,jdbcType=VARCHAR}
            </if>
            <if test="bindTime != null">
                and bind_time = #{bindTime,jdbcType=TIMESTAMP}
            </if>
            <if test="hasRemoved != null">
                and has_removed = #{hasRemoved,jdbcType=INTEGER}
            </if>
            <if test="syncStatus != null">
                and sync_status = #{syncStatus,jdbcType=INTEGER}
            </if>
            <if test="failMsg != null">
                and fail_msg = #{failMsg,jdbcType=VARCHAR}
            </if>
            <if test="removedTime != null">
                and removed_time = #{removedTime,jdbcType=TIMESTAMP}
            </if>
            <if test="refreshId != null">
                and refresh_id = #{refreshId,jdbcType=VARCHAR}
            </if>
            <if test="refreshTime != null">
                and refresh_time = #{refreshTime,jdbcType=TIMESTAMP}
            </if>
            <if test="operationNotEq != null">
                and operation != #{operationNotEq,jdbcType=INTEGER}
            </if>
            <if test="directoryIdList != null">
                and directory_id in(
                <foreach collection="directoryIdList" index="index" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="operationNoEq != null">
                and operation != #{operationNoEq,jdbcType=INTEGER}
            </if>
        </where>
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_knowledge_config
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="selectByCondition" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_knowledge_config
        <include refid="base_condition" />
    </select>

    <select id="selectByRobotId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_knowledge_config
        where  robot_id = #{robotId,jdbcType=BIGINT}
        and type = 0
        and operation in (1,2,3)
    </select>

    <select id="selectDirectId" parameterType="java.util.Map" resultType="java.lang.String">
        select directory_id
        from t_robot_knowledge_config
        <where>
            1 = 1
            <if test="robotId != null">
                and robot_id = #{robotId,jdbcType=BIGINT}
            </if>
            <if test="robotCode != null and robotCode != ''">
                and robot_code = #{robotCode,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="directoryId != null">
                and directory_id = #{directoryId,jdbcType=VARCHAR}
            </if>
            <if test="operationNoEq != null">
                and operation != #{operationNoEq,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="selectAll" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="All_Column_List" />
        from t_robot_knowledge_config
        <where>
            1 = 1
            <if test="robotId != null">
                and robot_id = #{robotId,jdbcType=BIGINT}
            </if>
            <if test="robotCode != null and robotCode != ''">
                and robot_code = #{robotCode,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="operationNoEq != null">
                and operation != #{operationNoEq,jdbcType=INTEGER}
            </if>
            <if test="firstDirectId != null and firstDirectId != ''">
                and first_direct_id = #{firstDirectId,jdbcType=VARCHAR}
            </if>
            <if test="directoryId != null and directoryId != ''">
                and directory_id = #{directoryId,jdbcType=VARCHAR}
            </if>
            <if test="updateId != null and updateId != ''">
                and update_id = #{updateId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectFirstDirectoryByRobot" parameterType="java.util.Map" resultType="com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeDirectRespVO">
        select distinct first_direct_id as directoryId,first_direct_name as directoryName
        from t_robot_knowledge_config
        <where>
            1 = 1
            <if test="robotId != null">
                and robot_id = #{robotId,jdbcType=BIGINT}
            </if>
            <if test="robotCode != null and robotCode != ''">
                and robot_code = #{robotCode,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="operationNoEq != null">
                and operation != #{operationNoEq,jdbcType=INTEGER}
            </if>
            <if test="firstDirectName != null and firstDirectName != ''">
                and first_direct_name like concat('%',#{firstDirectName,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>
    <select id="selectDirectoryByRobot" parameterType="java.util.Map" resultType="com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeDirectRespVO">
        select directory_id as directoryId,directory_name as directoryName
        from t_robot_knowledge_config
        <where>
            1 = 1
            <if test="robotId != null">
                and robot_id = #{robotId,jdbcType=BIGINT}
            </if>
            <if test="robotCode != null and robotCode != ''">
                and robot_code = #{robotCode,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="firstDirectId != null and firstDirectId != ''">
                and first_direct_id = #{firstDirectId,jdbcType=VARCHAR}
            </if>
            <if test="operationNoEq != null">
                and operation != #{operationNoEq,jdbcType=INTEGER}
            </if>
            <if test="directoryName != null and directoryName != ''">
                directory_name like concat('%',#{directoryName,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_robot_knowledge_config
        where  id = #{id,jdbcType=BIGINT} 
    </delete>

    <delete id="deleteByRobotIdAndDirect" parameterType="java.util.Map">
        delete from t_robot_knowledge_config
       <where>
           1 = 1
           <if test="robotId != null">
               and robot_id = #{robotId,jdbcType=BIGINT}
           </if>
           <if test="robotCode != null and robotCode != ''">
               and robot_code = #{robotCode,jdbcType=VARCHAR}
           </if>
                and directory_id in(
               <foreach collection="directoryIdList" separator="," item="item" index="index">
                   #{item}
               </foreach>
           )
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
       </where>
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig" useGeneratedKeys="true">
        insert into t_robot_knowledge_config
        ( id,directory_code,robot_id,robot_code,type,operation,first_direct_id,first_direct_name
        ,directory_name,directory_id,project_id,project_name,folder_id,folder_name
        ,bind_id,bind_time,has_removed,sync_status,fail_msg
        ,removed_time,refresh_id,refresh_time,update_id,update_time
        )
        values (#{id,jdbcType=BIGINT},#{directoryCode,jdbcType=VARCHAR},#{robotId,jdbcType=BIGINT},#{robotCode,jdbcType=VARCHAR},#{type,jdbcType=INTEGER},#{operation,jdbcType=INTEGER},#{firstDirectId,jdbcType=VARCHAR},#{firstDirectName,jdbcType=VARCHAR}
        ,#{directoryName,jdbcType=VARCHAR},#{directoryId,jdbcType=VARCHAR},#{projectId,jdbcType=VARCHAR},#{projectName,jdbcType=VARCHAR}
        ,#{folderId,jdbcType=VARCHAR},#{folderName,jdbcType=VARCHAR}
        ,#{bindId,jdbcType=VARCHAR},#{bindTime,jdbcType=TIMESTAMP},#{hasRemoved,jdbcType=INTEGER},#{syncStatus,jdbcType=INTEGER},#{failMsg,jdbcType=VARCHAR}
        ,#{removedTime,jdbcType=TIMESTAMP},#{refreshId,jdbcType=VARCHAR},#{refreshTime,jdbcType=TIMESTAMP},#{updateId,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig" useGeneratedKeys="true">
        insert into t_robot_knowledge_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="directoryCode != null">directory_code,</if>
                <if test="robotId != null">robot_id,</if>
                <if test="robotCode != null">robot_code,</if>
                <if test="operation != null">operation,</if>
                <if test="type != null">type,</if>
                <if test="firstDirectName != null">first_direct_name,</if>
                <if test="firstDirectId != null">first_direct_id,</if>
                <if test="directoryName != null">directory_name,</if>
                <if test="directoryId != null">directory_id,</if>
                <if test="projectId != null">projectId,</if>
                <if test="projectName != null">projectName,</if>
                <if test="folderId != null">folder_id,</if>
                <if test="folderName != null">folder_name,</if>
                <if test="bindId != null">bind_id,</if>
                <if test="bindTime != null">bind_time,</if>
                <if test="hasRemoved != null">has_removed,</if>
                <if test="syncStatus != null">sync_status,</if>
                <if test="failMsg != null">fail_msg,</if>
                <if test="removedTime != null">removed_time,</if>
                <if test="refreshId != null">refresh_id,</if>
                <if test="refreshTime != null">refresh_time,</if>
                <if test="updateId != null">update_id,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="directoryCode != null">#{directoryCode,jdbcType=VARCHAR},</if>
                <if test="robotId != null">#{robotId,jdbcType=BIGINT},</if>
                <if test="robotCode != null">#{robotCode,jdbcType=VARCHAR},</if>
                <if test="operation != null">#{operation,jdbcType=INTEGER},</if>
                <if test="type != null">#{type,jdbcType=INTEGER},</if>
                <if test="firstDirectName != null">#{firstDirectName,jdbcType=VARCHAR},</if>
                <if test="firstDirectId != null">#{firstDirectId,jdbcType=VARCHAR},</if>
                <if test="directoryName != null">#{directoryName,jdbcType=VARCHAR},</if>
                <if test="directoryId != null">#{directoryId,jdbcType=VARCHAR},</if>
                <if test="projectId != null">#{projectId,jdbcType=VARCHAR},</if>
                <if test="projectName != null">#{projectName,jdbcType=VARCHAR},</if>
                <if test="folderId != null">#{folder_id,jdbcType=VARCHAR},</if>
                <if test="folderName != null">#{folder_name,jdbcType=VARCHAR},</if>
                <if test="bindId != null">#{bindId,jdbcType=VARCHAR},</if>
                <if test="bindTime != null">#{bindTime,jdbcType=TIMESTAMP},</if>
                <if test="hasRemoved != null">#{hasRemoved,jdbcType=INTEGER},</if>
                <if test="syncStatus != null">#{syncStatus,jdbcType=INTEGER},</if>
                <if test="failMsg != null">#{failMsg,jdbcType=VARCHAR},</if>
                <if test="removedTime != null">#{removedTime,jdbcType=TIMESTAMP},</if>
                <if test="refreshId != null">#{refreshId,jdbcType=VARCHAR},</if>
                <if test="refreshTime != null">#{refreshTime,jdbcType=TIMESTAMP},</if>
                <if test="updateId != null">#{updateId,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>

        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig">
        update t_robot_knowledge_config
        <set>
                <if test="directoryCode != null">
                    directory_code = #{directoryCode,jdbcType=VARCHAR},
                </if>
                <if test="robotId != null">
                    robot_id = #{robotId,jdbcType=BIGINT},
                </if>
                <if test="robotCode != null">
                    robot_code = #{robotCode,jdbcType=VARCHAR},
                </if>
                <if test="operation != null">
                    operation = #{operation,jdbcType=INTEGER},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=INTEGER},
                </if>
                <if test="firstDirectName != null">
                    first_direct_name = #{firstDirectName,jdbcType=VARCHAR},
                </if>
                <if test="firstDirectId != null">
                    first_direct_id = #{firstDirectId,jdbcType=VARCHAR},
                </if>
                <if test="directoryName != null">
                    directory_name = #{directoryName,jdbcType=VARCHAR},
                </if>
                <if test="directoryId != null">
                    directory_id = #{directoryId,jdbcType=VARCHAR},
                </if>
                <if test="projectId != null">
                    project_id = #{projectId,jdbcType=VARCHAR},
                </if>
                <if test="projectName != null">
                    project_name = #{projectName,jdbcType=VARCHAR},
                </if>
                <if test="folderId != null">
                    folder_id = #{folderId,jdbcType=VARCHAR},
                </if>
                <if test="folderName != null">
                    folder_name = #{folderName,jdbcType=VARCHAR},
                </if>
                <if test="bindId != null">
                    bind_id = #{bindId,jdbcType=VARCHAR},
                </if>
                <if test="bindTime != null">
                    bind_time = #{bindTime,jdbcType=TIMESTAMP},
                </if>
                <if test="hasRemoved != null">
                    has_removed = #{hasRemoved,jdbcType=INTEGER},
                </if>
                <if test="syncStatus != null">
                    sync_status = #{syncStatus,jdbcType=INTEGER},
                </if>
                <if test="failMsg != null">
                    fail_msg = #{failMsg,jdbcType=VARCHAR},
                </if>
                <if test="removedTime != null">
                    removed_time = #{removedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="refreshId != null">
                    refresh_id = #{refreshId,jdbcType=VARCHAR},
                </if>
                <if test="refreshTime != null">
                    refresh_time = #{refreshTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateId != null">
                    update_id = #{updateId,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig">
        update t_robot_knowledge_config
        set 
            directory_code =  #{directoryCode,jdbcType=VARCHAR},
            robot_id =  #{robotId,jdbcType=BIGINT},
            robot_code =  #{robotCode,jdbcType=VARCHAR},
            operation =  #{operation,jdbcType=INTEGER},
            type =  #{type,jdbcType=INTEGER},
            first_direct_name =  #{firstDirectName,jdbcType=VARCHAR},
            first_direct_id =  #{firstDirectId,jdbcType=VARCHAR},
            directory_name =  #{directoryName,jdbcType=VARCHAR},
            directory_id =  #{directoryId,jdbcType=VARCHAR},
            project_id =  #{projectId,jdbcType=VARCHAR},
            project_name =  #{projectName,jdbcType=VARCHAR},
            folder_id =  #{folderId,jdbcType=VARCHAR},
            folder_name =  #{folderName,jdbcType=VARCHAR},
            bind_id =  #{bindId,jdbcType=VARCHAR},
            bind_time =  #{bindTime,jdbcType=TIMESTAMP},
            has_removed =  #{hasRemoved,jdbcType=INTEGER},
            sync_status =  #{syncStatus,jdbcType=INTEGER},
            fail_msg =  #{failMsg,jdbcType=VARCHAR},
            removed_time =  #{removedTime,jdbcType=TIMESTAMP},
            refresh_id =  #{refreshId,jdbcType=VARCHAR},
            refresh_time =  #{refreshTime,jdbcType=TIMESTAMP},
            update_id =  #{updateId,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <!--发布-->
    <update id="updateType" parameterType="java.lang.Long">
        update t_robot_knowledge_config
        set type =  1,
        where robot_id =  #{robotId,jdbcType=BIGINT}
    </update>

    <update id="updateByRobotIdAndDirect" parameterType="java.util.Map">
        update t_robot_knowledge_config
        set operation = #{operation,jdbcType=INTEGER}
        where robot_id =  #{robotId,jdbcType=BIGINT}
        and directory_id =  #{directoryId,jdbcType=VARCHAR}
        and type = #{type,jdbcType=INTEGER}
    </update>


    <update id="updateHasRemoved" parameterType="java.util.Map">
        update t_robot_knowledge_config
        set
            has_removed =  1,
            removed_time =  NOW()
        where directory_id =  #{directoryId,jdbcType=VARCHAR}
    </update>
</mapper>
