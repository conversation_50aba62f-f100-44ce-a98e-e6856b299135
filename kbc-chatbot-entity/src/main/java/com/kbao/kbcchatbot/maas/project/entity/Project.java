package com.kbao.kbcchatbot.maas.project.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR>
* @Description maas项目配置实体
* @Date 2024-12-19
*/
@Data
public class Project implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中name
     * 项目名称
     */  
	private String name;

    /**
     * 对应表中code
     * 项目code
     */  
	private String code;

    /**
     * 对应表中secret_key
     * 项目秘钥
     */  
	private String secretKey;

    /**
     * 对应表中status
     * 状态:0-正常，1-禁用
     */  
	private String status;

    /**
     * 对应表中chat_enable
     * 是否开启闲聊：0-否，1-是
     */  
	private String chatEnable;

    /**
     * 对应表中default_answer
     * 关闭闲聊时的默认回复
     */  
	private String defaultAnswer;

    /**
     * 对应表中final_words
     * 结束语
     */  
	private String finalWords;

    /**
     * 对应表中create_by
     * 创建人
     */  
	private String createBy;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中update_by
     * 更新人
     */  
	private String updateBy;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

}   