package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @program: kbc-chatbot
 * @description: 查询目录
 * @author: husw
 * @create: 2023-12-08 15:36
 **/
@Data
public class RobotKnowledgeDirectoryReqVO {

    private Long robotId;

    @NotNull(message = "机器人CODE不能为空！")
    private String robotCode;

    /**
     * 类型 0-测试版 1-正式版
     */
    @NotNull(message = "版本类型不能为空！")
    private Integer type;
    /**
     * 一级目录Id
     */
    private String firstDirectId;
    /**
     * 一级目录名称
     */
    private String firstDirectName;
    /**
     * 二级目录ID
     */
    private String directoryId;
    /**
     * 二级目录名称
     */
    private String directoryName;

    private Integer operationNoEq;
}
