<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.maas.channel.channelSession.dao.ChannelSessionMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.maas.channel.channelSession.entity.ChannelSession">
    	<id column="id" jdbcType="INTEGER"  property="id"  />
        <result property="sessionId" jdbcType="VARCHAR"  column="session_id" />  
        <result property="userId" jdbcType="VARCHAR"  column="user_id" />  
        <result property="channelCode" jdbcType="VARCHAR"  column="channel_code" />  
        <result property="title" jdbcType="VARCHAR"  column="title" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />
        <result property="isDelete" jdbcType="CHAR"  column="is_delete" />
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		session_id,  
		user_id,  
		channel_code,  
		title,  
		create_time,  
		is_delete,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.session_id, 
		t.user_id, 
		t.channel_code, 
		t.title, 
		t.create_time, 
		t.is_delete, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="sessionId != null and sessionId != ''">
	   		and t.session_id = #{sessionId,jdbcType=VARCHAR}
	    </if>
	    <if test="userId != null and userId != ''">
	   		and t.user_id = #{userId,jdbcType=VARCHAR}
	    </if>
	    <if test="channelCode != null and channelCode != ''">
	   		and t.channel_code = #{channelCode,jdbcType=VARCHAR}
	    </if>
	    <if test="title != null and title != ''">
	   		and t.title = #{title,jdbcType=VARCHAR}
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="isDelete != null">
	   		and t.is_delete = #{isDelete,jdbcType=CHAR}
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_channel_session t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from t_channel_session
		where  id = #{id,jdbcType=INTEGER}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from t_channel_session
		where id = #{id,jdbcType=INTEGER}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.maas.channel.channelSession.entity.ChannelSession">
		<!-- <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_channel_session(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=INTEGER}, 
                 
                #{sessionId,jdbcType=VARCHAR}, 
                 
                #{userId,jdbcType=VARCHAR}, 
                 
                #{channelCode,jdbcType=VARCHAR}, 
                 
                #{title,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{isDelete,jdbcType=CHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.maas.channel.channelSession.entity.ChannelSession">
		update t_channel_session
		<set>
	        <if test="sessionId != null ">  
	        	session_id = #{sessionId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="userId != null ">  
	        	user_id = #{userId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="channelCode != null ">  
	        	channel_code = #{channelCode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="title != null ">  
	        	title = #{title,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="isDelete != null ">  
	        	is_delete = #{isDelete,jdbcType=CHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>


	<!-- 自定义查询 -->
	<select id="getSessionList" resultMap="BaseResultMap">
		select session_id, title, create_time, update_time from t_channel_session
		where user_id = #{userId}
		and channel_code = #{channelCode}
		and is_delete = '0'
		order by update_time desc
	</select>

	<update id="renameSession">
		update t_channel_session set title = #{title} where session_id = #{sessionId}
	</update>
</mapper>
