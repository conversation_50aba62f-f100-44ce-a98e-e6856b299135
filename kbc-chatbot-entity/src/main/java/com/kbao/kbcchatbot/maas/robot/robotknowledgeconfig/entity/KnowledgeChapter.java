package com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity;

import com.kbao.kbcchatbot.maas.chatsession.enums.ChatKnowledgeTypeEnum;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @program: kbc-chatbot
 * @description: 历程图章节
 * @author: husw
 * @create: 2023-06-06 11:16
 **/
@Data
public class KnowledgeChapter {
    @Field(type = FieldType.Keyword)
    @Id
    private String id;
    /**
     * 所属知识id
     */
    @Field(type = FieldType.Keyword)
    private String knowledgeId;
    /**
     * 序号
     */
    @Field(type = FieldType.Integer)
    private Integer sort;
    /**
     * 类型 1图文，2视频，3文件
     * 字典：km.knowledge.chapter.type
     */
    @Field(type = FieldType.Keyword)
    private String chapterType;
    /**
     * 章节标题
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String chapterTitle;
    /**
     * 是否显示章节标题，1显示，0不显示"
     */
    @Field(type = FieldType.Integer)
    private Integer showChapterTitle;
    /**
     * 章节内容
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String chapterContent;
    /**
     * 附件名称
     */
    @Field(type = FieldType.Keyword)
    private String enclosureName;
    /**
     * 附件大小
     */
    @Field(type = FieldType.Keyword)
    private String enclosureSize;
    /**
     * 附件地址
     */
    @Field(type = FieldType.Keyword)
    private String enclosureUrl;

    public String getChapterAnswer() {
        if (ChatKnowledgeTypeEnum.FILE.getKey().equals(chapterType)
                || ChatKnowledgeTypeEnum.VIDEO.getKey().equals(chapterType)) {
            //文件附件
            return enclosureUrl;
        }else {
            return chapterContent;
        }
    }
}
