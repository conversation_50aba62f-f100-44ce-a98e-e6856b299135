package com.kbao.kbcchatbot.maas.robot.robotknowledge.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* <AUTHOR>
* @Description maas知识数据实体
* @Date 2024-12-12
*/
@Data
public class RobotKnowledgeData implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 自增主键
     */  
	private Integer id;

    /**
     * 对应表中robot_code
     * 机器人编码
     */  
	private String robotCode;

    /**
     * 对应表中item_id
     * 章节ID
     */  
	private String itemId;

    /**
     * 对应表中project_code
     * 项目code
     */  
	private String projectCode;

    /**
     * 对应表中project_item_id
     * 项目语料id
     */  
	private String projectItemId;

    /**
     * 对应表中knowledge_id
     * 外部知识id
     */  
	private String knowledgeId;

    /**
     * 对应表中title
     * 知识标题
     */  
	private String title;

    /**
     * 对应表中data_type
     * 数据类型
     */  
	private String dataType;

    /**
     * 对应表中content_length
     * 知识内容长度
     */  
	private Integer contentLength;

    /**
     * 对应表中status
     * 状态:0-上架，1-下架
     */  
	private String status;

    /**
     * 对应表中sync_status
     * 同步状态：0-成功，1-失败
     */  
	private String syncStatus;

    /**
     * 对应表中is_check
     * 检查知识是否可用:0-待检查，1-已检查
     */  
	private String isCheck;

    /**
     * 对应表中merge_item_id
     * 合并后item_id
     */  
	private String mergeItemId;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

    /**
     * 对应表中sync_time
     * 同步时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date syncTime;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中is_delete
     * 是否删除：0-否，1-是
     */  
	private String isDelete;

}   