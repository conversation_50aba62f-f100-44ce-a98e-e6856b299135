package com.kbao.kbcchatbot.maas.chatsessionrecord;

import com.kbao.kbcchatbot.externalapi.enums.LargeAnswerSourceEnum;
import com.kbao.tool.util.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Document(collection = "ChatSessionRecord")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatSessionRecord {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 聊天会话ID
     */
    @Indexed
    private String sessionId;
    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 机器人编码
     */
    private String robotCode;

    /**
     * 轮次ID
     */
    private String roundId;

    /**
     * 对话场景ID
     */
    @Deprecated
    private String sceneId;

    /**
     * 答案ID
     */
    private String answerId;

    /**
     * 场景对话展示类型
     */
    @Deprecated
    private Integer sceneShowType;

    /**
     * 聊天主体 1-用户 2-机器人
     */
    private String subject;

    /**
     * 聊天内容
     */
    private Object content;

    /**
     * 用户提问类型
     */
    @Deprecated
    private String questionType;

    /**
     * 用户提问业务ID
     */
    @Deprecated
    private String questionBizId;

    /**
     * 用户提问澄清记录ID
     */
    @Deprecated
    private String questionClearRecordId;

    /**
     * 机器人回复类型
     */
    @Deprecated
    private String answerType;

    /**
     * 机器人回复qaId
     */
    private String qaId;

    /**
     * 机器人回复qa来源
     */
    @Deprecated
    private String qaSource;
    /**
     * 关联知识ID
     */
    @Deprecated
    private String relatedArticles;
    /**
     * 关联知识标题
     */
    @Deprecated
    private String relatedArticlesName;

    /**
     * 评价结果 0-点踩 1-点赞
     */
    private String voteResult;

    /**
     * 点踩原因
     */
    private String voteRemark;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 客服消息sid
     */
    private String sid;

    /**
     * 客服消息mid
     */
    private String mid;

    /**
     * 模型名称
     */
    private String model;
    /**
     * 答案类型
     * @see LargeAnswerSourceEnum
     */
    private String answerSource;
    /**
     * 耗时
     */
    private String cost;
    /**
     * 设备类型 0-APP 1-web
     */
    private String deviceType;
    /**
     * 问题补全
     */
    private String contextualizeQuestion;

    private List<Map> files;
    /**
     * 其它参数
     */
    private Map<String,Object> customParam;

    /**
     * 创建时间（精确到毫秒）
     */
    @Indexed
    private String createTime;

    private String tenantId;

    @Transient
    public String recordCreateTime() {
        return DateUtils.date2Str(new Date(), "yyyy-MM-dd HH:mm:ss.SSS");
    }
    @Transient
    private String channelName;
    @Transient
    private Long robotId;
    @Transient
    private String robotName;

    private String isCopy;
}
