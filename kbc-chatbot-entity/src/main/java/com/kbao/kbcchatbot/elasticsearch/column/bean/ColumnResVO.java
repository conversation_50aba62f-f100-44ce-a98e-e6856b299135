package com.kbao.kbcchatbot.elasticsearch.column.bean;

import com.kbao.kbcchatbot.elasticsearch.column.entity.Column;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @BelongsProject: kbc-search
 * @BelongsPackage: com.kbao.kbcsearch.column.bean
 * @Author: 徐乐
 * @CreateTime: 2020-12-28 09:47
 * @Description:
 */
@Data
@NoArgsConstructor
public class ColumnResVO extends Column implements Serializable {
    
    private static final long serialVersionUID = -6006832478599401153L;
    
    private String typeLevel;
    
}
