package com.kbao.kbcchatbot.elasticsearch.index.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @BelongsProject: kbc-search
 * @BelongsPackage: com.kbao.kbcsearch.index.bean
 * @Author: 徐乐
 * @CreateTime: 2020-11-16 17:02
 * @Description:
 */
@Data
@NoArgsConstructor
public class IndexReqVO implements Serializable {

    /**
     * 对应表中ind_id
     * 索引ID
     */
    private String indId;
    /**
     * 对应表中app_id
     * 应用ID
     */
    private String appId;
    /**
     * 对应表中table_name
     * 关联表名
     */
    private String tableName;
    /**
     * 对应表中name
     * 索引名称
     */
    private String name;
    /**
     * 对应表中alias_name
     * 索引别名
     */
    private String aliasName;
    /**
     * 对应表中index_name
     * es索引真实名称
     */
    private String indexName;

}
