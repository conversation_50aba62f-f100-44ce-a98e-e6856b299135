package com.kbao.kbcchatbot.elasticsearch.queryanalysis.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @BelongsProject: kbc-search
 * @BelongsPackage: com.kbao.kbcsearch.queryanalysis.bean
 * @Author: 徐乐
 * @CreateTime: 2020-11-16 11:56
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryAnalysisReqVO implements Serializable {

    private static final long serialVersionUID = -4509370826393938650L;

    /**
     * 对应表中ind_id
     * 查询ID
     */
    private String analyId;
    /**
     * 对应表中ind_id
     * 索引ID
     */
    private String indId;
    /**
     * 对应表中query_column
     * 检索字段 存储字段ID，逗号分隔
     */
    private String queryColumn;
    /**
     * 对应表中query_analysis
     * 查询词解析功能 01-停用词，02-拼音纠错，03-同义词（多个用逗号分隔）
     */
    private String queryAnalysis;
    /**
     * 对应表中return_column
     * 返回字段 存储字段ID, 逗号分隔
     */
    private String returnColumn;
    /**
     * 对应表中analysis_status
     * 状态 1-正常，2-配置中，3-停用
     */
    private String analysisStatus;

}
