<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.elasticsearch.column.dao.ColumnMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.elasticsearch.column.entity.Column">
    	<id column="column_id" jdbcType="VARCHAR"  property="columnId"  />
        <result property="indId" jdbcType="VARCHAR"  column="ind_id" />  
        <result property="columnName" jdbcType="VARCHAR"  column="column_name" />  
        <result property="columnType" jdbcType="VARCHAR"  column="column_type" />  
        <result property="analyzer" jdbcType="VARCHAR"  column="analyzer" />  
        <result property="fieldName" jdbcType="VARCHAR"  column="field_name" />  
        <result property="isAnalysis" jdbcType="VARCHAR"  column="is_analysis" />  
        <result property="analysisMode" jdbcType="VARCHAR"  column="analysis_mode" />  
        <result property="defaultIsAnalysis" jdbcType="VARCHAR"  column="default_is_analysis" />  
        <result property="columnWeight" jdbcType="REAL"  column="column_weight" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="isDeleted" jdbcType="INTEGER"  column="is_deleted" />  
	</resultMap>

	<sql id="Base_Column_List">
		column_id,  
		ind_id,  
		column_name,  
		column_type,  
		analyzer,  
		field_name,  
		is_analysis,  
		analysis_mode,  
		default_is_analysis,  
		column_weight,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.column_id, 
		t.ind_id, 
		t.column_name, 
		t.column_type, 
		t.analyzer, 
		t.field_name, 
		t.is_analysis, 
		t.analysis_mode, 
		t.default_is_analysis, 
		t.column_weight, 
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0 
	    <if test="indId != null and indId != ''">
	   		and t.ind_id = #{indId,jdbcType=VARCHAR}  
	    </if>
	    <if test="columnName != null and columnName != ''">
	   		and t.column_name = #{columnName,jdbcType=VARCHAR}  
	    </if>
	    <if test="columnType != null and columnType != ''">
	   		and t.column_type = #{columnType,jdbcType=VARCHAR}  
	    </if>
	    <if test="analyzer != null and analyzer != ''">
	   		and t.analyzer = #{analyzer,jdbcType=VARCHAR}  
	    </if>
	    <if test="fieldName != null and fieldName != ''">
	   		and t.field_name = #{fieldName,jdbcType=VARCHAR}  
	    </if>
	    <if test="isAnalysis != null and isAnalysis != ''">
	   		and t.is_analysis = #{isAnalysis,jdbcType=VARCHAR}  
	    </if>
	    <if test="analysisMode != null and analysisMode != ''">
	   		and t.analysis_mode = #{analysisMode,jdbcType=VARCHAR}  
	    </if>
	    <if test="defaultIsAnalysis != null and defaultIsAnalysis != ''">
	   		and t.default_is_analysis = #{defaultIsAnalysis,jdbcType=VARCHAR}  
	    </if>
	    <if test="columnWeight != null">
	   		and t.column_weight = #{columnWeight,jdbcType=REAL}  
	    </if>
	    <if test="createTime != null">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}  
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="isDeleted != null">
	   		and t.is_deleted = #{isDeleted,jdbcType=INTEGER}  
	    </if>
		<!-- 自定义条件-->
		<if test="columnIds != null and columnIds.length > 0">
			and t.column_id in
			<foreach collection="columnIds" item="columnId" index="index"
					 open="(" close=")" separator=",">
				#{columnId}
			</foreach>
		</if>
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_es_column t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_es_column t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_es_column
		where  is_deleted = 0 and column_id = #{columnId,jdbcType=VARCHAR}
	</select>


	<!-- 根据主键删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.String">
		update t_es_column set is_deleted = 1
		where column_id = #{columnId,jdbcType=VARCHAR} and is_deleted = 0
	</update>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.elasticsearch.column.entity.Column">
		insert into t_es_column(
			<include refid="Base_Column_List" />
		)
		values(  
                #{columnId,jdbcType=VARCHAR}, 
                 
                #{indId,jdbcType=VARCHAR}, 
                 
                #{columnName,jdbcType=VARCHAR}, 
                 
                #{columnType,jdbcType=VARCHAR}, 
                 
                #{analyzer,jdbcType=VARCHAR}, 
                 
                #{fieldName,jdbcType=VARCHAR}, 
                 
                #{isAnalysis,jdbcType=VARCHAR}, 
                 
                #{analysisMode,jdbcType=VARCHAR}, 
                 
                #{defaultIsAnalysis,jdbcType=VARCHAR}, 
                 
                #{columnWeight,jdbcType=REAL}, 
                 
                #{createTime,jdbcType=TIMESTAMP}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=TIMESTAMP}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{isDeleted,jdbcType=INTEGER} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.elasticsearch.column.entity.Column">
		insert into t_es_column
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="columnId != null ">  
	       		column_id,
	        </if>  
	        <if test="indId != null ">  
	       		ind_id,
	        </if>  
	        <if test="columnName != null ">  
	       		column_name,
	        </if>  
	        <if test="columnType != null ">  
	       		column_type,
	        </if>  
	        <if test="analyzer != null ">  
	       		analyzer,
	        </if>  
	        <if test="fieldName != null ">  
	       		field_name,
	        </if>  
	        <if test="isAnalysis != null ">  
	       		is_analysis,
	        </if>  
	        <if test="analysisMode != null ">  
	       		analysis_mode,
	        </if>  
	        <if test="defaultIsAnalysis != null ">  
	       		default_is_analysis,
	        </if>  
	        <if test="columnWeight != null ">  
	       		column_weight,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="isDeleted != null ">  
	       		is_deleted,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="columnId != null">  
            	#{columnId,jdbcType=VARCHAR},
            </if>  
            <if test="indId != null">  
            	#{indId,jdbcType=VARCHAR},
            </if>  
            <if test="columnName != null">  
            	#{columnName,jdbcType=VARCHAR},
            </if>  
            <if test="columnType != null">  
            	#{columnType,jdbcType=VARCHAR},
            </if>  
            <if test="analyzer != null">  
            	#{analyzer,jdbcType=VARCHAR},
            </if>  
            <if test="fieldName != null">  
            	#{fieldName,jdbcType=VARCHAR},
            </if>  
            <if test="isAnalysis != null">  
            	#{isAnalysis,jdbcType=VARCHAR},
            </if>  
            <if test="analysisMode != null">  
            	#{analysisMode,jdbcType=VARCHAR},
            </if>  
            <if test="defaultIsAnalysis != null">  
            	#{defaultIsAnalysis,jdbcType=VARCHAR},
            </if>  
            <if test="columnWeight != null">  
            	#{columnWeight,jdbcType=REAL},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=TIMESTAMP},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="isDeleted != null">  
            	#{isDeleted,jdbcType=INTEGER},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.elasticsearch.column.entity.Column">
		update t_es_column
		<set>
	        <if test="indId != null ">  
	        	ind_id = #{indId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="columnName != null ">  
	        	column_name = #{columnName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="columnType != null ">  
	        	column_type = #{columnType,jdbcType=VARCHAR},  
	        </if>  
	        <if test="analyzer != null ">  
	        	analyzer = #{analyzer,jdbcType=VARCHAR},  
	        </if>  
	        <if test="fieldName != null ">  
	        	field_name = #{fieldName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="isAnalysis != null ">  
	        	is_analysis = #{isAnalysis,jdbcType=VARCHAR},  
	        </if>  
	        <if test="analysisMode != null ">  
	        	analysis_mode = #{analysisMode,jdbcType=VARCHAR},  
	        </if>  
	        <if test="defaultIsAnalysis != null ">  
	        	default_is_analysis = #{defaultIsAnalysis,jdbcType=VARCHAR},  
	        </if>  
	        <if test="columnWeight != null ">  
	        	column_weight = #{columnWeight,jdbcType=REAL},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="isDeleted != null ">  
	        	is_deleted = #{isDeleted,jdbcType=INTEGER},  
	        </if>  
		</set>
		where column_id = #{columnId,jdbcType=VARCHAR}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.elasticsearch.column.entity.Column">
		update t_es_column
		set
           ind_id = #{indId,jdbcType=VARCHAR},
           column_name = #{columnName,jdbcType=VARCHAR},
           column_type = #{columnType,jdbcType=VARCHAR},
           analyzer = #{analyzer,jdbcType=VARCHAR},
           field_name = #{fieldName,jdbcType=VARCHAR},
           is_analysis = #{isAnalysis,jdbcType=VARCHAR},
           analysis_mode = #{analysisMode,jdbcType=VARCHAR},
           default_is_analysis = #{defaultIsAnalysis,jdbcType=VARCHAR},
           column_weight = #{columnWeight,jdbcType=REAL},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           create_id = #{createId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=TIMESTAMP},
           update_id = #{updateId,jdbcType=VARCHAR},
           is_deleted = #{isDeleted,jdbcType=INTEGER}
		where column_id = #{columnId,jdbcType=VARCHAR}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_es_column(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.columnId}
	        <choose>
	            <when test="item.indId != null">,#{item.indId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.columnName != null">,#{item.columnName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.columnType != null">,#{item.columnType}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.analyzer != null">,#{item.analyzer}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.fieldName != null">,#{item.fieldName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isAnalysis != null">,#{item.isAnalysis}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.analysisMode != null">,#{item.analysisMode}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.defaultIsAnalysis != null">,#{item.defaultIsAnalysis}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.columnWeight != null">,#{item.columnWeight}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_es_column(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.columnId}
			<choose><when test="item.indId != null">,#{item.indId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.columnName != null">,#{item.columnName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.columnType != null">,#{item.columnType}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.analyzer != null">,#{item.analyzer}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.fieldName != null">,#{item.fieldName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isAnalysis != null">,#{item.isAnalysis}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.analysisMode != null">,#{item.analysisMode}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.defaultIsAnalysis != null">,#{item.defaultIsAnalysis}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.columnWeight != null">,#{item.columnWeight}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   ind_id=values(ind_id), 
		   column_name=values(column_name), 
		   column_type=values(column_type), 
		   analyzer=values(analyzer), 
		   field_name=values(field_name), 
		   is_analysis=values(is_analysis), 
		   analysis_mode=values(analysis_mode), 
		   default_is_analysis=values(default_is_analysis), 
		   column_weight=values(column_weight), 
		   create_time=values(create_time), 
		   create_id=values(create_id), 
		   update_time=values(update_time), 
		   update_id=values(update_id), 
		   is_deleted=values(is_deleted) 
	</update>
	
	<!-- 批量删除-->
	<update id="batchDelete" parameterType="java.util.List">
	update t_es_column set is_deleted = 1 where column_id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item.columnId}
		</foreach>
	and is_deleted = 0
	</update>
	
	<!-- 自定义查询 -->
	<!-- 根据条件查询并排序-->
	<select id="selectAllSort" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_es_column t
		<include refid="Base_Condition" />
		order by t.create_time
	</select>
</mapper>
