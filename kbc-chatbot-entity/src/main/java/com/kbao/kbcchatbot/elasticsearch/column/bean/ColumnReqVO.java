package com.kbao.kbcchatbot.elasticsearch.column.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @BelongsProject: kbc-search
 * @BelongsPackage: com.kbao.kbcsearch.column.bean
 * @Author: 徐乐
 * @CreateTime: 2020-11-16 15:42
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ColumnReqVO implements Serializable {

    private static final long serialVersionUID = 459891871241988251L;

    /**
     * 对应表中column_id
     * 表ID
     */
    private String columnId;

    /**
     * 对应表中ind_id
     * 索引ID
     */
    private String indId;

    /**
     * 对应表中column_name
     * 索引字段名
     */
    private String columnName;

    /**
     * 对应表中column_type
     * 索引字段类型
     */
    private String columnType;

    /**
     * 对应表中analyzer
     * 索引分词器
     */
    private String analyzer;

    /**
     * 对应表中field_name
     * 字段显示名称
     */
    private String fieldName;

    /**
     * 对应表中is_analysis
     * 是否检索 Y-检索，N-不检索
     */
    private String isAnalysis;

    /**
     * 对应表中analysis_mode
     * 分析方式 0-分词，1-全词匹配，2-模糊查询
     */
    private String analysisMode;

    /**
     * 对应表中default_is_analysis
     * 索引内是否位索引字段 Y-是，N-否
     */
    private String defaultIsAnalysis;

    /**
     * 对应表中column_weight
     * 字段权重
     */
    private Float columnWeight;

    /**
     * 
     * 实体等级
     */
    private String typeLevel;

}
