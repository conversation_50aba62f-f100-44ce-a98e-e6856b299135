package com.kbao.kbcchatbot.elasticsearch.index.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 对应表 t_index
 * 索引表
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Index implements Serializable{
	private static final long serialVersionUID = 1L;

    /**
     * 对应表中ind_id
     * 表ID
     */
	private String indId;


    /**
     * 对应表中app_id
     * 应用ID
     */

	private String appId;

    /**
     * 对应表中table_name
     * 关联表名
     */
	private String tableName;

    /**
     * 对应表中name
     * 索引名称
     */

	private String name;

    /**
     * 对应表中alias_name
     * 索引别名
     */

    private String aliasName;

	/**
	 * 对应表中index_name
	 * es索引真实名称
	 */

	private String indexName;

    /**
     * 对应表中create_time
     * 创建时间
     */

	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;


    /**
     * 对应表中create_id
     * 创建人
     */

	private String createId;

    /**
     * 对应表中update_time
     * 更新时间
     */

	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    /**
     * 对应表中update_id
     * 更新人ID
     */
	private String updateId;

    /**
     * 对应表中is_deleted
     *
     */
	private Integer isDeleted;

	private String userName;
	
}
