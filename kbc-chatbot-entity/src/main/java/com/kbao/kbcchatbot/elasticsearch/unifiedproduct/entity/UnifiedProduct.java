package com.kbao.kbcchatbot.elasticsearch.unifiedproduct.entity;

import com.kbao.kbcchatbot.elasticsearch.unifiedproduct.bean.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 统一产品库ES实体类
 * <AUTHOR>
 * @Date 2023-7-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = "#{@unifiedProductEsAttribute.indexName}", createIndex = false)
public class UnifiedProduct implements Serializable {

    private static final long serialVersionUID = 7391977090779219165L;

    /**
     * 统一产品主键
     */
    @Id
    private String unifiedId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 统一产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品简称
     */
    private String shortProductName;

    /**
     * 产品渠道
     */
    private String productChannel;

    /**
     * 保险公司信息
     */
    private InsuranceCompany company;

    /**
     * 险种一级类型信息
     */
    private InsureType insureTypeOne;

    /**
     * 险种二级类型信息
     */
    private InsureType insureTypeTwo;

    /**
     * 产品来源
     */
    private String source;

    /**
     * 销售渠道 销售渠道 broker-经代 internet-互联网 personal-个代 bank-银保 telemarketing-电销
     */
    private String saleChannel;

    /**
     * 产品上下架状态 产品上下架状态 0-下架 1-上架
     */
    private String productStatus;

    /**
     * 销售状态 当前销售状态 0-停售 1-出售 2-预售
     */
    private String forSale;

    /**
     * 拼音全拼
     */
    private String fullPinyin;

    /**
     * 拼音简称
     */
    private String shortPinyin;

    /**
     * 是否电投 0-否，1-是
     */
    private String online;

    /**
     * 是否司内 0-否，1-是
     */
    private String signing;

    /**
     * 是否组合 0-否，1-是
     */
    private String groupProduct;

    /**
     * 产品类型 M-主险 R-附加险
     */
    private String productType;

    /**
     * app是否展示 0-否，1-是
     */
    private String listShow;

    /**
     * 是否加入对比 0-否，1-是
     */
    private String contrast;

    /**
     * feed流图路径
     */
    private String homeHeaderImgPath;

    /**
     * banner图路径
     */
    private String productImgPath;

    /**
     * 列表图路径
     */
    private String listImgPath;

    /**
     * 产品小图 -- 分享配图
     */
    public String productMinImgPath;

    /**
     * 最高司内总推广费
     */
    private String maxInternalTotal;

    /**
     * 最高司外总推广费
     */
    private String maxExternalTotal;

    /**
     * 活动标签
     */
    private String activityLabelPath;

    /**
     * 投保条件
     */
    private InsuranceCondition insuranceCondition;

    /**
     * 险种责任组
     */
    private List<ProductLiabilityGroup> liabilityGroupList;

    /**
     * 产品全量标签
     */
    private List<ProductLabel> productFullLabelList;

    /**
     * 产品展示标签
     */
    private List<ProductLabel> productDisplayLabelList;

    /**
     * 产品标准标签
     */
    private List<ProductLabel> productStandardLabelList;

    /**
     * 疾病信息
     */
    private List<Disease> diseaseDtoList;

    /**
     * 高医服务商信息
     */
    private List<Partner> partnerDtoList;

    /**
     * 关键字
     */
    private List<String> keyWords;

    /**
     * 账号类型 1-无 2-线上（推广费） 3-线下（佣金）
     */
    private Integer accountType;

    /**
     * 交费类型 period-期交 short-短险 service-服务产品
     */
    private String payType;

    /**
     * 业务渠道 internet-简易,life-寿险,gai-团财,medical-高医,rescue-救援,health-健康
     */
    private String businessChannel;

    /**
     * 用户点击量用户数据
     */
    private List<String> hitsDtoUserIdList;

    /**
     * 用户点击量点击量数据
     */
    private List<Integer> hitsDtoNumList;

    /**
     * 销售平台，app-APP，mall-商城
     */
    public List<String> salesPlatformList;

    /**
     * 产品条款名称
     */
    private List<String> productSourceName;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
