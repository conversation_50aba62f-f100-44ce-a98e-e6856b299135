package com.kbao.kbcchatbot.elasticsearch.queryanalysis.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**  
 * 对应表 t_query_analysis
 * 查询词分析配置  
 *  
 */ 
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryAnalysis implements Serializable{
	private static final long serialVersionUID = 1L;
 
	
    /**
     * 对应表中analy_id
     * 查询ID
     */
    @Id
	private String analyId;
	
    /**
     * 对应表中ind_id
     * 索引ID
     */  
	private String indId;
	
    /**
     * 对应表中query_column
     * 检索字段 存储字段ID，逗号分隔
     */  
	private String queryColumn;
	
    /**
     * 对应表中query_analysis
     * 查询词解析功能 01-停用词，02-拼音纠错，03-同义词（多个用逗号分隔）
     */  
	private String queryAnalysis;

    /**
     * 对应表中return_column
     * 返回字段 存储字段ID, 逗号分隔
     */
    private String returnColumn;
	
    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;
	
    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
    /**
     * 对应表中update_id
     * 更新人ID
     */  
	private String updateId;
	
    /**
     * 对应表中analysis_status
     * 状态 1-正常，2-配置中，3-停用
     */  
	private String analysisStatus;
	
    /**
     * 对应表中is_deleted
     * 
     */  
	private Integer isDeleted;
  
}   