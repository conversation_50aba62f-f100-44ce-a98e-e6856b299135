<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.elasticsearch.index.dao.IndexMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.elasticsearch.index.entity.Index">
		<id column="ind_id" jdbcType="VARCHAR"  property="indId"  />
		<result property="appId" jdbcType="VARCHAR"  column="app_id" />
		<result property="tableName" jdbcType="VARCHAR"  column="table_name" />
		<result property="name" jdbcType="VARCHAR"  column="name" />
		<result property="aliasName" jdbcType="VARCHAR"  column="alias_name" />
		<result property="indexName" jdbcType="VARCHAR"  column="index_name" />
		<result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />
		<result property="createId" jdbcType="VARCHAR"  column="create_id" />
		<result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />
		<result property="updateId" jdbcType="VARCHAR"  column="update_id" />
		<result property="isDeleted" jdbcType="INTEGER"  column="is_deleted" />
	</resultMap>

	<sql id="Base_Column_List">
		ind_id,  
		app_id,  
		table_name,  
		name,  
		alias_name,  
		index_name,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.ind_id, 
		t.app_id, 
		t.table_name, 
		t.name, 
		t.alias_name, 
		t.index_name, 
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0
			<if test="appId != null and appId != ''">
				and t.app_id = #{appId,jdbcType=VARCHAR}
			</if>

			<if test="tableName != null and tableName != ''">
				and t.table_name = #{tableName,jdbcType=VARCHAR}
			</if>

			<if test="name != null and name != ''">
				and t.name = #{name,jdbcType=VARCHAR}
			</if>

			<if test="aliasName != null and aliasName != ''">
				and t.alias_name = #{aliasName,jdbcType=VARCHAR}
			</if>

			<if test="indexName != null and indexName != ''">
				and t.index_name = #{indexName,jdbcType=VARCHAR}
			</if>

			<if test="createTime != null">
				and t.create_time = #{createTime,jdbcType=TIMESTAMP}
			</if>

			<if test="createId != null and createId != ''">
				and t.create_id = #{createId,jdbcType=VARCHAR}
			</if>

			<if test="updateTime != null">
				and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
			</if>

			<if test="updateId != null and updateId != ''">
				and t.update_id = #{updateId,jdbcType=VARCHAR}
			</if>

			<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.elasticsearch.index.entity.Index">
		insert into t_es_index(
		<include refid="Base_Column_List" />
		)
		values(
		#{indId,jdbcType=VARCHAR},

		#{appId,jdbcType=VARCHAR},

		#{tableName,jdbcType=VARCHAR},

		#{name,jdbcType=VARCHAR},

		#{aliasName,jdbcType=VARCHAR},

		#{indexName,jdbcType=VARCHAR},

		#{createTime,jdbcType=TIMESTAMP},

		#{createId,jdbcType=VARCHAR},

		#{updateTime,jdbcType=TIMESTAMP},

		#{updateId,jdbcType=VARCHAR},

		#{isDeleted,jdbcType=INTEGER}
		)
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.elasticsearch.index.entity.Index">
		insert into t_es_index
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="indId != null and indId != ''">
				ind_id,
			</if>

			<if test="appId != null and appId != ''">
				app_id,
			</if>

			<if test="tableName != null and tableName != ''">
				table_name,
			</if>

			<if test="name != null and name != ''">
				name,
			</if>

			<if test="aliasName != null and aliasName != ''">
				alias_name,
			</if>

			<if test="indexName != null and indexName != ''">
				index_name,
			</if>

			<if test="createTime != null">
				create_time,
			</if>

			<if test="createId != null and createId != ''">
				create_id,
			</if>

			<if test="updateTime != null">
				update_time,
			</if>

			<if test="updateId != null and updateId != ''">
				update_id,
			</if>

			<if test="isDeleted != null">
				is_deleted,
			</if>

		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="indId != null and indId != ''">
				#{indId,jdbcType=VARCHAR},
			</if>
			<if test="appId != null and appId != ''">
				#{appId,jdbcType=VARCHAR},
			</if>
			<if test="tableName != null and tableName != ''">
				#{tableName,jdbcType=VARCHAR},
			</if>
			<if test="name != null and name != ''">
				#{name,jdbcType=VARCHAR},
			</if>
			<if test="aliasName != null and aliasName != ''">
				#{aliasName,jdbcType=VARCHAR},
			</if>
			<if test="indexName != null and indexName != ''">
				#{indexName,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createId != null and createId != ''">
				#{createId,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateId != null and updateId != ''">
				#{updateId,jdbcType=VARCHAR},
			</if>
			<if test="isDeleted != null">
				#{isDeleted,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_es_index(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
			(
			#{item.indId}
			<choose>
				<when test="item.appId != null and item.appId !='' ">,#{item.appId}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.tableName != null and item.tableName !='' ">,#{item.tableName}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.name != null and item.name !='' ">,#{item.name}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.aliasName != null and item.aliasName !='' ">,#{item.aliasName}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.indexName != null and item.indexName !='' ">,#{item.indexName}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.createId != null and item.createId !='' ">,#{item.createId}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.updateId != null and item.updateId !='' ">,#{item.updateId}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
			</choose>
			)
		</foreach>
	</insert>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.elasticsearch.index.entity.Index">
		update t_es_index
		set
			app_id = #{appId,jdbcType=VARCHAR},
			table_name = #{tableName,jdbcType=VARCHAR},
			name = #{name,jdbcType=VARCHAR},
			alias_name = #{aliasName,jdbcType=VARCHAR},
			index_name = #{indexName,jdbcType=VARCHAR},
			create_time = #{createTime,jdbcType=TIMESTAMP},
			create_id = #{createId,jdbcType=VARCHAR},
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			update_id = #{updateId,jdbcType=VARCHAR},
			is_deleted = #{isDeleted,jdbcType=INTEGER}
		where ind_id = #{indId,jdbcType=VARCHAR}
	</update>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.elasticsearch.index.entity.Index">
		update t_es_index
		<set>
			<if test="appId != null and appId !='' ">
				app_id = #{appId,jdbcType=VARCHAR},
			</if>
			<if test="tableName != null and tableName !='' ">
				table_name = #{tableName,jdbcType=VARCHAR},
			</if>
			<if test="name != null and name !='' ">
				name = #{name,jdbcType=VARCHAR},
			</if>
			<if test="aliasName != null and aliasName !='' ">
				alias_name = #{aliasName,jdbcType=VARCHAR},
			</if>
			<if test="indexName != null and indexName !='' ">
				index_name = #{indexName,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createId != null and createId !='' ">
				create_id = #{createId,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateId != null and updateId !='' ">
				update_id = #{updateId,jdbcType=VARCHAR},
			</if>
			<if test="isDeleted != null">
				is_deleted = #{isDeleted,jdbcType=INTEGER},
			</if>
		</set>
		where ind_id = #{indId,jdbcType=VARCHAR}
	</update>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_es_index(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
			(
			#{item.indId}
			<choose><when test="item.appId != null and item.appId != ''">,#{item.appId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tableName != null and item.tableName != ''">,#{item.tableName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.name != null and item.name != ''">,#{item.name}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.aliasName != null and item.aliasName != ''">,#{item.aliasName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.indexName != null and item.indexName != ''">,#{item.indexName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null and item.createId != ''">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null and item.updateId != ''">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise></choose>
			)
		</foreach>
		on duplicate key update
		app_id=values(app_id),
		table_name=values(table_name),
		name=values(name),
		alias_name=values(alias_name),
		index_name=values(index_name),
		create_time=values(create_time),
		create_id=values(create_id),
		update_time=values(update_time),
		update_id=values(update_id),
		is_deleted=values(is_deleted)
	</update>

	<!-- 逻辑删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.String">
		update t_es_index set is_deleted = 1
		where ind_id = #{indId,jdbcType=VARCHAR} and is_deleted = 0
	</update>

	<!-- 批量逻辑删除 -->
	<update id="batchDelete" parameterType="java.util.List">
		update t_es_index set is_deleted = 1 where ind_id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		and is_deleted = 0
	</update>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_es_index
		where ind_id = #{indId,jdbcType=VARCHAR}
	</select>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_es_index t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_es_index t
		<include refid="Base_Condition" />
	</select>
	<!-- 自定义查询 -->

	<select id="isExistIndexName" resultType="java.lang.Integer" parameterType="com.kbao.kbcchatbot.elasticsearch.index.bean.IndexReqVO">
		select count(0)
		from t_es_index t
		where t.name = #{name}
		and t.is_deleted = 0
		<if test="indId != null and indId != ''">
			and t.ind_id != #{indId}
		</if>
	</select>

</mapper>
