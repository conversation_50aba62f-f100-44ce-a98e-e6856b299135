package com.kbao.kbcchatbot.elasticsearch.unifiedproduct.bean;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @Description 统一产品库索引属性
 * <AUTHOR>
 * @Date 2023-7-14
 */
//@Data
//@Component
//@RefreshScope
//public class UnifiedProductEsAttribute {
//
//    @Value("${es.indexName.unifiedProduct:sta-kbcs-search_unifiedproduct_latest}")
//    private String indexName;
//
//}
