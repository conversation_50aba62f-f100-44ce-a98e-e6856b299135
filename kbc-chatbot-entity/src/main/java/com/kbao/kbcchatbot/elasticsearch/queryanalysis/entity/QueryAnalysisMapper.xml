<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.elasticsearch.queryanalysis.dao.QueryAnalysisMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.elasticsearch.queryanalysis.entity.QueryAnalysis">
		<id column="analy_id" jdbcType="VARCHAR"  property="analyId"  />
		<result property="indId" jdbcType="VARCHAR"  column="ind_id" />
		<result property="queryColumn" jdbcType="VARCHAR"  column="query_column" />
		<result property="queryAnalysis" jdbcType="VARCHAR"  column="query_analysis" />
		<result property="returnColumn" jdbcType="VARCHAR"  column="return_column" />
		<result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />
		<result property="createId" jdbcType="VARCHAR"  column="create_id" />
		<result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />
		<result property="updateId" jdbcType="VARCHAR"  column="update_id" />
		<result property="analysisStatus" jdbcType="VARCHAR"  column="analysis_status" />
		<result property="isDeleted" jdbcType="INTEGER"  column="is_deleted" />
	</resultMap>

	<sql id="Base_Column_List">
		analy_id,  
		ind_id,  
		query_column,  
		query_analysis,  
		return_column,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		analysis_status,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.analy_id, 
		t.ind_id, 
		t.query_column, 
		t.query_analysis, 
		t.return_column, 
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.analysis_status, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0
			<if test="indId != null and indId != ''">
				and t.ind_id = #{indId,jdbcType=VARCHAR}
			</if>

			<if test="queryColumn != null and queryColumn != ''">
				and t.query_column = #{queryColumn,jdbcType=VARCHAR}
			</if>

			<if test="queryAnalysis != null and queryAnalysis != ''">
				and t.query_analysis = #{queryAnalysis,jdbcType=VARCHAR}
			</if>

			<if test="returnColumn != null and returnColumn != ''">
				and t.return_column = #{returnColumn,jdbcType=VARCHAR}
			</if>

			<if test="createTime != null">
				and t.create_time = #{createTime,jdbcType=TIMESTAMP}
			</if>

			<if test="createId != null and createId != ''">
				and t.create_id = #{createId,jdbcType=VARCHAR}
			</if>

			<if test="updateTime != null">
				and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
			</if>

			<if test="updateId != null and updateId != ''">
				and t.update_id = #{updateId,jdbcType=VARCHAR}
			</if>

			<if test="analysisStatus != null and analysisStatus != ''">
				and t.analysis_status = #{analysisStatus,jdbcType=VARCHAR}
			</if>

			<!-- 自定义条件-->
			<if test="analyId_notEq != null">
				and t.analy_id != #{analyId_notEq,jdbcType=VARCHAR}
			</if>
		</where>
	</sql>

	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.elasticsearch.queryanalysis.entity.QueryAnalysis">
		insert into t_es_query_analysis(
		<include refid="Base_Column_List" />
		)
		values(
		#{analyId,jdbcType=VARCHAR},

		#{indId,jdbcType=VARCHAR},

		#{queryColumn,jdbcType=VARCHAR},

		#{queryAnalysis,jdbcType=VARCHAR},

		#{returnColumn,jdbcType=VARCHAR},

		#{createTime,jdbcType=TIMESTAMP},

		#{createId,jdbcType=VARCHAR},

		#{updateTime,jdbcType=TIMESTAMP},

		#{updateId,jdbcType=VARCHAR},

		#{analysisStatus,jdbcType=VARCHAR},

		#{isDeleted,jdbcType=INTEGER}
		)
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.elasticsearch.queryanalysis.entity.QueryAnalysis">
		insert into t_es_query_analysis
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="analyId != null and analyId != ''">
				analy_id,
			</if>

			<if test="indId != null and indId != ''">
				ind_id,
			</if>

			<if test="queryColumn != null and queryColumn != ''">
				query_column,
			</if>

			<if test="queryAnalysis != null and queryAnalysis != ''">
				query_analysis,
			</if>

			<if test="returnColumn != null and returnColumn != ''">
				return_column,
			</if>

			<if test="createTime != null">
				create_time,
			</if>

			<if test="createId != null and createId != ''">
				create_id,
			</if>

			<if test="updateTime != null">
				update_time,
			</if>

			<if test="updateId != null and updateId != ''">
				update_id,
			</if>

			<if test="analysisStatus != null and analysisStatus != ''">
				analysis_status,
			</if>

			<if test="isDeleted != null">
				is_deleted,
			</if>

		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="analyId != null and analyId != ''">
				#{analyId,jdbcType=VARCHAR},
			</if>
			<if test="indId != null and indId != ''">
				#{indId,jdbcType=VARCHAR},
			</if>
			<if test="queryColumn != null and queryColumn != ''">
				#{queryColumn,jdbcType=VARCHAR},
			</if>
			<if test="queryAnalysis != null and queryAnalysis != ''">
				#{queryAnalysis,jdbcType=VARCHAR},
			</if>
			<if test="returnColumn != null and returnColumn != ''">
				#{returnColumn,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createId != null and createId != ''">
				#{createId,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateId != null and updateId != ''">
				#{updateId,jdbcType=VARCHAR},
			</if>
			<if test="analysisStatus != null and analysisStatus != ''">
				#{analysisStatus,jdbcType=VARCHAR},
			</if>
			<if test="isDeleted != null">
				#{isDeleted,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_es_query_analysis(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
			(
			#{item.analyId}
			<choose>
				<when test="item.indId != null and item.indId !='' ">,#{item.indId}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.queryColumn != null and item.queryColumn !='' ">,#{item.queryColumn}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.queryAnalysis != null and item.queryAnalysis !='' ">,#{item.queryAnalysis}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.returnColumn != null and item.returnColumn !='' ">,#{item.returnColumn}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.createId != null and item.createId !='' ">,#{item.createId}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.updateId != null and item.updateId !='' ">,#{item.updateId}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.analysisStatus != null and item.analysisStatus !='' ">,#{item.analysisStatus}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
			</choose>
			)
		</foreach>
	</insert>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.elasticsearch.queryanalysis.entity.QueryAnalysis">
		update t_es_query_analysis
		set
			ind_id = #{indId,jdbcType=VARCHAR},
			query_column = #{queryColumn,jdbcType=VARCHAR},
			query_analysis = #{queryAnalysis,jdbcType=VARCHAR},
			return_column = #{returnColumn,jdbcType=VARCHAR},
			create_time = #{createTime,jdbcType=TIMESTAMP},
			create_id = #{createId,jdbcType=VARCHAR},
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			update_id = #{updateId,jdbcType=VARCHAR},
			analysis_status = #{analysisStatus,jdbcType=VARCHAR},
			is_deleted = #{isDeleted,jdbcType=INTEGER}
		where analy_id = #{analyId,jdbcType=VARCHAR}
	</update>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.elasticsearch.queryanalysis.entity.QueryAnalysis">
		update t_es_query_analysis
		<set>
			<if test="indId != null and indId !='' ">
				ind_id = #{indId,jdbcType=VARCHAR},
			</if>
			<if test="queryColumn != null and queryColumn !='' ">
				query_column = #{queryColumn,jdbcType=VARCHAR},
			</if>
			<if test="queryAnalysis != null and queryAnalysis !='' ">
				query_analysis = #{queryAnalysis,jdbcType=VARCHAR},
			</if>
			<if test="returnColumn != null and returnColumn !='' ">
				return_column = #{returnColumn,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createId != null and createId !='' ">
				create_id = #{createId,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateId != null and updateId !='' ">
				update_id = #{updateId,jdbcType=VARCHAR},
			</if>
			<if test="analysisStatus != null and analysisStatus !='' ">
				analysis_status = #{analysisStatus,jdbcType=VARCHAR},
			</if>
			<if test="isDeleted != null">
				is_deleted = #{isDeleted,jdbcType=INTEGER},
			</if>
		</set>
		where analy_id = #{analyId,jdbcType=VARCHAR}
	</update>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_es_query_analysis(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
			(
			#{item.analyId}
			<choose><when test="item.indId != null and item.indId != ''">,#{item.indId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.queryColumn != null and item.queryColumn != ''">,#{item.queryColumn}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.queryAnalysis != null and item.queryAnalysis != ''">,#{item.queryAnalysis}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.returnColumn != null and item.returnColumn != ''">,#{item.returnColumn}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null and item.createId != ''">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null and item.updateId != ''">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.analysisStatus != null and item.analysisStatus != ''">,#{item.analysisStatus}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise></choose>
			)
		</foreach>
		on duplicate key update
		ind_id=values(ind_id),
		query_column=values(query_column),
		query_analysis=values(query_analysis),
		return_column=values(return_column),
		create_time=values(create_time),
		create_id=values(create_id),
		update_time=values(update_time),
		update_id=values(update_id),
		analysis_status=values(analysis_status),
		is_deleted=values(is_deleted)
	</update>

	<!-- 逻辑删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.String">
		update t_es_query_analysis set is_deleted = 1
		where analy_id = #{analyId,jdbcType=VARCHAR} and is_deleted = 0
	</update>

	<!-- 批量逻辑删除 -->
	<update id="batchDelete" parameterType="java.util.List">
		update t_es_query_analysis set is_deleted = 1 where analy_id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		and is_deleted = 0
	</update>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from t_es_query_analysis
		where analy_id = #{analyId,jdbcType=VARCHAR}
	</select>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_es_query_analysis t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_es_query_analysis t
		<include refid="Base_Condition" />
	</select>
	<!-- 自定义查询 -->
	
	<update id="deleteByIndId" parameterType="java.lang.String">
		update t_es_query_analysis t
		set t.is_deleted = 1
		where t.ind_id = #{indId}
	</update>
	
	<update id="updateQueryColumn" parameterType="java.lang.String">
		UPDATE t_es_query_analysis a set query_column =
		(
			SELECT GROUP_CONCAT(column_id) FROM t_es_column c
			WHERE c.is_analysis = 'Y'
			AND c.ind_id = #{indId}
			AND c.is_deleted = 0)
		where a.ind_id = #{indId}
		AND a.is_deleted = 0 
		AND a.analysis_status = 1
	</update>
	
</mapper>
