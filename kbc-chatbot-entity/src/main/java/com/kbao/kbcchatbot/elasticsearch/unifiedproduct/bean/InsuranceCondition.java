package com.kbao.kbcchatbot.elasticsearch.unifiedproduct.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 统一产品库ES物料-投保条件对象
 * @Date 2020-12-4
 */
@Data
public class InsuranceCondition {
    /**
     * 最小投保年龄
     */
    private int minAge;

    /**
     * 最小投保年龄单位
     */
    private String minAgeUnit;

    /**
     * 最大投保年龄
     */
    private int maxAge;

    /**
     * 最大投保年龄单位
     */
    private String maxAgeUnit;

    /**
     * 投保年龄
     */
    private String ageDisplay;

    /**
     * 保障期间
     */
    private String coveragePeriod;

    /**
     * 性别
     */
    private String gender;

    /**
     * 缴费期间
     */
    private String payPeriod;

    /**
     * 最低保费
     */
    private BigDecimal minPremium;
}
