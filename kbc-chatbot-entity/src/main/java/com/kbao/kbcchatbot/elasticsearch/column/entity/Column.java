package com.kbao.kbcchatbot.elasticsearch.column.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 对应表 t_column
 * 索引字段表  
 *  
 */
@Data
public class Column implements Serializable{
	private static final long serialVersionUID = 1L;
 
	
    /**
     * 对应表中column_id
     * 表ID
     */  
	private String columnId;
	
    /**
     * 对应表中ind_id
     * 索引ID
     */  
	private String indId;
	
    /**
     * 对应表中column_name
     * 索引字段名
     */  
	private String columnName;
	
    /**
     * 对应表中column_type
     * 索引字段类型
     */  
	private String columnType;
	
    /**
     * 对应表中analyzer
     * 索引分词器
     */  
	private String analyzer;
	
    /**
     * 对应表中field_name
     * 字段显示名称
     */  
	private String fieldName;
	
    /**
     * 对应表中is_analysis
     * 是否检索 Y-检索，N-不检索
     */  
	private String isAnalysis;
	
    /**
     * 对应表中analysis_mode
     * 分析方式 0-分词，1-全词匹配，2-模糊查询
     */  
	private String analysisMode;
	
    /**
     * 对应表中default_is_analysis
     * 索引内是否位索引字段 Y-是，N-否
     */  
	private String defaultIsAnalysis;
	
    /**
     * 对应表中column_weight
     * 字段权重
     */  
	private Float columnWeight;
	
    /**
     * 对应表中create_time
     * 创建时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;
	
    /**
     * 对应表中update_time
     * 更新时间
     */  
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
    /**
     * 对应表中update_id
     * 更新人ID
     */  
	private String updateId;
	
    /**
     * 对应表中is_deleted
     * 
     */  
	private Integer isDeleted;
}   