package com.kbao.kbcchatbot.robotknowledgeconfig.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 好赔知识同步结构
 * @date 2025/3/12 14:19
 */
@Data
public class RobotKnowledgeClaimSyncVO {
    /**
     * 知识ID
     */
    private String knowledgeId;

    /**
     * 1-新增  2-更新  3-删除
     */
    private String syncType;
    /**
     * 知识类型 0-文本  1-图文
     */
    private String type;
    /**
     * 标题
     */
    private String title;
    /**
     * 标签
     */
    private List<String> labels;
    /**
     * 样本数据
     */
    private String content;

    /**
     * 文件链接
     */
    private List<String> fileUrl;

    /**
     * 知识原文
     */
    private String link;


}
