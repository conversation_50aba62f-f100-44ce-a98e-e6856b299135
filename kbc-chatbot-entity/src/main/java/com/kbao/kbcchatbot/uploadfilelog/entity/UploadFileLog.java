package com.kbao.kbcchatbot.uploadfilelog.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR> qiuzb
 * @Description: 文件上传日志
 * @create 2023/5/23 16:42
 */
@Data
@Document(collection = "c_upload_file_log")
public class UploadFileLog {
    /**
     * 上传文件Id
     */
    @Indexed
    @Id
    private String fileId;

    /**
     * 文件名
     */
    private String fileName;


    /**
     * 上传文件url
     */
    private String uploadFileUrl;
    /**
     * 公网网关代理转发地址
     */
    private String foreignPath;


    /**
     * 对应表中file_size
     * 文件大小
     */
    private String fileSize;

    /**
     * 业务Id
     */
    private String bizId;

    /**
     * 业务使用模块
     */
    private String bizModule;


    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人Id
     */
    private String updateId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人Id
     */
    private String createId;

    /**
     * 租户Id
     */
    private String tenantId;
}
