package com.kbao.kbcchatbot.uploadfilelog.bean;

import lombok.Data;

import java.io.Serializable;

@Data
public class CustomFileUploadRes implements Serializable {
    private static final long serialVersionUID = 1L;
    //"文件相对路径"
    private String relativePath;
    //"内网网关代理转发地址"
    private String absolutePath;
    //"公网网关代理转发地址"
    private String foreignPath;
    //"文件ID"
    private String fileId;
    //"OSS公网端访问原始地址"
    private String sourcePath;
    //"OSS内网端访问原始地址"
    private String intranetSourcePath;
    //文件名
    private String fileName;
    //文件后缀
    private String fileType;
}
