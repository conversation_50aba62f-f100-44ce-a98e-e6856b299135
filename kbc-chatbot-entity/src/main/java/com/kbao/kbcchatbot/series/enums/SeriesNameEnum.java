package com.kbao.kbcchatbot.series.enums;


public enum SeriesNameEnum {
    /**
     * 机器人编码
     */
    ROBOT_ID("RobotId", "RT", 6),
    ROBOT_VERSION("RobotVersion","v",1);

    private String seriesName;
    private String prefix;
    private Integer num;


    SeriesNameEnum(String seriesName, String prefix, Integer num) {
        this.seriesName = seriesName;
        this.prefix = prefix;
        this.num = num;
    }

    public static SeriesNameEnum seriesNameOf(String seriesName) {
        for (SeriesNameEnum seriesNameEnum : SeriesNameEnum.values()) {
            if (seriesNameEnum.seriesName.equals(seriesName)) {
                return seriesNameEnum;
            }
        }
        return null;
    }

    public static SeriesNameEnum prefixOf(String prefix) {
        for (SeriesNameEnum seriesNameEnum : SeriesNameEnum.values()) {
            if (seriesNameEnum.prefix.equals(prefix)) {
                return seriesNameEnum;
            }
        }
        return null;
    }

    public static SeriesNameEnum numOf(Integer num) {
        for (SeriesNameEnum seriesNameEnum : SeriesNameEnum.values()) {
            if (seriesNameEnum.num.equals(num)) {
                return seriesNameEnum;
            }
        }
        return null;
    }

    public String getSeriesName() {
        return seriesName;
    }

    public void setSeriesName(String seriesName) {
        this.seriesName = seriesName;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }
}
