package com.kbao.kbcchatbot.series.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @version V1.0
 * @Description 唯一序列生成方法
 * @date 2020/5/6 16:40
 * @since V1.0
 */
@Data
@Document(collection = "SeriesData")
public class SeriesDataMo {
    /**
     * 序列名
     */
    @Id
    String seriesName;

    /**
     * 前缀
     */
    String preFix;

    /**
     * 序列值
     */
    Long incId;
}
