package com.kbao.kbcchatbot.customer;

import lombok.Data;

/**
 * <AUTHOR> qiuzb
 * @Description: 客服消息
 * @create 2023/11/20 11:40
 */
@Data
public class ChatMessagePush {

    /**
     * 用户ID
     */
    private String userId;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 会话ID
     */
    private String sid;

    private String mid;

    private String meetingId;

    /**
     * 会话标识ID
     */
    private String sessionId;

    /**
     * 消息内容类型
     */
    private String contentType;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 客服ID
     */
    private String agentId;

    /**
     * 创建时间
     */
    private String createTime;
}
