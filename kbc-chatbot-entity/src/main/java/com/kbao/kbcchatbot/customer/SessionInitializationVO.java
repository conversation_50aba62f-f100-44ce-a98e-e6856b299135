package com.kbao.kbcchatbot.customer;

import lombok.Data;

/**
 * <AUTHOR> qiuzb
 * @Description: 初始化接口响应
 * @create 2023/11/15 10:04
 */
@Data
public class SessionInitializationVO {

    /**
     * 转人工文本
     */
    private String transferText;

    /**
     * 是否使用留言消息
     */
    private String use_leave_msg;

    /**
     * 是否使用机器人
     */
    private int use_robot;

    /**
     * 坐席号
     */
    private String seatNo;

    /**
     * 是否存在
     */
    private boolean isExist;

    /**
     * WebSocket链接URL
     */
    private String url_ws_v;

    /**
     * 机器人链接URL
     */
    private String url_robot;

    /**
     * 超时时间
     */
    private int outTime;

    /**
     * 会话ID
     */
    private String sid;


    private String nickName;
}
