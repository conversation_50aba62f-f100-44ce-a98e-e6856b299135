package com.kbao.kbcchatbot.customer;

import lombok.Data;

/**
 * <AUTHOR> qiuzb
 * @Description: 转人工响应
 * @create 2023/11/15 10:20
 */
@Data
public class TransferVO {

    /**
     * 技能ID
     */
    private String skillId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 坐席号
     */
    private String seatNo;

    /**
     * 旧会话ID
     */
    private String oldSid;

    /**
     * 业务
     */
    private String bus;

    /**
     * 会话ID
     */
    private String sid;

    /**
     * socket链接URL
     */
    private String url_ws_v;

    /**
     * 是否有进行中的会话
     */
    private Boolean isExist;

    private String nickName;

    /**
     * true：工作时间
     * false：非工作时间
     */
    private boolean workTimeFlag;


}
