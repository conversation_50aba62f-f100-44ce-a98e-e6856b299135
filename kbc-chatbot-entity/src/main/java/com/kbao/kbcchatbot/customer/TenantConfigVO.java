package com.kbao.kbcchatbot.customer;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> qiuzb
 * @Description: 租户信息
 * @create 2023/11/13 16:22
 */
@Getter
@Setter
public class TenantConfigVO {

    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 大童租户编码
     */
    private String dtTenantCode;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 租户名称
     */
    private String name;
    /**
     * 用户接口地址
     */
    private String userUrl;
    /**
     * 用户接口参数
     */
    private String userParam;
    /**
     * 订单接口地址
     */
    private String orderUrl;
    /**
     * 订单接口参数
     */
    private String orderParam;
    /**
     * 工作时间模型
     */
    private String workTimeModel;
    /**
     * 来源
     */
    private String source;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 流程微信通知地址
     */
    private String flowWechatNotifyUrl;
    /**
     * 流程内部邮件地址
     */
    private String flowInsidexMailUrl;
    /**
     * 呼叫用户接口地址
     */
    private String callUserUrl;
    /**
     * 呼叫流程一
     */
    private String callFlowOne;
    /**
     * 呼叫流程二
     */
    private String callFlowTwo;
    /**
     * 呼叫流程三
     */
    private String callFlowThree;
    /**
     * 状态
     */
    private String status;

    /**
     * 机构编码
     */
    private String orgCode;
}
