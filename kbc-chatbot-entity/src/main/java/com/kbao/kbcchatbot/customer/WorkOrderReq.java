package com.kbao.kbcchatbot.customer;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> qiuzb
 * @Description: 客服创建工单请求实体
 * @create 2023/10/23 14:52
 */
@Getter
@Setter
public class WorkOrderReq {

    /**
     * 用户id
     */
    private String dtUserId;

    /**
     * 工单大类 必填
     * 通知型工单
     * 理赔问题:5-理赔查询:501
     * 续期问题:10-续期问题:1002
     * 订单问题:22-订单状态处理:2204
     * 计划书/首期电投:3-首期电投寿险:301
     * 童管家:61-实名认证问题:6104
     * 资金支付:23-佣金问题:2302
     * C端客户工单
     * C端童管家:58-实名认证问题:5815
     */
    private String bigCategory;

    /**
     * 工单小类 必填
     */
    private String smallCagetory;

    /**
     * 工单标题 必填
     */
    private String actheadline;

    /**
     * 工单内容 必填
     */
    private String appel;

    /**
     * 发起人姓名 必填
     */
    private String name;

    /**
     * 发起人手机号 必填
     */
    private String phone;

    /**
     * 区域中心
     */
    private String location;

    /**
     * 流程标识 必填
     * 通知型工单：DT_FLOW_01
     * C端客户工单：DT_FLOW_12
     */
    private String flowMarker;
}
