package com.kbao.kbcchatbot.customer;

import lombok.Data;

/**
 * <AUTHOR> qiuzb
 * @Description: 初始化请求类
 * @create 2023/11/14 17:45
 */
@Data
public class SessionInitializationReq {
    /**
     * 电话号码
     */
    private String phone;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 源参数，表示租户接口里的参数
     */
    private String source;

    /**
     * 拓展字段，用于存储额外的信息
     */
    private String ext;

    /**
     * 接入鉴权校验的令牌，用于验证用户权限
     */
    private String token;

    /**
     * 租户接口里的参数，表示通道信息
     */
    private String channel;

    /**
     * 技能组标识，链接里的bus参数
     */
    private String bus;

    /**
     * 截屏类型，默认为pp值
     */
    private String screenShotType;

    /**
     * 访客标识，与bus参数相同
     */
    private String openId;

    /**
     * 表示聊天入口的参数，与channel参数相同
     */
    private String chatInlet;

    private String robotSessionId;
}
