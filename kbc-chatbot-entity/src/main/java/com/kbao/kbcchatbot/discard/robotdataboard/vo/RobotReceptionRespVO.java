package com.kbao.kbcchatbot.discard.robotdataboard.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: kbc-chatbot
 * @description: 机器人接待数据
 * @author: husw
 * @create: 2023-06-29 15:13
 **/
@Data
public class RobotReceptionRespVO {

    private String date;
    /**
     * 接待人次
     */
    private int receptionNum;
    /**
     * 对话轮次
     */
    private int totalRound;
    /**
     * 平均对话轮次
     */
    private BigDecimal averageTotalRound;
    /**
     * 解决量
     */
    private int resolveNum;
    /**
     * 解决率
     */
    private int resolvePercent;

    /**
     * 知识对话轮次
     */
    private int knowledgeRound;

    /**
     * 闲聊对话轮次
     */
    private int chatRound;

    /**
     * 人机对练轮次
     */
    private int manMachineRound;

    /**
     * 产品解读轮次
     */
    private int productAnalyzeRound;
    /**
     * 转人工次数
     */
    private int transformCount;
    /**
     * 转人工率
     */
    private int transformPercent;

    /**
     * 点赞轮次
     */
    private int upVoteRoundCount;
    /**
     * 点赞率
     */
    private int upVoteRoundPercent;

    /**
     * 点踩轮次
     */
    private int downVoteRoundCount;
    /**
     * 点踩率
     */
    private int downVoteRoundPercent;
}
