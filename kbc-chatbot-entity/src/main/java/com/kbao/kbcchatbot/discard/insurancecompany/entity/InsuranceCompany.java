package com.kbao.kbcchatbot.discard.insurancecompany.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
* <AUTHOR>
* @Description 保险公司实体
* @Date 2023-11-07
*/
@Data
public class InsuranceCompany implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Long id;


    /**
     * 对应表中company_name
     * 公司名称
     */  
	private String companyName;

    /**
     * 对应表中phone
     * 联系方式
     */  
	private String phone;

    /**
     * 对应表中show_phone
     * 保险公司电话回访 外显号码
     */  
	private String showPhone;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

    /**
     * 对应表中update_time
     * 更新时间
     */  
	private Date updateTime;

    /**
     * 对应表中update_id
     * 更新人
     */  
	private String updateId;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	private Date createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中tenant_id
     * 租户id
     */  
	private String tenantId;

}   