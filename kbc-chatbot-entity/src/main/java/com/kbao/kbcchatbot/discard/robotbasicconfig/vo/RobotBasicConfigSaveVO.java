package com.kbao.kbcchatbot.discard.robotbasicconfig.vo;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @program: kbc-chatbot
 * @description: 保存配置
 * @author: husw
 * @create: 2023-05-16 17:06
 **/
@Data
public class RobotBasicConfigSaveVO {

    /**
     * 编码
     */
    @NotNull(message = "机器人ID不能为空！")
    private Long robotId;

    /**
     * 澄清开启状态 0-未开启 1-开启
     */
    private Integer clearStatus;

    /**
     * 澄清最高条数
     */
    private Integer clearMaxNumber;

    /**
     * 澄清阈值
     */
    @DecimalMin(value = "0",message = "澄清阈值不能小于0")
    @DecimalMax(value = "1",message = "澄清阈值不能超过1")
    private BigDecimal clearThreshold;

    /**
     * 直接回复阈值
     */
    @DecimalMin(value = "0",message = "直接回复阈值不能小于0")
    @DecimalMax(value = "1",message = "直接回复阈值不能超过1")
    private BigDecimal directReplyThreshold;

    /**
     * 敏感词回复文案
     */
    private String sensitiveWordsReply;

    /**
     * 无答案回复文案
     */
    private String noAnswerReply;

    /**
     * 澄清回复文案
     */
    private String clearReply;
}
