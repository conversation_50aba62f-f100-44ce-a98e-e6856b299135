package com.kbao.kbcchatbot.discard.robotdataboard.vo;

import com.kbao.commons.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: kbc-chatbot
 * @description: 机器人接待导出VO
 * @author: Davy
 * @create: 2023-11-3 11:35
 **/
@Data
public class RobotReceptionExportVO {

    @Excel(name = "日期")
    private String date;

    @Excel(name = "接待人次（一个session记为一人次）")
    private Integer receptionNum;

    @Excel(name = "对话轮次（一问一答记为一轮）")
    private Integer totalRound;

    @Excel(name = "平均对话轮次（对话轮次/接待人次）")
    private BigDecimal averageTotalRound;

    @Excel(name = "解决量（排除转人工、点踩的对话轮次）")
    private Integer resolveNum;

    @Excel(name = "解决率（解决量/对话轮次）")
    private Integer resolvePercent;

    @Excel(name = "知识对话轮次（知识库答案回答的轮次）")
    private Integer knowledgeRound;

    @Excel(name = "闲聊对话轮次（闲聊回答的轮次）")
    private Integer chatRound;

    @Excel(name = "人机对练轮次（人机对练回答的轮次）")
    private Integer manMachineRound;

    @Excel(name = "产品解读（产品解读回答的轮次）")
    private Integer productAnalyzeRound;

    @Excel(name = "转人工人次（转人工的session量）")
    private Integer transformCount;
//
    @Excel(name = "转人工率（转人工人次/接待人次）")
    private Integer transformPercent;
//
//    @Excel(name = "无答案轮次（机器人无回复的轮次）")
//    private Integer noAnswerRound;

    @Excel(name = "点赞轮次（答案被点赞的轮次）")
    private Integer upVoteRoundCount;

    @Excel(name = "点赞率（点赞轮次/对话轮次）")
    private Integer upVoteRoundPercent;

    @Excel(name = "点踩轮次（答案被点踩的轮次）")
    private Integer downVoteRoundCount;

    @Excel(name = "点踩率（点踩轮次/对话轮次）")
    private Integer downVoteRoundPercent;

}
