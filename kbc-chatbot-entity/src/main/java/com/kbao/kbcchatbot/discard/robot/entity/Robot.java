package com.kbao.kbcchatbot.discard.robot.entity;

import com.kbao.kbcchatbot.discard.robot.enums.RobotStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天机器人表
 * @TableName t_robot
 */
@Data
public class Robot implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 前状态
     */
    private Integer preStatus;

    /**
     * 状态 0-未发布 1-已发布 2-发布中 3-编辑中 9-已下线
     * @see RobotStatusEnum
     */
    private Integer status;
    /**
     * 发布类型 0-默认(es) 1-rasa 2-大模型
     */
    private Integer releaseType;
    /**
     * 模型类型 1-千帆
     */
    private Integer modelType;

    /**
     * 版本号
     */
    private String version;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 创建人
     */
    private String createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String upadteId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}