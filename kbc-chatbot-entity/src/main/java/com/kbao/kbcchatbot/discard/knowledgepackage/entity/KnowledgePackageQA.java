package com.kbao.kbcchatbot.discard.knowledgepackage.entity;

import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.KnowledgeChapter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 知识包QA内容
 * <AUTHOR>
 * @Date 2023-5-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = "#{@knowledgePackageQAEsAttribute.indexName}", createIndex = false)
public class KnowledgePackageQA implements Serializable {

    private static final long serialVersionUID = 7391977090779219165L;

    /**
     * 主键
     */
    @Id
    private String qaId;

    /**
     * 知识包ID
     */
    @Field(type = FieldType.Keyword)
    private String packageCode;
    /**
     * 知识ID
     */
    @Field(type = FieldType.Keyword)
    private String knowledgeId;

    /**
     * 机器人ID
     */
    @Field(type = FieldType.Keyword)
    private Long robotId;

    /**
     * 版本类型 0-测试版 1-正式版
     */
    @Field(type = FieldType.Integer)
    private Integer environment;
    /**
     * 类型: 1知识，2略树
     * 字典:km.knowledge.type
     */
    @Field(type = FieldType.Keyword)
    private String type;
    /**
     * 所属项目不能为空
     */
    @Field(type = FieldType.Keyword)
    private String projectId;
    /**
     * 所属项目名称
     */
    @Field(type = FieldType.Keyword)
    private String projectName;
    /**
     * 所属知识库夹不能为空
     */
    @Field(type = FieldType.Keyword)
    private String folderId;
    /**
     * 所属知识库夹名称
     */
    @Field(type = FieldType.Keyword)
    private String folderName;
    /**
     * 一级目录
     */
    @Field(type = FieldType.Keyword)
    private String firstDirect;
    /**
     * 一级目录名称
     */
    @Field(type = FieldType.Keyword)
    private String firstDirectName;
    /**
     * 二级目录
     */
    @Field(type = FieldType.Keyword)
    private String secondDirect;
    /**
     * 二级目录名称
     */
    @Field(type = FieldType.Keyword)
    private String secondDirectName;
    /**
     * 所属类型: 1公司，2产品
     * 字典：km.knowledge.belong.type
     */
    @Field(type = FieldType.Keyword)
    private String belongType;
    /**
     * 所属名称
     */
    @Field(type = FieldType.Keyword)
    private String belongName;
    /**
     * 关联公司
     */
    @Field(type = FieldType.Keyword)
    private String companyId;
    /**
     * 关联公司名称
     */
    @Field(type = FieldType.Keyword)
    private String companyName;
    /**
     * 关联产品
     */
    @Field(type = FieldType.Keyword)
    private String productId;
    /**
     * 关联产品名称
     */
    @Field(type = FieldType.Keyword)
    private String productName;
    /**
     * 关键字
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String keyword;
    /**
     * 关键字数量
     */
    @Field(type = FieldType.Integer)
    private Integer keywordNum;
    /**
     * 关联文章
     */
    @Field(type = FieldType.Keyword)
    private String relatedArticles;
    /**
     * 内容
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String subContent;
    /**
     * 历程图章节
     */
    @Field(type = FieldType.Nested)
    private List<KnowledgeChapter> chapters;
    /**
     * 状态：1待发布，2已发布
     * 字典：km.knowledge.state
     */
    @Field(type = FieldType.Keyword)
    private String state;
    /**
     * 允许分享，1可分享，0不可享
     */
    @Field(type = FieldType.Integer)
    private Integer shareable;
    /**
     * "是共享，1是，0否
     */
    @Field(type = FieldType.Integer)
    private Integer isShare;
    /**
     * 来源知识库
     */
    @Field(type = FieldType.Keyword)
    private String sourceFolder;
    /**
     * 标题
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String title;

    /**
     * 问题
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String question;
    /**
     * 相似问题
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String similarQuestion;

    /**
     * 相似问题集合
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private List<String> similarQuestions;

    /**
     * 回复文案
     */
    @Field(type = FieldType.Text, searchAnalyzer = "ik_smart", analyzer = "ik_max_word")
    private String answer;

    /**
     * 删除状态：0未删除，1删除
     */
    @Field(type = FieldType.Integer)
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Keyword)
    private String createTime;
    /**
     * 更新人id
     */
    @Field(type = FieldType.Keyword)
    private String updateId;
    /**
     * 更新人姓名
     */
    @Field(type = FieldType.Keyword)
    private String updateName;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Date)
    private Date updateTime;

    /**
     * 租户ID
     */
    @Field(type = FieldType.Keyword)
    private String tenantId;

    /**
     * 获取知识QA答案
     * @return
     */
    @Transient
    public String getAnswer() {
        if(CollectionUtils.isEmpty(chapters)) {
            return subContent;
        }else {
            return chapters.get(0).getChapterContent();
        }
    }

}
