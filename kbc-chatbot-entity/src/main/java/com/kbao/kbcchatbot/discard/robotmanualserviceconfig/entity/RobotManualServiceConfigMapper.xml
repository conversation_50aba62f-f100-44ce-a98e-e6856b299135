<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcchatbot.discard.robotmanualserviceconfig.dao.RobotManualServiceConfigMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.discard.robotmanualserviceconfig.entity.RobotManualServiceConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="noReplyTransferStatus" column="no_reply_transfer_status" jdbcType="INTEGER"/>
            <result property="noReplyTransferTimes" column="no_reply_transfer_times" jdbcType="INTEGER"/>
            <result property="clearTransferStatus" column="clear_transfer_status" jdbcType="INTEGER"/>
            <result property="clearTransferTimes" column="clear_transfer_times" jdbcType="INTEGER"/>
            <result property="sameQuestionTransferStatus" column="same_question_transfer_status" jdbcType="INTEGER"/>
            <result property="sameQuestionTransferTimes" column="same_question_transfer_times" jdbcType="INTEGER"/>
            <result property="keywordsTransferStatus" column="keywords_transfer_status" jdbcType="INTEGER"/>
            <result property="custServPlatAccessType" column="cust_serv_plat_access_type" jdbcType="INTEGER"/>
            <result property="custServPlatAddr" column="cust_serv_plat_addr" jdbcType="VARCHAR"/>
            <result property="updateId" column="update_id" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,robot_id,type,
        no_reply_transfer_status,no_reply_transfer_times,clear_transfer_status,
        clear_transfer_times,same_question_transfer_status,same_question_transfer_times,
        keywords_transfer_status,cust_serv_plat_access_type,cust_serv_plat_addr,
        update_id,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_manual_service_config
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByRobotId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_manual_service_config
        where  robot_id = #{robotId,jdbcType=BIGINT}
    </select>

    <select id="selectProdByRobotId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_manual_service_config
        where  robot_id = #{robotId,jdbcType=BIGINT}
        AND type = 1
    </select>

    <select id="selectAll" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_manual_service_config
        <where>
            robot_id = #{robotId,jdbcType=BIGINT}
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
        </where>
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_robot_manual_service_config
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robotmanualserviceconfig.entity.RobotManualServiceConfig" useGeneratedKeys="true">
        insert into t_robot_manual_service_config
        ( id,robot_id,type
        ,no_reply_transfer_status,no_reply_transfer_times,clear_transfer_status
        ,clear_transfer_times,same_question_transfer_status,same_question_transfer_times
        ,keywords_transfer_status,cust_serv_plat_access_type,cust_serv_plat_addr
        ,update_id,update_time)
        values (#{id,jdbcType=BIGINT},#{robotId,jdbcType=BIGINT},#{type,jdbcType=INTEGER}
        ,#{noReplyTransferStatus,jdbcType=INTEGER},#{noReplyTransferTimes,jdbcType=INTEGER},#{clearTransferStatus,jdbcType=INTEGER}
        ,#{clearTransferTimes,jdbcType=INTEGER},#{sameQuestionTransferStatus,jdbcType=INTEGER},#{sameQuestionTransferTimes,jdbcType=INTEGER}
        ,#{keywordsTransferStatus,jdbcType=INTEGER},#{custServPlatAccessType,jdbcType=INTEGER},#{custServPlatAddr,jdbcType=VARCHAR}
        ,#{updateId,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robotmanualserviceconfig.entity.RobotManualServiceConfig" useGeneratedKeys="true">
        insert into t_robot_manual_service_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="robotId != null">robot_id,</if>
                <if test="type != null">type,</if>
                <if test="noReplyTransferStatus != null">no_reply_transfer_status,</if>
                <if test="noReplyTransferTimes != null">no_reply_transfer_times,</if>
                <if test="clearTransferStatus != null">clear_transfer_status,</if>
                <if test="clearTransferTimes != null">clear_transfer_times,</if>
                <if test="sameQuestionTransferStatus != null">same_question_transfer_status,</if>
                <if test="sameQuestionTransferTimes != null">same_question_transfer_times,</if>
                <if test="keywordsTransferStatus != null">keywords_transfer_status,</if>
                <if test="custServPlatAccessType != null">cust_serv_plat_access_type,</if>
                <if test="custServPlatAddr != null">cust_serv_plat_addr,</if>
                <if test="updateId != null">update_id,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="robotId != null">#{robotId,jdbcType=BIGINT},</if>
                <if test="type != null">#{type,jdbcType=INTEGER},</if>
                <if test="noReplyTransferStatus != null">#{noReplyTransferStatus,jdbcType=INTEGER},</if>
                <if test="noReplyTransferTimes != null">#{noReplyTransferTimes,jdbcType=INTEGER},</if>
                <if test="clearTransferStatus != null">#{clearTransferStatus,jdbcType=INTEGER},</if>
                <if test="clearTransferTimes != null">#{clearTransferTimes,jdbcType=INTEGER},</if>
                <if test="sameQuestionTransferStatus != null">#{sameQuestionTransferStatus,jdbcType=INTEGER},</if>
                <if test="sameQuestionTransferTimes != null">#{sameQuestionTransferTimes,jdbcType=INTEGER},</if>
                <if test="keywordsTransferStatus != null">#{keywordsTransferStatus,jdbcType=INTEGER},</if>
                <if test="custServPlatAccessType != null">#{custServPlatAccessType,jdbcType=INTEGER},</if>
                <if test="custServPlatAddr != null">#{custServPlatAddr,jdbcType=VARCHAR},</if>
                <if test="updateId != null">#{updateId,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.discard.robotmanualserviceconfig.entity.RobotManualServiceConfig">
        update t_robot_manual_service_config
        <set>
                <if test="robotId != null">
                    robot_id = #{robotId,jdbcType=BIGINT},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=INTEGER},
                </if>
                <if test="noReplyTransferStatus != null">
                    no_reply_transfer_status = #{noReplyTransferStatus,jdbcType=INTEGER},
                </if>
                <if test="noReplyTransferTimes != null">
                    no_reply_transfer_times = #{noReplyTransferTimes,jdbcType=INTEGER},
                </if>
                <if test="clearTransferStatus != null">
                    clear_transfer_status = #{clearTransferStatus,jdbcType=INTEGER},
                </if>
                <if test="clearTransferTimes != null">
                    clear_transfer_times = #{clearTransferTimes,jdbcType=INTEGER},
                </if>
                <if test="sameQuestionTransferStatus != null">
                    same_question_transfer_status = #{sameQuestionTransferStatus,jdbcType=INTEGER},
                </if>
                <if test="sameQuestionTransferTimes != null">
                    same_question_transfer_times = #{sameQuestionTransferTimes,jdbcType=INTEGER},
                </if>
                <if test="keywordsTransferStatus != null">
                    keywords_transfer_status = #{keywordsTransferStatus,jdbcType=INTEGER},
                </if>
                <if test="custServPlatAccessType != null">
                    cust_serv_plat_access_type = #{custServPlatAccessType,jdbcType=INTEGER},
                </if>
                <if test="custServPlatAddr != null">
                    cust_serv_plat_addr = #{custServPlatAddr,jdbcType=VARCHAR},
                </if>
                <if test="updateId != null">
                    update_id = #{updateId,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.discard.robotmanualserviceconfig.entity.RobotManualServiceConfig">
        update t_robot_manual_service_config
        set 
            robot_id =  #{robotId,jdbcType=BIGINT},
            type =  #{type,jdbcType=INTEGER},
            no_reply_transfer_status =  #{noReplyTransferStatus,jdbcType=INTEGER},
            no_reply_transfer_times =  #{noReplyTransferTimes,jdbcType=INTEGER},
            clear_transfer_status =  #{clearTransferStatus,jdbcType=INTEGER},
            clear_transfer_times =  #{clearTransferTimes,jdbcType=INTEGER},
            same_question_transfer_status =  #{sameQuestionTransferStatus,jdbcType=INTEGER},
            same_question_transfer_times =  #{sameQuestionTransferTimes,jdbcType=INTEGER},
            keywords_transfer_status =  #{keywordsTransferStatus,jdbcType=INTEGER},
            cust_serv_plat_access_type =  #{custServPlatAccessType,jdbcType=INTEGER},
            cust_serv_plat_addr =  #{custServPlatAddr,jdbcType=VARCHAR},
            update_id =  #{updateId,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
