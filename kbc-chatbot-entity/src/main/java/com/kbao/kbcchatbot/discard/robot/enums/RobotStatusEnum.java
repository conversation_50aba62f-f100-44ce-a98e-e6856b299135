package com.kbao.kbcchatbot.discard.robot.enums;

import lombok.Getter;

/**
 * @program: kbc-chatbot
 * @description: 机器人发布状态
 * @author: husw
 * @create: 2023-05-16 14:09
 **/
@Getter
public enum RobotStatusEnum {

    NOT_PUBlish(0,"未发布"),
    PUBLISHED(1,"已发布"),
    PUBLISHING(2,"发布中"),
    EDITING(3,"编辑中"),
    OFFLINE(9,"已下线");

    private Integer code;

    private String value;

    RobotStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static RobotStatusEnum getByCode(Integer code){
        for (RobotStatusEnum enums:RobotStatusEnum.values()){
            if (enums.code.equals(code)){
                return enums;
            }
        }
        return null;
    }
}
