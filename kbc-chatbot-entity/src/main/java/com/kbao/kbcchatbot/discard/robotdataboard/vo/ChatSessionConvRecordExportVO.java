package com.kbao.kbcchatbot.discard.robotdataboard.vo;

import com.kbao.commons.annotation.Excel;
import lombok.Data;

@Data
public class ChatSessionConvRecordExportVO {

     @Excel(name = "对话ID")
     private String convId;

     @Excel(name = "会话ID")
     private String sessionId;

     @Excel(name = "开始时间")
     private String startTime;

//     @Excel(name = "结束时间")
     private String endTime;

     @Excel(name = "聊天机器人")
     private String robotName;

     @Excel(name = "来源渠道")
     private String channelName;

     @Excel(name = "用户ID")
     private String userId;

     @Excel(name = "用户")
     private String userName;

     @Excel(name = "提问内容")
     private String userContent;

     @Excel(name = "补全内容")
     private String contextualizeQuestion;

     @Excel(name = "意图")
     private String intention;

     @Excel(name = "回复内容")
     private String answerContent;

     @Excel(name = "输出秒数")
     private String time;

     @Excel(name = "大模型类型")
     private String model;

     @Excel(name = "回复类型")
     private String answerSourceDesc;

//     @Excel(name = "回复类型")
//     private String robotAnswerTypeDesc;

     @Excel(name = "用户反馈")
     private String userVoteResultDesc;

     @Excel(name = "反馈内容")
     private String userVoteRemark;

     @Excel(name = "复制")
     private String isCopy;

}
