<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.discard.insurancecompany.dao.InsuranceCompanyMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.discard.insurancecompany.entity.InsuranceCompany">
    	<id column="id" jdbcType="BIGINT"  property="id"  />
        <result property="companyName" jdbcType="VARCHAR"  column="company_name" />  
        <result property="phone" jdbcType="VARCHAR"  column="phone" />  
        <result property="showPhone" jdbcType="VARCHAR"  column="show_phone" />  
        <result property="remark" jdbcType="VARCHAR"  column="remark" />  
        <result property="updateTime" jdbcType="TIMESTAMP"  column="update_time" />
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="createTime" jdbcType="TIMESTAMP"  column="create_time" />
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		company_name,  
		phone,  
		show_phone,  
		remark,  
		update_time,  
		update_id,  
		create_time,  
		create_id,  
		tenant_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.company_name, 
		t.phone, 
		t.show_phone, 
		t.remark, 
		t.update_time, 
		t.update_id, 
		t.create_time, 
		t.create_id, 
		t.tenant_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="companyName != null and companyName != ''">
	   		and t.company_name = #{companyName,jdbcType=VARCHAR}  
	    </if>
	    <if test="phone != null and phone != ''">
	   		and t.phone = #{phone,jdbcType=VARCHAR}  
	    </if>
	    <if test="showPhone != null and showPhone != ''">
	   		and t.show_phone = #{showPhone,jdbcType=VARCHAR}  
	    </if>
	    <if test="remark != null and remark != ''">
	   		and t.remark = #{remark,jdbcType=VARCHAR}  
	    </if>
	    <if test="updateTime != null and updateTime != ''">
	   		and t.update_time = #{updateTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="updateId != null and updateId != ''">
	   		and t.update_id = #{updateId,jdbcType=VARCHAR}  
	    </if>
	    <if test="createTime != null and createTime != ''">
	   		and t.create_time = #{createTime,jdbcType=TIMESTAMP}
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
	    <if test="tenantId != null and tenantId != ''">
	   		and t.tenant_id = #{tenantId,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_insurance_company t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_insurance_company t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_insurance_company
		where  id = #{id,jdbcType=BIGINT}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from t_insurance_company
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.discard.insurancecompany.entity.InsuranceCompany">
		<!-- <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_insurance_company(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=BIGINT}, 
                 
                #{companyName,jdbcType=VARCHAR}, 
                 
                #{phone,jdbcType=VARCHAR}, 
                 
                #{showPhone,jdbcType=VARCHAR}, 
                 
                #{remark,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=VARCHAR}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{createTime,jdbcType=VARCHAR}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.discard.insurancecompany.entity.InsuranceCompany">
		<selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_insurance_company
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="companyName != null ">  
	       		company_name,
	        </if>  
	        <if test="phone != null ">  
	       		phone,
	        </if>  
	        <if test="showPhone != null ">  
	       		show_phone,
	        </if>  
	        <if test="remark != null ">  
	       		remark,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=BIGINT},
            </if>  
            <if test="companyName != null">  
            	#{companyName,jdbcType=VARCHAR},
            </if>  
            <if test="phone != null">  
            	#{phone,jdbcType=VARCHAR},
            </if>  
            <if test="showPhone != null">  
            	#{showPhone,jdbcType=VARCHAR},
            </if>  
            <if test="remark != null">  
            	#{remark,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=VARCHAR},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=VARCHAR},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.discard.insurancecompany.entity.InsuranceCompany">
		update t_insurance_company
		<set>
	        <if test="companyName != null ">  
	        	company_name = #{companyName,jdbcType=VARCHAR},  
	        </if>  
	        <if test="phone != null ">  
	        	phone = #{phone,jdbcType=VARCHAR},  
	        </if>  
	        <if test="showPhone != null ">  
	        	show_phone = #{showPhone,jdbcType=VARCHAR},  
	        </if>  
	        <if test="remark != null ">  
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=TIMESTAMP},
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=TIMESTAMP},
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.discard.insurancecompany.entity.InsuranceCompany">
		update t_insurance_company
		set
           company_name = #{companyName,jdbcType=VARCHAR},
           phone = #{phone,jdbcType=VARCHAR},
           show_phone = #{showPhone,jdbcType=VARCHAR},
           remark = #{remark,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=VARCHAR},
           update_id = #{updateId,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=TIMESTAMP},
           create_id = #{createId,jdbcType=VARCHAR},
           tenant_id = #{tenantId,jdbcType=VARCHAR}
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_insurance_company(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.companyName != null">,#{item.companyName}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.phone != null">,#{item.phone}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.showPhone != null">,#{item.showPhone}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_insurance_company(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.companyName != null">,#{item.companyName}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.phone != null">,#{item.phone}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.showPhone != null">,#{item.showPhone}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   company_name=values(company_name), 
		   phone=values(phone), 
		   show_phone=values(show_phone), 
		   remark=values(remark), 
		   update_time=values(update_time), 
		   update_id=values(update_id), 
		   create_time=values(create_time), 
		   create_id=values(create_id), 
		   tenant_id=values(tenant_id) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_insurance_company where id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>
	
	<!-- 自定义查询 -->
</mapper>
