package com.kbao.kbcchatbot.discard.insurancecompanyrule.entity;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
* <AUTHOR>
* @Description 保险公司回访规则实体
* @Date 2023-11-07
*/
@Data
public class InsuranceCompanyRule implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Long id;


    /**
     * 对应表中follow_up_type
     * 保险公司回访方式
     */  
	private String followUpType;

    /**
     * 对应表中follow_up_rule
     * 保险公司回访规则
     */  
	private String followUpRule;


    /**
     * 对应表中company_name
     * 保险公司名称
     */  
	private String companyName;

    /**
     * 对应表中show_phone
     * 保险公司电话回访外显号码
     */  
	private String showPhone;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

    /**
     * 对应表中update_time
     * 修改时间
     */  
	private Date updateTime;

    /**
     * 对应表中update_id
     * 修改人
     */  
	private String updateId;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	private Date createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中tenant_id
     * 租户id
     */  
	private String tenantId;

}   