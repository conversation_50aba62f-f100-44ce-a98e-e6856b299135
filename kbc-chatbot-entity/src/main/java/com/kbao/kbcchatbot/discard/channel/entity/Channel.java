package com.kbao.kbcchatbot.discard.channel.entity;
import java.io.Serializable;
import lombok.Data;
import org.springframework.data.annotation.Transient;

/**
* <AUTHOR>
* @Description 渠道表实体
* @Date 2023-05-18
*/
@Data
public class Channel implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Long id;

    /**
     * 对应表中robot_id
     * 聊天机器人ID
     */  
	private Long robotId;

    /**
     * 对应表中code
     * 编码
     */  
	private String code;

    /**
     * 对应表中name
     * 名称
     */  
	private String name;

    /**
     * 对应表中status
     * 状态 0-未启用 1-启用
     */  
	private String status;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

	/**
	 * 对应表中private_key
	 * 私钥
	 */
	private String privateKey;

	/**
	 * 对应表中public_key
	 * 公钥
	 */
	private String publicKey;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	private String createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中update_time
     * 最后更新时间
     */  
	private String updateTime;

    /**
     * 对应表中update_id
     * 最后更新人
     */  
	private String updateId;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

    /**
     * 对应表中is_deleted
     * 是否删除
     */  
	private Integer isDeleted;

	/**
	 * 类型 1-跳转页面 2-发送文本 3-关联知识库
	 */
	@Transient
	private String type;

	/**
	 * 机器人名称
	 */
	@Transient
	private String robotName;

	/**
	 * 基础配置id
	 */
	@Transient
	private Long channelBasicConfigId;

}   