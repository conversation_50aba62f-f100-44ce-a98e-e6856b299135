package com.kbao.kbcchatbot.discard.robotmanualserviceconfig.vo;

import lombok.Data;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 保存转人工配置
 * @author: husw
 * @create: 2023-05-17 14:26
 **/
@Data
public class RobotManualServiceConfigSaveReqVO {
    /**
     * 机器人主键id
     */
    private Long robotId;
    /**
     * 无回复转人工开启状态 0-未开启 1-开启
     */
    private Integer noReplyTransferStatus;

    /**
     * 连续无回复多少次后转人工
     */
    private Integer noReplyTransferTimes;

    /**
     * 澄清重复转人工开启状态 0-未开启 1-开启
     */
    private Integer clearTransferStatus;

    /**
     * 澄清重复多少次转人工
     */
    private Integer clearTransferTimes;

    /**
     * 用户重复提问转人工开启状态 0-未开启 1-开启
     */
    private Integer sameQuestionTransferStatus;

    /**
     * 用户重复提问多少次转人工
     */
    private Integer sameQuestionTransferTimes;

    /**
     * 触发关键词转人工开启状态 0-未开启 1-开启
     */
    private Integer keywordsTransferStatus;

    /**
     * 客服平台接入方式 1-网页跳转 2-系统集成
     */
    private Integer custServPlatAccessType;

    /**
     * 客服平台跳转地址
     */
    private String custServPlatAddr;

    /**
     * 关键词
     */
    private List<String> keywords;
}
