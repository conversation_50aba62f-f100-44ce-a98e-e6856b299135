package com.kbao.kbcchatbot.discard.robotbasicconfig.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @program: kbc-chatbot
 * @description: 授权码返参
 * @author: husw
 * @create: 2023-06-12 09:35
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RobotAuthCodeVO {
    /**
     * 授权token
     */
    @NotBlank(message = "初始化token不能为空！")
    private String token;
}
