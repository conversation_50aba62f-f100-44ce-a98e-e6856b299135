package com.kbao.kbcchatbot.discard.robotdataboard.vo;

import lombok.Data;

import java.util.Date;

/**
 * @program: kbc-chatbot
 * @description: 接待概况数据
 * @author: husw
 * @create: 2023-06-29 15:32
 **/
@Data
public class RobotReceptionPageReqVO {
    /**
     * 机器人ID
     */
    private Long robotId;
    /**
     * 机器人CODE
     */
    private String robotCode;
    /**
     * 渠道ID
     */
    private Long channelId;
    /**
     * 渠道CODE
     */
    private String channelCode;
    /**
     * 开始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;
}
