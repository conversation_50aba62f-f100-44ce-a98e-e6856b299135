package com.kbao.kbcchatbot.discard.robotbasicconfig.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 聊天机器人基础配置表
 * @TableName t_robot_basic_config
 */
@Data
public class RobotBasicConfig implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 机器人主键ID
     */
    private Long robotId;

    /**
     * 类型 0-测试版 1-正式版
     */
    private Integer type;

    /**
     * 澄清开启状态 0-未开启 1-开启
     */
    private Integer clearStatus;

    /**
     * 澄清最高条数
     */
    private Integer clearMaxNumber;

    /**
     * 澄清阈值
     */
    private BigDecimal clearThreshold;

    /**
     * 直接回复阈值
     */
    private BigDecimal directReplyThreshold;

    /**
     * 更新人
     */
    private String updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 敏感词回复文案
     */
    private byte[] sensitiveWordsReply;

    /**
     * 无答案回复文案
     */
    private byte[] noAnswerReply;

    /**
     * 澄清回复文案
     */
    private byte[] clearReply;

    private static final long serialVersionUID = 1L;

}