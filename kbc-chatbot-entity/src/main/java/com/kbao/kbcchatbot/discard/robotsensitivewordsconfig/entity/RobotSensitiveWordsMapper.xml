<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.dao.RobotSensitiveWordsMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.entity.RobotSensitiveWords">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="words" column="words" jdbcType="VARCHAR"/>
            <result property="createId" column="create_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,robot_id,words,
        create_id,create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_sensitive_words
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="selectAll" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_sensitive_words
        <where>
            robot_id = #{robotId,jdbcType=BIGINT}
            <if test="words != null">
                and words like concat('%',#{words},'%')
            </if>
        </where>
    </select>

    <select id="selectCountByWords" parameterType="java.lang.Object" resultType="java.lang.Integer">
        select count(*)
        from t_robot_sensitive_words
        where robot_id = #{robotId,jdbcType=BIGINT}
        and words = #{words,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_robot_sensitive_words
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.entity.RobotSensitiveWords" useGeneratedKeys="true">
        insert into t_robot_sensitive_words
        ( id,robot_id,words
        ,create_id,create_time)
        values (#{id,jdbcType=BIGINT},#{robotId,jdbcType=BIGINT},#{words,jdbcType=VARCHAR}
        ,#{createId,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.entity.RobotSensitiveWords" useGeneratedKeys="true">
        insert into t_robot_sensitive_words
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="robotId != null">robot_id,</if>
                <if test="words != null">words,</if>
                <if test="createId != null">create_id,</if>
                <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="robotId != null">#{robotId,jdbcType=BIGINT},</if>
                <if test="words != null">#{words,jdbcType=VARCHAR},</if>
                <if test="createId != null">#{createId,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.entity.RobotSensitiveWords">
        update t_robot_sensitive_words
        <set>
                <if test="robotId != null">
                    robot_id = #{robotId,jdbcType=BIGINT},
                </if>
                <if test="words != null">
                    words = #{words,jdbcType=VARCHAR},
                </if>
                <if test="createId != null">
                    create_id = #{createId,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.entity.RobotSensitiveWords">
        update t_robot_sensitive_words
        set 
            robot_id =  #{robotId,jdbcType=BIGINT},
            words =  #{words,jdbcType=VARCHAR},
            create_id =  #{createId,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
