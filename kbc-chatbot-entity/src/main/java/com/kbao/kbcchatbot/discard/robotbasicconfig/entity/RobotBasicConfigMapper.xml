<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcchatbot.discard.robotbasicconfig.dao.RobotBasicConfigMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.discard.robotbasicconfig.entity.RobotBasicConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="clearStatus" column="clear_status" jdbcType="INTEGER"/>
            <result property="clearMaxNumber" column="clear_max_number" jdbcType="INTEGER"/>
            <result property="clearThreshold" column="clear_threshold" jdbcType="DECIMAL"/>
            <result property="directReplyThreshold" column="direct_reply_threshold" jdbcType="DECIMAL"/>
            <result property="updateId" column="update_id" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="sensitiveWordsReply" column="sensitive_words_reply" jdbcType="BLOB"/>
            <result property="noAnswerReply" column="no_answer_reply" jdbcType="BLOB"/>
            <result property="clearReply" column="clear_reply" jdbcType="BLOB"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,robot_id,type,
        clear_status,clear_max_number,clear_threshold,
        direct_reply_threshold,update_id,update_time
    </sql>
    <sql id="All_Column_List">
        id,robot_id,type,
        clear_status,clear_max_number,clear_threshold,
        direct_reply_threshold,update_id,update_time,
        sensitive_words_reply,no_answer_reply,clear_reply
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_basic_config
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="selectByRobotId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="All_Column_List" />
        from t_robot_basic_config
        where robot_id = #{robotId,jdbcType=BIGINT}
    </select>

    <select id="selectAll" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="All_Column_List" />
        from t_robot_basic_config
        <where>
            robot_id = #{robotId,jdbcType=BIGINT}
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_robot_basic_config
        where  id = #{id,jdbcType=BIGINT} 
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robotbasicconfig.entity.RobotBasicConfig" useGeneratedKeys="true">
        insert into t_robot_basic_config
        ( id,robot_id,type
        ,clear_status,clear_max_number,clear_threshold
        ,direct_reply_threshold,update_id,update_time
        ,sensitive_words_reply,no_answer_reply,clear_reply
        )
        values (#{id,jdbcType=BIGINT},#{robotId,jdbcType=BIGINT},#{type,jdbcType=INTEGER}
        ,#{clearStatus,jdbcType=INTEGER},#{clearMaxNumber,jdbcType=INTEGER},#{clearThreshold,jdbcType=DECIMAL}
        ,#{directReplyThreshold,jdbcType=DECIMAL},#{updateId,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP}
        ,#{sensitiveWordsReply,jdbcType=BLOB},#{noAnswerReply,jdbcType=BLOB},#{clearReply,jdbcType=BLOB}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robotbasicconfig.entity.RobotBasicConfig" useGeneratedKeys="true">
        insert into t_robot_basic_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="robotId != null">robot_id,</if>
                <if test="type != null">type,</if>
                <if test="clearStatus != null">clear_status,</if>
                <if test="clearMaxNumber != null">clear_max_number,</if>
                <if test="clearThreshold != null">clear_threshold,</if>
                <if test="directReplyThreshold != null">direct_reply_threshold,</if>
                <if test="updateId != null">update_id,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="sensitiveWordsReply != null">sensitive_words_reply,</if>
                <if test="noAnswerReply != null">no_answer_reply,</if>
                <if test="clearReply != null">clear_reply,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="robotId != null">#{robotId,jdbcType=BIGINT},</if>
                <if test="type != null">#{type,jdbcType=INTEGER},</if>
                <if test="clearStatus != null">#{clearStatus,jdbcType=INTEGER},</if>
                <if test="clearMaxNumber != null">#{clearMaxNumber,jdbcType=INTEGER},</if>
                <if test="clearThreshold != null">#{clearThreshold,jdbcType=DECIMAL},</if>
                <if test="directReplyThreshold != null">#{directReplyThreshold,jdbcType=DECIMAL},</if>
                <if test="updateId != null">#{updateId,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="sensitiveWordsReply != null">#{sensitiveWordsReply,jdbcType=BLOB},</if>
                <if test="noAnswerReply != null">#{noAnswerReply,jdbcType=BLOB},</if>
                <if test="clearReply != null">#{clearReply,jdbcType=BLOB},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.discard.robotbasicconfig.entity.RobotBasicConfig">
        update t_robot_basic_config
        <set>
                <if test="robotId != null">
                    robot_id = #{robotId,jdbcType=BIGINT},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=INTEGER},
                </if>
                <if test="clearStatus != null">
                    clear_status = #{clearStatus,jdbcType=INTEGER},
                </if>
                <if test="clearMaxNumber != null">
                    clear_max_number = #{clearMaxNumber,jdbcType=INTEGER},
                </if>
                <if test="clearThreshold != null">
                    clear_threshold = #{clearThreshold,jdbcType=DECIMAL},
                </if>
                <if test="directReplyThreshold != null">
                    direct_reply_threshold = #{directReplyThreshold,jdbcType=DECIMAL},
                </if>
                <if test="updateId != null">
                    update_id = #{updateId,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="sensitiveWordsReply != null">
                    sensitive_words_reply = #{sensitiveWordsReply,jdbcType=BLOB},
                </if>
                <if test="noAnswerReply != null">
                    no_answer_reply = #{noAnswerReply,jdbcType=BLOB},
                </if>
                <if test="clearReply != null">
                    clear_reply = #{clearReply,jdbcType=BLOB},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.discard.robotbasicconfig.entity.RobotBasicConfig">
        update t_robot_basic_config
        set 
            robot_id =  #{robotId,jdbcType=BIGINT},
            type =  #{type,jdbcType=INTEGER},
            clear_status =  #{clearStatus,jdbcType=INTEGER},
            clear_max_number =  #{clearMaxNumber,jdbcType=INTEGER},
            clear_threshold =  #{clearThreshold,jdbcType=DECIMAL},
            direct_reply_threshold =  #{directReplyThreshold,jdbcType=DECIMAL},
            update_id =  #{updateId,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            sensitive_words_reply =  #{sensitiveWordsReply,jdbcType=BLOB},
            no_answer_reply =  #{noAnswerReply,jdbcType=BLOB},
            clear_reply =  #{clearReply,jdbcType=BLOB}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
