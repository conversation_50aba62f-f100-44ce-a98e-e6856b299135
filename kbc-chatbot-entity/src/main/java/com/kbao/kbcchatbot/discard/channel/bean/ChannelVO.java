package com.kbao.kbcchatbot.discard.channel.bean;

import lombok.Data;
import org.springframework.data.annotation.Transient;

import java.io.Serializable;

/**
* <AUTHOR>
* @Description 渠道表实体
* @Date 2023-05-18
*/
@Data
public class ChannelVO implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Long id;

    /**
     * 对应表中robot_id
     * 聊天机器人ID
     */  
	private Long robotId;

    /**
     * 对应表中code
     * 编码
     */  
	private String code;

    /**
     * 对应表中name
     * 名称
     */  
	private String channelName;


	/**
	 * 对应表中welcome_reply
	 * 欢迎语文案
	 */
	private String welcomeReply;

	/**
	 * 对应表中input_place_holder
	 * 输入框引导语
	 */
	private String inputPlaceHolder;

	/**
	 * 对应表中guess_question_status
	 * 猜你想问开启状态 0-未开启 1-开启
	 */
	private String guessQuestionStatus;

	/**
	 * 对应表中common_card_status
	 * 常用卡片开启状态 0-未开启 1-开启
	 */
	private String commonCardStatus;

	/**
	 * 对应表中common_phrase_status
	 * 常用短语开启状态 0-未开启 1-开启
	 */
	private String commonPhraseStatus;

	/**
	 * 对应表中answer_evaluate_status
	 * 答案评价开启状态 0-未开启 1-开启
	 */
	private String answerEvaluateStatus;
	/**
	 * 是否开启语音识别
	 */
	private String voiceStatus;

	/**
	 * 通道ID
	 */
	private String passId;

	/**
	 * 对应表中remark
	 * 备注
	 */
	private String remark;


    /**
     * 对应表中status
     * 状态 0-未启用 1-启用
     */  
	private String status;


    /**
     * 对应表中create_time
     * 创建时间
     */  
	private String createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中update_time
     * 最后更新时间
     */  
	private String updateTime;

    /**
     * 对应表中update_id
     * 最后更新人
     */  
	private String updateId;

    /**
     * 对应表中tenant_id
     * 租户ID
     */  
	private String tenantId;

    /**
     * 对应表中is_deleted
     * 是否删除
     */  
	private Integer isDeleted;

	/**
	 * 类型 1-跳转页面 2-发送文本 3-关联知识库
	 */
	@Transient
	private String type;

	/**
	 * 机器人名称
	 */
	@Transient
	private String robotName;

	/**
	 * 基础配置id
	 */
	@Transient
	private Long channelBasicConfigId;

}   