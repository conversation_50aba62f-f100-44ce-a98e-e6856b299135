<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.discard.knowledgepackage.dao.KnowledgePackageMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackage">
		<id column="id" jdbcType="BIGINT"  property="id"  />
		<result property="name" jdbcType="VARCHAR"  column="name" />
		<result property="code" jdbcType="VARCHAR"  column="code" />
		<result property="status" jdbcType="VARCHAR"  column="status" />
		<result property="type" jdbcType="VARCHAR"  column="type" />
		<result property="remark" jdbcType="VARCHAR"  column="remark" />
		<result property="createTime" jdbcType="VARCHAR"  column="create_time" />
		<result property="createId" jdbcType="VARCHAR"  column="create_id" />
		<result property="updateTime" jdbcType="VARCHAR"  column="update_time" />
		<result property="updateId" jdbcType="VARCHAR"  column="update_id" />
		<result property="isDeleted" jdbcType="INTEGER"  column="is_deleted" />
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		name,  
		code,  
		status,  
		type,  
		remark,  
		create_time,  
		create_id,  
		update_time,  
		update_id,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.name, 
		t.code, 
		t.status, 
		t.type, 
		t.remark, 
		t.create_time, 
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
		<where>
			t.is_deleted = 0
			<if test="name != null and name != ''">
				and t.name = #{name,jdbcType=VARCHAR}
			</if>
			<if test="code != null and code != ''">
				and t.code = #{code,jdbcType=VARCHAR}
			</if>
			<if test="status != null and status != ''">
				and t.status = #{status,jdbcType=VARCHAR}
			</if>
			<if test="type != null and type != ''">
				and t.type = #{type,jdbcType=VARCHAR}
			</if>
			<if test="remark != null and remark != ''">
				and t.remark = #{remark,jdbcType=VARCHAR}
			</if>
			<if test="createTime != null and createTime != ''">
				and t.create_time = #{createTime,jdbcType=VARCHAR}
			</if>
			<if test="createId != null and createId != ''">
				and t.create_id = #{createId,jdbcType=VARCHAR}
			</if>
			<if test="updateTime != null and updateTime != ''">
				and t.update_time = #{updateTime,jdbcType=VARCHAR}
			</if>
			<if test="updateId != null and updateId != ''">
				and t.update_id = #{updateId,jdbcType=VARCHAR}
			</if>
			<if test="isDeleted != null">
				and t.is_deleted = #{isDeleted,jdbcType=INTEGER}
			</if>
			<!-- 自定义条件-->
			<if test="idNotEq != null">
				and t.id != #{idNotEq,jdbcType=BIGINT}
			</if>
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_knowledge_package t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_knowledge_package t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_knowledge_package
		where  is_deleted = 0 and id = #{id,jdbcType=BIGINT}
	</select>


	<!-- 根据主键删除 -->
	<update id="deleteByPrimaryKey" parameterType="java.lang.Long">
		update t_knowledge_package set is_deleted = 1
		where id = #{id,jdbcType=BIGINT} and is_deleted = 0
	</update>



	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackage">
		<!-- <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_knowledge_package(
		<include refid="Base_Column_List" />
		)
		values(
		#{id,jdbcType=BIGINT},

		#{name,jdbcType=VARCHAR},

		#{code,jdbcType=VARCHAR},

		#{status,jdbcType=VARCHAR},

		#{type,jdbcType=VARCHAR},

		#{remark,jdbcType=VARCHAR},

		#{createTime,jdbcType=VARCHAR},

		#{createId,jdbcType=VARCHAR},

		#{updateTime,jdbcType=VARCHAR},

		#{updateId,jdbcType=VARCHAR},

		#{isDeleted,jdbcType=INTEGER}
		)
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackage">
		<selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
			SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_knowledge_package
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null ">
				id,
			</if>
			<if test="name != null ">
				name,
			</if>
			<if test="code != null ">
				code,
			</if>
			<if test="status != null ">
				status,
			</if>
			<if test="type != null ">
				type,
			</if>
			<if test="remark != null ">
				remark,
			</if>
			<if test="createTime != null ">
				create_time,
			</if>
			<if test="createId != null ">
				create_id,
			</if>
			<if test="updateTime != null ">
				update_time,
			</if>
			<if test="updateId != null ">
				update_id,
			</if>
			<if test="isDeleted != null ">
				is_deleted,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="name != null">
				#{name,jdbcType=VARCHAR},
			</if>
			<if test="code != null">
				#{code,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				#{type,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=VARCHAR},
			</if>
			<if test="createId != null">
				#{createId,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=VARCHAR},
			</if>
			<if test="updateId != null">
				#{updateId,jdbcType=VARCHAR},
			</if>
			<if test="isDeleted != null">
				#{isDeleted,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackage">
		update t_knowledge_package
		<set>
			<if test="name != null ">
				name = #{name,jdbcType=VARCHAR},
			</if>
			<if test="code != null ">
				code = #{code,jdbcType=VARCHAR},
			</if>
			<if test="status != null ">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="type != null ">
				type = #{type,jdbcType=VARCHAR},
			</if>
			<if test="remark != null ">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null ">
				create_time = #{createTime,jdbcType=VARCHAR},
			</if>
			<if test="createId != null ">
				create_id = #{createId,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null ">
				update_time = #{updateTime,jdbcType=VARCHAR},
			</if>
			<if test="updateId != null ">
				update_id = #{updateId,jdbcType=VARCHAR},
			</if>
			<if test="isDeleted != null ">
				is_deleted = #{isDeleted,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackage">
		update t_knowledge_package
		set
           name = #{name,jdbcType=VARCHAR},
           code = #{code,jdbcType=VARCHAR},
           status = #{status,jdbcType=VARCHAR},
           type = #{type,jdbcType=VARCHAR},
           remark = #{remark,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=VARCHAR},
           create_id = #{createId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=VARCHAR},
           update_id = #{updateId,jdbcType=VARCHAR},
           is_deleted = #{isDeleted,jdbcType=INTEGER}
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_knowledge_package(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
			(
			#{item.id}
			<choose>
				<when test="item.name != null">,#{item.name}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.code != null">,#{item.code}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.type != null">,#{item.type}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
			</choose>
			)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_knowledge_package(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
			(
			#{item.id}
			<choose><when test="item.name != null">,#{item.name}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.code != null">,#{item.code}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.type != null">,#{item.type}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise></choose>
			)
		</foreach>
		on duplicate key update
		name=values(name),
		code=values(code),
		status=values(status),
		type=values(type),
		remark=values(remark),
		create_time=values(create_time),
		create_id=values(create_id),
		update_time=values(update_time),
		update_id=values(update_id),
		is_deleted=values(is_deleted)
	</update>

	<!-- 批量删除-->
	<update id="batchDelete" parameterType="java.util.List">
		update t_knowledge_package set is_deleted = 1 where id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
		and is_deleted = 0
	</update>

	<!-- 自定义查询 -->
	<select id="getPackageCodeListByRobotId" parameterType="java.lang.Long" resultType="java.lang.String" >
		select t2.code
		from t_robot_package_rel t1 inner join t_knowledge_package t2 on t1.package_id = t2.id
		where t1.robot_id = #{robotId,jdbcType=BIGINT}
	</select>
</mapper>
