package com.kbao.kbcchatbot.discard.robotdataboard.vo;

import lombok.Data;

@Data
public class ChatSessionConvRecord {

     private String recordId;

     private String sessionId;

     private Long channelId;

     private String channelCode;

     private String channelName;

     private Long robotId;

     private String robotCode;

     private String robotName;

     private String startTime;

     private String endTime;

     private long time;

     private String userId;

     private String userName;

     private String userContent;

     private String answerContent;

     private String intention;

     private String contextualizeQuestion;

     private String model;

     private String answerSource;

     private String robotAnswerType;

     private String userVoteResult;

     private String userVoteRemark;

     private String isCopy;

     private String deviceType;

}
