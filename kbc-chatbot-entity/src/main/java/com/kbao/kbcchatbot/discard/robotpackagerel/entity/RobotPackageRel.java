package com.kbao.kbcchatbot.discard.robotpackagerel.entity;
import java.io.Serializable;
import lombok.Data;

/**
* <AUTHOR>
* @Description 机器人包关联实体
* @Date 2023-06-02
*/
@Data
public class RobotPackageRel implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Long id;

    /**
     * 对应表中robot_id
     * 机器人ID
     */  
	private Long robotId;

    /**
     * 对应表中packge_id
     * 知识包ID
     */  
	private Long packageId;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	private String createTime;

    /**
     * 对应表中create_id
     * 创建人ID
     */  
	private String createId;

}   