package com.kbao.kbcchatbot.discard.channel.bean;

import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean.ChannelCommonCardVO;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean.ChannelCommonPhraseVO;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> qiuzb
 * @Description: 渠道全量数据VO
 * @create 2023/6/8 11:27
 */
@Data
public class ChannelFullVO {

    /**
     * 渠道信息
     */
    private ChannelVO channelVO;

    /**
     * 渠道基础配置
     */
    private ChannelBasicConfig channelBasicConfig;

    /**
     * 渠道猜你想问列表
     */
    private List<ChannelGuessQuestionVO> channelGuessQuestionList;


    /**
     * 渠道通用卡片列表
     */
    private List<ChannelCommonCardVO> channelCommonCardList;

    /**
     * 渠道通用短语列表
     */
    private List<ChannelCommonPhraseVO> channelCommonPhraseList;

}
