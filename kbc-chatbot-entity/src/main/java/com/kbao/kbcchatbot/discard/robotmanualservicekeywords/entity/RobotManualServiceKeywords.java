package com.kbao.kbcchatbot.discard.robotmanualservicekeywords.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天机器人转人工关键词表
 * @TableName t_robot_manual_service_keywords
 */
@Data
public class RobotManualServiceKeywords implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 机器人主键id
     */
    private Long robotId;

    /**
     * 类型 0-测试版 1-正式版
     */
    private Integer type;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 创建人
     */
    private String createId;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}