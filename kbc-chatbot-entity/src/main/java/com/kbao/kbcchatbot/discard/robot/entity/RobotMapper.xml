<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcchatbot.discard.robot.dao.RobotMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.discard.robot.entity.Robot">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="preStatus" column="pre_status" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="publishTime" column="publish_time" jdbcType="TIMESTAMP"/>
            <result property="publisher" column="publisher" jdbcType="VARCHAR"/>
            <result property="releaseType" column="release_type" jdbcType="INTEGER"/>
            <result property="modelType" column="model_type" jdbcType="INTEGER"/>
            <result property="createId" column="create_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="upadteId" column="upadte_id" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,code,name,pre_status,
        status,version,publish_time,
        publisher,release_type,model_type,create_id,create_time,
        upadte_id,update_time,tenant_id,
        is_deleted
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="selectAll" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot
        <where>
            is_deleted = 0
            and tenant_id = #{tenantId,jdbcType=VARCHAR}
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="code != null and code !=''">
                and code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="name != null and name !=''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>
            <if test="nameEq != null">
                and name = #{nameEq,jdbcType=VARCHAR}
            </if>
            <if test="statusList != null">
                and status in(
                <foreach collection="statusList" index="index" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>

    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_robot
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robot.entity.Robot" useGeneratedKeys="true">
        insert into t_robot
        ( id,code,name,pre_status
        ,status,version,publish_time
        ,publisher,release_type,model_type,create_id,create_time
        ,upadte_id,update_time,tenant_id
        ,is_deleted)
        values (#{id,jdbcType=BIGINT},#{code,jdbcType=VARCHAR},#{name,jdbcType=VARCHAR},#{preStatus,jdbcType=INTEGER}
        ,#{status,jdbcType=INTEGER},#{version,jdbcType=VARCHAR},#{publishTime,jdbcType=TIMESTAMP}
        ,#{publisher,jdbcType=VARCHAR},#{releaseType,jdbcType=INTEGER},#{modelType,jdbcType=INTEGER},#{createId,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{upadteId,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{tenantId,jdbcType=VARCHAR}
        ,#{isDeleted,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robot.entity.Robot" useGeneratedKeys="true">
        insert into t_robot
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="code != null">code,</if>
                <if test="name != null">name,</if>
                <if test="preStatus != null">pre_status,</if>
                <if test="status != null">status,</if>
                <if test="version != null">version,</if>
                <if test="publishTime != null">publish_time,</if>
                <if test="publisher != null">publisher,</if>
                <if test="releaseType != null">release_type,</if>
                <if test="modelType != null">model_type,</if>
                <if test="createId != null">create_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="upadteId != null">upadte_id,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="tenantId != null">tenant_id,</if>
                <if test="isDeleted != null">is_deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="code != null">#{code,jdbcType=VARCHAR},</if>
                <if test="name != null">#{name,jdbcType=VARCHAR},</if>
                <if test="preStatus != null">#{preStatus,jdbcType=INTEGER},</if>
                <if test="status != null">#{status,jdbcType=INTEGER},</if>
                <if test="version != null">#{version,jdbcType=VARCHAR},</if>
                <if test="publishTime != null">#{publishTime,jdbcType=TIMESTAMP},</if>
                <if test="publisher != null">#{publisher,jdbcType=VARCHAR},</if>
                <if test="releaseType != null">#{releaseType,jdbcType=INTEGER},</if>
                <if test="modelType != null">#{modelType,jdbcType=INTEGER},</if>
                <if test="createId != null">#{createId,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="upadteId != null">#{upadteId,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="tenantId != null">#{tenantId,jdbcType=VARCHAR},</if>
                <if test="isDeleted != null">#{isDeleted,jdbcType=INTEGER},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.discard.robot.entity.Robot">
        update t_robot
        <set>
                <if test="code != null">
                    code = #{code,jdbcType=VARCHAR},
                </if>
                <if test="name != null">
                    name = #{name,jdbcType=VARCHAR},
                </if>
                <if test="preStatus != null">
                    pre_status = #{preStatus,jdbcType=INTEGER},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=INTEGER},
                </if>
                <if test="version != null">
                    version = #{version,jdbcType=VARCHAR},
                </if>
                <if test="publishTime != null">
                    publish_time = #{publishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="publisher != null">
                    publisher = #{publisher,jdbcType=VARCHAR},
                </if>
                <if test="releaseType != null">
                    release_type = #{releaseType,jdbcType=INTEGER},
                </if>
                <if test="modelType != null">
                    model_type = #{modelType,jdbcType=INTEGER},
                </if>
                <if test="createId != null">
                    create_id = #{createId,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="upadteId != null">
                    upadte_id = #{upadteId,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="tenantId != null">
                    tenant_id = #{tenantId,jdbcType=VARCHAR},
                </if>
                <if test="isDeleted != null">
                    is_deleted = #{isDeleted,jdbcType=INTEGER},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.discard.robot.entity.Robot">
        update t_robot
        set 
            code =  #{code,jdbcType=VARCHAR},
            name =  #{name,jdbcType=VARCHAR},
            pre_status =  #{preStatus,jdbcType=INTEGER},
            status =  #{status,jdbcType=INTEGER},
            version =  #{version,jdbcType=VARCHAR},
            publish_time =  #{publishTime,jdbcType=TIMESTAMP},
            publisher =  #{publisher,jdbcType=VARCHAR},
            release_type =  #{releaseType,jdbcType=INTEGER},
            model_type =  #{modelType,jdbcType=INTEGER},
            create_id =  #{createId,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            upadte_id =  #{upadteId,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            tenant_id =  #{tenantId,jdbcType=VARCHAR},
            is_deleted =  #{isDeleted,jdbcType=INTEGER}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

<!--根据机器人Ids查询机器人信息-->
    <select id="selectBatchByPrimaryKey" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT name,id
        FROM t_robot
        WHERE id IN
        <foreach item="item" index="index" collection="robotIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from t_robot
        where code = #{code,jdbcType=VARCHAR}
    </select>
</mapper>
