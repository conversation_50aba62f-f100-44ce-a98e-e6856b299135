package com.kbao.kbcchatbot.discard.knowledgepackage.entity;
import java.io.Serializable;
import lombok.Data;

/**
* <AUTHOR>
* @Description 知识包实体
* @Date 2023-05-23
*/
@Data
public class KnowledgePackage implements Serializable{
	private static final long serialVersionUID = 1L;
 
    /**
     * 对应表中id
     * 主键
     */  
	private Long id;

    /**
     * 对应表中name
     * 知识包名称
     */  
	private String name;

    /**
     * 对应表中code
     * 知识包编号
     */  
	private String code;

    /**
     * 对应表中status
     * 启用状态 0-未启用 1-启用
     */  
	private String status;

    /**
     * 对应表中type
     * 知识包类型
     */  
	private String type;

    /**
     * 对应表中remark
     * 备注
     */  
	private String remark;

    /**
     * 对应表中create_time
     * 创建时间
     */  
	private String createTime;

    /**
     * 对应表中create_id
     * 创建人
     */  
	private String createId;

    /**
     * 对应表中update_time
     * 最后更新时间
     */  
	private String updateTime;

    /**
     * 对应表中update_id
     * 最后创建人
     */  
	private String updateId;

    /**
     * 对应表中is_deleted
     * 是否删除
     */  
	private Integer isDeleted;

}   