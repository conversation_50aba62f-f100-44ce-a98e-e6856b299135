<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.channel.dao.ChannelMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.discard.channel.entity.Channel">
    	<id column="id" jdbcType="BIGINT"  property="id"  />
        <result property="robotId" jdbcType="BIGINT"  column="robot_id" />
        <result property="code" jdbcType="VARCHAR"  column="code" />  
        <result property="name" jdbcType="VARCHAR"  column="name" />  
        <result property="status" jdbcType="VARCHAR"  column="status" />  
        <result property="remark" jdbcType="VARCHAR"  column="remark" />
        <result property="privateKey" jdbcType="VARCHAR"  column="private_key" />
        <result property="publicKey" jdbcType="VARCHAR"  column="public_key" />
        <result property="createTime" jdbcType="VARCHAR"  column="create_time" />
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
        <result property="updateTime" jdbcType="VARCHAR"  column="update_time" />  
        <result property="updateId" jdbcType="VARCHAR"  column="update_id" />  
        <result property="tenantId" jdbcType="VARCHAR"  column="tenant_id" />  
        <result property="isDeleted" jdbcType="TINYINT"  column="is_deleted" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		robot_id,  
		code,  
		name,  
		status,  
		remark,  
		private_key,
		public_key,
		create_time,
		create_id,  
		update_time,  
		update_id,  
		tenant_id,  
		is_deleted  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.robot_id, 
		t.code, 
		t.name, 
		t.status, 
		t.remark, 
<!--		t.private_key,-->
<!--		t.public_key,-->
		t.create_time,
		t.create_id, 
		t.update_time, 
		t.update_id, 
		t.tenant_id, 
		t.is_deleted 
	</sql>

	<sql id="Base_Condition">
        <where>
			is_deleted = 0
            <if test="robotId != null">
                and t.robot_id = #{robotId,jdbcType=BIGINT}
            </if>
            <if test="code != null and code != ''">
                and t.code = #{code,jdbcType=VARCHAR}
            </if>
			<if test="name != null and name != ''">
				and t.name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
			</if>
            <if test="status != null and status != ''">
                and t.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="remark != null and remark != ''">
                and t.remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null and createTime != ''">
                and t.create_time = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="createId != null and createId != ''">
                and t.create_id = #{createId,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null and updateTime != ''">
                and t.update_time = #{updateTime,jdbcType=VARCHAR}
            </if>
            <if test="updateId != null and updateId != ''">
                and t.update_id = #{updateId,jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null and tenantId != ''">
                and t.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="isDeleted != null">
                and t.is_deleted = #{isDeleted,jdbcType=TINYINT}
            </if>
            <!-- 自定义条件-->

            <if test="type != null and type != ''">
                AND t.id IN (
                SELECT channel_id
                FROM t_channel_common_card
                WHERE type = #{type, jdbcType=VARCHAR}
                )
            </if>
        </where>
    </sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_channel t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_channel t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_channel
		where  id = #{id,jdbcType=BIGINT}
	</select>

	<select id="selectKeyByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
			id,robot_id,code,private_key,public_key,status,tenant_id
		from t_channel
		where  code = #{code,jdbcType=VARCHAR}
		and is_deleted = 0
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from t_channel
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.discard.channel.entity.Channel">
		insert into t_channel(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=BIGINT},
                 
                #{robotId,jdbcType=BIGINT},
                 
                #{code,jdbcType=VARCHAR}, 
                 
                #{name,jdbcType=VARCHAR}, 
                 
                #{status,jdbcType=VARCHAR}, 
                 
                #{remark,jdbcType=VARCHAR},

                #{privateKey,jdbcType=VARCHAR},

                #{publicKey,jdbcType=VARCHAR},

                #{createTime,jdbcType=VARCHAR}, 
                 
                #{createId,jdbcType=VARCHAR}, 
                 
                #{updateTime,jdbcType=VARCHAR}, 
                 
                #{updateId,jdbcType=VARCHAR}, 
                 
                #{tenantId,jdbcType=VARCHAR}, 
                 
                #{isDeleted,jdbcType=TINYINT} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.discard.channel.entity.Channel">
		insert into t_channel
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="robotId != null ">  
	       		robot_id,
	        </if>  
	        <if test="code != null ">  
	       		code,
	        </if>  
	        <if test="name != null ">  
	       		name,
	        </if>  
	        <if test="status != null ">  
	       		status,
	        </if>  
	        <if test="remark != null ">  
	       		remark,
	        </if>
			<if test="privateKey != null ">
				private_key,
			</if>
			<if test="public_key != null ">
				public_key,
			</if>
			<if test="createTime != null ">
	       		create_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
	        <if test="updateTime != null ">  
	       		update_time,
	        </if>  
	        <if test="updateId != null ">  
	       		update_id,
	        </if>  
	        <if test="tenantId != null ">  
	       		tenant_id,
	        </if>  
	        <if test="isDeleted != null ">  
	       		is_deleted,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=BIGINT},
            </if>  
            <if test="robotId != null">  
            	#{robotId,jdbcType=BIGINT},
            </if>  
            <if test="code != null">  
            	#{code,jdbcType=VARCHAR},
            </if>  
            <if test="name != null">  
            	#{name,jdbcType=VARCHAR},
            </if>  
            <if test="status != null">  
            	#{status,jdbcType=VARCHAR},
            </if>  
            <if test="remark != null">  
            	#{remark,jdbcType=VARCHAR},
            </if>
			<if test="privateKey != null">
				#{privateKey,jdbcType=VARCHAR},
			</if>
			<if test="publicKey != null">
				#{publicKey,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
            	#{createTime,jdbcType=VARCHAR},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
            <if test="updateTime != null">  
            	#{updateTime,jdbcType=VARCHAR},
            </if>  
            <if test="updateId != null">  
            	#{updateId,jdbcType=VARCHAR},
            </if>  
            <if test="tenantId != null">  
            	#{tenantId,jdbcType=VARCHAR},
            </if>  
            <if test="isDeleted != null">  
            	#{isDeleted,jdbcType=TINYINT},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.discard.channel.entity.Channel">
		update t_channel
		<set>
	        <if test="robotId != null ">  
	        	robot_id = #{robotId,jdbcType=BIGINT},
	        </if>  
	        <if test="code != null ">  
	        	code = #{code,jdbcType=VARCHAR},  
	        </if>  
	        <if test="name != null ">  
	        	name = #{name,jdbcType=VARCHAR},  
	        </if>  
	        <if test="status != null ">  
	        	status = #{status,jdbcType=VARCHAR},  
	        </if>  
	        <if test="remark != null ">  
	        	remark = #{remark,jdbcType=VARCHAR},  
	        </if>
			<if test="privateKey != null ">
				private_key = #{privateKey,jdbcType=VARCHAR},
			</if>
			<if test="publicKey != null ">
				public_key = #{publicKey,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null ">
	        	create_time = #{createTime,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateTime != null ">  
	        	update_time = #{updateTime,jdbcType=VARCHAR},  
	        </if>  
	        <if test="updateId != null ">  
	        	update_id = #{updateId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="tenantId != null ">  
	        	tenant_id = #{tenantId,jdbcType=VARCHAR},  
	        </if>  
	        <if test="isDeleted != null ">  
	        	is_deleted = #{isDeleted,jdbcType=TINYINT},  
	        </if>  
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.discard.channel.entity.Channel">
		update t_channel
		set
           robot_id = #{robotId,jdbcType=BIGINT},
           code = #{code,jdbcType=VARCHAR},
           name = #{name,jdbcType=VARCHAR},
           status = #{status,jdbcType=VARCHAR},
           remark = #{remark,jdbcType=VARCHAR},
		   private_key = #{privateKey,jdbcType=VARCHAR},
		   public_key = #{publicKey,jdbcType=VARCHAR},
           create_time = #{createTime,jdbcType=VARCHAR},
           create_id = #{createId,jdbcType=VARCHAR},
           update_time = #{updateTime,jdbcType=VARCHAR},
           update_id = #{updateId,jdbcType=VARCHAR},
           tenant_id = #{tenantId,jdbcType=VARCHAR},
           is_deleted = #{isDeleted,jdbcType=TINYINT}
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_channel(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.robotId != null">,#{item.robotId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.code != null">,#{item.code}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.name != null">,#{item.name}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise>
	        </choose>
			<choose>
				<when test="item.privateKey != null">,#{item.remark}</when><otherwise>,default</otherwise>
			</choose>
			<choose>
				<when test="item.publicKey != null">,#{item.remark}</when><otherwise>,default</otherwise>
			</choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_channel(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.robotId != null">,#{item.robotId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.code != null">,#{item.code}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.name != null">,#{item.name}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.status != null">,#{item.status}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.remark != null">,#{item.remark}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.privateKey != null">,#{item.remark}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.publicKey != null">,#{item.remark}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateTime != null">,#{item.updateTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.updateId != null">,#{item.updateId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.tenantId != null">,#{item.tenantId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.isDeleted != null">,#{item.isDeleted}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   robot_id=values(robot_id), 
		   code=values(code), 
		   name=values(name), 
		   status=values(status), 
		   remark=values(remark),
		   private_key=values(private_key),
		   public_key=values(public_key),
		   create_time=values(create_time),
		   create_id=values(create_id), 
		   update_time=values(update_time), 
		   update_id=values(update_id), 
		   tenant_id=values(tenant_id), 
		   is_deleted=values(is_deleted) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_channel where id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>



    <!-- 自定义查询 -->
<!--根据机器人id查询启用渠道数量-->
	<select id="getEnabledRobotNumByRobotId" resultType="int">
		select count(robot_id) as enabledRobotNum from t_channel where robot_id = #{robotId,jdbcType=BIGINT} and status = '1'	</select>
</mapper>
