<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcchatbot.discard.robotmanualservicekeywords.dao.RobotManualServiceKeywordsMapper">

    <resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.discard.robotmanualservicekeywords.entity.RobotManualServiceKeywords">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="keywords" column="keywords" jdbcType="VARCHAR"/>
            <result property="createId" column="create_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,robot_id,type,
        keywords,create_id,create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_manual_service_keywords
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="selectByRobotId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_manual_service_keywords
        where robot_id = #{robotId,jdbcType=BIGINT}
    </select>

    <select id="selectAll" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_robot_manual_service_keywords
        <where>
            robot_id = #{robotId,jdbcType=BIGINT}
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_robot_manual_service_keywords
        where  id = #{id,jdbcType=BIGINT} 
    </delete>

    <delete id="deleteByRobotIdAndType" parameterType="java.util.Map">
        delete from t_robot_manual_service_keywords
        where  robot_id = #{robotId,jdbcType=BIGINT}
        and type = #{type,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robotmanualservicekeywords.entity.RobotManualServiceKeywords" useGeneratedKeys="true">
        insert into t_robot_manual_service_keywords
        ( id,robot_id,type
        ,keywords,create_id,create_time
        )
        values (#{id,jdbcType=BIGINT},#{robotId,jdbcType=BIGINT},#{type,jdbcType=INTEGER}
        ,#{keywords,jdbcType=VARCHAR},#{createId,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.kbao.kbcchatbot.discard.robotmanualservicekeywords.entity.RobotManualServiceKeywords" useGeneratedKeys="true">
        insert into t_robot_manual_service_keywords
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="robotId != null">robot_id,</if>
                <if test="type != null">type,</if>
                <if test="keywords != null">keywords,</if>
                <if test="createId != null">create_id,</if>
                <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="robotId != null">#{robotId,jdbcType=BIGINT},</if>
                <if test="type != null">#{type,jdbcType=INTEGER},</if>
                <if test="keywords != null">#{keywords,jdbcType=VARCHAR},</if>
                <if test="createId != null">#{createId,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.discard.robotmanualservicekeywords.entity.RobotManualServiceKeywords">
        update t_robot_manual_service_keywords
        <set>
                <if test="robotId != null">
                    robot_id = #{robotId,jdbcType=BIGINT},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=INTEGER},
                </if>
                <if test="keywords != null">
                    keywords = #{keywords,jdbcType=VARCHAR},
                </if>
                <if test="createId != null">
                    create_id = #{createId,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.discard.robotmanualservicekeywords.entity.RobotManualServiceKeywords">
        update t_robot_manual_service_keywords
        set 
            robot_id =  #{robotId,jdbcType=BIGINT},
            type =  #{type,jdbcType=INTEGER},
            keywords =  #{keywords,jdbcType=VARCHAR},
            create_id =  #{createId,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
