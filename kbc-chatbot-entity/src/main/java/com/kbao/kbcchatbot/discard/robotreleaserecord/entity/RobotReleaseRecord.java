package com.kbao.kbcchatbot.discard.robotreleaserecord.entity;

import com.kbao.kbcchatbot.discard.robot.bean.TrainRasaModelFailureReqVO;
import com.kbao.kbcchatbot.discard.robotreleaserecord.enums.RobotReleaseStatusEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.RobotKnowledgeTrainVO;
import com.kbao.kbcchatbot.discard.robotbasicconfig.entity.RobotBasicConfig;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.entity.RobotManualServiceConfig;
import com.kbao.kbcchatbot.discard.robotmanualservicekeywords.entity.RobotManualServiceKeywords;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 机器人发布记录
 * @author: husw
 * @create: 2023-05-18 09:05
 **/
@Data
@Document(collection = "RobotReleaseRecord")
public class RobotReleaseRecord {

    @Id
    private String id;

    /**
     * 机器人ID
     */
    private Long robotId;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 发布人
     */
    private String publisher;
    /**
     * 版本号
     */
    private String version;

    /**
     * 发布状态 1-发布成功  2-发布失败 0-发布中
     * @see RobotReleaseStatusEnum
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String errorMsg;
    /**
     * 版本基础配置对象
     */
    private RobotBasicConfig basicConfig;
    /**
     * 版本知识配置对象
     */
    private List<RobotKnowledgeConfig> knowledgeConfig;
    /**
     * 版本转人工配置对象
     */
    private RobotManualServiceConfig manualServiceConfig;
    /**
     * 版本转人工关键词配置对象
     */
    private List<RobotManualServiceKeywords> manualServiceKeywords;

    /**
     * RASA模型文件OSS fileId
     */
    private String rasaModelOSSFileId;
    /**
     * rasa训练失败回调参数
     */
    private TrainRasaModelFailureReqVO trainRasaModelFailure;

    /**
     * 模型训练数据对象
     */
    private RobotKnowledgeTrainVO trainVO;
}
