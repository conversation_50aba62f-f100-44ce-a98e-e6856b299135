package com.kbao.kbcchatbot.discard.robot.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: kbc-chatbot
 * @description: 更新状态
 * @author: husw
 * @create: 2023-05-16 14:37
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RobotUpdateStatusVO {

    /**
     * id
     */
    private Long id;

    /**
     * 状态
     */
    private Integer status;
}
