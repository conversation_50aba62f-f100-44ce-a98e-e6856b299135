<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kbao.kbcchatbot.discard.robotpackagerel.dao.RobotPackageRelMapper">

	<resultMap id="BaseResultMap" type="com.kbao.kbcchatbot.discard.robotpackagerel.entity.RobotPackageRel">
    	<id column="id" jdbcType="BIGINT"  property="id"  />
        <result property="robotId" jdbcType="BIGINT"  column="robot_id" />  
        <result property="packageId" jdbcType="BIGINT"  column="package_id" />  
        <result property="createTime" jdbcType="VARCHAR"  column="create_time" />  
        <result property="createId" jdbcType="VARCHAR"  column="create_id" />  
	</resultMap>

	<sql id="Base_Column_List">
		id,  
		robot_id,  
		package_id,  
		create_time,  
		create_id  
	</sql>

	<sql id="Alias_Column_List">
		t.id, 
		t.robot_id, 
		t.package_id, 
		t.create_time, 
		t.create_id 
	</sql>

	<sql id="Base_Condition">
		<where>
	    <if test="robotId != null">
	   		and t.robot_id = #{robotId,jdbcType=BIGINT}  
	    </if>
	    <if test="packageId != null">
	   		and t.package_id = #{packageId,jdbcType=BIGINT}  
	    </if>
	    <if test="createTime != null and createTime != ''">
	   		and t.create_time = #{createTime,jdbcType=VARCHAR}  
	    </if>
	    <if test="createId != null and createId != ''">
	   		and t.create_id = #{createId,jdbcType=VARCHAR}  
	    </if>
		<!-- 自定义条件-->
		</where>
	</sql>

	<!-- 根据条件查询-->
	<select id="selectAll" resultMap="BaseResultMap" parameterType="java.util.HashMap">
		select
		<include refid="Alias_Column_List" />
		from t_robot_package_rel t
		<include refid="Base_Condition" />
	</select>

	<!-- 查询总数-->
	<select id="count" resultType="java.lang.Integer" parameterType="java.util.HashMap">
		select count(0)
		from t_robot_package_rel t
		<include refid="Base_Condition" />
	</select>

	<!-- 根据主键查询-->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List" />
		from t_robot_package_rel
		where  id = #{id,jdbcType=BIGINT}
	</select>


	<!-- 根据主键删除 -->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from t_robot_package_rel
		where id = #{id,jdbcType=BIGINT}
	</delete>
	
	
	
	<!-- 插入所有字段 -->
	<insert id="insert" parameterType="com.kbao.kbcchatbot.discard.robotpackagerel.entity.RobotPackageRel">
		<!-- <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>  -->
		insert into t_robot_package_rel(
			<include refid="Base_Column_List" />
		)
		values(  
                #{id,jdbcType=BIGINT}, 
                 
                #{robotId,jdbcType=BIGINT}, 
                 
                #{packageId,jdbcType=BIGINT}, 
                 
                #{createTime,jdbcType=VARCHAR}, 
                 
                #{createId,jdbcType=VARCHAR} 
               )
	</insert>

	<!-- 插入部分字段 -->
	<insert id="insertSelective" parameterType="com.kbao.kbcchatbot.discard.robotpackagerel.entity.RobotPackageRel">
		<selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
		SELECT LAST_INSERT_ID() AS id
		</selectKey>
		insert into t_robot_package_rel
		<trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null ">  
	       		id,
	        </if>  
	        <if test="robotId != null ">  
	       		robot_id,
	        </if>  
	        <if test="packageId != null ">  
	       		package_id,
	        </if>  
	        <if test="createTime != null ">  
	       		create_time,
	        </if>  
	        <if test="createId != null ">  
	       		create_id,
	        </if>  
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">  
            	#{id,jdbcType=BIGINT},
            </if>  
            <if test="robotId != null">  
            	#{robotId,jdbcType=BIGINT},
            </if>  
            <if test="packageId != null">  
            	#{packageId,jdbcType=BIGINT},
            </if>  
            <if test="createTime != null">  
            	#{createTime,jdbcType=VARCHAR},
            </if>  
            <if test="createId != null">  
            	#{createId,jdbcType=VARCHAR},
            </if>  
		</trim>
	</insert>

	<!-- 更新部分字段-->
	<update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcchatbot.discard.robotpackagerel.entity.RobotPackageRel">
		update t_robot_package_rel
		<set>
	        <if test="robotId != null ">  
	        	robot_id = #{robotId,jdbcType=BIGINT},  
	        </if>  
	        <if test="packageId != null ">  
	        	package_id = #{packageId,jdbcType=BIGINT},  
	        </if>  
	        <if test="createTime != null ">  
	        	create_time = #{createTime,jdbcType=VARCHAR},  
	        </if>  
	        <if test="createId != null ">  
	        	create_id = #{createId,jdbcType=VARCHAR},  
	        </if>  
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 更新所有字段 -->
	<update id="updateByPrimaryKey" parameterType="com.kbao.kbcchatbot.discard.robotpackagerel.entity.RobotPackageRel">
		update t_robot_package_rel
		set
           robot_id = #{robotId,jdbcType=BIGINT},
           package_id = #{packageId,jdbcType=BIGINT},
           create_time = #{createTime,jdbcType=VARCHAR},
           create_id = #{createId,jdbcType=VARCHAR}
		where id = #{id,jdbcType=BIGINT}
	</update>

	<!-- 批量插入 -->
	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_robot_package_rel(
		<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
	        <choose>
	            <when test="item.robotId != null">,#{item.robotId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.packageId != null">,#{item.packageId}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise>
	        </choose>
	        <choose>
	            <when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise>
	        </choose>
		)
		</foreach>
	</insert>

	<!-- 批量插入或更新 -->
	<update id="batchInsertOrUpdate" parameterType="java.util.List">
		insert into t_robot_package_rel(
			<include refid="Base_Column_List" />
		)
		values
		<foreach collection="list" index="index" item="item" separator=",">
		(
			#{item.id}
			<choose><when test="item.robotId != null">,#{item.robotId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.packageId != null">,#{item.packageId}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createTime != null">,#{item.createTime}</when><otherwise>,default</otherwise></choose>
			<choose><when test="item.createId != null">,#{item.createId}</when><otherwise>,default</otherwise></choose>
		)
		</foreach>
		on duplicate key update 
		   robot_id=values(robot_id), 
		   package_id=values(package_id), 
		   create_time=values(create_time), 
		   create_id=values(create_id) 
	</update>
	
	<!-- 批量删除-->
	<delete id="batchDelete" parameterType="java.util.List">
		delete from t_robot_package_rel where id in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")"> #{item}
		</foreach>
	</delete>
	
	<!-- 自定义查询 -->

</mapper>
