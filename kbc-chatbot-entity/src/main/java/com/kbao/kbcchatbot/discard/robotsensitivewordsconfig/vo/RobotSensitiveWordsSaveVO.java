package com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: kbc-chatbot
 * @description: 敏感词
 * @author: husw
 * @create: 2023-07-04 09:01
 **/
@Data
public class RobotSensitiveWordsSaveVO {

    @NotNull(message = "机器人ID不能为空！")
    private Long robotId;

    /**
     * 敏感词
     */
    @NotBlank(message = "机器人ID不能为空！")
    private String words;
}
