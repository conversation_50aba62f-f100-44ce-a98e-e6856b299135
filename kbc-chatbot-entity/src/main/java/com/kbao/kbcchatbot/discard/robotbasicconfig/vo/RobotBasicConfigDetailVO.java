package com.kbao.kbcchatbot.discard.robotbasicconfig.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: kbc-chatbot
 * @description: 基础配置信息
 * @author: husw
 * @create: 2023-05-17 09:26
 **/
@Data
public class RobotBasicConfigDetailVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 机器人主键ID
     */
    private Long robotId;

    /**
     * 澄清开启状态 0-未开启 1-开启
     */
    private Integer clearStatus;

    /**
     * 澄清最高条数
     */
    private Integer clearMaxNumber;

    /**
     * 澄清阈值
     */
    private BigDecimal clearThreshold;

    /**
     * 直接回复阈值
     */
    private BigDecimal directReplyThreshold;

    /**
     * 更新人
     */
    private String updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 敏感词回复文案
     */
    private String sensitiveWordsReply;

    /**
     * 无答案回复文案
     */
    private String noAnswerReply;

    /**
     * 澄清回复文案
     */
    private String clearReply;
}
