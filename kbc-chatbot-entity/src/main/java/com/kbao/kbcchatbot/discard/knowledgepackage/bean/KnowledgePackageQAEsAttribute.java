package com.kbao.kbcchatbot.discard.knowledgepackage.bean;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @Description 知识包QA内容索引属性
 * <AUTHOR>
 * @Date 2023-5-23
 */
@Data
@Component
@RefreshScope
public class KnowledgePackageQAEsAttribute {

    @Value("${es.indexName.knowledgePackageQA:sta-kbcs-search_knowledge_qa_extra_latest}")
    private String indexName;

}
