package com.kbao.kbcchatbot.discard.robotbasicconfig.vo;

import com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean.ChannelCommonCardVO;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean.ChannelCommonPhraseVO;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import lombok.Data;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 初始化配置
 * @author: husw
 * @create: 2023-06-12 11:40
 **/
@Data
public class RobotInitConfigRespVO {

    /**
     * 项目编码
     */
    private String code;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 用户姓名
     */
    private String userName;
    private String agentCode;
    /**
     * 机器人ID
     */
    private Long robotId;
    /**
     * 机器人编码
     */
    private String robotCode;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 渠道Code
     */
    private String channelCode;

    private String channelType;
    private String sessionId;

    /**
     * 知识库通道ID
     */
    private String passId;
    /**
     * 欢迎语文案
     */
    private String welcomeReply;
    /**
     * 输入框引导语
     */
    private String inputPlaceHolder;

    /**
     * 猜你想问开启状态 0-未开启 1-开启
     */
    private String guessQuestionStatus;

    /**
     * 常用卡片开启状态 0-未开启 1-开启
     */
    private String commonCardStatus;

    /**
     * 常用短语开启状态 0-未开启 1-开启
     */
    private String commonPhraseStatus;

    /**
     * 答案评价开启状态 0-未开启 1-开启
     */
    private String answerEvaluateStatus;

    /**
     * 语音开启状态 0-未开启 1-开启
     */
    private String voiceStatus;
    /**
     * 语音合成状态 0-未开启 1-开启
     */
    private String voiceSynthesis;

    private String uploadFileStatus;

    /**
     * 对应表中remark
     * 备注
     */
    private String remark;
    /**
     * 常用卡片
     */
    private List<ChannelCommonCardVO> channelCommonCards;
    /**
     * 猜你想问-随机4条
     */
    private List<ChannelGuessQuestionVO> channelGuessQuestions;
    /**
     * 常用短语
     */
    private List<ChannelCommonPhraseVO> channelCommonPhrases;

    /**
     * 客服平台接入方式 1-网页跳转 2-系统集成
     */
    private Integer custServPlatAccessType;

    /**
     * 客服平台接入配置
     */
    private String custServPlatAddr;

    private boolean firstVisitFlag;

    private String skillMarker;

}
