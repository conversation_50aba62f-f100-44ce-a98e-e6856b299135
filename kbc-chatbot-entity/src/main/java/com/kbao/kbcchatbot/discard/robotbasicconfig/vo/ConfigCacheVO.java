package com.kbao.kbcchatbot.discard.robotbasicconfig.vo;

import com.kbao.kbcchatbot.maas.chatsession.bean.ChatSessionCache;
import lombok.Data;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 配置缓存信息
 * @author: husw
 * @create: 2023-06-12 09:26
 **/
@Data
public class ConfigCacheVO {

    private Long channelId;

    private String channelCode;

    private Long robotId;

    private String robotCode;

    private String sessionId;

    private String userId;

    private String userName;

    private String agentCode;

    private String version;

    private String tenantId;
    /**
     * 猜你想问ID
     */
    private List<Long> guessQuestionId;

    /**
     * 会话配置
     */
    private ChatSessionCache chatSessionCache;
    /**
     * 来源类型 01是通用 02是订单
     */
    private String sourceType;
    /**
     * 项目ID
     */
    private Integer projectId;
    /**
     * 设备ID 0-APP 1-web
     */
    private String deviceType;
}
