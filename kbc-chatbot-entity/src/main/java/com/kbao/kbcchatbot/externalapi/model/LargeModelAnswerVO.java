package com.kbao.kbcchatbot.externalapi.model;

import com.kbao.kbcchatbot.maas.chatsession.enums.ChatKnowledgeTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 大模型返回对象
 * @author: husw
 * @create: 2024-06-17 14:28
 **/
@Data
public class LargeModelAnswerVO {

    /**
     * 回复ID
     */
    private String answerId;
    /**
     * 文本
     */
    private String text;
    /**
     * 链接
     */
    private List<String> urls;
    /**
     * 消息ID
     */
    private String recordId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 产品信息
     */
    private List<ProductBase> productList;

    /**
     * 回复类型 1-知识 2-闲聊  3-转人工
     */
    private String llmAnswerType;

    /**
     * 来源对象
     */
    private List<Reference> references;

    /**
     * 联网搜索来源对象
     */
    private Object net_references;

    /**
     * 产品咨询引用文件
     */
    private Object referenceFiles;

    /**
     * 意图
     */
    private String intention;

    /**
     * 是否开启评价  0-否 1-是
     */
    private String vote;


    @Data
    public static class Reference{
        /**
         * 知识ID
         */
        private String knowledgeId;
        /**
         * 章节id
         */
        private String itemId;
        /**
         * 知识名称
         */
        private String title;
        /**
         * 类型 0-文本 1-图文，2视频，3文件
         * @see ChatKnowledgeTypeEnum
         */
        private String type;

        /**
         * 分数
         */
        private BigDecimal score;

        /**
         * 原文链接
         */
        private String link;
    }

    @Data
    public static class ProductBase{
        /**
         * 产品ID
         */
        private String productId;
        /**
         * 产品明细
         */
        private String productName;
        /**
         * 可售标签
         */
        private List<String> saleLabels;
        /**
         * 不可售标签
         */
        private List<String> unsaleLabels;

    }
}
