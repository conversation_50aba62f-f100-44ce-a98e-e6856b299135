package com.kbao.kbcchatbot.externalapi.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 上传聊天文件返参
 * @date 2025/6/6 10:15
 */
@Data
public class MaasChatFileVO {
    /**
     * 文件唯一标识
     */
    private String id;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件大小（单位：字节）
     */
    private Long size;

    /**
     * 文件扩展名
     */
    private String extension;

    /**
     * 文件MIME类型
     */
    private String mimeType;

    /**
     * 创建人ID
     */
    private String createdBy;

    /**
     * 创建时间（时间戳，单位：毫秒）
     */
    private Long createdAt;
}
