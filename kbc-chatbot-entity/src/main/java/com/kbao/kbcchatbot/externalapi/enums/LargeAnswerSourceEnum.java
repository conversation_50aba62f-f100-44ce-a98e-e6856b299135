package com.kbao.kbcchatbot.externalapi.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum LargeAnswerSourceEnum {

    DEFAULT("0","默认"),
    KNOWLEDGE("1","知识问答"),
    CHITCHAT("2","闲聊"),
    TRANSFER_LABOR("3","转人工"),
    PRODUCT_LIST("4","产品列表"),
    PRODUCT_DATA("5","产品资料"),
    PRODUCT_RECOMMEND("6","产品推荐"),
    PRODUCT_SELECT("7","产品选择"),
    MAN_MACHINE("8","人机对练"),
    ;

    private String key;

    private String value;

    LargeAnswerSourceEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String findNameByKey(String key) {
        for (LargeAnswerSourceEnum e : LargeAnswerSourceEnum.values()) {
            if (e.getKey().equals(key)) {
                return e.getValue();
            }
        }
        return null;
    }

}
