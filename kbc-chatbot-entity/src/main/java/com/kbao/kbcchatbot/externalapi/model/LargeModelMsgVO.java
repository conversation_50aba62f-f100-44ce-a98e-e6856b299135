package com.kbao.kbcchatbot.externalapi.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description: 大模型消息推送
 * @author: husw
 * @create: 2024-08-23 15:29
 **/
@Data
public class LargeModelMsgVO {

    /**
     * 消息类型 0-问题  1-答案
     */
    private String messageType;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户姓名
     */
    private String username;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 机器人编码
     */
    private String robotCode;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 问题ID
     */
    private String questionId;
    /**
     * 问题内容
     */
    private String question;
    /**
     * 答案ID
     */
    private String recordId;

    /**
     * 模型名称
     */
    private String model;
    /**
     * 答案
     */
    private LargeModelAnswerVO answer;

    /**
     * 耗时（秒）
     */
    private String cost;
    /**
     * 问题补全
     */
    private String contextualizeQuestion;

    private List<Map> files;

    private String isCopy;
    private String voteResult;
    private String voteRemark;

    /**
     * 产品ID
     */
    private String productId;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 设备类型 0-APP 1-web
     */
    private String deviceType;

    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 其他参数
     */
    private External external;

    @Data
    public static class External{
        /**
         * 渠道ID
         */
        private Long channelId;
        /**
         * 租户ID
         */
        private String tenantId;
    }

}
