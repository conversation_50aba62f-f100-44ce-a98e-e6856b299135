package com.kbao.kbcchatbot.externalapi.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class LargeModelSyncVO {
    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    private String startDate;
    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 租户id
     */
    @NotBlank(message = "租户id不能为空")
    private String tenantId;
    /**
     * 用户姓名
     */
    private String username;
    /**
     * 评价
     */
    private String voteResult;
    /**
     * 模型
     */
    private String model;
    /**
     * 问题
     */
    private String question;
    /**
     * 意图
     */
    private String intention;
    /**
     * 回复类型
     */
    private String llmAnswerType;

    /**
     * 对话ID
     */
    private String questionId;

    /**
     * 是否复制
     */
    private String isCopy;
    /**
     * 设备类型 0-APP 1-web
     */
    private String deviceType;
}
