spring:
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: dev
    include: dev
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

############## mybatis 配置 ################33
mybatis:
  mapperLocations: classpath*:/com/kbao/kbcchatbot/**/entity/*Mapper.xml


#开启json压缩
server:
  compression:
    enabled: true
    mime-types: application/json
    min-response-size: 20480

pagehelper:
  helper-dialect: mysql
  reasonable: false
  support-methods-arguments: true
  params: count=countSql
  offset-as-page-num: false
