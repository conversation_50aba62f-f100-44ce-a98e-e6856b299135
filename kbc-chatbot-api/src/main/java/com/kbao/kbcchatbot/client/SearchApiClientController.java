package com.kbao.kbcchatbot.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.externalapi.model.*;
import com.kbao.tool.util.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class SearchApiClientController implements SearchApiClientService {

    @Autowired
    private MaasHttpService maasHttpService;

    @Override
    public Result<String> searchCorrection(LargeModelCorrectionReq correctionReq) {
        String result = maasHttpService.searchCorrection(correctionReq);
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @Override
    public Result<List<String>> productRecommend(LargeModelRecommendReq recommendReq) {
        List<String> result = maasHttpService.productRecommend(recommendReq);
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

    public Result<JSONObject> policyExtract(MaasPolicyExtractReq req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONObject result = maasHttpService.postMaasApi(objectMap, "/ex/external/policyExtract");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

    public Result<JSONArray> bdtgSearchProduct(MaasBdtgSearchReq req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONArray result = maasHttpService.postMaasArrayApi(objectMap, "/ex/external/bdtg/searchProduct");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

    public Result<JSONObject> commonSearch(MaasCommonSearchReq req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONObject result = maasHttpService.postMaasApi(objectMap, "/ex/external/commonSearch");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }
}
