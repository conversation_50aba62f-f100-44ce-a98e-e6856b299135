package com.kbao.kbcchatbot.client;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.externalapi.model.LargeModelMqttTokenVO;
import com.kbao.kbcchatbot.externalapi.model.MaasChatFileVO;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.tool.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: maas系统对接接口
 * @date 2025/6/5 16:34
 */
@RestController
public class MaasApiClientController implements MaasApiClientService{

    @Autowired
    private MaasHttpService maasHttpService;
    @Override
    public Result<LargeModelMqttTokenVO> getMqttToken(String code, String secretKey) {
        LargeModelMqttTokenVO mqttToken = maasHttpService.getMqttToken(code, secretKey);
        return Result.succeed(mqttToken, ResultStatusEnum.SUCCESS.getMsg());
    }

    @Override
    public Result<MaasChatFileVO> uploadChatFile(MultipartFile file, String projectCode, String channelCode, String userId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectCode", projectCode);
        param.put("channelCode", channelCode);
        param.put("userId", userId);
        JSONObject jsonObject = maasHttpService.doPostMapApi(file, param, "ex/flow/uploadFile");
        if (EmptyUtils.isNotEmpty(jsonObject)) {
            MaasChatFileVO maasChatFileVO = JSONObject.parseObject(jsonObject.toJSONString(), MaasChatFileVO.class);
            return Result.succeed(maasChatFileVO, ResultStatusEnum.SUCCESS.getMsg());
        }
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
