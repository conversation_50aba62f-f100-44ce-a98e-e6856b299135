package com.kbao.kbcchatbot;

import com.kbao.kbcbsc.log.annotation.EnableLogging;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;

@EnableFeignClients(basePackages = "com.kbao")
@Configuration
@EnableLogging
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.kbao")
@ComponentScan(basePackages = "com.kbao", excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
        value = {com.kbao.kbcbsc.mybatis.MybatisConfiguration.class})})
@EnableTransactionManagement
@EnableScheduling
@EnableEncryptableProperties
public class KbcChatbotApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(KbcChatbotApiApplication.class, args);
    }

}
