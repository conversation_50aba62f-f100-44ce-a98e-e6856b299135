package com.kbao.kbcchatbot.web.interceptor;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcccs.dc.client.user.CcsDcUserApiClientService;
import com.kbao.kbcccs.dc.commons.bean.CUserBean;
import com.kbao.kbcchatbot.common.enums.FromSourceEnum;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.channel.channel.entity.Channel;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.kbcchatbot.maas.robot.robot.service.RobotService;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.ConfigCacheVO;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.bean.ChatUser;
import com.kbao.kbcucs.service.UserInfoAccessService;
import com.kbao.kbcucs.user.model.UserInfoResp;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import static com.kbao.kbcchatbot.constants.CacheConstant.*;

/**
 * <AUTHOR>
 * @Desription 聊天机器人API端用户拦截器
 * @Date 2023-6-6
 */
@Slf4j
@Component
public class ChatUserInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    @Lazy
    private CcsDcUserApiClientService ccsDcUserApiClientService;
    @Autowired
    @Lazy
    private UserInfoAccessService userInfoAccessService;
    @Autowired
    @Lazy
    private ChannelService channelService;
    @Autowired
    @Lazy
    private RobotService robotService;

    @Autowired
    private MaasHttpService maasHttpService;


    /**
     * 在执行action里面的处理逻辑之前执行，它返回的是boolean，
     * true： 接着执行postHandle和afterCompletion
     * false: 中断执行
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        //header参数
        String token = request.getHeader("token");
        Assert.notNull(token, "token不能为空!");

        String tenantId = request.getHeader("tenantId");
        Assert.notNull(tenantId,"tenantId不能为空!");

        String fromSource = request.getHeader("fromSource");
        Assert.notNull(fromSource,"fromSource不能为空!");

        String deviceType = request.getHeader("deviceType");

        ConfigCacheVO configCacheVO = new ConfigCacheVO();
        configCacheVO.setDeviceType(deviceType);
        //认证授权来源
        boolean isAuth = authFromSource(fromSource, token, tenantId, configCacheVO);
        if (!isAuth){
            returnResult(response, ResultStatusEnum.UNAUTHORIZED);
            return false;
        }
        String channelCode = request.getHeader("channelCode");
        if (StringUtils.isEmpty(channelCode)) {
            cacheTrainUser(token, configCacheVO);
            return true;
        }
        //查询渠道信息
        String redisKey = redisUtil.generateKey(MessageFormat.format(CHAT_TOKEN,channelCode, token));
        ConfigCacheVO cacheVO = (ConfigCacheVO)redisUtil.get(redisKey);
        ChatUser chatUser;
        if (EmptyUtils.isEmpty(cacheVO)){
            //获取渠道信息
            if ("train".equals(channelCode)){
            	getConfigInfo(channelCode, configCacheVO);
            }
            getConfigInfo(channelCode, configCacheVO);
            redisUtil.set(redisKey, configCacheVO, CHAT_TOKEN_TIME);
            chatUser = getChatUser(configCacheVO, token);
        }else {
            chatUser = getChatUser(cacheVO, token);
        }
        //设置通用参数
        ChatUserUtil.setUser(chatUser);
        return true;
    }

    private void cacheTrainUser(String token, ConfigCacheVO configCacheVO) {
        //查询渠道信息
        String redisKey = redisUtil.generateKey(MessageFormat.format(CHAT_TOKEN, "train", token));
        ConfigCacheVO cacheVO = (ConfigCacheVO)redisUtil.get(redisKey);
        ChatUser chatUser;
        if (EmptyUtils.isEmpty(cacheVO)){
            redisUtil.set(redisKey, configCacheVO, CHAT_TOKEN_TIME);
            chatUser = getChatUser(configCacheVO, token);
        }else {
            chatUser = getChatUser(cacheVO, token);
        }
        //设置通用参数
        ChatUserUtil.setUser(chatUser);
    }

    /**
    * @Description: 获取渠道配置信息
    * @Param: [channelCode, configCacheVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/8/19 16:15
    */
    private void getConfigInfo(String channelCode, ConfigCacheVO configCacheVO) {
        Channel channel = channelService.getMapper().selectByCode(channelCode);
        if (EmptyUtils.isEmpty(channel)){
            throw new BusinessException("渠道编码错误!");
        }
//        Robot robot = robotService.getMapper().selectByPrimaryKey(channel.getRobotId());
//        if (EmptyUtils.isEmpty(robot)){
//            throw new BusinessException("渠道不存在可服务机器人!");
//        }
        configCacheVO.setChannelId(Long.valueOf(channel.getId()));
        configCacheVO.setChannelCode(channel.getChannelCode());
//        configCacheVO.setVersion(robot.getVersion());
//        configCacheVO.setRobotId(channel.getRobotId());
//        configCacheVO.setRobotCode(robot.getCode());
        configCacheVO.setTenantId(channel.getTenantId());
        configCacheVO.setProjectId(channel.getProjectId());
    }

    /**
    * @Description: 认证授权来源并返回用户信息
    * @Param: [fromSource, token, tenantId, configCacheVO]
    * @return: void
    * @Author: husw
    * @Date: 2024/8/19 16:13
    */
    private boolean authFromSource(String fromSource, String token, String tenantId, ConfigCacheVO configCacheVO) {
        configCacheVO.setTenantId(tenantId);
        if (FromSourceEnum.KB_APP.getCode().equals(fromSource)){
            //快保认证
            UserInfoResp userInfoByToken = userInfoAccessService.getUserInfoByToken(token, tenantId);
            if (EmptyUtils.isEmpty(userInfoByToken)){
                log.error("认证失败,失败来源:"+ fromSource);
                return false;
            }
            //提取用户信息
            configCacheVO.setUserId(userInfoByToken.getId());
            configCacheVO.setUserName(userInfoByToken.getUserName());
            configCacheVO.setAgentCode(userInfoByToken.getAgentCode());
        } else if (FromSourceEnum.TGJ.getCode().equals(fromSource)) {
            //童管家来源
            setHeader(tenantId, token);
            Result<CUserBean> cUserBean = ccsDcUserApiClientService.getCUserBeanByToken();
            if (EmptyUtils.isEmpty(cUserBean) || !ResultStatusEnum.SUCCESS.getStatus().equals(cUserBean.getResp_code())
                    || EmptyUtils.isEmpty(cUserBean.getDatas()) || EmptyUtils.isEmpty(cUserBean.getDatas().getCUserId())) {
                log.error("认证失败,失败来源:"+ fromSource);
                return false;
            }
            configCacheVO.setUserId(cUserBean.getDatas().getCUserId());
            configCacheVO.setUserName(cUserBean.getDatas().getRealName());
        }else {
            log.error("认证失败,失败来源:"+ fromSource);
            return false;
        }
        return true;
    }

    private void setHeader(String tenantId, String token){
        Map<String, String> headerMap = new HashMap();
        headerMap.put("tenantId", tenantId);
        headerMap.put("Authorization", token);
        headerMap.put("appCode", "ccsDcApp");
        FeignRequestHeader.Header.set(headerMap);
    }

    /**
    * @Description: 获取缓存数据
    * @Param: [cacheVO, token]
    * @return: com.kbao.kbcchatbot.utils.chatbotuser.bean.ChatUser
    * @Author: husw
    * @Date: 2024/7/25 17:43
    */
    private ChatUser getChatUser(ConfigCacheVO cacheVO, String token) {
        ChatUser chatUser = new ChatUser();
        chatUser.setUserId(cacheVO.getUserId());
        chatUser.setUserName(cacheVO.getUserName());
        chatUser.setTenantId(cacheVO.getTenantId());
        chatUser.setChannelId(cacheVO.getChannelId());
        chatUser.setChannelCode(cacheVO.getChannelCode());
//        chatUser.setRobotId(cacheVO.getRobotId());
//        chatUser.setRobotVersion(cacheVO.getVersion());
        chatUser.setSessionId(cacheVO.getSessionId());
        chatUser.setToken(token);
        chatUser.setSourceType(cacheVO.getSourceType());
        chatUser.setChatSessionCache(cacheVO.getChatSessionCache());
//        chatUser.setRobotCode(cacheVO.getRobotCode());
        chatUser.setProjectId(cacheVO.getProjectId());
        chatUser.setAgentCode(cacheVO.getAgentCode());
        chatUser.setDeviceType(cacheVO.getDeviceType());
        return chatUser;
    }

    private void returnResult(HttpServletResponse response,ResultStatusEnum resultStatusEnum){
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try (PrintWriter writer = response.getWriter()) {
            writer.print(JSONObject.toJSONString(Result.failedWith((Object)null, resultStatusEnum.getStatus(), resultStatusEnum.getMsg())));
        } catch (IOException e) {
            log.error("拦截器输出流异常" + e);
        }
    }

    /**
     * 在执行action里面的逻辑后返回视图之前执行
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    /**
     * 在action返回视图后执行
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        ChatUserUtil.remove();
    }

}
