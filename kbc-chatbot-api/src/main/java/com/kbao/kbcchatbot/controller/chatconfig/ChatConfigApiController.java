package com.kbao.kbcchatbot.controller.chatconfig;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.entity.ChannelGuessQuestion;
import com.kbao.kbcchatbot.maas.chatconfig.service.ChatConfigApiService;
import com.kbao.kbcchatbot.externalapi.model.LargeModelMqttTokenVO;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.RobotInitConfigRespVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 机器人基础配置
 * @author: husw
 * @create: 2023-06-08 11:20
 **/
@RestController
@RequestMapping("/api/noauth/chatconfig")
public class ChatConfigApiController {


    @Autowired
    private ChatConfigApiService chatConfigApiService;

    @PostMapping("/getInitConfig")
    @LogAnnotation(module = "机器人配置", action = "查询", desc = "查询初始化配置")
    public Result<RobotInitConfigRespVO> getInitConfig() {
        RobotInitConfigRespVO initConfig = chatConfigApiService.getInitConfig();
        return Result.succeed(initConfig, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/exchange")
    @LogAnnotation(module = "机器人配置", action = "查询", desc = "换一换")
    public Result<List<ChannelGuessQuestionVO>> exchange(@RequestBody ChannelGuessQuestion channelGuessQuestion) {
        List<ChannelGuessQuestionVO> exchange = chatConfigApiService.exchange(channelGuessQuestion.getSourceType());
        return Result.succeed(exchange,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getMToken")
    @LogAnnotation(module = "机器人配置", action = "查询", desc = "获取mqttToken")
    public Result<LargeModelMqttTokenVO> getMToken() {
        LargeModelMqttTokenVO mToken = chatConfigApiService.getMToken();
        return Result.succeed(mToken, ResultStatusEnum.SUCCESS.getMsg());
    }
}
