package com.kbao.kbcchatbot.controller.file;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.upload.UploadService;
import com.kbao.kbcchatbot.uploadfilelog.bean.CustomFileUploadRes;
import com.kbao.kbcchatbot.uploadfilelog.entity.UploadFileLog;
import com.kbao.kbcchatbot.uploadfilelog.service.UploadFileLogService;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static com.kbao.kbcchatbot.constants.Constant.TENANT_ID_COMMON;

/**
 * @program: kbc-chatbot
 * @description: 聊天文件管理
 * @author: husw
 * @create: 2023-11-08 11:28
 **/
@RestController
@RequestMapping("/api/noauth/chatfile")
public class ChatFileController {


    @Autowired
    private UploadFileLogService uploadFileLogService;


    @Autowired
    private UploadService uploadService;

    @PostMapping("/getEmoji")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "查询表情包")
    public Result<List<UploadFileLog>> getEmoji() {
        List<UploadFileLog> fileByBizModule = uploadFileLogService.getFileByBizModule("chatbot-emoji-pack", TENANT_ID_COMMON);
        return Result.succeed(fileByBizModule, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping(value = "/uploadImages")
    public Result uploadImages(@RequestParam("images") MultipartFile[] images, @RequestParam("bizModule") String bizModule) {
        List<FileUploadResponse> fileUploadResponses = uploadService.uploadImages(images, bizModule);
        return Result.succeed(fileUploadResponses,"上传成功");
    }

    @PostMapping(value = "/uploadImage")
    public Result uploadImage(@RequestParam("image") MultipartFile image, @RequestParam("bizModule") String bizModule) {
        CustomFileUploadRes fileUploadResponses = uploadService.uploadImage(image, bizModule);
        return Result.succeed(fileUploadResponses,"上传成功");
    }
}
