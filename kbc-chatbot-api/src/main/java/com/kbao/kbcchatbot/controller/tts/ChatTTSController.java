package com.kbao.kbcchatbot.controller.tts;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.voice.bean.VoiceVO;
import com.kbao.kbcchatbot.voice.service.ChatVoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 语音
 * @date 2025/4/3 10:57
 */
@RestController
@RequestMapping("/api/noauth/tts")
public class ChatTTSController {

    @Autowired
    private ChatVoiceService chatVoiceService;

    @GetMapping("/speakToText")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "查询语音文本")
    public Result<Object> speakToText(@RequestParam String fileId) {
        String result = chatVoiceService.speakToText(fileId);
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getVoice")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "文本转语音")
    public Result<String> getVoice(@RequestBody VoiceVO vo) {
        chatVoiceService.getVoice(vo.getId(),vo.getText());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
