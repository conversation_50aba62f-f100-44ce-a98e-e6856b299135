package com.kbao.kbcchatbot.controller.chatsession;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.model.RequestPage;
import com.kbao.kbcchatbot.externalapi.model.MaasBdtgSearchReq;
import com.kbao.kbcchatbot.externalapi.model.MaasFlowDetailReq;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelPageReqVO;
import com.kbao.kbcchatbot.maas.channel.channelSession.bean.ChannelSessionReqVo;
import com.kbao.kbcchatbot.maas.channel.channelSession.entity.ChannelSession;
import com.kbao.kbcchatbot.maas.channel.channelSession.service.ChannelSessionService;
import com.kbao.kbcchatbot.maas.chatsession.bean.ChatSessionRecordPageReqVO;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.*;
import com.kbao.kbcchatbot.maas.chatsession.service.ChatSessionApiService;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.maas.onlineUser.service.OnlineUserStatService;
import com.kbao.kbcchatbot.maas.train.score.entity.TrainScore;
import com.kbao.kbcchatbot.utils.StringUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import com.kbao.tool.util.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desciption 聊天会话API控制器
 * <AUTHOR>
 * @Date 2023-6-2
 */
@RestController
@RequestMapping("/api/noauth/chatsession")
public class ChatSessionApiController extends BaseController {

    @Autowired
    private ChatSessionApiService chatSessionApiService;

    @Autowired
    private OnlineUserStatService onlineUserStatService;

    @Autowired
    private ChannelSessionService channelSessionService;

    @Autowired
    private MaasHttpService maasHttpService;

/*    @PostMapping("/queryAnswer")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "查询机器人回复")
    public Result<ChatQueryAnswerRespVO> queryAnswer(@RequestBody ChatQueryAnswerReqVO req) {
        ChatQueryAnswerRespVO respVO = chatSessionApiService.queryAnswer(req,false);
        return Result.succeed(respVO, ResultStatusEnum.SUCCESS.getMsg());
    }*/

    @PostMapping("/questionAssociate")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "问题联想")
    public Result<List<ChatQuestionAssociateRespVO>> questionAssociate(@RequestBody ChatQuestionAssociateReqVO req) {
        req.setQuery(StringUtil.escape(req.getQuery()));
//        List<ChatQuestionAssociateRespVO> respVOList = chatSessionApiService.questionAssociate(req);
        String userId = ChatUserUtil.getUserId();
        onlineUserStatService.record(userId);
        return Result.succeed(null, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/commonCardAnswer")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "查询常用卡片答案")
    public Result<ChatCommonCardAnswerRespVO> commonCardAnswer(@RequestBody ChatCommonCardAnswerReqVO req) {
        ChatCommonCardAnswerRespVO respVO = chatSessionApiService.commonCardAnswer(req);
        return Result.succeed(respVO, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/commonPhraseAnswer")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "查询常用卡片答案")
    public Result<ChatCommonPhraseAnswerRespVO> commonPhraseAnswer(@RequestBody ChatCommonPhraseAnswerReqVO req) {
        ChatCommonPhraseAnswerRespVO respVO = chatSessionApiService.commonPhraseAnswer(req);
        return Result.succeed(respVO, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/guessQuestionAnswer")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "查询常用卡片答案")
    public Result<ChatGuessQuestionAnswerRespVO> commonCardAnswer(@RequestBody ChatGuessQuestionAnswerReqVO req) {
        ChatGuessQuestionAnswerRespVO respVO = chatSessionApiService.guessQuestionAnswer(req);
        return Result.succeed(respVO, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/questionClearAnswer")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "查询问题澄清答案")
    public Result<ChatQueryAnswerRespVO> questionClearAnswer(@RequestBody ChatQueryAnswerReqVO req) {
        ChatQueryAnswerRespVO respVO = chatSessionApiService.questionClearAnswer(req);
        return Result.succeed(respVO, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/vote")
    @LogAnnotation(module = "聊天会话管理", action = "操作", desc = "知识库回复反馈")
    public Result vote(@RequestBody @Validated ChatKnowledgeAnswerVoteReqVO req) {
        chatSessionApiService.vote(req);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/pageRecord")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "查询聊天记录")
    public Result<PageInfo<ChatSessionRecord>> pageRecord(@RequestBody RequestObjectPage<ChatSessionRecordPageReqVO> req) {
        PageInfo<ChatSessionRecord> pageInfo = chatSessionApiService.pageRecord(req);
        return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/copy")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "复制聊天内容")
    public Result pageRecord(@RequestBody ChatSessionRecord record) {
        chatSessionApiService.copyRecord(record.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    /*@PostMapping("/createSession")
    @LogAnnotation(module = "聊天会话管理", action = "新增", desc = "创建聊天会话")
    public Result<ChatSessionCreateRespVO> createSession(@RequestBody ChatSessionCreateReqVO req) {
        ChatSessionCreateRespVO respVO = chatSessionApiService.createSession(req);
        return Result.succeed(respVO, ResultStatusEnum.SUCCESS.getMsg());
    }*/

    /**
     * 根据开始时间和结束时间查询聊天记录
     */
    @PostMapping("/queryChatSessionRecord")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "起止时间查询聊天记录")
    public Result<List<ChatSessionRecord>> queryChatSessionRecord(@RequestBody ChatSessionRecordPageReqVO req) {
        return Result.succeed(chatSessionApiService.listByStartTimeAndEndTime(req), ResultStatusEnum.SUCCESS.getMsg());
    }


    @PostMapping("/listByStartTime")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "结束时间查询聊天记录")
    public Result<List<ChatSessionRecord>> listByStartTime(@RequestBody ChatSessionRecordPageReqVO req) {
        return Result.succeed(chatSessionApiService.listByStartTime(req), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/clean")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "开启新对话")
    public Result<Map> cleanSession() {
        Map<String, Object> resultMap = new HashMap<>();
        String sessionId = chatSessionApiService.cleanSession();
        resultMap.put("sessionId", sessionId);
        return Result.succeed(resultMap, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "会话管理", action = "查询", desc = "查询会话列表")
    public Result<PageInfo<ChannelSession>> page(@RequestBody RequestObjectPage<ChannelPageReqVO> requestPage){
        PageInfo<ChannelSession> list = channelSessionService.getApiSessionList(requestPage);
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/get")
    @LogAnnotation(module = "会话管理", action = "查询", desc = "查询会话")
    public Result<ChannelSession> get(@RequestBody ChannelSessionReqVo req){
        ChannelSession session = channelSessionService.get(req);
        return Result.succeed(session, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/rename")
    @LogAnnotation(module = "会话管理", action = "查询", desc = "重命名会话")
    public Result<PageInfo<ChannelSession>> rename(@RequestBody ChannelSession reqVo){
        channelSessionService.renameSession(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/del")
    @LogAnnotation(module = "会话管理", action = "查询", desc = "删除会话")
    public Result<JSONObject> delChannelSession(@RequestBody ChannelSessionReqVo req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONObject result = maasHttpService.postMaasApi(objectMap, "/ex/channel/session/del");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/qaRecords")
    @LogAnnotation(module = "会话管理", action = "查询", desc = "查询会话记录")
    public Result<PageInfo<JSONObject>> qaRecordPage(@RequestBody PageRequest<ChannelSessionReqVo> pageRequest){
        PageInfo<JSONObject> pageInfo = channelSessionService.qaRecordPage(pageRequest);
        return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/reload")
    @LogAnnotation(module = "会话管理", action = "查询", desc = "重载会话")
    public Result<JSONObject> reload(@RequestBody ChannelSessionReqVo req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONObject result = maasHttpService.postMaasApi(objectMap, "/ex/channel/session/reload");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/pageFlowRecord")
    @LogAnnotation(module = "执行记录", action = "查询", desc = "查询执行记录")
    public Result<JSONObject> pageFlowRecord(@RequestBody RequestObjectPage<MaasFlowDetailReq> req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONObject result = maasHttpService.postMaasApi(objectMap, "ex/flow/getFlowRecord");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }
}
