package com.kbao.kbcchatbot.controller.order;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.discard.chatorder.service.ChatOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: 统一订单
 * @author: husw
 * @create: 2023-11-03 09:56
 **/
@RestController
@RequestMapping("/api/noauth/chatorder")
public class ChatOrderController {

    @Autowired
    private ChatOrderService chatOrderService;

    @PostMapping("/page")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "查询用户订单信息")
    public Result<Object> page(@RequestBody RequestObjectPage<Object> req) {
//        PageInfo<UocPolicyInfoResp> page = chatOrderService.page(req);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

}
