package com.kbao.kbcchatbot.controller.train;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitReqVO;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitResVo;
import com.kbao.kbcchatbot.maas.train.scene.service.TrainSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/api/noauth/trainScene")
public class TrainSceneApiController extends BaseController {
    @Autowired
    private TrainSceneService trainSceneService;

    @PostMapping("/init")
    @LogAnnotation(module = "人机对练", action = "查询", desc = "")
    public Result<TrainSceneInitResVo> trainSceneInit(@RequestBody @Validated TrainSceneInitReqVO req) {
        TrainSceneInitResVo respVO = trainSceneService.trainSceneInit(req);
        return Result.succeed(respVO, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/list")
    @LogAnnotation(module = "人机对练", action = "查询", desc = "")
    public Result<PageInfo<JSONObject>> trainSceneInit(@RequestBody PageRequest<TrainSceneInitReqVO> pageRequest) {
        PageInfo<JSONObject> respVO = trainSceneService.trainRecordPage(pageRequest, true);
        return Result.succeed(respVO, ResultStatusEnum.SUCCESS.getMsg());
    }
}

