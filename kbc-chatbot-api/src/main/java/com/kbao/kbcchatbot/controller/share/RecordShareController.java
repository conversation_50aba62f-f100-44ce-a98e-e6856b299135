package com.kbao.kbcchatbot.controller.share;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.recordShareLog.bean.RecordShareVo;
import com.kbao.kbcchatbot.maas.recordShareLog.entity.RecordShareLog;
import com.kbao.kbcchatbot.maas.recordShareLog.service.RecordShareLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/share")
public class RecordShareController {
    @Autowired
    private RecordShareLogService recordShareLogService;

    @PostMapping("/create")
    @LogAnnotation(module = "分享", action = "查询", desc = "创建分享记录")
    public Result<RecordShareVo> addRecordShareLog(@RequestBody @Validated RecordShareVo req) {
        String shareId = recordShareLogService.addRecordShareLog(req.getRecordId());
        req.setShareId(shareId);
        return Result.succeed(req, ResultStatusEnum.SUCCESS.getMsg());
    }
}
