package com.kbao.kbcchatbot.controller.rasa3x;

import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.ActionRequest;
import com.kbao.kbcchatbot.rasa3x.sdk.dto.ActionResponse;
import com.kbao.kbcchatbot.rasa3x.service.RasaActionsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: rasa actions【供Rasa调用】
 * @author: husw
 * @create: 2023-08-04 17:39
 **/
@Slf4j
@RestController
@RequestMapping("/api/nontenant/chatauth/rasa3x")
public class Rasa3xActionsController {

    @Autowired
    private RasaActionsService rasaActionsService;

    @PostMapping("/webhook")
    @LogAnnotation(module = "Rasa3X对话管理", action = "操作", desc = "custom actions")
    public ActionResponse executeAction(@RequestBody ActionRequest reqVO) {
        long startTime = System.currentTimeMillis();
        ActionResponse actionResponse = rasaActionsService.executeAction(reqVO);
        log.info("请求成功-executeAction,耗时：{}",System.currentTimeMillis()-startTime);
        return actionResponse;
    }
}
