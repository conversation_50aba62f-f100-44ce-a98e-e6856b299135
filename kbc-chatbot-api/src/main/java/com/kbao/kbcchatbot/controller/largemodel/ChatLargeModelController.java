package com.kbao.kbcchatbot.controller.largemodel;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerRespVO;
import com.kbao.kbcchatbot.externalapi.model.LargeModelAnswerVO;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: 大模型平台
 * @author: husw
 * @create: 2024-07-01 15:37
 **/
@RestController
@RequestMapping("/api/noauth/model")
public class ChatLargeModelController extends BaseController {

    @Autowired
    private MaasHttpService maasHttpService;


    @PostMapping("/callback/answer")
    @LogAnnotation(module = "大模型", action = "新增", desc = "推送答案")
    public Result<ChatQueryAnswerRespVO> pushAnswer(@RequestBody LargeModelAnswerVO req) {
        String largeModelToken = request.getHeader("model_token");
//        largeModelService.callbackPushAnswer(req);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
