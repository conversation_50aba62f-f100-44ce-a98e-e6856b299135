package com.kbao.kbcchatbot.controller.noauth;

import com.kbao.kbcchatbot.discard.chatauth.service.ChatAuthApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: 认证
 * @author: husw
 * @create: 2023-06-13 11:58
 **/
@RestController
@RequestMapping("/api/noauth/chatauth")
public class ChatAuthApiController {

    @Autowired
    private ChatAuthApiService chatAuthApiService;





//    @PostMapping("/getKey")
//    @LogAnnotation(module = "渠道认证", action = "创建", desc = "获取授权码")
//    public Result<CryptoRSAKeyPair> getKey(@RequestBody @Validated ChatAuthKeyReqVO req) {
//        CryptoRSAKeyPair key = chatAuthApiService.getKey(req);
//        return Result.succeed(key, ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PostMapping("/createInitToken")
//    @LogAnnotation(module = "渠道认证", action = "创建", desc = "创建初始化授权token")
//    public Result<RobotAuthCodeVO> createInitToken(@RequestBody @Validated ChatAuthTokenReqVO req) {
//        RobotAuthCodeVO authCode = chatAuthApiService.createInitToken(req);
//        return Result.succeed(authCode, ResultStatusEnum.SUCCESS.getMsg());
//    }
}
