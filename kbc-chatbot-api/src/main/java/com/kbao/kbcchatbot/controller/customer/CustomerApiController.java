package com.kbao.kbcchatbot.controller.customer;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.chatsession.bean.qa.ChatQueryAnswerReqVO;
import com.kbao.kbcchatbot.maas.chatsession.service.ChatSessionApiService;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.kbcchatbot.customer.*;
import com.kbao.kbcchatbot.externalapi.work.ExternalApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> qiuzb
 * @Description: 客服系统
 * @create 2023/11/15 11:44
 */
@RestController
@RequestMapping("/api/noauth/customer")
public class CustomerApiController extends BaseController {

    @Autowired
    private ExternalApiService externalApiService;

    @Autowired
    private ChatSessionApiService chatSessionApiService;

    @Autowired
    private ChatSessionRecordService chatSessionRecordService;

    @PostMapping("/transferToHumanChat")
    @LogAnnotation(module = "客服系统", action = "人工", desc = "人工聊天")
    public Result<TransferVO> transferToHumanChat(@RequestBody TransferToHumanChatReq transferToHumanChatReq) {
        TransferVO transferVO = externalApiService.transferToHumanChat(transferToHumanChatReq);
        return Result.succeed(transferVO, ResultStatusEnum.SUCCESS.getMsg());
    }


    @PostMapping("/saveChatSessionRecord")
    @LogAnnotation(module = "客服系统", action = "新增", desc = "保存客户聊天记录")
    public Result saveChatSessionRecord(@RequestBody ChatQueryAnswerReqVO req) {
        chatSessionApiService.saveChatSessionRecord(req);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }


    @PostMapping("/chatMessagePush")
    @LogAnnotation(module = "客服系统", action = "新增", desc = "保存推送客服聊天记录")
    public Result chatMessagePush(@RequestBody ChatMessagePush chatMessagePush) {
        externalApiService.chatMessagePush(chatMessagePush);
        return Result.succeed(chatMessagePush,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/createLeavingMessage")
    @LogAnnotation(module = "客服系统", action = "新增", desc = "保存留言")
    public Result createLeavingMessage(@RequestBody LeaveMessageReq leaveMessageReq) {
        externalApiService.createLeavingMessage(leaveMessageReq);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/submitSurvey")
    @LogAnnotation(module = "客服系统", action = "新增", desc = "提交评价")
    public Result submitSurvey(@RequestBody SurveyReq surveyReq) {
        return Result.succeed(externalApiService.submitSurvey(surveyReq),ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 根据sid和mid查询会话记录
     *
     */
    @PostMapping("/findBySidAndMid")
    @LogAnnotation(module = "客服系统", action = "查询", desc = "根据sid和mid查询会话记录")
    public Result<List<ChatSessionRecord>> findBySidAndMid(@RequestBody ChatSessionRecord chatSessionRecord) {
        return Result.succeed(chatSessionRecordService.findBySidAndMid(chatSessionRecord), ResultStatusEnum.SUCCESS.getMsg());
    }


    @PostMapping("/findRecordByUserId")
    @LogAnnotation(module = "客服系统", action = "查询", desc = "根据userId查询三天内的会话记录")
    public Result<List<ChatSessionRecord>> findRecordByUserId(@RequestBody ChatSessionRecord chatSessionRecord) {
        return Result.succeed(chatSessionRecordService.findRecordByUserId(chatSessionRecord.getUserId()), ResultStatusEnum.SUCCESS.getMsg());
    }


}
