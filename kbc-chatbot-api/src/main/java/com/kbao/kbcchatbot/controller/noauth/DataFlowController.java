package com.kbao.kbcchatbot.controller.noauth;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.dataflow.client.DataAccessApiClientService;
import com.kbao.dataflow.module.data.vo.*;
import com.kbao.kbcbsc.annotations.AppApiCheckAnnotation;
import com.kbao.kbcbsc.util.BscApiContext;
import com.kbao.kbcchatbot.utils.FeignUtil;
import com.kbao.kbcchatbot.utils.chatbotuser.ChatUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version V1.0
 * @Description
 * @date 2020-9-27 15:09
 * @since V1.0
 */
@RestController
@RequestMapping("/api/noauth/dataflow")
@Api(tags = "数据回流管理")
public class DataFlowController extends BaseController {

    @Autowired
    private DataAccessApiClientService dataAccessApiClientService;

    @ApiOperation(value = "创建用户分享行为")
    @PostMapping("/share/behavior")
    public Result shareBehavior(@RequestBody @Valid ShareDataVo vo) {
        FeignUtil.setBaseFeignHeader(vo.getTenantId());
        return dataAccessApiClientService.shareBehavior(vo);
    }

    @ApiOperation(value = "修改用户分享微信目标类型 1好友或微信群，2朋友圈")
    @PostMapping("/share/target")
    public Result shareTarget(@RequestBody @Valid ShareDataVo vo) {
        FeignUtil.setBaseFeignHeader(vo.getTenantId());
        return dataAccessApiClientService.shareTarget(vo);
    }

    @ApiOperation(value = "保存访客访问行为")
    @PostMapping("/access/behavior")
    public Result<AccessResponseVo> accessBehavior(@RequestBody @Valid AccessDataVo vo) {
        FeignUtil.setBaseFeignHeader(vo.getTenantId());
        return dataAccessApiClientService.accessBehavior(vo);
    }

    @ApiOperation(value = "记录访客浏览页面时长")
    @PostMapping("/record/brow/time")
    public Result<BrowTimeResponseVo> recordBrowTime(@RequestBody BrowTimeVo vo) {
        FeignUtil.setBaseFeignHeader(vo.getTenantId());
        return dataAccessApiClientService.recordBrowTime(vo);
    }

}
