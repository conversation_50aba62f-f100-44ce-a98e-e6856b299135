package com.kbao.kbcchatbot.controller.flow;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 工作流接口
 * @date 2025/6/5 11:38
 */
@RestController
@RequestMapping("/api/noauth/flow")
public class FlowApiController {

    @Autowired
    private MaasHttpService maasHttpService;
    @PostMapping(value = "/uploadFile")
    public Result<JSONObject> uploadImage(@RequestParam("file") MultipartFile file, @RequestParam("channelCode") String channelCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("file", file);
        param.put("channelCode", channelCode);
        JSONObject jsonObject = maasHttpService.postMaasApi(param, "ex/flow/uploadFile");
        return Result.succeed(jsonObject,"上传成功");
    }

}
