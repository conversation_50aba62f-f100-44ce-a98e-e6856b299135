package com.kbao.kbcchatbot.controller.noauth;

import com.kbao.common.exception.BusinessException;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbsc.client.TenantApiClientService;
import com.kbao.kbcbsc.client.WechatServiceClientService;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo;
import com.kbao.kbcbsc.tenant.bean.TenantVo;
import com.kbao.kbcbsc.tenant.entity.Tenant;
import com.kbao.kbcbsc.tenantinfo.entity.TenantInfo;
import com.kbao.kbcbsc.wechat.request.AppParam;
import com.kbao.kbcbsc.wechat.request.WechatParam;
import com.kbao.kbcbsc.wechat.response.SignatureVO;
import com.kbao.kbcccs.dc.commons.vo.UrlReq;
import com.kbao.kbcchatbot.kbc.bsc.config.KbcBscConfig;
import com.kbao.kbcchatbot.maas.base.WxAuthService;
import com.kbao.kbcchatbot.maas.recordShareLog.bean.RecordShareVo;
import com.kbao.kbcchatbot.maas.recordShareLog.entity.RecordShareLog;
import com.kbao.kbcchatbot.maas.recordShareLog.service.RecordShareLogService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/noauth/base")
public class NoAuthController  extends BaseController {
    @Autowired
    private RecordShareLogService recordShareLogService;
    @Autowired
    private WxAuthService wxAuthService;
    @Autowired
    private KbcBscConfig kbcBscConfig;
    @Autowired
    private TenantApiClientService tenantApiClientService;
    @Autowired
    private WechatServiceClientService wechatServiceClientService;

    @PostMapping("/share/info")
    @LogAnnotation(module = "分享", action = "查询", desc = "查询分享内容")
    public Result<RecordShareLog> getShareInfo(@RequestBody @Validated RecordShareVo req) {
        RecordShareLog shareInfo = recordShareLogService.getShareInfo(req.getShareId());
        return Result.succeed(shareInfo, ResultStatusEnum.SUCCESS.getMsg());
    }

    @LogAnnotation(module = "无认证接口管理", action = "其他", desc = "获取微信签字")
    @ApiOperation(value = "获取微信签字", notes = "获取微信签字")
    @PostMapping("/wechat/signature")
    public Result<SignatureVO> signature(@RequestBody @Valid UrlReq url) {
        SignatureVO signatureVO = wxAuthService.getSignature(url.getUrl());
        return Result.succeedWith(signatureVO);
    }

    @ApiOperation(value = "微信后台授权", notes = "微信后台授权")
    @GetMapping("/wechat/auth")
    public void wechatAuth(@RequestParam("redirectUrl") String redirectUrl,@RequestParam("tenantId") String tenantId){
        TenantVo vo = new TenantVo();
        vo.setTenantId(tenantId);
        String userAgent = request.getHeader("User-Agent");
        try {
            if (!StringUtils.contains(userAgent, "MicroMessenger")) {
                response.sendRedirect(redirectUrl);
                return;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        Result<TenantConfigInfoVo> tenantConfigInfo = tenantApiClientService.getTenantConfigInfo(vo);
        if (ResultStatusEnum.SUCCESS.getStatus().equals(tenantConfigInfo.getResp_code())) {
            String tenantName = "";
            String targetName = "";
            TenantConfigInfoVo tenantConfig = tenantConfigInfo.getDatas();
            if (null != tenantConfig) {
                Tenant tenant = tenantConfig.getTenant();
                TenantInfo tenantInfo = tenantConfig.getTenantInfo();
                if (null != tenant && StringUtils.isNotEmpty(tenant.getTenantName())) {
                    tenantName = tenant.getTenantName();
                }
                if (null != tenantInfo && StringUtils.isNotEmpty(tenantInfo.getTargetName())) {
                    targetName = tenantInfo.getTargetName();
                }
            }
            String appCode = kbcBscConfig.getAppCode();
            Map<String,String> headerMap = new HashMap<>();
            headerMap.put("tenantId", tenantId);
            headerMap.put("tenantName", tenantName);
            headerMap.put("appCode", appCode);
            headerMap.put("appName", targetName);
            FeignRequestHeader.Header.set(headerMap);
            AppParam param = new AppParam();
            param.setTenantId(tenantId);
            param.setAppCode(appCode);
            param.setServiceType("2");
            WechatParam wechatParam = new WechatParam();
            wechatParam.setRedirectUrl(redirectUrl);
            param.setWechatParam(wechatParam);
            Result<Map> rs = wechatServiceClientService.authorize(param);
            if(ResultStatusEnum.SUCCESS.getStatus().equals(rs.getResp_code())) {
                try {
                    response.sendRedirect(String.valueOf(rs.getDatas().get("url")));
                }catch (Exception e){
                    e.printStackTrace();
                }
            }else{
                throw new BusinessException("获取微信授权失败");
            }
        } else {
            throw new BusinessException("获取微信授权失败");
        }
    }
}
