package com.kbao.kbcchatbot.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.externalapi.model.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "kbc-chatbot-api")
public interface SearchApiClientService {

    @PostMapping("/api/noauth/client/search/correction")
    Result<String> searchCorrection(@RequestBody LargeModelCorrectionReq correctionReq);

    @PostMapping("/api/noauth/client/search/productRecommend")
    Result<List<String>> productRecommend(@RequestBody LargeModelRecommendReq recommendReq);

    @PostMapping("/api/noauth/client/search/policyExtract")
    Result<JSONObject> policyExtract(@RequestBody MaasPolicyExtractReq req);

    @PostMapping("/api/noauth/client/bdtg/search/product")
    Result<JSONArray> bdtgSearchProduct(@RequestBody MaasBdtgSearchReq req);

    @PostMapping("/api/noauth/client/common/search")
    Result<JSONObject> commonSearch(@RequestBody MaasCommonSearchReq req);
}
