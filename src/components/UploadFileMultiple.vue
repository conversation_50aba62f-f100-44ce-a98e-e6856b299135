<template>
  <div class="flex flex-col">
    <el-upload
      action="#"
      :accept="'.' + props.accept.join(',.')"
      :multiple="true"
      :http-request="uploadData"
      :show-file-list="false"
    >
      <el-button type="primary" size="small">
        {{ files.length > 0 ? '继续上传' : '上传' }}
      </el-button>
    </el-upload>
    <div class="flex" v-for="(item, idx) in files" :key="idx">
      <div class="mr-3">{{ item.fileName }}</div>
      <el-button
        type="primary"
        link
        @click="exportByUrl(item.filePath, item.fileName)"
      >
        下载
      </el-button>
      <el-button type="primary" link @click="onDel(idx)"> 删除 </el-button>
    </div>
    <div class="text-xs text-gray-400 mt-1" v-if="showTips">
      文件格式支持{{
        props.accept.join('、')
      }}为后缀名的文件，文件大小不能超过{{ byteConvert(props.limit) }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { UploadRequestOptions } from 'element-plus'
import { httpServe } from '@/utils/httpServe'
import { exportByUrl, byteConvert } from '@/utils'
import { reactive } from 'vue'
import type { UploadFileProp, UploadFile } from './types'

const props = withDefaults(
  defineProps<{
    fileData?: UploadFileProp[]
    limit?: number
    accept?: string[]
    showTips?: boolean
    // 文件上传服务接口使用字段 标记文件使用目录
    fileType?: string
  }>(),
  {
    fileData: () => [],
    // 10M
    limit: 1024 * 1024 * 20,
    accept: () => ['xls', 'xlsx', 'jpg', 'png', 'pdf', 'doc', 'docx'],
    showTips: true,
    fileType: 'agreement'
  }
)
const emit = defineEmits<{
  (e: 'upload-end', data: UploadFile): void
  (e: 'del-file', val: string): void
}>()

const files = reactive<UploadFileProp[]>([...props.fileData])

const onDel = (index: number) => {
  emit('del-file', files[index].filePath)
  files.splice(index, 1)
}

// 上传操作
const uploadData = async (d: UploadRequestOptions): Promise<void> => {
  const accept = props.accept.join('|')
  const regex = new RegExp(`\\.(${accept})$`)
  if (!regex.test(d.file.name.toLowerCase())) {
    ElMessage.error(`上传格式不正确，请上传${props.accept.join('、')}格式`)
    return
  }
  if (d.file.size > props.limit) {
    ElMessage.error(`文件大小不能超过${byteConvert(props.limit)}!`)
    return
  }

  const res = await httpServe(
    '/web/gpupload/file',
    {
      fileType: props.fileType,
      file: d.file
    },
    { isUpload: true }
  )

  if (res.status === 0) {
    files.push({
      fileName: res.data.fileName,
      filePath: res.data.absolutePath
    })
    emit('upload-end', {
      fileId: res.data.fileId,
      fileName: res.data.fileName,
      filePath: res.data.absolutePath,
      file: d.file
    })
  }
}
</script>
