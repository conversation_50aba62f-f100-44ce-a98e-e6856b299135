<template>
  <el-cascader
    v-model="area"
    class="inpu2-dt"
    :options="(areaOptions() as any)"
    :props="{
      checkStrictly: props.checkStrictly,
      emitPath: props.emitPath
    }"
    @change="emit('update:area', area)"
    clearable
  />
</template>

<script lang="ts" setup>
import { areaOptions } from '@/utils/areaList'
import { ref, watch } from 'vue'

const props = withDefaults(
  defineProps<{
    // 省市区是否可以单独选择:默认需要选择到最后一级
    checkStrictly?: boolean
    // 返回数据是否为省市区编码数组 还是最后一级编码: 默认返回数组
    emitPath?: boolean
    area: string | string[]
  }>(),
  {
    checkStrictly: false,
    emitPath: true
  }
)

const area = ref(props.area)

watch(
  () => props.area,
  val => (area.value = val)
)

const emit = defineEmits<{
  (e: 'update:area', data: string | string[]): void
}>()
</script>
