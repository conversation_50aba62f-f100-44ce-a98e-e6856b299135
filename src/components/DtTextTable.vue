<template>
  <div>
    <div
      class="text-box float-left box-border pr-3 w-1/4 min-w-[250px] flex my-[6px] text-sm"
      v-for="(item, index) in list"
      :key="index"
    >
      <el-tooltip
        effect="dark"
        :content="item.val || ''"
        placement="top"
        v-if="item.showTip"
      >
        <div class="w-[250px] ellipsis-dt">
          {{ `${item.lable}:&nbsp;&nbsp;${item.val || emptySign}` }}
        </div>
      </el-tooltip>
      <div v-else class="break-all">
        {{ `${item.lable}:&nbsp;&nbsp;${item.val || emptySign}` }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { textWidth } from '@/utils'
import { onMounted, reactive, watch } from 'vue'
import type { TextTable } from './types'

interface ListData extends TextTable {
  showTip: boolean
}

const props = withDefaults(
  defineProps<{
    textList: TextTable[]
    emptySign?: string
  }>(),
  {
    emptySign: ''
  }
)

const list = reactive<ListData[]>(
  props.textList.map(e => {
    return {
      ...e,
      showTip: false
    }
  })
)

const setTexTips = () => {
  if (list.length === 0) return
  const el = document.querySelector('.text-box') as HTMLElement
  if (!el) return
  const minW = el.offsetWidth - 12
  list.forEach(e => {
    const textW = textWidth(`${e.lable}:&nbsp;&nbsp;${e.val}`, '14px')
    e.showTip = textW > minW
  })
}

watch(
  () => props.textList,
  newVal => {
    list.splice(0)
    if (newVal.length > 0) {
      newVal.forEach(e => {
        list.push({ ...e, showTip: false })
      })
      setTexTips()
    }
  }
)

onMounted(() => {
  setTexTips()
})
</script>
