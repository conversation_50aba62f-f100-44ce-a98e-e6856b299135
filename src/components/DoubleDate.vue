<template>
  <el-date-picker
    v-if="elType == 'DateTimePicker'"
    v-model="dateValue"
    :clearable="clearable"
    type="datetimerange"
    range-separator="至"
    start-placeholder="开始时间"
    end-placeholder="结束时间"
    @change="getValue"
    value-format="YYYY-MM-DD HH:mm:ss"
    :default-time="[new Date(2000, 1, 1), new Date(2000, 1, 1, 23, 59, 59)]"
  ></el-date-picker>
  <el-date-picker
    v-model="dateValue"
    :clearable="clearable"
    v-else-if="elType == 'DatePicker'"
    type="daterange"
    range-separator="至"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
    value-format="YYYY-MM-DD"
    @change="getValue"
  ></el-date-picker>
  <el-time-picker
    is-range
    :clearable="clearable"
    v-else-if="elType == 'TimePicker'"
    v-model="dateValue"
    range-separator="至"
    start-placeholder="开始时间"
    end-placeholder="结束时间"
    value-format="HH:mm:ss"
    @change="getValue"
  ></el-time-picker>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch } from 'vue'
import type { DoubleDateType } from './types'
const props = withDefaults(
  defineProps<{
    startTime?: string
    endTime?: string
    elType?: DoubleDateType
    name?: string
    clearable?: boolean
  }>(),
  {
    startTime: '',
    endTime: '',
    elType: 'DateTimePicker',
    name: '',
    clearable: true,
  }
)

const emit = defineEmits<{
  (e: 'editParams', data: string[], name: string): void
  (e: 'update:startTime', data: string): void
  (e: 'update:endTime', data: string): void
}>()

const { startTime, endTime } = toRefs(props)
const dateValue = ref<any>([props.startTime, props.endTime])

watch([startTime, endTime], ([newStartTime, newEndTime]) => {
  dateValue.value = [newStartTime, newEndTime]
})

const getValue = (data: any) => {
  emit('editParams', data, props.name)
  let startTime = ''
  let endTime = ''
  if (data) {
    startTime = data[0] ?? ''
    endTime = data[1] ?? ''
  }
  emit('update:startTime', startTime)
  emit('update:endTime', endTime)
}
</script>

<style scoped lang="less">
.query-conditions-container {
  background: #eee;
  padding: 18px 0 0;
  border: 1px solid #d3dce6;

  .el-input,
  .el-select {
    width: 190px;
  }

  .search-btn {
    margin-left: 20px;
  }
}
</style>
