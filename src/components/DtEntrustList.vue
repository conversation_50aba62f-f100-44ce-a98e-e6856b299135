<template>
  <DtPopup
    v-model:show="isShow"
    :confirmBeforeClose="true"
    title="选择转委托平台"
    width="880px"
    @close="onClose"
    @confirm="onOk"
  >
    <SearchForm
      ref="searchRef"
      :searchFormTemp="searchFormTemp"
      labelWidth="90px"
      @handleSearch="handleSearch"
      @handleResetQuery="searchResetQuery"
    />
    <el-radio-group v-model="pickId" class="w-full">
      <el-table
        :data="tableData"
        class="dt-table"
        max-height="35vh"
        @current-change="onCurrentChange"
      >
        <el-table-column label="操作" align="center" width="70">
          <template #default="scope">
            <el-radio :label="scope.row.entId" size="large"> &nbsp; </el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="entCode" label="委托商编码" align="center" />
        <el-table-column prop="entFullName" label="委托商名称" align="center" />
      </el-table>
    </el-radio-group>
  </DtPopup>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue'
import type { SearchFormTemp } from './types'
import { getEntrustList, type EntrustParm, type EntrustData } from '@/api'

const props = defineProps<{ modelValue: boolean }>()

const isShow = ref(false)
const tableData = ref<EntrustData[]>([])
const pickId = ref('')
const pickData = ref<EntrustData | null>(null)
const searchFormTemp: SearchFormTemp[] = [
  {
    label: '委托商名称',
    name: 'entName',
    type: 'input'
  },
  {
    label: '委托商编码',
    name: 'entCode',
    type: 'input'
  }
]

const emits = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'select', data: EntrustData): void
}>()

const searchRef = ref()
watch(
  () => props.modelValue,
  async val => {
    isShow.value = val
    if (val) {
      nextTick(() => {
        if (!pickId.value) getData({ entName: '', entCode: '' })
      })
    }
  }
)

const getData = async (data: EntrustParm) => {
  pickId.value = ''
  pickData.value = null
  tableData.value = await getEntrustList(data)
}

const onCurrentChange = (data: EntrustData) => {
  pickData.value = data
}

const onClose = () => {
  emits('update:modelValue', false)
}
const onOk = () => {
  if (!pickData.value) {
    ElMessage.error('请选择委托商!')
    return
  }
  emits('select', pickData.value)
  onClose()
}

const handleSearch = (data: any) => {
  getData(data)
}

const searchResetQuery = (data: any) => {
  getData(data)
}
</script>
