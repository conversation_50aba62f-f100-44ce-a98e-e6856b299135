<template>
    <el-form
      ref="updateForm"
      :model="form"
      :rules="rules"
      :label-width="labelWidth"
      :label-position="labelPosition"
      style="width: 600px"
      @clearValidate="clearValidate"
    >
      <div v-for="item in filtedFormList">
        <el-form-item v-if="!item.isHidden" :label="item.title" :labelWidth="item.labelWidth?item.labelWidth:'120px'" :prop="item.field" :required="item.require">
          <!-- 输入框 -->
          <el-input
            v-if="item.type == 'input'"
            v-model="form[item.field]"
            :placeholder="item.placeholder || '请输入'"
            :disabled="item.disabled"
            :maxlength="item.maxlength ?? 30"
            clearable
          />
  
          <el-input
            class=""
            v-else-if="item.type == 'number'"
            v-model="form[item.field]"
            type="number"
            :placeholder="item.placeholder || '请输入'"
            :disabled="item.disabled"
            clearable
          />
  
          <el-input-number
            v-else-if="item.type == 'numberInput'"
            class="input-number"
            v-model="form[item.field]"
            :precision="item.precision"
            :min="item.min"
            :max="item.max"
            :step="item.step || 1"
            :step-strictly="item.stepStrictly || false"
            :controls="false"
          ></el-input-number>
          <!-- 下拉框 -->
          <el-select
            v-else-if="item.type == 'select'"
            v-model="form[item.field]"
            :placeholder="item.placeholder || '请选择'"
            :disabled="item.disabled"
            clearable
          >
            <el-option v-for="el in item.dict" :label="el.dicItemName" :value="el.dicItemCode" />
          </el-select>
  
          <!-- 多选框 -->
          <el-checkbox-group v-else-if="item.type == 'checkbox'" v-model="form[item.field]" :disabled="item.disabled">
            <el-checkbox v-for="el in item.dict" :label="el.dicItemName" :name="el.dicItemCode" />
          </el-checkbox-group>
  
          <!-- 单选框 -->
          <el-radio-group v-else-if="item.type == 'radio'" v-model="form[item.field]" :disabled="item.disabled" @change="item.action(form)">
            <el-radio v-for="el in item.dict" :label="el.dicItemCode" :name="el.dicItemCode">{{ el.dicItemName }}</el-radio>
          </el-radio-group>
  
          <!-- 多行输入框 -->
          <div v-else-if="item.type == 'textarea'" class="w-full">
            <el-input
              v-model="form[item.field]"
              type="textarea"
              rows="4"
              resize="none"
              :maxlength="item.maxlength ?? 50"
              show-word-limit
              :placeholder="item.placeholder || '请输入'"
              :disabled="item.disabled"
            />
            <p v-if="item.desc" class="text-gray mt-2">{{ item.desc }}</p>
          </div>
  
          <!-- editor -->
          <div v-else-if="item.type == 'editor'">
            <Editor v-model="form[item.field]" :maxlength="item.maxlength" />
          </div>
          <el-switch v-else-if="item.type == 'switch'" v-model="form[item.field]" :active-value="1" :inactive-value="0" :disabled="item.disabled" />
          <el-switch v-else-if="item.type == 'switchBasic'" v-model="form[item.field]" :active-value="0" :inactive-value="1" :disabled="item.disabled" />
          <!-- remote search -->
          <el-autocomplete
            v-else-if="item.type == 'autocomplete'"
            v-model="form[item.field]"
            :disabled="item.disabled"
            :fetch-suggestions="item.fetchSuggestions"
          />
  
          <DtRobotSelect v-else-if="item.type == 'robotselect'" v-model="form[item.field]" :disabled="item.disabled" />
  
          <DtprojectList v-else-if="item.type == 'proselect'" v-model="form[item.field]" :disabled="item.disabled" />
  
          <Dtflow v-else-if="item.type == 'flowIdSelect'" v-model="form[item.field]" :disabled="item.disabled" />
          
          <!-- File upload -->
          <div v-else-if="item.type === 'upload'">
            <UploadCard v-model:files="form[item.field]" file-type="file" />
          </div>
  
          <div v-else-if="item.type === 'uploadimg'">
            <UploadCard v-model:files="form[item.field]"   :limit="item.limit" file-type="file" accept=".jpg,.jpeg,.png" />
          </div>
  
          <!-- 文字 -->
          <span v-else-if="item.type == 'text'">{{ form[item.field] }}</span>
        </el-form-item>
      </div>
      <slot name="default" :form="form"> </slot>
  
      <el-form-item v-if="showSubmit"  class="submit-buttons">
        <el-button type="primary" @click="submitForm(updateForm)">{{ props.certainText }}</el-button>
        <el-button @click="cancel">{{ props.cancelText }}</el-button>
      </el-form-item>
    </el-form>
  </template>
  
  <script lang="ts" setup>
  import type { UpdateFormList } from './types'
  import type { FormInstance, FormRules } from 'element-plus'
  import UploadCard from './UploadCard.vue'
  import DtRobotSelect from './DtRobotSelect.vue'
  import Editor from '@/components/Editor.vue'
  import DtprojectList from './DtprojectList.vue'
  import Dtflow  from './Dtflow.vue'
  import { reactive, ref, onMounted, watch } from 'vue'
  import { useRouter } from 'vue-router'
  
  interface Props {
    formList: UpdateFormList[]
    rules?: FormRules
    labelPosition?: 'left' | 'right'
    showSubmit?: boolean
    labelWidth?: string
    formParams?: Record<string, any>
    certainText: string
    cancelText: string
    showbtn:boolean
  }
  const props = withDefaults(defineProps<Props>(), {
    labelPosition: 'left',
    showSubmit: true,
    labelWidth: '120px',
    certainText: '提交',
    cancelText: '取消',
    formParams: () => ({} as Record<string, any>),
  })
  
  const emit = defineEmits<{
    (e: 'submit', data: Record<string, any>): void
    (e: 'cancel'): void
    (e: 'update', value: any): void
  }>()
  
  const filtedFormList = ref<UpdateFormList[]>([])
  const updateForm = ref()
  const router = useRouter()
  
  /* 表单参数 */
  const form: Record<string, any> = reactive(props.formParams)
  
  /* 表单提交 */
  const submitForm = async (formEl: FormInstance) => {
    try {
      const flag = await formEl.validate()
      if (flag) emit('submit', form)
    } catch (e) {
      console.log(e)
    }
  }
  
  /* 取消 */
  const cancel = () => {
      emit('cancel')
   
  }
  
  // Rebind default data to tmplate
  function setDefault() {
    filtedFormList.value = props.formList.filter((item: UpdateFormList) => !item.except)
    props.formList.forEach((item: UpdateFormList) => {
      if (Object.hasOwn(item, 'value')) {
        form[item.field] = item.value
      }
    })
  }
  
  /* 清理表单校验 */
  const clearValidate = () => {
    const formEl: FormInstance = updateForm.value
    formEl.clearValidate()
  }
  
  async function getFormData() {
    try {
      const formEl: FormInstance = updateForm.value
      // 校验表单后返回
      const flag = await formEl.validate()
      return flag ? form : null
    } catch (e) {
      console.log(e)
    }
  }
  
  const getValidate = async () => {
    const formEl: FormInstance = updateForm.value
    // 校验表单后返回
    const flag = await formEl.validate()
    return flag
  }
  
  function reset(key: string, val: any) {
    form[key] = val
  }
  
  watch(
    () => props.formParams,
    () => {
      Object.assign(form, props.formParams)
    }
  )
  
  watch(
    () => props.formList,
    () => {
      setDefault()
    }
  )
  
  onMounted(() => {
    setDefault()
  })
  
  defineExpose({ getFormData, getValidate, clearValidate, reset })
  </script>
  
  <style lang="less" scoped>
  .input-number {
    width: 100%;
  
    :deep(.el-input__inner) {
      text-align: left;
    }
  }
  
  .el-form-item {
    // 让所有表单项的内容宽度100%
    .el-input,
    .el-input-number,
    .el-textarea,
    .el-textarea__inner,
    .el-select,
    .el-select .el-input,
    .el-select .el-input__wrapper {
      width: 100% !important;
      box-sizing: border-box;
    }
  }
  
  .submit-buttons {
    text-align: center;
    
    :deep(.el-form-item__content) {
      justify-content: center;
      margin-left: 0 !important;
    }
  }
  </style>
  