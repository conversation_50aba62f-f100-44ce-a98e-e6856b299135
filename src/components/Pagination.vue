<template>
  <el-pagination v-if="total > 0" class="pagination" v-model:page-size="pageSize" v-model:current-page="pageNum"
    :page-sizes="[5, 10, 20, 30]" layout="total,slot, sizes, prev, pager, next, jumper" :total="Number(total)">
    <span class="add-select" v-if="props.showSelect">
      已选：{{ props.selNum }}条
      <span class="text-sm checkbox">
        仅显示已选
        <el-switch v-model="value" @change="change"></el-switch>
      </span>
    </span>
  </el-pagination>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import type { PageData } from './types'
const value = ref(false)
const props = withDefaults(
  defineProps<{
    pageData?: PageData
    showSelect?: boolean
    selNum?: number
    total: number
  }>(),
  {
    pageData: () => {
      return { pageNum: 1, pageSize: 10 }
    },
    showSelect: false,
    selNum: 0,
    total: 0
  }
)
const pageNum = ref(props.pageData.pageNum)
const pageSize = ref(props.pageData.pageSize)
const emit = defineEmits<{
  (e: 'changeSel', val: boolean): void
  (e: 'update:pageData', val: PageData): void
  (e: 'change', val: PageData): void
}>()

const normalReset = () => {
  pageNum.value = 1
  pageSize.value = 10
}

watch([pageNum, pageSize], ([newP, newSize]) => {
  emit('update:pageData', { pageNum: newP, pageSize: newSize })
  emit('change', { pageNum: newP, pageSize: newSize })
})

const change = (val: string | number | boolean) =>
  emit('changeSel', val as boolean)

defineExpose({ normalReset })
</script>

<style lang="less">
.pagination {
  margin: 20px 0 0 0;
  justify-content: flex-end;
  padding-left: 10px;
  padding-right: 10px;

  .el-pagination__total {
    justify-content: flex-start !important;
  }

  .el-pagination__sizes {
    flex: 1;
    justify-content: flex-end !important;
  }

  .add-select {
    font-weight: 400;
    color: var(--el-text-color-regular);

    .el-switch__core {
      height: 20px;
      line-height: 20px;
    }

    .checkbox {
      margin: 0 10px;
    }
  }
}
</style>
