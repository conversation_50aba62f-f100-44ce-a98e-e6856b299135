<template>
  <div class="flex flex-col">
    <div class="flex bg-white py-2 items-center">
      <!-- 展示信息页标题 -->
      <div class="flex font-semibold mr-2 text-gray-700 items-center" v-if="props.level === 'l1-v'">
        <ElIcon class="mr-2" v-if="collapse !== null" @click="onCollapse">
          <CaretBottom v-if="collapseStatus" />
          <CaretRight v-else />
        </ElIcon>
        <span class="w-[10px] h-[10px] mr-2 bg-dt rotate-45" v-else></span>
        <span class="text-xl">{{ props.toolTitle }}</span>
      </div>
      <!-- 二级标题 -->
      <div class="flex font-semibold mr-2 text-gray-700 items-center" v-else-if="props.level === 'l2'">
        <ElIcon class="mr-1" v-if="collapse !== null" @click="onCollapse">
          <CaretBottom v-if="collapseStatus" />
          <CaretRight v-else />
        </ElIcon>
        <span class="w-2 h-2 mr-1 bg-dt rotate-45 ml-5" v-else></span>
        <span class="text-sm">{{ props.toolTitle }}</span>
      </div>
      <!-- 一级级标题 -->
      <div class="flex font-semibold mr-2 text-gray-700 items-center" v-else>
        <ElIcon class="mr-2" v-if="collapse !== null" @click="onCollapse">
          <CaretBottom v-if="collapseStatus" />
          <CaretRight v-else />
        </ElIcon>
        <span class="w-1 h-5 mr-2 bg-dt" v-else></span>
        <span class="text-xl">{{ props.toolTitle }}</span>
      </div>
      <div class="flex items-center" v-for="(item, index) in props.toolList" :key="index">
        <el-upload action="#" :data="{ item, index }"
          :accept="item.upload.accept?.map(e => '.' + e).join(',') || defAccept" :multiple="item.upload.multiple"
          :http-request="uploadData" :show-file-list="false" v-auth="item.btnCode" v-if="item.upload">
          <el-button type="primary" link class="mr-3">
            <DtIcon v-if="item.icon && mapToolIcon[item.icon]" :icon-name="mapToolIcon[item.icon]" /><span>{{ item.name
            }}</span>
          </el-button>
        </el-upload>
        <el-button v-else type="primary" link v-auth="item.btnCode" class="mr-3" @click="handleTool(item, index)">
          <DtIcon v-if="item.icon && mapToolIcon[item.icon]" :icon-name="mapToolIcon[item.icon]" />
          <span>{{ item.name }}</span>
        </el-button>
      </div>
      <slot name="title"> </slot>
    </div>
    <div class="w-full box-border px-4" v-if="collapse === null">
      <slot name="default"> </slot>
    </div>
    <div class="w-full box-border px-4" v-show="collapseStatus" v-else>
      <slot name="default"> </slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ToolListProps, ToolIcon, ToolUpload } from './types'
import type { UploadRequestOptions } from 'element-plus'
import { httpServe } from '@/utils/httpServe'
import { exportData } from '@/utils'
import { CaretRight, CaretBottom } from '@element-plus/icons-vue'
import { ref, watch } from 'vue'

const mapToolIcon: Record<ToolIcon, string> = {
  add: 'icondt8',
  down: 'icondt10',
  up: 'icondt21',
  link: 'icondt20',
  submit: 'Promotion',
  del: 'DeleteFilled',
  pass: 'SuccessFilled',
  reject: 'RemoveFilled',
  invalid: 'BrushFilled',
  lock: 'Lock',
  sync: 'Refresh',
  back: 'Back'
}
const props = withDefaults(
  defineProps<{
    collapse?: boolean | null
    toolTitle: string
    level?: string
    toolList?: ToolListProps[]
  }>(),
  {
    level: 'l1',
    collapse: null
  }
)

const emit = defineEmits<{
  (e: 'handleTool', item: ToolListProps, index: number): void
  (e: 'upload-end', ret: any, item: ToolListProps, index: number): void
  (e: 'collapse-change', val: boolean): void
}>()

const handleTool = async (
  item: ToolListProps,
  index: number
): Promise<void> => {
  if (item.download) {
    if (item.download.check && !item.download.check()) return

    let params = item.download.params
    if (params && {}.toString.call(params) === '[object Function]') {
      params = params()
    }
    const res = await httpServe(
      item.download.url,
      {
        ...params
      },
      {
        isExport: true,
        method: item.download.isPost ? 'post' : 'get',
        baseURL: item.download.baseUrl
      }
    )
    console.log(res)
    if(!res.fileName){
      ElMessage.error('导出数据为空！')
      return
    }
    if (res.status === 0) {
      exportData(res.data, res.fileName)
    }
  } else {
    if (item.action) await item.action()
    emit('handleTool', item, index)
  }
}

// 默认限制的文件格式
const defAccept = '.xls,.xlsx,.json,.jpg,.png,.pdf,.doc,.docx'
// 上传操作
const uploadData = async (d: UploadRequestOptions): Promise<void> => {
  const item = d.data.item as any as ToolListProps
  const toolUpload = item.upload as ToolUpload

  if (!toolUpload.noAccept) {
    const accept =
      toolUpload.accept?.join('|') || 'xls|xlsx|json|jpg|png|pdf|doc|docx'
    const regex = new RegExp(`\\.(${accept})$`)
    if (!regex.test(d.file.name.toLowerCase())) {
      ElMessage.error(
        `上传格式不正确，请上传${toolUpload.accept?.join('、') || 'xls、xlsx、json'
        }格式`
      )
      return
    }
  }
  const filename = toolUpload.filename || d.filename
  const res = await httpServe(
    toolUpload.url,
    {
      ...toolUpload.params,
      [filename]: d.file
    },
    { isUpload: true, baseURL: toolUpload.baseUrl }
  )

  if (res.status === 0) {
    ElMessage.success(toolUpload.success ?? res.msg)
    emit('upload-end', res.data, item, d.data.index as unknown as number)
  }
}

// 折叠操作
const collapseStatus = ref(props.collapse || false)
const onCollapse = () => {
  collapseStatus.value = !collapseStatus.value
  emit('collapse-change', collapseStatus.value)
}

watch(
  () => props.collapse,
  (val: boolean | null) => {
    collapseStatus.value = val as boolean
  }
)
</script>
