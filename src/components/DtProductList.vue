<template>
  <DtPopup
    v-model:show="isShow"
    :confirmBeforeClose="true"
    title="选择产品"
    width="850px"
    @close="onClose"
    @confirm="onOk"
  >
    <PageTable
      ref="pageRef"
      :searchFormTemp="searchFormTemp"
      apiUrl="/web/gpproducttenant/policyProductSelectPage"
      baseUrl="/gateway/kbc-cbs-ism"
      :param="{
        ...searchParam
      }"
      :noImmediate="true"
    >
      <template #default="{ tableData }">
        <el-checkbox-group
          v-model="gpProductId"
          class="w-full"
          @change="onChange(tableData as Product[])"
        >
          <el-table
            :data="tableData"
            class="dt-table"
            max-height="35vh"
            :empty-text="props.emptyText"
          >
            <el-table-column label="操作" align="center" width="70">
              <template #default="scope">
                <el-checkbox :label="scope.row.gpProductId">
                  &nbsp;
                </el-checkbox>
              </template>
            </el-table-column>
            <el-table-column
              prop="unifiedProductId"
              label="产品编码"
              align="center"
            />
            <el-table-column
              prop="productName"
              label="产品名称"
              align="center"
            />
            <el-table-column prop="productType" label="产品类型" align="center">
              <template #default="scope">{{
                dicItemName('cbs.ism.gp.productType', scope.row.productType)
              }}</template>
            </el-table-column>
          </el-table>
        </el-checkbox-group>
      </template>
    </PageTable>
  </DtPopup>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted, toRefs } from 'vue'
import type { SearchFormTemp, Product } from './types'
import { useDicItemStore } from '@/store/dicItem'
import { policyProductConfirmSelect } from '@/api'
import { useCompanyStore } from '@/store/company'

const { company } = useCompanyStore()
const { dicItemName, addDicItems } = useDicItemStore()
interface PListProps {
  modelValue: boolean // for 'v-model'
  type: 'group' | 'property' // 团/财
  hasSelectedIds: string[] | null
  companyId?: string
  emptyText?: string
  showCompanySel?: boolean
}

const pageRef = ref()
const props = withDefaults(defineProps<PListProps>(), {
  modelValue: false,
  showCompanySel: false,
  type: 'group',
  hasSelectedIds: null,
  companyId: '',
  emptyText: '暂无数据'
})

const { companyId } = toRefs(props)
const isShow = ref(false)
const gpProductId = ref<string[]>([])
let tableData: Product[] = []

const searchFormTemp: SearchFormTemp[] = [
  {
    label: '产品编码',
    name: 'unifiedProductId',
    type: 'input',
    placeholder: '请输入产品编码'
  },
  {
    label: '产品名称',
    name: 'productName',
    type: 'input',
    placeholder: '请输入产品名称'
  }
]
const searchParam: Record<string, any> = {
  hasSelectedIds: props.hasSelectedIds,
  systemGpType: props.type === 'group' ? 'GI' : 'PI'
}
if (props.showCompanySel) {
  searchFormTemp.push({
    label: '保险公司',
    name: 'companyId',
    type: 'select',
    value: props.companyId,
    list: [
      ...company.map(e => {
        return {
          dicItemCode: e.id,
          dicItemName: e.companyName
        }
      })
    ]
  })
} else {
  searchParam.companyId = companyId.value
}

const emits = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'select', data: Product[]): void
}>()

const onClose = () => {
  emits('update:modelValue', false)
}

const onOk = async () => {
  if (!gpProductId.value) {
    ElMessage.error('请选择产品!')
    return
  }

  const check = await policyProductConfirmSelect(gpProductId.value)
  if (!check) return
  const p: Product[] = []
  gpProductId.value.forEach(e => {
    const d = tableData.find(f => f.gpProductId === e)
    if (d) {
      p.push(d)
    }
  })

  if (p.length === 0) return
  emits('select', p)
  onClose()
}

const onChange = (list: Product[]) => {
  tableData = list
}
onMounted(async () => {
  await addDicItems(['cbs.ism.gp.productType'])
})

watch(
  () => props.modelValue,
  async val => {
    isShow.value = val

    if (val) {
      gpProductId.value = []
      tableData = []
      nextTick(() => {
        if (pageRef.value) {
          pageRef.value.normalReset()
        }
      })
    }
  }
)
</script>
