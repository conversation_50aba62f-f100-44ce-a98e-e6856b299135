<template>
  <el-dialog
    v-model="dialogVisible"
    :destroy-on-close="props.destroy"
    :title="props.title"
    :width="props.width"
    :show-close="false"
    :center="props.center"
    :close-on-click-modal="false"
    @close="changeClose"
    class="dt-popup"
    :append-to-body="appendToBody"
    :fullscreen="isFullscreen"
  >
    <DtIcon icon-name="icondt25" class="popup-icon" v-show="showClose" @click="handleCloseByIcon" />
    <slot></slot>
    <template #footer v-if="props.footer">
      <el-button type="primary" plain class="!bg-white hover:!text-dt !text-dt" @click="dialogVisible = false">
        {{ cancelBtnText }}
      </el-button>
      <el-button type="primary" @click="handelConfirm">{{ confirmBtnText }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch, onMounted } from 'vue'
const dialogVisible = ref(false)

const props = withDefaults(
  defineProps<{
    show: boolean
    title?: string
    width?: string
    center?: boolean
    footer?: boolean
    confirmBeforeClose?: boolean
    appendToBody?: boolean
    cancelBtnText?: string
    confirmBtnText?: string
    showClose?: boolean
    isFullscreen?: boolean
    destroy?: boolean
  }>(),
  {
    title: '系统提示',
    width: '670px',
    footer: true,
    center: true,
    confirmBeforeClose: false,
    appendToBody: true,
    cancelBtnText: '取消',
    confirmBtnText: '确定',
    showClose: true,
    isFullscreen: false,
    destroy: false,
  }
)
const emit = defineEmits<{
  (e: 'update:show', val: boolean): void
  (e: 'close'): void
  (e: 'confirm'): void
  (e: 'close-by-icon'): void
}>()
const { show } = toRefs(props)

watch(show, (val: boolean) => {
  dialogVisible.value = val
})

const changeClose = () => {
  emit('update:show', false)
  emit('close')
}
const handelConfirm = () => {
  emit('confirm')
  if (!props.confirmBeforeClose) {
    dialogVisible.value = false
  }
}
const handleCloseByIcon = () => {
  dialogVisible.value = false
  emit('update:show', false)
  emit('close-by-icon')
}

onMounted(() => {
  dialogVisible.value = props.show
})
</script>

<style lang="less">
.dt-popup {
  position: relative;
  border-radius: 10px;
  overflow: hidden;

  .el-dialog__title {
    font-weight: bold;
  }

  .popup-icon {
    position: absolute;
    font-size: 65px;
    top: 7px;
    right: -61px;
    color: #b8b8b8;
    cursor: pointer;
  }
}
</style>
