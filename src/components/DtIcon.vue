<template>
  <span class="iconfont dt-icon text-dt text-xl" :class="props.iconName" v-if="dtIcons"></span>

  <span v-else class="dt-icon-el text-dt">
    <component :is="icon" />
  </span>
</template>

<script lang="ts" setup>
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import '@/assets/icons/iconfont.css'

import { computed} from "vue"
/**
 * 	支持参数格式有2种 icondt1/icondt-33
 *  具体图标样式参考 /iconList 页面
 */
const props = defineProps<{ iconName?: string, name?: string }>()
const dtIcons = /^icondt*/.test(props.iconName)

const name = props.iconName || props.name || ""
const icon = computed(() => {
  if (name) return ElementPlusIconsVue[name]
  return ElementPlusIconsVue['Document']
})
</script>

<style lang="less" scoped>
.dt-icon-el {
  svg {
    width: 16px;
    height: 16px;
    margin-right: 2px;
  }
}
</style>
