<template>
  <el-select
    v-model="pid"
    filterable
    clearable
    remote
    reserve-keyword
    :placeholder="props.placeholder"
    :remote-method="remoteMethod"
    :loading="loading"
    @change="onChange"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { httpServe } from '@/utils/httpServe'
import type { PageTableSearch } from '@/components/types'

const emits = defineEmits<{
  (e: 'update:modelValue', status: boolean): void
}>()
const pid = ref('')
const loading = ref(false)
const options = ref([])
const props = defineProps({
  modelValue: {
    default: '',
    type: String,
    required: true
  },
  placeholder: {
    type: String
  },
  apiURL: {
    default: '',
    type: String,
    required: true
  },
  baseURL: {
    default: '',
    type: String,
    required: true
  },
  bindName: {
    default: '',
    type: String,
    required: true
  },
  mapKey: {
    type: Object,
    default() {
      return { label: 'productName', value: 'gpProductId' }
    }
  }
})

const pageParam: PageTableSearch = {
  pageNum: 1,
  pageSize: 100,
  param: {}
}

const remoteMethod = async (query: string) => {
  if (!query) return

  loading.value = true
  pageParam.param[props.bindName] = query

  const res = await httpServe(props.apiURL, pageParam, {
    baseURL: props.baseURL
  })

  loading.value = false
  if (res.status === 0 && res.data.list) {
    options.value = res.data.list.map(item => {
      return {
        value: item[props.mapKey.value],
        label: item[props.mapKey.label]
      }
    })
  }
}

const onChange = (val: string) => {
  pid.value = val
  emits("update:modelValue", val)
}
</script>
