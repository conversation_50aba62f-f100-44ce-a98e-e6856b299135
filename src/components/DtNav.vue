<template>
  <el-breadcrumb :separator-icon="ArrowRight" class="py-3 px-5 text-gray-400 border-b">
    <el-breadcrumb-item class="cursor-pointer" :to="props.fromPath">
      {{ props.from }}
    </el-breadcrumb-item>
    <el-breadcrumb-item class="cursor-pointer" :to="router.options.history.state.back" v-if="props.subFrom">
      {{ props.subFrom }}
    </el-breadcrumb-item>
    <el-breadcrumb-item class="breadcrumb-dt">
      {{ props.name }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script lang="ts" setup>
import { ArrowRight } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { nextTick } from 'vue'
const props = defineProps<{
  // 前第一个页面名称
  from: string
  // 前第二个页面名称
  subFrom?: string
  // 当前页面名称
  name: string
  //  返回方法
  back?: (index: number) => void
  // 前第一个页面路径
  fromPath?: string
}>()

const route = useRoute()
const router = useRouter()
const handelBack = (index: number) => {
  if (props.back) {
    nextTick(() => {
      props.back(index)
    })
  } else {
    nextTick(() => {
      router.go(index)
    })
  }
}
</script>
