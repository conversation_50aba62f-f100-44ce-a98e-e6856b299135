<template>
  <el-dialog
    :title="dialogTitle"
    v-model:visible="visible"
    :width="width"
    :close-on-click-modal="closeOnClickModal"
    @close="handleClose"
    :class="['universal-form-dialog', dialogClass]"
  >
    <el-form
      :model="formData"
      :rules="formRules"
      ref="formRef"
      :label-width="labelWidth"
      class="universal-form"
      label-position="right"
    >
      <template v-for="field in formFields">
        <!-- 输入框 -->
        <el-form-item
          v-if="field.type === 'input'"
          :key="`input-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-input
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled || (field.disabledOnEdit && isEdit)"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
            :clearable="field.clearable !== false"
          />
        </el-form-item>
        <!-- 文本域 -->
        <el-form-item
          v-else-if="field.type === 'textarea'"
          :key="`textarea-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-input
            v-model="formData[field.prop]"
            type="textarea"
            :rows="field.rows || 3"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
          />
        </el-form-item>

        <!-- 选择器 -->
        <el-form-item
          v-else-if="field.type === 'select'"
          :key="`select-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-select
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            style="width: 100%"
            @change="val => field.onChange && field.onChange(val)"
          >
            <el-option
              v-for="option in field.options"
              :key="option[field.optionValue || 'value']"
              :label="option[field.optionLabel || 'label']"
              :value="option[field.optionValue || 'value']"
            />
          </el-select>
        </el-form-item>
 <!-- 选择器 -->
        <!-- <el-form-item
          v-else-if="field.type === 'proselect'"
          :key="`select-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
        <DtprojectList v-model="form[field.field]" :disabled="field.disabled" />
        </el-form-item> -->
        <!-- 选择器 -->
        <el-form-item
          v-else-if="field.type === 'proselect'"
          :key="`select-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <DtprojectList v-model="formData[field.prop]" :disabled="field.disabled" />
        </el-form-item>
        <el-form-item
          v-else-if="field.type === 'flowIdSelect' && field.visible(formData)"
          :key="`select-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <Dtflow v-model="formData[field.prop]" :disabled="field.disabled" />
        </el-form-item>
        <!-- 数字输入框 -->
        <el-form-item
          v-else-if="field.type === 'number'"
          :key="`number-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-input-number
            v-model="formData[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :min="field.min"
            :max="field.max"
            :step="field.step"
            :precision="field.precision"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 日期选择器 -->
        <el-form-item
          v-else-if="field.type === 'date'"
          :key="`date-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-date-picker
            v-model="formData[field.prop]"
            :type="field.dateType || 'date'"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :format="field.format"
            :value-format="field.valueFormat"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 开关 -->
        <el-form-item
          v-else-if="field.type === 'switch'"
          :key="`switch-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-switch
            v-model="formData[field.prop]"
            :disabled="field.disabled"
            :active-text="field.activeText"
            :inactive-text="field.inactiveText"
            :active-value="field.activeValue || 1"
            :inactive-value="field.inactiveValue || 0"
          />
        </el-form-item>
        <!-- 上传图片配置 -->
        <el-form-item
          v-else-if="field.type === 'uploadimg'"
          :key="`switch-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
         <UploadCard v-model:files="formData[field.prop]"   :limit="field.limit" file-type="file" accept=".jpg,.jpeg,.png" />
         <!-- <div v-if="field.tip" style="font-size: 12px; color: #999; margin-top: 4px; white-space: pre-line;">
          {{ field.tip }}
        </div> -->
        </el-form-item>

        <!-- 单选框组 -->
        <el-form-item
          v-else-if="field.type === 'radio'"
          :key="`radio-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-radio-group v-model="formData[field.prop]" :disabled="field.disabled">
            <el-radio
              v-for="option in field.options"
              :key="option[field.optionValue || 'value']"
              :label="option[field.optionValue || 'value']"
            >
              {{ option[field.optionLabel || 'label'] }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 复选框组 -->
        <el-form-item
          v-else-if="field.type === 'checkbox'"
          :key="`checkbox-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <el-checkbox-group v-model="formData[field.prop]" :disabled="field.disabled">
            <el-checkbox
              v-for="option in field.options"
              :key="option[field.optionValue || 'value']"
              :label="option[field.optionValue || 'value']"
            >
              {{ option[field.optionLabel || 'label'] }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 自定义插槽 -->
        <el-form-item
          v-else-if="field.type === 'slot'"
          :key="`slot-${field.prop}`"
          :label="field.label"
          :prop="field.prop"
        >
          <slot :name="field.slotName || field.prop" :field="field" :formData="formData"></slot>
        </el-form-item>
      </template>

      <!-- 额外的插槽内容 -->
      <slot name="form-extra" :formData="formData" />
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">
          {{ cancelText }}
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="loading"
          class="confirm-btn"
        >
          {{ computedConfirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import DtprojectList from './DtprojectList.vue'
import UploadCard from './UploadCard.vue'
const props = defineProps({
  value: Boolean,
  // 弹窗基本配置
  title: {
    type: String,
    default: ''
  },
  editTitle: {
    type: String,
    default: ''
  },
  addTitle: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '600px'
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  },
  dialogClass: {
    type: String,
    default: ''
  },
  
  // 表单配置
  formFields: {
    type: Array,
    required: true
  },
  formRules: {
    type: Object,
    default: () => ({})
  },
  labelWidth: {
    type: String,
    // default:""
    // default: '100px'
  },
  
  // 数据
  formData: {
    type: Object,
    required: true
  },
  
  // 模式
  isEdit: {
    type: Boolean,
    default: false
  },
  
  // 按钮文本
  confirmText: {
    type: String,
    default: ''
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['update:value', 'confirm', 'cancel', 'close']);

const visible = computed({
  get: () => props.value,
  set: (val) => emit('update:value', val)
});

const formRef = ref(null);

function handleConfirm() {
  if (!formRef.value) return;
  formRef.value.validate().then(() => {
    emit('confirm', {
      formData: props.formData,
      isEdit: props.isEdit
    });
  }).catch((error) => {
    console.warn('表单验证失败:', error);
  });
}

function handleCancel() {
  emit('cancel');
  visible.value = false;
}

function handleClose() {
  emit('close');
  clearValidation();
}

function clearValidation() {
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  });
}

function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

function validate() {
  return formRef.value ? formRef.value.validate() : Promise.resolve(true);
}

function validateField(prop) {
  if (formRef.value) {
    formRef.value.validateField(prop);
  }
}

// 新增：暴露重置表单验证方法
function clearFormValidate() {
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  });
}

// 新增：暴露方法
defineExpose({
  clearFormValidate,
  resetForm,        // 你原本就有 resetForm 方法
  validate,
  validateField
});

const dialogTitle = computed(() => {
  if (props.title) return props.title;
  if (props.isEdit && props.editTitle) return props.editTitle;
  if (!props.isEdit && props.addTitle) return props.addTitle;
  return props.isEdit ? '编辑' : '新增';
});

const computedConfirmText = computed(() => {
  if (props.confirmText) return props.confirmText;
  return props.isEdit ? '保存' : '新增';
});
</script>


<style lang="less">
.el-dialog.universal-form-dialog {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(215, 162, 86, 0.15);
  .el-dialog__header {
  background: #D7A256;
  padding: 16px 28px;
  border-bottom: 1px solid #C4933C;
  margin-right: 0px;

  .el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 10px;

    &::before {
      content: '';
      width: 24px;
      height: 24px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' fill='white' viewBox='0 0 16 16'%3E%3Cpath d='M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5z'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: 14px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
  }

  .el-dialog__headerbtn {
    .el-dialog__close {
      color: rgba(255, 255, 255, 0.8);
      font-size: 18px;
      transition: all 0.3s ease;

      &:hover {
        color: white;
        transform: rotate(90deg);
      }
    }
  }
}
}



.el-dialog__body {
  padding: 24px 32px 8px 32px; // 上下间距更紧凑
  background: #fcfaf7;

  .universal-form {
    .el-input__wrapper{
        border: 0.15px solid #e8dcc0;
        border-radius: 8px;
        padding: 8px 12px;
        height: 36px;
        font-size: 14px;
        background: white;
        box-sizing: border-box;
    }
    .el-form-item {
      margin-bottom: 20px; // 缩小表单项间距
    
      .el-form-item__label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 14px;
        line-height: 36px; // 调整label高度
        padding-right: 18px;
        text-align: left;
      }

      .el-form-item__content {
        display: flex;
        align-items: center; // 垂直居中内容
       
        .el-input, .el-select, .el-input-number, .el-date-editor, .el-textarea {
          width: 100%;
          min-height: 36px; // 统一高度
          // .el-input__inner, .el-textarea__inner {
          //   border: 1px solid #e8dcc0;
          //   border-radius: 8px;
          //   padding: 8px 12px;
          //   height: 36px;
          //   font-size: 14px;
          //   background: white;
          //   box-sizing: border-box;
          // }
        }

        // .el-switch {
        //   margin-left: 0;
        //   min-width: 48px;
        //   height: 36px;
        //   display: flex;
        //   align-items: center;
        // }
      }

      // &.is-error {
      //   .el-input__inner, .el-textarea__inner {
      //     border-color: #f56c6c !important;
      //     box-shadow: 0 0 0 3px rgba(245, 108, 108, 0.1) !important;
      //   }
      // }
    }
  }
}
.el-switch {
          .el-switch__core {
            border-color: #e8dcc0;
            background: #e8dcc0;
          }
        }
.el-switch.is-checked{
  .el-switch__core {
    border-color: #D7A256;
    background: #D7A256;
  }
}
.el-dialog__footer {
  padding: 12px 28px 16px 28px; // 缩小底部间距
  background: #f8f4ec;
  border-top: 1px solid #e8dcc0;
  text-align: right;

  .el-button {
    height: 36px;
    padding: 0 20px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    margin-left: 12px;

    &:first-child {
      margin-left: 0;
    }
  }
}
.universal-form .el-form-item__label {
  text-align: right !important;
}
</style>