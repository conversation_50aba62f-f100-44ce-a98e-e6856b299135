<template>
  <div class="mr-5 flex rounded-md border-[1px] border-gray-300 border-solid text-[12px] h-[32px] overflow-hidden">
    <div class="px-3 text-center" :class="[gapType === '10mins' ? 'btn-active' : '']" @click="setDefaultTime('10mins')">10分钟</div>
    <div class="w-[1px] bg-gray-300"></div>
    <div class="px-3 text-center" :class="[gapType === '1hour' ? 'btn-active' : '']" @click="setDefaultTime('1hour')">一小时</div>
    <div class="w-[1px] bg-gray-300"></div>
    <div class="px-3 text-center" :class="[gapType === 'last3days' ? 'btn-active' : '']" @click="setDefaultTime('last3days')">近三天</div>
    <div class="w-[1px] bg-gray-300"></div>
    <div class="px-3 text-center" :class="[gapType === '' ? 'btn-active' : '']" @click="setDefaultTime('')">其他</div>
  </div>
  
  <el-date-picker
    class='limit-date-picker'
    v-model="val"
    :clearable="props.clearable"
    type="datetimerange"
    range-separator="至"
    start-placeholder="开始时间"
    end-placeholder="结束时间"
    value-format="YYYY-MM-DD HH:mm:ss"
    @change="onChange"
    :default-time="defaultTime"
    :readonly="readonly"
  ></el-date-picker>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { formatTimestamp } from '@/utils'
import { computed, watch, toRefs } from 'vue'

const gapType = ref('')
const props = defineProps<{
  startTime: string
  endTime: string
  clearable?: boolean
  defaultTime?: Date[]
}>()

const defaultTime = ref(props.defaultTime)
const val = ref([props.startTime, props.endTime])
const readonly = computed(() => !!gapType.value)

const emits = defineEmits<{
  (e: 'update:startTime', val: string): void
  (e: 'update:endTime', val: string): void
  (e: 'search'): void
}>()

const onChange = (data: string[]) => {
  emits('update:startTime', data[0])
  emits('update:endTime', data[1])
}

const setDefaultTime = (type: string) => {
  gapType.value = type
  if (type === '10mins') {
    defaultTime.value = [new Date(new Date().getTime() - 600000), new Date()]
  } else if (type === '1hour') {
    defaultTime.value = [new Date(new Date().getTime() - 3600000), new Date()]
  } else if (type === 'last3days') {
    defaultTime.value = [new Date(new Date().getTime() - 259200000), new Date()]
  } else {
    defaultTime.value = []
  }

  val.value = defaultTime.value.map((d) => (d ? formatTimestamp('YYYY-MM-DD HH:mm:ss', d.getTime()) : ''))
  onChange(val.value)

  if (type && type !== 'other') {
    emits('search')
  }
}

const { startTime, endTime } = toRefs(props)
watch([startTime, endTime], ([startTime, endTime]) => {
  val.value = [startTime, endTime]
  if (startTime === endTime && startTime === '') {
    gapType.value = ''
  }
})
</script>

<style scoped lang="less">
.btn-active {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}
</style>

<style lang="less">
.limit-date-picker.el-date-editor.el-input, .el-date-editor.el-input__wrapper {
width: 400px !important;
}

</style>
