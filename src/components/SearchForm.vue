<template>
  <div class="p-2 flex divide-x divide-dashed">
    <el-form :model="searchForm" :label-width="props.labelWidth" :inline="true" :label-position="labelPosition" class="dt-from">
      <el-form-item v-for="(item, index) in filterSearchTemp" :key="index" :label="item.label">
        <el-input
          v-if="item.type == 'input' && item.name"
          v-model="searchForm[item.name]"
          :placeholder="item.placeholder || ''"
          clearable
          :disabled="item.disabled"
        ></el-input>
        <el-select
          v-if="item.type == 'selectGroup' && item.name"
          v-model="searchForm[item.name]"
          filterable
          :placeholder="item.placeholder || '请选择'"
          :clearable="true"
          :multiple="item.multiple"
          @change="onChange(item)"
        >
          <el-option-group v-for="group in item.listGroup" :key="group.label" :label="group.label">
            <el-option v-for="item in group.options" :key="(item.dicItemCode as string)" :label="item.dicItemName" :value="item.dicItemCode">
            </el-option>
          </el-option-group>
        </el-select>
        <el-select
          v-if="item.type == 'select' && item.name && !item.listFn"
          v-model="searchForm[item.name]"
          filterable
          :placeholder="item.placeholder || '请选择'"
          :clearable="true"
          :multiple="item.multiple"
          @change="onChange(item)"
        >
          <el-option v-for="(el, idx) in item.list" :key="idx" :label="el.dicItemName" :value="el.dicItemCode"></el-option>
        </el-select>
        <el-select
          v-if="item.type == 'select' && item.name && item.listFn"
          v-model="searchForm[item.name]"
          filterable
          :placeholder="item.placeholder || '请选择'"
          :clearable="true"
          :multiple="item.multiple"
          @change="onChange(item)"
        >
          <el-option v-for="(el, idx) in item.listFn" :key="idx" :label="el.dicItemName" :value="el.dicItemCode"></el-option>
        </el-select>
        <el-date-picker
          v-if="item.type == 'datePicker' && item.name"
          v-model="searchForm[item.name]"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
          type="date"
          :placeholder="item.placeholder || '请选择'"
        ></el-date-picker>
        <el-date-picker
          v-if="item.type == 'datePickerMonth' && item.name"
          v-model="searchForm[item.name]"
          format="YYYY-MM"
          :value-format="item.valueFormat ?? 'YYYY-MM'"
          clearable
          type="month"
          :placeholder="item.placeholder || '请选择'"
        ></el-date-picker>
        <DoubleDate
          v-if="item.type === 'doubleDate' && item.doubleDate"
          :elType="item.doubleDate.elType"
          v-model:startTime="searchForm[item.doubleDate.startTime.name]"
          v-model:endTime="searchForm[item.doubleDate.endTime.name]"
        />
        <DoubleDateWithQuick
          v-if="item.type === 'doubleDateWithQuick' && item.doubleDate"
          :elType="item.doubleDate.elType"
          v-model:startTime="searchForm[item.doubleDate.startTime.name]"
          v-model:endTime="searchForm[item.doubleDate.endTime.name]"
          @search="normalSearch"
        />

        <RemoteSelect
          v-model="searchForm[item.name]"
          v-if="item.type === 'remoteSelect' && item.name"
          :placeholder="item.placeholder"
          :apiURL="item.apiURL"
          :baseURL="item.baseURL"
          :bindName="item.name"
          :mapKey="item.mapKey"
        />
      </el-form-item>

      <el-form-item v-if="!isAccurates && filterSearchTemp.length > 0">
        <el-button type="primary" @click="normalSearch()">搜索</el-button>
        <el-button @click="normalReset()" type="primary" plain class="!bg-white hover:!text-dt !text-dt"> 重置 </el-button>

        <slot name="btns"> </slot>
      </el-form-item>
    </el-form>

    <div v-if="isAccurates && filterSearchTemp.length > 0" class="flex items-center pl-4">
      <el-button type="primary" @click="normalSearch()">搜索</el-button>
      <el-button @click="normalReset()" type="primary" plain class="!bg-white hover:!text-dt !text-dt"> 重置 </el-button>
      <el-button v-if="isAccurates" @click="showAccurates = !showAccurates" type="primary" plain class="!bg-white hover:!text-dt !text-dt">
        精确搜索
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { SearchFormTemp, PageData } from './types'
import { reactive, toRefs, watch, ref, computed } from 'vue'
import RemoteSelect from './RemoteSelect.vue'
import DoubleDateWithQuick from './DoubleDateWithQuick.vue'

const props = withDefaults(
  defineProps<{
    searchFormTemp?: SearchFormTemp[]
    accuratesTemp?: SearchFormTemp[]
    pageData?: PageData
    labelWidth?: string | number
    labelPosition?: 'left' | 'right' | 'top'
  }>(),
  {
    searchFormTemp: () => [],
    accuratesTemp: () => [],
    labelWidth: '100px',
    labelPosition: 'right',
  }
)

const showAccurates = ref(false)
const isAccurates = ref(props.accuratesTemp && props.accuratesTemp.length > 0)
const { pageData } = toRefs(props)
const getFormName = (): Record<string, any> => {
  const form: Record<string, any> = {}
  props.searchFormTemp.forEach((e) => {
    if (e.type === 'doubleDate' && e.doubleDate) {
      form[e.doubleDate.startTime.name] = e.doubleDate.startTime.value ?? ''
      form[e.doubleDate.endTime.name] = e.doubleDate.endTime.value ?? ''
    } else if (e.name) {
      form[e.name] = e.value ?? ''
    }

    if (e.type === 'doubleDateWithQuick' && e.doubleDate) {
      form[e.doubleDate.startTime.name] = e.doubleDate.startTime.value ?? ''
      form[e.doubleDate.endTime.name] = e.doubleDate.endTime.value ?? ''
    } else if (e.name) {
      form[e.name] = e.value ?? ''
    }
  })
  props.accuratesTemp.forEach((e) => {
    if (e.type === 'doubleDate' && e.doubleDate) {
      form[e.doubleDate.startTime.name] = e.doubleDate.startTime.value ?? ''
      form[e.doubleDate.endTime.name] = e.doubleDate.endTime.value ?? ''
    } else if (e.name) {
      form[e.name] = e.value ?? ''
    }
  })
  console.log('form',form)
  return form
}

const searchForm = reactive(getFormName())
console.log('getFormName',getFormName())
const emit = defineEmits<{
  (e: 'handleSearch', data: Record<string, any>, pageData?: PageData): void
  (e: 'handleResetQuery', data: Record<string, any>): void
}>()

const normalSearch = () => {
  emit('handleSearch', { ...searchForm }, pageData?.value)
}

const normalReset = () => {
  const defForm = getFormName()
  for (const key in defForm) {
    searchForm[key] = defForm[key]
  }

  emit('handleResetQuery', { ...searchForm })
}

const normalSearchReset = () => {
  const defForm = getFormName()
  for (const key in defForm) {
    searchForm[key] = defForm[key]
  }
  emit('handleSearch', { ...searchForm }, { pageNum: 1, pageSize: 10 })
}

if (pageData) {
  watch(pageData, () => normalSearch())
}

const filterSearchTemp = computed(() => {
  const s = props.searchFormTemp.filter((el: SearchFormTemp) => el.type != 'hidden')
  const a = props.accuratesTemp.filter((el: SearchFormTemp) => el.type != 'hidden')
  return showAccurates.value ? s.concat(a) : s
})

const getSearchQuery = () => {
  console.log('111')
  return { ...searchForm }
}
const getSearchQueryexport = () => {
  return { param:{...searchForm}, ...pageData?.value  }
}
const onChange = (temp: SearchFormTemp) => {
  if (temp.changeCall && temp.name) {
    temp.changeCall(searchForm[temp.name])
    if (temp.chanegRest) temp.chanegRest.forEach((e) => (searchForm[e] = ''))
  }
}

defineExpose({ normalReset, normalSearchReset, getSearchQuery,getSearchQueryexport })
</script>
<style scoped>
.el-input,
  .el-select {
    width: 190px;
  }
</style>