export type ToolIcon = 'add' | 'down' | 'up' | 'link' | 'del' | 'submit' | 'pass' | 'reject' | 'invalid' | 'lock' | 'sync' | 'back'
export interface ToolUpload {
  url: string
  baseUrl?: string
  params?: Record<string, any>
  multiple?: boolean
  // 上传文件格式 默认规则['xls','xlsx','json']
  accept?: string[]
  // 不限制上传格式 默认为false
  noAccept?: boolean
  success?: string
  filename?: string
}
interface ToolDownload {
  url: string
  baseUrl?: string
  isPost?: boolean
  params?: Record<string, any> | (() => Record<string, any>)
  check?: () => boolean
  success?: string
}

/**
 * 组件Pagination 数据类型
 */
export interface PageData {
  pageNum: number
  pageSize: number
}

/**
 * 组件TableToolTemp操作按钮参数
 */
export interface ToolListProps {
  name: string
  icon?: ToolIcon
  btnCode?: string
  // 按钮操作
  action?: () => Promise<void>
  // 上传导入操作
  upload?: ToolUpload
  // 下载导出操作
  download?: ToolDownload
}

type SearchFormType =
  | 'input'
  | 'select'
  | 'selectGroup'
  | 'datePicker'
  | 'datePickerMonth'
  | 'doubleDate'
  | 'doubleDateWithQuick'
  | 'hidden'
  | 'org'
  | 'channel'
  | 'channelL3'
  | 'remoteSelect'
  | 'datetime'
export type DoubleDateType = 'DateTimePicker' | 'DatePicker' | 'TimePicker'

export interface DoubleDate {
  elType?: DoubleDateType
  startTime: { name: string; value?: string }
  endTime: { name: string; value?: string }
}
// 选择列表数据格式
export interface SelectLits {
  dicItemCode: string | number | boolean
  dicItemName: string
}
export interface SelectLitsGroup {
  label: string
  options: SelectLits[]
}
/**
 * SearchFormTemp 数据类型定义
 */
export interface SearchFormTemp {
searchState: any
  label: string
  name?: string

  type: SearchFormType
  // 多选 仅仅select有效
  multiple?: boolean
  // select组件change方法回调
  changeCall?: (val: any) => void
  // select组件change方法回调执行 之后需要重置的其他搜索选项数据
  chanegRest?: string[]
  value?: any
  placeholder?: string
  doubleDate?: DoubleDate
  list?: SelectLits[]
  listGroup?: SelectLitsGroup[]
  // select组件 option 数据 用来实现 其他搜索选项改变联动该组件list 列表选项
  // listFn?: () => SelectLits[]
  listFn?: SelectLits[]
  hidden?: boolean
  apiURL?: string
  baseURL?: string
  bindName?: string
  mapKey?: Record<string, string>

  // 目前仅仅用于 input 组件的设置
  disabled?: boolean

  // date 组件
  valueFormat?: string

  // tree node-key
  nodeKey?: string

  // set select list label name
  listLabel?: string
}

/**
 * PageTable 组件搜索参数
 */
export interface PageTableSearch extends PageData {
  param: Record<string, any>
}

/**
 * upload props
 *
 *
 * */
export interface UpProps {
  url: string
  limit?: number
  tips?: string
  data?: Record<string, any>
  autoUpload: boolean
}
/**
 * productList component's props
 * */

export interface PListProps {
  modelValue: boolean // for 'v-model'
  type: 'group' | 'property' // 团/财
}

interface Plan {
  planCode: string
  planId: string
  planName: string
}
export interface Product {
  bizType: string
  compProductCode: string
  effectiveDate: string
  expirationDate: string
  gpProductId: string
  listGpProductPlan: Plan[]
  listGpProductCoverage: any[]
  listGpProductPeriod: any[]
  productName: string
  productType: string
  unifiedProductId: string
}

export interface Agent {
  id: string
  brokerCategory: string
  brokerCode: string
  brokerGrade: string // grade
  brokerGradeState: string
  brokerState: string // status
  name: string
  orgCode: string
  orgId: string
  orgName: string
  orgPath: string
  orgPathName: string
  cbsChannelId: string
  cbsChannelName: string
  cbsChannelPath: string
  cbsChannelPathName: string
}

export interface UploadFileProp {
  fileName: string
  filePath: string
}

export interface UploadFile {
  fileId: string
  fileName: string
  filePath: string
  file: File
}

export interface TextTable {
  lable: string
  val?: string
}

/**
 * @interface UpdateFormList
 * @property {string} title 表单标题
 * @property {Type} type 输入框类型
 * @property {boolean} require 是否必填
 * @property {string} field 字段名
 * @property {string} placeholder 占位符
 * @property {boolean} disabled 禁用
 * @property {Record<string, any>[]} [dict] 字典
 * @property {fetchSuggestions} function for remote search
 * @property {value} default value for init
 * @property {boolean} except 模版过滤
 * @property {number} precision 精度type为numberInput时有效
 * @property {number} min 最小值type为numberInput时有效
 * @property {number} max 最大值type为numberInput时有效
 * @property {function} action 处理方法
 * @property {boolean} isHidden 是否隐藏
 **/
export interface UpdateFormList {
  title: string
  type: Type
  require?: boolean
  field: string
  placeholder?: string
  disabled?: boolean
  dict?: Record<string, any>[]
  fetchSuggestions?: (queryStr: string, cb: (arg: any) => void) => void
  value?: any
  desc?: string
  except?: boolean
  precision?: number
  stepStrictly?: boolean
  step?: number
  min?: number
  max?: number
  maxlength?: number
  accept?: string
  action?: (item: Record<string, any>) => void
  isHidden?: boolean
}

/**
 * 表单元素类型
 * @typedef {string} Type
 * @enum {Type} Type
 * @property {'input'} input 输入框
 * @property {'select'} select 下拉框
 * @property {'radio'} radio 单选框
 * @property {'textarea'} textarea 多行文本框
 * @property {'checkbox'} checkbox 复选框
 */
type Type =
  | 'input'
  | 'numberInput'
  | 'select'
  | 'radio'
  | 'textarea'
  | 'checkbox'
  | 'text'
  | 'switch'
  | 'autocomplete'
  | 'upload'
  | 'uploadimg'
  | 'robotselect'
  | 'number'
  | 'editor'
  | 'numberInput'
  | 'proselect'
  | 'switchBasic'
  | 'flowIdSelect'
