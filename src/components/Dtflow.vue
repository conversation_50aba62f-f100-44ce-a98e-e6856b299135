<template>
    <el-select v-model="value" :disabled="props.disabled" filterable clearable @change="onSelect">
      <el-option v-for="(item, i) in channelConfig.flowList" :key="i" :label="item.flowName" :value="item.flowId" />
    </el-select>
  </template>
  
  <script setup lang="ts">
  import { computed, onBeforeMount } from 'vue'
  import { flowList } from '@/api/channels'
  import { useChannel } from '@/stores/channel'
  
  const { channelConfig,setFlow } = useChannel()
  const props = withDefaults(
    defineProps<{
      keyValue: string
      modelValue: string
      disabled?: boolean
    }>(),
    {
      keyValue: 'name,id',
      disabled: false,
    }
  )
  const emits = defineEmits<{
    (e: 'update:modelValue', val: string): void
    (e: 'change', val: string): void
  }>()
  
  const value = computed({
    set(val: string) {
      emits('update:modelValue', val)
    },
    get() {
      return props.modelValue
    },
  })
  
  const onSelect = (val: string) => {
    emits('update:modelValue', val)
    emits('change', val)
  }
  
  async function init() {
    if (!channelConfig.robots.length) {
      const list = await flowList({})
      setFlow(list.data)
    }
  
    if (!props.modelValue) {
      return
    }
  }
  
  onBeforeMount(() => {
    init()
  })
  </script>
  