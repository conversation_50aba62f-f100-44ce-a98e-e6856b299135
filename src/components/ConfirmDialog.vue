<template>
  <el-dialog
    :title="title"
    :width="width"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    class="confirm-dialog"
    @close="handleClose"
  >
    <div class="confirm-content">
      <div class="confirm-icon" v-if="icon">
        <el-icon><WarningFilled /></el-icon>
      </div>
      <div class="confirm-text">
        <p>{{ message }}</p>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleCancel">{{ cancelText }}</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        {{ confirmText }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import { WarningFilled } from '@element-plus/icons-vue'

const props = defineProps({
  title: { type: String, default: '确认操作' },
  message: { type: String, default: '请确认是否执行此操作？' },
  icon: { type: String, default: 'el-icon-warning' },
  width: { type: String, default: '400px' },
  cancelText: { type: String, default: '取消' },
  confirmText: { type: String, default: '确认' },
  loading: { type: Boolean, default: false }
});

const emits = defineEmits(['close', 'cancel', 'confirm']);

const visible = ref(true);

function show() {
  visible.value = true;
}
function hide() {
  visible.value = false;
}
function handleClose() {
  hide();
  emits('close');
}
function handleCancel() {
  hide();
  emits('cancel');
}
function handleConfirm() {
  emits('confirm');
}

defineExpose({ show, hide });
</script>

<style lang="less">
.confirm-dialog {
 

  .confirm-content {
    display: flex;
    align-items: center;
    gap: 20px;

    .confirm-icon {
      width: 40px;
      height: 40px;
      background: rgba(230, 162, 60, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 24px;
        color: #e6a23c;
      }
    }

    .confirm-text {
      flex: 1;

      h4 {
        margin: 0 0 8px 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #7f8c8d;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}

// 预设样式类
.confirm-dialog {
  &.warning {
    .el-dialog__header {
      background: #e6a23c;
      border-bottom-color: #d19b3a;
    }
    
    .confirm-icon {
      background: rgba(230, 162, 60, 0.1);
      
      i {
        color: #e6a23c;
      }
    }
  }

  &.danger {
    .el-dialog__header {
      background: #f56c6c;
      border-bottom-color: #e55a5a;
    }
    
    .confirm-icon {
      background: rgba(245, 108, 108, 0.1);
      
      i {
        color: #f56c6c;
      }
    }
  }

  &.success {
    .el-dialog__header {
      background: #67c23a;
      border-bottom-color: #5daf34;
    }
    
    .confirm-icon {
      background: rgba(103, 194, 58, 0.1);
      
      i {
        color: #67c23a;
      }
    }
  }

  &.info {
    .el-dialog__header {
      background: #409eff;
      border-bottom-color: #3a8ee6;
    }
    
    .confirm-icon {
      background: rgba(64, 158, 255, 0.1);
      
      i {
        color: #409eff;
      }
    }
  }
}
.el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(215, 162, 86, 0.15);
    background: white;
    
    .el-dialog__header {
      background: #D7A256;
      padding: 16px 28px;
      border-bottom: 1px solid #C4933C;
      position: relative;
      margin-right: 0px;
      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: white;
        display: flex;
        align-items: center;
        gap: 10px;

        &::before {
          content: '';
          width: 24px;
          height: 24px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' fill='white' viewBox='0 0 16 16'%3E%3Cpath d='M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5z'/%3E%3C/svg%3E");
          background-repeat: no-repeat;
          background-position: center;
          background-size: 14px;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);
        }
      }
    }

    .el-dialog__body {
      padding: 28px 32px;
      background: #fcfaf7;
    }

    .el-dialog__footer {
      padding: 20px 28px 20px;
      background: #f8f4ec;
      border-top: 1px solid #e8dcc0;
      text-align: right;

      .el-button {
        height: 36px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;
        margin-left: 12px;

        &:first-child {
          margin-left: 0;
        }

        &.el-button--default {
          background: white;
          border: 1px solid #e8dcc0;
          color: #8b7355;

          &:hover {
            background: #fcfaf7;
            border-color: #D7A256;
            color: #D7A256;
            transform: translateY(-1px);
          }
        }

        &.el-button--primary {
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
          border: none;
          color: white;
          box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(215, 162, 86, 0.4);
          }

          &.is-loading {
            transform: none;
            box-shadow: 0 2px 8px rgba(215, 162, 86, 0.2);
          }
        }
      }
    }
  }
</style> 