<template>
  <div class="w-[480px] dt-editor">
    <div id="toolbar">
      <button class="ql-bold"></button>
      <button class="ql-italic"></button>
      <button class="ql-link"></button>
      <button class="ql-image"></button>
    </div>
    <div id="editor" v-if="props.returnType === 'html'"></div>
    <div id="editor" v-else></div>
    <div class="flex justify-end text-12px text-gray-400" v-if="props.maxlength">
      <span :class="{ 'text-danger': isOver }">{{ words }}</span
      >/{{ props.maxlength }}
    </div>

    <div class="dt-hidden">
      <el-upload accept=".jpg,.jpeg,.png" :multiple="false" :http-request="uploadData" :show-file-list="false">
        <el-button type="primary" link ref="editorUploadBtnRef">重新上传</el-button>
      </el-upload>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { UploadRequestOptions } from 'element-plus'
import { byteConvert } from '@/utils'
import { upload } from '@/api/index'
import { onMounted, ref, nextTick, computed } from 'vue'
import quill from 'quill'
import 'quill/dist/quill.snow.css'

export interface EditorProps {
  modelValue: string
  returnType: 'json' | 'html' // 最终复文本编辑器保存的数据类型
  maxlength?: number
  trim: boolean
}

const words = ref(0)
const editorUploadBtnRef = ref()
const props = withDefaults(defineProps<EditorProps>(), {
  modelValue: '',
  returnType: 'html',
  trim: false,
})

const emits = defineEmits<{
  (e: 'update:modelValue', val: string)
  (e: 'change', val: string)
}>()

const isOver = computed(() => {
  return props.maxlength && words.value > props.maxlength
})

let editor: any = null
function init() {
  editor = new quill('#editor', {
    modules: {
      toolbar: {
        container: '#toolbar',
        handlers: {
          image: function (value: string) {
            const format = this.quill.format
            if (value && editorUploadBtnRef.value) {
              editorUploadBtnRef.value.$el.click()
            } else {
              format('link', false)
            }
          },
        },
      },
    },
    theme: 'snow',
  })

  setContents()

  nextTick(() => {
    editor.on('text-change', async function () {
      words.value = getContentsLen()
      const content = getContents(words.value)
      emits('update:modelValue', content)
      emits('change', content)
    })
  })
}

function setContents() {
  if (props.returnType === 'html') {
    editor.root.innerHTML = props.modelValue
    return
  }

  if (editor && props.modelValue) editor.setContents(props.modelValue)
}

function getContentsLen() {
  let innerText = editor.root.innerText
  
  if (props.trim) {
    innerText = innerText.trim()
  }

  console.log(props.trim)

  if (innerText.replaceAll(/[\n|\r]/g, '') === '') {
    return 0
  }

  return innerText.length
}

function getContents(len?: number) {
  if (!editor) return ''
  if (len === undefined) len = getContentsLen()
  if (!len) return ''
  if (props.returnType === 'html') {
    return editor.root.innerText
  }
  return JSON.stringify(editor.getContents())
}

// 上传操作
const uploadData = async (d: UploadRequestOptions): Promise<void> => {
  const limit = 2 * 1024 * 1024
  if (d.file.size > limit) {
    ElMessage.error(`文件大小不能超过${byteConvert(limit)}!`)
    return
  }

  const formData = new FormData()
  formData.append('file', d.file)
  formData.append('fileType', 'file')
  const { status, data } = await upload(formData)
  try {
    if (status === 0) {
      const fn = editor.format.bind(editor)
      fn('image', data.absolutePath)
      return
    }
  } catch (e) {}
}

onMounted(() => {
  init()
})

defineExpose({ getContents })
</script>

<style lang="less">
.dt-editor {
  .ql-snow .ql-picker-label::before {
    position: absolute;
  }
  .ql-tooltip {
    &::before {
      content: '访问地址' !important;
    }
    &[data-mode='link']::before {
      content: '链接' !important;
    }

    a.ql-remove::before {
      content: '删除' !important;
    }
    a.ql-action::after {
      content: '编辑' !important;
    }
  }

  .ql-tooltip.ql-editing {
    a.ql-action::after {
      content: '保存' !important;
    }
  }
  .ql-container.ql-snow {
    min-height: 200px;
  }
}

.dt-hidden {
  display: none;
}
.text-danger {
  color: var(--el-color-danger);
}
</style>
