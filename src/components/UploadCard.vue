<template>
  <el-upload v-model:file-list="fileList" action="#" list-type="picture-card" :limit="props.limit"
    :http-request="uploadData" ref="uploadRef" :accept="props.accept" :on-exceed="handleExceed"
    :before-upload="handleBefore">
    <el-icon v-if="fileList.length < props.limit">
      <Plus />
    </el-icon>
    <div class="text-20px" v-else>最多上传{{ props.limit }}张</div>
    <template #file="{ file }">
      <div class="flex justify-center items-center w-full h-full">
        <img v-if="isPic(file)" class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
        <div class="flex justify-center items-center w-full h-full px-5" v-else>
          <span class="text-sm">{{ file.name }}</span>
        </div>
        <span class="el-upload-list__item-actions">
          <span v-if="isPic(file)" class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <el-icon><zoom-in /></el-icon>
          </span>
          <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
            <el-icon>
              <Download />
            </el-icon>
          </span>
          <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
            <el-icon>
              <Delete />
            </el-icon>
          </span>
        </span>
      </div>
    </template>
  </el-upload>

  <el-dialog v-model="dialogVisible">
    <img w-full :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
  <!-- <div class="text-12px text-gray-400 leading-normal mt-2">推荐尺寸:200x200</div> -->
</template>

<script setup lang="ts">
export interface CardFile extends UploadUserFile {
  absolutePath: string
  fileId: string
  foreignPath: string
  relativePath: string
  sourcePath: string
  fileName: string
}

import { ref } from 'vue'
import { genFileId } from 'element-plus'
import { Delete, Download, Plus, ZoomIn } from '@element-plus/icons-vue'
import type { UploadFile, UploadUserFile, UploadRequestOptions, UploadProps, UploadRawFile } from 'element-plus'
import { exportByUrl, byteConvert } from '@/utils'
import { upload } from '@/api/index'

interface UCard {
  files: CardFile[]
  size?: number
  limit?: number
  fileType?: string
  accept?: string
}

const uploadRef = ref()
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const disabled = ref(false)

const props = withDefaults(defineProps<UCard>(), {
  files: () => [],
  limit: 1,
  size: 1,
  fileType: 'settle',
})
const fileList = ref(props.files ?? [])

const emits = defineEmits<{
  (e: 'update:files', data: UploadUserFile[]): void
}>()

const handleRemove = (file: UploadFile) => {
  const uid = file.uid
  const idx = fileList.value.findIndex((item) => item.uid === uid)
  if (idx > -1) {
    fileList.value.splice(idx, 1)
  }
  emits('update:files', fileList.value)
}

const handlePictureCardPreview = (file: UploadFile) => {
  dialogImageUrl.value = file.url!
  dialogVisible.value = true
}

const handleDownload = (file: UploadFile) => {
  if (file.url) {
    exportByUrl(file.url, file.name)
  }
}

const isPic = (file: UploadFile | UploadRawFile) => {
  const name = file.name
  return (
    name.endsWith('.jpg') ||
    name.endsWith('.png') ||
    name.endsWith('.jpeg') ||
    name.endsWith('.webp') ||
    name.endsWith('.gif') ||
    name.endsWith('.jfif')
  )
}

const setData = (file: UploadUserFile, data: any) => {
  const uid = file.uid
  const item = fileList.value.find((temp) => temp.uid === uid)
  if (item) {
    item.url = data.absolutePath

    item.absolutePath = data.absolutePath
    item.fileId = data.fileId
    item.fileName = data.fileName
    item.foreignPath = data.foreignPath
    item.relativePath = data.relativePath
    item.sourcePath = data.sourcePath
  }
}

const handleBefore = (file: UploadRawFile) => {
  const limit = props.size * 1024 * 1024
  if (file.size > limit) {
    ElMessage.error(`文件大小不能超过${byteConvert(limit)}!`)
    return false
  }

  if (props.accept && /jpg/.test(props.accept) && !isPic(file)) {
    ElMessage.error(`不支持该文件格式`)
    return false
  }
  return true
}
// 上传操作
const uploadData = async (d: UploadRequestOptions): Promise<void> => {
  const formData = new FormData()
  formData.append('file', d.file)
  formData.append('fileType', props.fileType ?? 'file')
  const { status, data } = await upload(formData)
  if (status === 0) {
    ElMessage.success('上传成功!')
    setData(d.file as UploadUserFile, data)

    emits('update:files', fileList.value)
  }
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  if (fileList.value.length >= props.limit) {
    ElMessage.error(`最多上传${props.limit}张图片!`)
    return false
  }
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}

defineExpose({
  fileList,
})
</script>

<style lang="less" scoped></style>
