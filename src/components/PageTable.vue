<template>
  <SearchForm
    v-if="!hiddenState"
    ref="searchRef"
    :searchFormTemp="props.searchFormTemp"
    :accuratesTemp="props.accuratesTemp"
    :labelWidth="props.labelWidth"
    :labelPosition="props.labelPosition"
    :pageData="pageData"
    @handleSearch="handleSearch"
    @handleResetQuery="searchResetQuery"
  >
    <template #btns>
      <slot name="btns" :tableData="tableData"> </slot>
    </template>
  </SearchForm>
  <SearchFormUp
    v-if="hiddenState"
    ref="searchRef"
    :searchFormTemp="props.searchFormTemp"
    :accuratesTemp="props.accuratesTemp"
    :labelWidth="props.labelWidth"
    :labelPosition="props.labelPosition"
    :pageData="pageData"
    @handleSearch="handleSearch"
    @spread="spread" 
    @pack="pack"
    @handleResetQuery="searchResetQuery"
  >
    <template #btns>
      <slot name="btns" :tableData="tableData"> </slot>
    </template>
  </SearchFormUp>

  <slot name="default" :tableData="tableData"> </slot>

  <Pagination ref="paginationRef" v-model:pageData="pageData" :total="total" />
</template>

<script lang="ts" setup>
import type { SearchFormTemp, PageData, PageTableSearch } from '@/components/types'
import { onMounted, ref, toRefs } from 'vue'
import { useAxios } from '@/hooks/http'

const props = defineProps<{
  searchFormTemp?: SearchFormTemp[]
  accuratesTemp?: SearchFormTemp[]
  pageSize?: number
  labelWidth?: string | number
  labelPosition?: 'left' | 'right' | 'top'
  baseUrl?: string
  apiUrl?: string
  param?: Record<string, any>
  noLoading?: boolean
  noImmediate?: boolean
  noPagination?: boolean
  hiddenState?:boolean
  formatterParam?: (data: PageTableSearch) => PageTableSearch
  checkSearchForm?: (data: PageTableSearch) => boolean
  multiField?: string
  listName?: string
}>()

const { param } = toRefs(props)
const emit = defineEmits<{
  (e: 'search', data: PageTableSearch): void
  (e: 'searchEnd', data: Record<string, any>[], resReturn?: Record<string, any>): void
    (e: 'spread', data:boolean): void
    (e: 'pack', data:boolean): void
}>()

const total = ref(0)
const tableData = ref<Record<string, any>[]>([])
const pageData = ref<PageData>({
  pageNum: 1,
  pageSize: props.pageSize ?? 10,
})

const pageParam: PageTableSearch = {
  pageNum: 1,
  pageSize: props.pageSize ?? 10,
  param: {},
}

const getPageData = async () => {
  if (param && param.value) {
    pageParam.param = { ...pageParam.param, ...param.value }
  }

  if (!props.apiUrl) {
    emit('search', pageParam)
    return
  }

  let _pageParam = pageParam
  if (props.formatterParam) {
    _pageParam = props.formatterParam(pageParam)
  }
  if (props.checkSearchForm) {
    const check = props.checkSearchForm(_pageParam)
    emit('search', _pageParam)
    if (!check) return
  }

  const baseURL = props.baseUrl ? props.baseUrl : import.meta.env.VITE_APP_API
  const res = await useAxios(props.apiUrl, _pageParam, { baseURL, loading: true, msg: true })

  const { status, data } = res

  if (status != 0) return

  if (props.multiField) {
    const listData = data[props.multiField]

    if (listData) {
      total.value = Number(listData.total)
      tableData.value = listData.list
    }
  } else if (props.listName) {
    total.value = Number(data.total)
    tableData.value = data[props.listName]
  }  else if (props.noPagination) {
    total.value = Number(data.total)
    tableData.value = data
  } else {
    total.value = Number(data.total)
    tableData.value = data.list
  }
  emit('searchEnd', tableData.value, data)
}

const handleSearch = (data: Record<string, any>, pageData?: PageData) => {
  pageParam.param = data
  if (pageData) {
    pageParam.pageNum = pageData.pageNum
    pageParam.pageSize = pageData.pageSize
  }
  getPageData()
}

const onSearchBtn = () => {
  pageParam.param = getSearchQuery()
  getPageData()
}
// 展开
const spread =(value: any)=> {
  console.log(value)
  emit("spread", value);
}
// 收起
const pack=(value: any)=>  {
  console.log(value)
  emit("pack", value);
}
onMounted(() => {
  if (!props.noImmediate) {
    onSearchBtn()
  }
})

const paginationRef = ref()
const searchRef = ref()
const normalReset = () => {
  if (searchRef.value) searchRef.value.normalReset()
  if (paginationRef.value) paginationRef.value.normalReset()
}

const searchResetQuery = () => {
  if (paginationRef.value) {
    if (pageParam.pageNum === 1 && pageParam.pageSize === 10) searchRef.value.normalSearchReset()
    else paginationRef.value.normalReset()
  } else {
    searchRef.value.normalSearchReset()
  }
}
const getSearchQueryexport=()=>{
  const _param = param ? { ...param.value } : {}
  let _pageParam: PageTableSearch = {
    pageNum: pageParam.pageNum,
    pageSize: pageParam.pageSize,
    param: { ...searchRef.value.getSearchQueryexport() },
  }
  if (props.formatterParam) {
    _pageParam = props.formatterParam(_pageParam)
  }
 console.log('_pageParam',_pageParam)
  return { ..._pageParam.param, ..._param }
}
const getSearchQuery = () => {
  const _param = param ? { ...param.value } : {}
  let _pageParam: PageTableSearch = {
    pageNum: pageParam.pageNum,
    pageSize: pageParam.pageSize,
    param: { ...searchRef.value.getSearchQuery() },
  }
  if (props.formatterParam) {
    _pageParam = props.formatterParam(_pageParam)
  }
 console.log('_pageParam',_pageParam)
  return { ..._pageParam.param, ..._param }
}

defineExpose({
  getPageData,
  normalReset,
  getSearchQuery,
  tableData,
  onSearchBtn,
  getSearchQueryexport,
  total,
})
</script>
