<template>
  <div class="secondary-page-header bg-dt">
    <!-- 页面头部区域 -->
    <div class="header-section">
      <div class="header-top">
        <div class="title-section">
          <div class="page-title">
            <div class="title-icon" v-if="icon">
              <!-- 使用 Element Plus 图标 -->
              <el-icon style="margin-right: 3px;">
                <component :is="icon" />
              </el-icon>
            </div>
            <div class="title-text">
              <h2 class="main-title">{{ title }}</h2>
              <p class="page-subtitle" v-if="subtitle">{{ subtitle }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 面包屑导航 - 按钮风格 -->
      <div class="breadcrumb-section" v-if="showBreadcrumb && breadcrumbItems.length > 0">
        <div class="breadcrumb-buttons">
          <template v-for="(item, index) in breadcrumbItems">
            <!-- 可点击的面包屑按钮 -->
            <el-button 
              v-if="item.to"
              :key="`btn-${index}`"
              @click="handleBreadcrumbClick(item)"
              class="breadcrumb-btn clickable-btn"
              size="small"
            >
              <el-icon v-if="item.icon" style="margin-right: 3px;">
                <component :is="item.icon" />
              </el-icon>
              {{ item.text }}
            </el-button>
            
            <!-- 当前页面按钮（不可点击） -->
            <el-button 
              v-else
              :key="`current-${index}`"
              class="breadcrumb-btn current-btn"
              size="small"
              disabled
            >
              <el-icon v-if="item.icon" style="margin-right: 3px;">
                <component :is="item.icon" />
              </el-icon>
              {{ item.text }}
            </el-button>
            
            <!-- 分隔符箭头 -->
            <el-icon 
              v-if="index < breadcrumbItems.length - 1" 
              :key="`separator-${index}`"
              class="breadcrumb-separator"
            >
              <ArrowRight />
            </el-icon>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { User, House, Document, ArrowRight,DataBoard, Back } from '@element-plus/icons-vue'
export default {
  name: "SecondaryPageHeader",
  components: {
    User,
    House,
    Document,
    ArrowRight,
    DataBoard,
    Back
  },
  props: {
    // 页面标题
    title: {
      type: String,
      required: true
    },
    // 页面副标题
    subtitle: {
      type: String,
      default: ""
    },
    // 标题图标
    icon: {
      type: String,
      default: ""
    },
    // 是否显示操作区域
    showActions: {
      type: Boolean,
      default: true
    },
    // 是否显示返回按钮
    showBackButton: {
      type: Boolean,
      default: true
    },
    // 返回按钮文字
    backButtonText: {
      type: String,
      default: "返回上级"
    },
    // 返回路由
    backRoute: {
      type: [String, Object],
      default: null
    },
    // 是否显示面包屑
    showBreadcrumb: {
      type: Boolean,
      default: true
    },
    // 面包屑数据
    breadcrumbItems: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 处理返回操作
    handleBack() {
      this.$emit('back');
      
      if (this.backRoute) {
        if (typeof this.backRoute === 'string') {
          this.$router.push({ name: this.backRoute });
        } else {
          this.$router.push(this.backRoute);
        }
      } else {
        // 默认返回上一页
        this.$router.go(-1);
      }
    },
    
    // 处理面包屑点击
    handleBreadcrumbClick(item) {
      this.$emit('breadcrumb-click', item);
      
      if (item.to) {
        if (typeof item.to === 'string') {
          this.$router.push({ name: item.to });
        } else {
          this.$router.push(item.to);
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.secondary-page-header {
  
  background: #fbf6ee;
  // 页面头部区域
  .header-section {
    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 28px;
      border-bottom: 1px solid #f7ecdd;

      .title-section {
        .page-title {
          margin: 0;
          display: flex;
          align-items: center;
          gap: 12px;

          .title-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);

            i {
              color: white;
              font-size: 18px;
            }
          }

          .title-text {
            .main-title {
              font-size: 20px;
              font-weight: 600;
              color: #2c3e50;
              margin: 0 0 2px 0;
              display: block;
            }

            .page-subtitle {
              margin: 0;
              color: #7f8c8d;
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }
    }


    // 面包屑导航 - 按钮风格
    .breadcrumb-section {
      padding: 16px 28px;
      border-bottom: 1px solid #f7ecdd;

      .breadcrumb-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .breadcrumb-btn {
          height: 28px;
          padding: 0 12px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 13px;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 6px;

          i {
            font-size: 12px;
          }

          // 可点击的面包屑按钮
          &.clickable-btn {
            background: white;
            border: 1px solid #e8dcc0 !important;
            color: #8b7355;

            &:hover {
              background: #fcfaf7 !important;
              border-color: #D7A256 !important;
              color: #D7A256 !important;
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(215, 162, 86, 0.15);
            }

            &:active {
              transform: translateY(0);
            }
          }

          // 当前页面按钮（不可点击）
          &.current-btn {
            background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
            border: 1px solid #D7A256 !important;
            color: white !important;
            cursor: default;
            box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);

            &:hover {
              background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
              border-color: #D7A256 !important;
              color: white !important;
              transform: none !important;
            }

            /deep/ (.el-button.is-disabled){
              background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
              border-color: #D7A256 !important;
              color: white !important;
            }
          }
        }

        .breadcrumb-separator {
          color: #c0c4cc;
          font-size: 12px;
          margin: 0 4px;
          display: flex;
          align-items: center;
          opacity: 0.6;
        }
      }
    }
  }
}
</style> 