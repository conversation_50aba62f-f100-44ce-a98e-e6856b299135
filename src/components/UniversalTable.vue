<template>
  <div class="universal-table-container">
    <!-- 页面标题和操作区域 -->
     <!-- v-if="!noHeader" -->
    <div  class="header-section">
      <div v-if="!noHeader" class="header-top">
        <div class="title-section">
          <h1 class="page-title">
            <div  v-if="!noHeader" class="title-icon">
              <!-- <i :class="titleIcon || 'el-icon-data-analysis'"></i> -->
              <el-icon><Grid /></el-icon>
            </div>
            <div   v-else class="title-icon1">
              <el-icon><Grid /></el-icon>
            </div>
            <div v-if="!noHeader" class="title-text">
              <span class="main-title">{{ title }}</span>
              <p class="page-subtitle">{{ subtitle }}</p>
            </div>
            <div v-else class="title-text">
              <span class="main-title1">{{ title }}</span>
              <p class="page-subtitle1">{{ subtitle }}</p>
            </div>
          </h1>
        </div>
        <div class="action-section2">
          <slot name="headerActions">
            <el-button
              v-if="showAddButton"
              type="primary"
              @click="handleAdd"
              class="add-btn primary-btn"
            >
              <el-icon style="margin-right: 3px;">
                <Plus />
              </el-icon>
              {{ addButtonText }}
            </el-button>
          </slot>
        </div>
      </div>
         <!-- 面包屑导航 - 按钮风格 -->
         <div class="breadcrumb-section" v-if="showBreadcrumb && breadcrumbItems.length > 0">
        <div class="breadcrumb-buttons">
          <template v-for="(item, index) in breadcrumbItems">
            <!-- 可点击的面包屑按钮 -->
            <el-button 
              v-if="item.to"
              :key="`btn-${index}`"
              @click="handleBreadcrumbClick(item)"
              class="breadcrumb-btn clickable-btn"
              size="small"
            >
              <el-icon v-if="item.icon" style="margin-right: 3px;">
                <component :is="item.icon" />
              </el-icon>
              {{ item.text }}
            </el-button>
            
            <!-- 当前页面按钮（不可点击） -->
            <el-button 
              v-else
              :key="`current-${index}`"
              class="breadcrumb-btn current-btn"
              size="small"
              disabled
            >
              <el-icon v-if="item.icon" style="margin-right: 3px;">
                <component :is="item.icon" />
              </el-icon>
              {{ item.text }}
            </el-button>
            
            <!-- 分隔符箭头 -->
            <el-icon 
              v-if="index < breadcrumbItems.length - 1" 
              :key="`separator-${index}`"
              class="breadcrumb-separator"
            >
              <ArrowRight />
            </el-icon>
          </template>
        </div>
      </div>
      <!-- 搜索表单区域 -->
      <div :class="noHeader?'search-section1':'search-section2'"  v-if="showSearchForm">
        <NewSearch
          :searchForm="searchParams"
          :labelWidth="searchLabelWidth"
          :searchFormTemp="searchFormConfig"
          @handleSearch="handleSearch"
          @normalResetQuery="handleReset"
        />
        <div v-if="noHeader" class="action-section1">
          <slot name="headerActions">
            <el-button
              v-if="showAddButton"
              type="primary"
              @click="handleAdd"
            >
              <!-- <el-icon style="margin-right: 3px;">
                <Plus />
              </el-icon> -->
              {{ addButtonText }}
            </el-button>
          </slot>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div v-if="tableData.length > 0" class="table-wrapper">
        <el-table
          :data="tableData"
          stripe
          v-hover
          class="modern-table"
          style="width: 100%"
          v-loading="loading"
          :element-loading-text="loadingText"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(251, 246, 238, 0.8)"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          @row-click="handleRowClick"
        >
          <!-- 选择列 -->
          <el-table-column
            v-if="showSelection"
            type="selection"
            width="55"
            align="center"
          />
          
          <!-- 动态生成的列 -->
          <el-table-column
            v-for="column in columns"
            :key="column.prop"
            :align="column.align || 'center'"
            :prop="column.prop"
            :width="column.width"
            :min-width="column.minWidth"
            :label="column.label"
            :sortable="column.sortable"
            :fixed="column.fixed"
            :show-overflow-tooltip="column.showOverflowTooltip"
          >
            <template #default="scope">
              <slot 
                :name="column.prop" 
                :row="scope.row" 
                :column="column" 
                :$index="scope.$index"
              >
                <span v-if="!column.render">{{ getColumnValue(scope.row, column) }}</span>
                
                <component 
                  v-else
                  :is="getRenderComponent(scope.row, column, scope.$index)"
                />
              </slot>
            </template>
          </el-table-column>
          
          <!-- 操作列 -->
          <el-table-column
            v-if="showActions && actions.length > 0"
            align="center"
            header-align="center"
            label="操作"
            :width="actionColumnWidth"
            :fixed="actionColumnFixed"
          >
            <template #default="scope">
              <div class="action-buttons">
                <template v-for="action in actions">
                  <el-button
                    v-if="!action.hidden || !action.hidden(scope.row)"
                    :key="action.key"
                    :size="action.size || 'mini'"
                    :plain="action.plain !== false"
                    :disabled="action.disabled && action.disabled(scope.row)"
                    @click="handleAction(action, scope.row, scope.$index)"
                    :class="['action-btn', action.class]"
                  >
                    <!-- <i v-if="action.icon" :class="action.icon"></i> -->
                    <el-icon><component :is="action.icon" /></el-icon>
                    {{ action.label }}
                  </el-button>
                </template>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && tableData.length === 0" class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">
            <i :class="emptyIcon || titleIcon || 'el-icon-data-analysis'"></i>
            <el-icon><component :is="titleIcon" /></el-icon>
          </div>
          <h3>{{ emptyTitle || '暂无数据' }}</h3>
          <p>{{ emptyDescription || '暂时没有相关数据' }}</p>
        </div>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section" v-if="showPagination && total > 0">
      <Pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pageData="paginationData"
        :total="total"
        :layout="paginationLayout"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import SearchForm from './SearchForm.vue'
import Pagination from './Pagination.vue'
import { User, House, Document, ArrowRight,DataBoard, Back,Plus,Grid } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const props = defineProps({
  // 页面标题相关
  title: {
    type: String,
    default: '数据列表'
  },
  subtitle: {
    type: String,
    default: ''
  },
  titleIcon: {
    type: String,
    default: 'el-icon-data-analysis'
  },
  noHeader:{
    type: Boolean,
    default: false
  },
  // 表格数据
  tableData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: '加载中...'
  },
  
  // 列配置
  columns: {
    type: Array,
    required: true
  },
  
  // 操作按钮配置
  actions: {
    type: Array,
    default: () => []
  },
  showActions: {
    type: Boolean,
    default: true
  },
  actionColumnWidth: {
    type: [String, Number],
    default: 320
  },
  actionColumnFixed: {
    type: String,
    default: 'right'
  },
  
  // 搜索表单配置
  showSearchForm: {
    type: Boolean,
    default: true
  },
  searchFormConfig: {
    type: Array,
    default: () => []
  },
  searchParams: {
    type: Object,
    default: () => ({})
  },
  searchLabelWidth: {
    type: String,
    // default: '120px'
  },
  
  // 分页配置
  showPagination: {
    type: Boolean,
    default: true
  },
  paginationData: {
    type: Object,
    default: () => ({
      pageSize: 10,
      pageNum: 1
    })
  },
  total: {
    type: Number,
    default: 0
  },
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  
  // 其他功能配置
  showSelection: {
    type: Boolean,
    default: false
  },
  showAddButton: {
    type: Boolean,
    default: true
  },
  addButtonText: {
    type: String,
    default: '新增'
  },
  showAddInEmpty: {
    type: Boolean,
    default: true
  },
   // 返回路由
  backRoute: {
    type: [String, Object],
    default: null
  },
  // 空状态配置
  emptyTitle: {
    type: String,
    default: ''
  },
  emptyDescription: {
    type: String,
    default: ''
  },
  emptyIcon: {
    type: String,
    default: ''
  },
   // 是否显示面包屑
  showBreadcrumb: {
    type: Boolean,
    default: false
  },
  // 面包屑数据
  breadcrumbItems: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'action-click', 'search', 'reset', 'add', 'size-change', 'current-change', 'selection-change', 'sort-change', 'row-click'
])

// 方法全部用普通函数写法
function getColumnValue(row, column) {
  const keys = column.prop.split('.')
  let value = row
  for (let key of keys) {
    value = value ? value[key] : ''
  }
  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(value, row, column)
  }
  return value
}

function getRenderComponent(row, column, index) {
  if (column.render && typeof column.render === 'function') {
    return column.render(row, column, index)
  }
  return null
}

function handleAction(action, row, index) {
  emit('action-click', {
    action: action,
    row,
    index,
    actionConfig: action
  })
}

// 处理搜索
function handleSearch(searchData) {
  emit('search', searchData);
}

// 处理重置
function handleReset() {
  emit('reset');
}

// 处理新增
function handleAdd() {
  emit('add');
}

// 处理分页大小变化
function handleSizeChange(size) {
  emit('size-change', size);
}

// 处理页码变化
function handleCurrentChange(page) {
  emit('current-change', page);
}

// 处理选择变化
function handleSelectionChange(selection) {
  emit('selection-change', selection);
}

// 处理排序变化
function handleSortChange(sortInfo) {
  emit('sort-change', sortInfo);
}

// 处理行点击
function handleRowClick(row, column, event) {
  emit('row-click', row, column, event);
}
 // 处理返回操作
 function handleBack() {
      emit('back');
      
      if (backRoute) {
        if (typeof backRoute === 'string') {
          router.push({ name: backRoute });
        } else {
          router.push(backRoute);
        }
      } else {
        // 默认返回上一页
        router.go(-1);
      }
    } 
    // 处理面包屑点击
    function  handleBreadcrumbClick(item) {
      emit('breadcrumb-click', item);
      
      if (item.to) {
        if (typeof item.to === 'string') {
          router.push({ name: item.to });
        } else {
          router.push(item.to);
        }
      }
    }
</script>

<style lang="less" scoped>
.universal-table-container {
  min-height: 100vh;
  background: white;
  overflow: hidden;

  // 页面头部和搜索区域
  .header-section {
    .header-top {
      background-color: #fbf6ee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 28px 28px;

      .title-section {
        .page-title {
          margin: 0;
          display: flex;
          align-items: center;
          gap: 12px;

          .title-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);

            i {
              color: white;
              font-size: 18px;
            }
          }
          .title-icon1 {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);

            i {
              color: white;
              font-size: 14px;
            }
          }

          .title-text {
            .main-title {
              font-size: 20px;
              font-weight: 600;
              color: #2c3e50;
              margin: 0 0 2px 0;
              display: block;
            }

            .page-subtitle {
              margin: 0;
              color: #7f8c8d;
              font-size: 12px;
              font-weight: 400;
            }
            .main-title1 {
              font-size: 14px;
              font-weight: 600;
              color: #2c3e50;
              margin: 0 0 2px 0;
              display: block;
            }

            .page-subtitle1 {
              margin: 0;
              color: #7f8c8d;
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }

      .action-section2 {
        .primary-btn {
          height: 32px;
          padding: 0 16px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 13px;
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
          border: none;
          box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(215, 162, 86, 0.4);
          }

          i {
            margin-right: 6px;
            font-size: 12px;
          }
        }
      }
    }

    .search-section2 {
      padding: 24px 0;
      padding-left: 0px;
      border-top: 1px solid #f7ecdd;
      border-bottom: 1px solid #f7ecdd;
      padding-left: 30px;
      padding-top:40px;
    }
    .search-section1 {
      padding: 24px 0;
      padding-left: 0px;
      // border-top: 1px solid #f7ecdd;
      border-bottom: 1px solid #f7ecdd;
      padding-left: 30px;
      display: flex;
      align-items: center;
      .action-section1 {
        margin-left: -30px;
        display: flex;
        align-items: center;
        margin-top: -18px;
        .primary-btn {
          height: 32px;
          padding: 0 16px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 13px;
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
          border: none;
          box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
          transition: all 0.3s ease;

          // &:hover {
          //   transform: translateY(-1px);
          //   box-shadow: 0 4px 12px rgba(215, 162, 86, 0.4);
          // }

          i {
            margin-right: 6px;
            font-size: 12px;
          }
        }
      }
    }
  }
 // 面包屑导航 - 按钮风格
 .breadcrumb-section {
      padding: 16px 28px;
      border-bottom: 1px solid #f7ecdd;
      background-color: #fbf6ee;
      border-top: 1px solid #f7ecdd;
      .breadcrumb-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .breadcrumb-btn {
          height: 28px;
          padding: 0 12px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 13px;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 6px;

          i {
            font-size: 12px;
          }

          // 可点击的面包屑按钮
          &.clickable-btn {
            background: white;
            border: 1px solid #e8dcc0 !important;
            color: #8b7355;

            &:hover {
              background: #fcfaf7 !important;
              border-color: #D7A256 !important;
              color: #D7A256 !important;
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(215, 162, 86, 0.15);
            }

            &:active {
              transform: translateY(0);
            }
          }

          // 当前页面按钮（不可点击）
          &.current-btn {
            background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
            border: 1px solid #D7A256 !important;
            color: white !important;
            cursor: default;
            box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);

            &:hover {
              background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
              border-color: #D7A256 !important;
              color: white !important;
              transform: none !important;
            }

            /deep/ (.el-button.is-disabled){
              background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%) !important;
              border-color: #D7A256 !important;
              color: white !important;
            }
          }
        }

        .breadcrumb-separator {
          color: #c0c4cc;
          font-size: 12px;
          margin: 0 4px;
          display: flex;
          align-items: center;
          opacity: 0.6;
        }
      }
  }
  // 表格区域
  .table-section {
    .table-wrapper {
      .modern-table {
        :deep(.el-table__header-wrapper) {
          .el-table__header {
            th {
              color: #2c3e50;
              font-weight: 600;
              font-size: 14px;
            }
          }
        }

        :deep(.el-table__body-wrapper) {
          .el-table__row {
            transition: all 0.3s ease;

            &:hover {
              background: rgba(215, 162, 86, 0.05) !important;
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(215, 162, 86, 0.1);
            }

            td {
              border-bottom: 1px solid #f7ecdd;
            }
          }
        }

        :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
          background: #fefdfb;
        }
      }

      // 操作按钮样式
      .action-buttons {
        display: flex;
        gap: 4px;
        justify-content: center;
        align-items: center;

        .action-btn {
          border-radius: 4px;
          font-size: 11px;
          padding: 4px 8px;
          transition: all 0.3s ease;
          font-weight: 500;
          min-width: 56px;
          height: 28px;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }

          i {
            margin-right: 3px;
            font-size: 11px;
          }

          &.config-btn {
            color: #D7A256;
            border-color: rgba(215, 162, 86, 0.3);
            background: rgba(215, 162, 86, 0.1);

            &:hover {
              background: #D7A256;
              color: white;
              border-color: #D7A256;
            }
          }

          &.edit-btn {
            color: #D7A256;
            border-color: rgba(215, 162, 86, 0.3);
            background: rgba(215, 162, 86, 0.1);

            &:hover {
              background: #D7A256;
              color: white;
              border-color: #D7A256;
            }
          }

          &.version-btn {
            color: #D7A256;
            border-color: rgba(215, 162, 86, 0.3);
            background: rgba(215, 162, 86, 0.1);

            &:hover {
              background: #D7A256;
              color: white;
              border-color: #D7A256;
            }
          }

          &.delete-btn {
            color: #f56c6c;
            border-color: rgba(245, 108, 108, 0.3);
            background: rgba(245, 108, 108, 0.1);

            &:hover {
              background: #f56c6c;
              color: white;
              border-color: #f56c6c;
            }
          }

          &.primary-btn {
            color: #67c23a;
            border-color: rgba(103, 194, 58, 0.3);
            background: rgba(103, 194, 58, 0.1);

            &:hover {
              background: #67c23a;
              color: white;
              border-color: #67c23a;
            }
          }
        }
      }
    }

    // 空状态
    .empty-state {
      padding: 80px 20px;
      text-align: center;

      .empty-content {
        .empty-icon {
          width: 80px;
          height: 80px;
          background: rgba(215, 162, 86, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 20px;

          i {
            font-size: 40px;
            color: #D7A256;
          }
        }

        h3 {
          margin: 0 0 8px 0;
          color: #2c3e50;
          font-size: 18px;
          font-weight: 600;
        }

        p {
          margin: 0 0 24px 0;
          color: #7f8c8d;
          font-size: 14px;
        }

        .primary-btn {
          background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
          border: none;
          box-shadow: 0 4px 12px rgba(215, 162, 86, 0.3);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(215, 162, 86, 0.4);
          }
        }
      }
    }
  }

  // 分页区域
  .pagination-section {
    padding: 20px 28px;
    border-top: 1px solid #f7ecdd;
  }
}
</style> 