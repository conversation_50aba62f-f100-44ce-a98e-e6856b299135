<template>
  <div class="input-dt ellipsis-dt" v-if="!showTip">
    {{ props.text }}
  </div>
  <el-tooltip effect="dark" :content="props.text" placement="top" v-else>
    <div class="input-dt ellipsis-dt">
      {{ props.text }}
    </div>
  </el-tooltip>
</template>

<script lang="ts" setup>
import { isTextOverFlow } from '@/utils'
const props = withDefaults(
  defineProps<{ text?: string; emptySign?: string }>(),
  {
    text: '',
    emptySign: ''
  }
)
const showTip = props.text ? isTextOverFlow(props.text, 199) : false
</script>
