import type { SelectLits } from '@/components/types'
import { useAxios } from '@/hooks/http'

export interface TenantUser {
  email: string
  funcId: string
  nickName: string
  phone: string
  userId: string
  userName: string
}

/**
 * 获取租户后台用户列表
 */
export const getTenantUsers = async (): Promise<TenantUser[]> => {
  const { status, data } = await useAxios('/web/bsc/tenant/getTenantUsers', {}, { loading: false })
  if (status != 0) return []
  return data
}

/**
 * 获取用户权限位列表
 */
interface Auths {
  auth: string[]
  userEnName: string // ex: zsdt
}

export const getUsersAuth = async (): Promise<Auths> => {
  const { data, status } = await useAxios('/web/bsc/getWebUserInfo', {})
  if (status != 0) {
    return {
      auth: [],
      userEnName: "'",
    }
  }
  return {
    auth: data.funcAuthDTO.map((e: any) => e.authCode),
    userEnName: data.userName,
  }
}

// 查询搜索字典配置
export const getSearchDicItems = async (dicCodeList: string[]): Promise<Record<string, SelectLits[]>> => {
  const { status, data } = await useAxios('/web/bsc/dic/getDicItems', { dicCodeList })
  if (status != 0) return {}
  return data
}

/**
 * 获取租户配置
 * @returns
 */
export const getTenant = async (): Promise<Record<string, any>> => {
  const res = await useAxios('/web/gpcbstenant/getTenantConfig', undefined)
  if (res.status === 0) return res.data
  return {}
}

// 获取租户信息
export const getTenantInit = async (): Promise<Record<string, any>> => {
  const res = await useAxios('/web/bsc/tenant/init', undefined)
  if (res.status === 0) return res.data
  return {}
}

/**
* 文件上传
* param {form-data}
* 
* file: blob
* flleType: file
 * */
export interface RemoteFile {
  absolutePath: string
  foreignPath: string
  fileId: string
  sourcePath: string
}

export const upload = (param: any) => useAxios<any, RemoteFile>('/web/upload/uploadFile', param)
