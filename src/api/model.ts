
import { useAxios } from '@/hooks/http'
/**
 * @description 新增更新/web/model/getById
 */
export const modelSave = async (data: any) => await useAxios('/web/model/save', data)

/**
 * @description 删除模型
 */
export const modelDelete = async (id: string) => await useAxios('/web/model/delete', {id})


// 设置通用模型

export const modelsetCommonModel = async (id: string) => await useAxios('/web/model/setCommonModel', {id})

// 模型详情
export const modelGetById = async (data: any) => await useAxios('/web/model/getById', data)

//可用模型列表
export const modelList = async () => await useAxios('/web/model/list')
//可以模型保存
export const robotmodelSave = async (data: any) => await useAxios('/web/robotmodel/save', data)
// 模型列表
export const robotmodelList = async (data: any) => await useAxios('/web/robotmodel/list', data)
