
import { useAxios } from '@/hooks/http'
/**
 * @description 场景列表
 */
export const trainScenePage = async ( params: any ) => await useAxios('/web/train/scene/page',params)

/**
 * @description 删除
 */
export const trainDelete = async (id: string) => await useAxios('/web/train/scene/delete', {id})

// 详情
export const sceneDetail = async (data: any) => await useAxios('/web/train/scene/detail', data)

//可用模型列表
export const modelList = async () => await useAxios('/web/model/list')
//新增保存
export const trainSceneCreate = async (data: any) => await useAxios('/web/train/scene/create', data)
//更新保存
export const trainSceneUpdate = async (data: any) => await useAxios('/web/train/scene/update', data)
// 模型列表
export const robotmodelList = async (data: any) => await useAxios('/web/robotmodel/list', data)
// 模型列表
export const trainPage = async (data: any) => await useAxios('/web/train/score/page', data)
