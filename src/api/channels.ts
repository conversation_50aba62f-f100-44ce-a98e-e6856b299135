import { useAxios } from '@/hooks/http'

export interface Channel {
  id?: string
  name?: string
  status?: string
  robotId?: string
  remark?: string
}


// 所属项目列表
export const projectList = async (name?: string, id?: string): Promise<Record<string, any>[]> => {
  const { status, data } = await useAxios('/web/maas/project/list', {
      name: name ?? '',
      code: '',
      statusList: [1, 3],
      id: id ?? '',
  })

  if (status != 0) return []
  return data as Record<string, any>[]
}

// 渠道-新增
// export const add = (data: Channel) => useAxios('/web/channel/add', data)
export const add = (data: Channel) => useAxios('/web/maas/channel/create', data)


// 渠道-编辑
// export const update = (data: Channel) => useAxios('/web/channel/update', data)
export const update = (data: Channel) => useAxios('/web/maas/channel/update', data)

//渠道状态更新 //
export const updateStatus = (data) => useAxios('web/maas/channel/status/update', data)

// 渠道-删除
export const delChannel = (channelId: string) => useAxios('/web/maas/channel/delete', { channelId })

// 删除模型-删除
export const del = (id: string) => useAxios('/web/channel/delete', { id })

//智能体状态-启用禁用
export const channelrobotUpdate = (params: any) => useAxios('/web/maas/channelrobot/updateStatus', params)

// 智能体-列表
// 默认查 已发布和编辑中的智能体
export const robotPage = async (name?: string, id?: string): Promise<Record<string, any>[]> => {
  const { status, data } = await useAxios('/web/robot/page', {
    pageNum: 1,
    pageSize: 1000,
    param: {
      name: name ?? '',
      code: '',
      statusList: [1, 3],

      // todo need check
      id: id ?? '',
    },
  })

  if (status != 0) return []
  return data.list as Record<string, any>[]
}

// 渠道基础配置-根据渠道ID查询
// export const basicGet = (channelId: string) => useAxios('/web/channelbasicconfig/getChannelBasicConfigByChannelId', { channelId })
export const basicGet = (channelCode: string) => useAxios('/web/maas/channel/find', { channelCode })

//  渠道基础配置-新增
export const basicAdd = (params: any) => useAxios('/web/maas/channel/update', params)
//  渠道智能体列表
export const channelrobotPage = (params: any) => useAxios('/web/maas/channelrobot/page', params)

// 渠道基础配置-编辑
export const basicUpdate = (params: any) => useAxios('/web/channelbasicconfig/update', params)

// 渠道基础配置删除
export const basicDel = (params: any) => useAxios('/web/channelbasicconfig/delete', params)

// 渠道常用卡片-列表
export const cardList = (params: any) => useAxios('/web/channelcommoncard/page', params)

//渠道常用卡片-新增
export const cardAdd = (params: any) => useAxios('web/channelcommoncard/add', params)

//渠道常用卡片-编辑
export const cardUpdate = (params: any) => useAxios('web/channelcommoncard/update', params)

// 渠道智能体配置-删除
export const channelrobotDelete = (id: string) => useAxios('/web/maas/channelrobot/delete', { id })
// 渠道常用卡片-删除
export const cardDel = (id: string) => useAxios('/web/channelcommoncard/deleteChannelCommonCard', { id })

// 渠道常用卡片-根据ID查询
export const cardDetail = (id: string) => useAxios('/web/channelcommoncard/getChannelCommonCardById', { id })

// 渠道猜你想问-列表
export const qaPage = (params: any) => useAxios('/web/channelguessquestion/page', params)

// 渠道猜你想问-新增
export const qaAdd = (params: any) => useAxios('/web/channelguessquestion/add', params)

// 渠道猜你想问-编辑
export const qaUpdate = (params: any) => useAxios('/web/channelguessquestion/update', params)

// 渠道猜你想问-删除
export const qaDel = (id: string) => useAxios('/web/channelguessquestion/deleteChannelGuessQuestion', { id })

// 渠道猜你想问-根据ID查询
export const qaDetail = (id: string) => useAxios('/web/channelguessquestion/getChannelGuessQuestionById', { id })

// 渠道常用短语-列表
export const wordPage = (param: any, pageNum: number, pageSize: number = 10) =>
  useAxios('/web/channelcommonphrase/page', { param, pageSize, pageNum })

// 渠道常用短语-新增
export const wordAdd = (param: any) => useAxios('/web/channelcommonphrase/add', param)

// 渠道常用短语-编辑
export const wordUpdate = (param: any) => useAxios('/web/channelcommonphrase/update', param)

// 渠道常用短语-删除
export const wordDel = (id: string) => useAxios('/web/channelcommonphrase/deleteChannelCommonPhrase', { id })

// 渠道常用短语-根据ID查询
export const wordDetail = (id: string) => useAxios('/web/channelcommonphrase/getChannelCommonPhraseById', { id })

/**
 * @description 获取FuncId
 */
export const getFuncId = async () => await useAxios('/web/knowledgeconfig/getFuncId')


/**
 * @description 渠道机器人列表
 */
export const maasRobotList = async ( params: any ) => await useAxios('/web/maas/robot/list',params)
// 渠道机器人列表
export const channelrobotList = async (params: any) => await useAxios('/web/maas/channelrobot/list',params)
// 渠道机器人提示词新增
export const channelRobotpromptAdd = async (params: any) => await useAxios('/web/maas/channelrobot/prompt/create',params)
// 渠道机器人提示词编辑
export const channelRobotpromptUpdate = async (params: any) => await useAxios('/web/maas/channelrobot/prompt/update',params)
// 渠道机器人提示词编辑
export const channelrobotPromptDelete = async (id: any) => await useAxios('/web/maas/channelrobot/prompt/delete',{ id })

// 智能体机器人新增
export const robotAdd = async (params: any) => await useAxios('/web/maas/channelrobot/add',params)

/**
 * @description 保存转人工配置
 */
export const saveManualConfig = async (data: any) => await useAxios('/web/channelbasicconfig/manual/service/update', data)

/**
 * @description 查询转人工配置
 */
export const getManualConfig = async (data: any) => await useAxios('/web/channelbasicconfig/manual/service/find', data)


// 敏感词列表

export const channelsensitivewordsPage = async (data: any) => await useAxios('/web/maas/channelsensitivewords/page', data)

// 新增渠道敏感词
export const channelsensitivewordsAdd = async (data: any) => await useAxios('/web/maas/channelsensitivewords/save', data)

//删除渠道敏感词
export const deleteSensitiveWords = async (data: any) => await useAxios('/web/maas/channelsensitivewords/delete', data)

//流程下拉框管理
export const flowList = async (data: any) => await useAxios('/web/flow/list', data)

//渠道编辑接口
export const updateChannel = async (data: any) => await useAxios('/web/maas/channel/updateChannel', data)

/**
 * @description 渠道列表
 */
export const searchChannelPage = async (data: any) => await useAxios('/web/maas/channel/page', data)

/**
 * @description 提示词列表
 */
export const getPromptList = async (data: any) => await useAxios('/web/maas/channelrobot/prompt/page', data)
