import http from '@/utils/httpService'
import { rootPath } from '@/utils/globalParam'

/**
 * 获取应用服务器列表
 * @param params 查询参数
 * @returns Promise
 */
export function getAppServerList(params: any) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/listAll`, params)
}

/**
 * 分页查询应用服务器
 * @param params 查询参数
 * @returns Promise
 */
export function getAppServerPage(params: any) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/list`, params)
}

/**
 * 根据ID查询应用服务器详情
 * @param appServerId 应用服务器ID
 * @returns Promise
 */
export function getAppServerDetail(appServerId: string) {
  return http.Axios.get(`${rootPath}/api/mcp/appserver/get?appServerId=${appServerId}`)
}

/**
 * 根据编码查询应用服务器详情
 * @param appServerCode 应用服务器编码
 * @returns Promise
 */
export function getAppServerDetailByCode(appServerCode: string) {
  return http.Axios.get(`${rootPath}/api/mcp/appserver/getByCode?appServerCode=${appServerCode}`)
}

/**
 * 新增应用服务器
 * @param data 应用服务器数据
 * @returns Promise
 */
export function createAppServer(data: any) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/add`, data)
}

/**
 * 更新应用服务器
 * @param data 应用服务器数据
 * @returns Promise
 */
export function updateAppServer(data: any) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/update`, data)
}

/**
 * 删除应用服务器
 * @param appServerId 应用服务器ID
 * @returns Promise
 */
export function deleteAppServer(appServerId: string) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/delete?appServerId=${appServerId}`)
}

/**
 * 批量删除应用服务器
 * @param appServerIds 应用服务器ID数组
 * @returns Promise
 */
export function batchDeleteAppServers(appServerIds: string[]) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/batchDelete`, { appServerIds })
}

/**
 * 更新应用服务器状态
 * @param appServerId 应用服务器ID
 * @param status 状态值
 * @returns Promise
 */
export function updateAppServerStatus(appServerId: string, status: number) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/updateStatus`, { appServerId, status })
}

/**
 * 获取可用应用服务器列表
 * @returns Promise
 */
export function getAvailableAppServers() {
  return http.Axios.get(`${rootPath}/api/mcp/appserver/available`)
}