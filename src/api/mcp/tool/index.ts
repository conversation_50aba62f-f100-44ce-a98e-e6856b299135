import http from '@/utils/httpService'
import { rootPath } from '@/utils/globalParam'

/**
 * 获取工具列表
 * @param params 查询参数
 * @returns Promise
 */
export function getToolList(params: any) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/listAll`, params)
}

/**
 * 分页查询工具
 * @param params 查询参数
 * @returns Promise
 */
export function getToolPage(params: any) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/list`, params)
}

/**
 * 根据ID查询工具详情
 * @param toolId 工具ID
 * @returns Promise
 */
export function getToolDetail(toolId: string) {
  return http.Axios.get(`${rootPath}/api/mcp/tool/get?toolId=${toolId}`)
}

/**
 * 根据编码查询工具详情
 * @param toolCode 工具编码
 * @returns Promise
 */
export function getToolDetailByCode(toolCode: string) {
  return http.Axios.get(`${rootPath}/api/mcp/tool/getByCode?toolCode=${toolCode}`)
}

/**
 * 新增工具
 * @param data 工具数据
 * @returns Promise
 */
export function createTool(data: any) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/add`, data)
}

/**
 * 更新工具
 * @param data 工具数据
 * @returns Promise
 */
export function updateTool(data: any) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/update`, data)
}

/**
 * 删除工具
 * @param toolId 工具ID
 * @returns Promise
 */
export function deleteTool(toolId: string) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/delete?toolId=${toolId}`)
}

/**
 * 更新工具状态
 * @param toolId 工具ID
 * @param status 状态值
 * @returns Promise
 */
export function updateToolStatus(toolId: string, status: number) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/updateStatus`, { toolId, status })
}

/**
 * 根据服务器ID获取工具列表
 * @param serverId 服务器ID
 * @returns Promise
 */
export function getToolsByServerId(serverId: string) {
  return http.Axios.get(`${rootPath}/api/mcp/tool/getByServerId?serverId=${serverId}`)
}