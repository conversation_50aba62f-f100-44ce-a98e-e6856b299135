import http from '@/utils/httpService'
import { rootPath } from '@/utils/globalParam'

/**
 * 获取服务器列表
 * @param params 查询参数
 * @returns Promise
 */
export function getServerList(params: any) {
  return http.Axios.post(`${rootPath}/api/mcp/server/listAll`, params)
}

/**
 * 分页查询服务器
 * @param params 查询参数
 * @returns Promise
 */
export function getServerPage(params: any) {
  return http.Axios.post(`${rootPath}/api/mcp/server/list`, params)
}

/**
 * 根据ID查询服务器详情
 * @param serverId 服务器ID
 * @returns Promise
 */
export function getServerDetail(serverId: string) {
  return http.Axios.get(`${rootPath}/api/mcp/server/get?serverId=${serverId}`)
}

/**
 * 根据编码查询服务器详情
 * @param serverCode 服务器编码
 * @returns Promise
 */
export function getServerDetailByCode(serverCode: string) {
  return http.Axios.get(`${rootPath}/api/mcp/server/getByCode?serverCode=${serverCode}`)
}

/**
 * 新增服务器
 * @param data 服务器数据
 * @returns Promise
 */
export function createServer(data: any) {
  return http.Axios.post(`${rootPath}/api/mcp/server/add`, data)
}

/**
 * 更新服务器
 * @param data 服务器数据
 * @returns Promise
 */
export function updateServer(data: any) {
  return http.Axios.post(`${rootPath}/api/mcp/server/update`, data)
}

/**
 * 删除服务器
 * @param serverId 服务器ID
 * @returns Promise
 */
export function deleteServer(serverId: string) {
  return http.Axios.post(`${rootPath}/api/mcp/server/delete?serverId=${serverId}`)
}

/**
 * 更新服务器状态
 * @param serverId 服务器ID
 * @param status 状态值
 * @returns Promise
 */
export function updateServerStatus(serverId: string, status: number) {
  return http.Axios.post(`${rootPath}/api/mcp/server/updateStatus`, { serverId, status })
}

/**
 * 获取可用服务器列表
 * @returns Promise
 */
export function getAvailableServers() {
  return http.Axios.get(`${rootPath}/api/mcp/server/available`)
}