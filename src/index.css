@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-dt {
    color: var(--el-color-primary);
  }
  .bg-dt {
    background-color: var(--el-color-primary);
  }
  .input-dt {
    width: var(--el-from-input-width);
  }
  .input2-dt {
    width: var(--el-from-input2-width);
  }
  .ellipsis-dt {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .bg-primary {
    background-color: var(--el-color-primary);
  }
  .bg-dt {
    background-color: var(--el-color-primary);
  }
  .border-color-dt {
    border-color: var(--el-color-primary);
  }
}
