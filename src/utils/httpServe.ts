import axios from 'axios'
import type { Method, AxiosRequestConfig } from 'axios'
import useSystemStore from '@/stores/system'
// import { monitorAxios } from '@dt/dt-monitor'

/**
 * Token 失效错误显示标记,防止重复显示错误
 */
let G_HasErr = false

export interface DtHttpConfig {
  customPrefix: string
  baseURL?: string
  headers?: Record<string, any>
  method?: Method
  isUpload?: boolean
  isExport?: boolean
  noLoading?: boolean
  noError?: boolean
}

interface DtHttpRet {
  status: number
  data: any
  msg: string
  fileName?: string
}
/**
 *
 * @param {string} url
 * @param {any} data
 * @param {DtHttpConfig} config
 * @returns
 */
export const httpServe = async (url: string, data?: any, config?: DtHttpConfig): Promise<DtHttpRet> => {
  const { system } = useSystemStore()
  // const defaultBase = import.meta.env.MODE === 'dev' ? '/dev-api' + import.meta.env.VITE_APP_API : import.meta.env.VITE_APP_API
  const baseURL = config && config.baseURL ? config.baseURL : import.meta.env.VITE_APP_API
  const axiosConfig: AxiosRequestConfig = {
    baseURL,
    timeout: 60000,
    headers: {
      'Access-Control-Allow-Origin': '*',
      access_token: system.access_token,
      funcId: system.funcId,
      tenantId: system.tenantId,
    },
    method: 'post',
    data,
    url,
  }
  // 是否显示全局loading
  let isLoading = true
  // 默认显示标题
  let showErr = true

  if (config) {

    if (config.baseURL) {
      //axiosConfig.baseURL = import.meta.env.MODE === 'dev' ? `/dev-api${config.baseURL}` : config.baseURL
      axiosConfig.baseURL = config.baseURL
    }

    if (config.headers) axiosConfig.headers = { ...axiosConfig.headers, ...config.headers }
    if (config.method && config.method === 'get') {
      axiosConfig.method = 'get'
      axiosConfig.params = data
      axiosConfig.data = null
    }
    if (config.isUpload) {
      const fd = new FormData()
      for (const o in data) {
        fd.append(o, data[o])
      }
      axiosConfig.data = fd
      axiosConfig.headers = {
        ...axiosConfig.headers,
        'Content-Type': 'multipart/form-data',
      }
    }
    if (config.isExport) axiosConfig.responseType = 'blob'
    if (config.noLoading) isLoading = false
    if (config.noError) showErr = false
  }

  const Axios = axios.create()

  // 响应拦截器
  Axios.interceptors.response.use(async (response) => {
      // monitorAxios(response);//监听axios的返回状态
    const { system, updateToken } = useSystemStore()
    const status = response.data.resp_code ?? Number(response.data.status)
    if (status === 0 || status === 200) {
      return response
    }

    if ([401, 400, 402].includes(status)) {
      // accessToken 失效
      // 通知父系统刷新accessToken
      const parent = window.parent as any
      if (parent && parent.kbcChangeToken) {
        await parent.kbcChangeToken(system.access_token)
        const newAccessToken = sessionStorage.getItem('LoginAccessToken')
        if (newAccessToken && newAccessToken !== system.access_token) {
          // accessToken更新成功
          updateToken(newAccessToken)
          // 重新请求
          if (axiosConfig.headers) axiosConfig.headers.access_token = newAccessToken
          return await axios(axiosConfig)
        }
      }
      // accessToken更新失败
      if(status!=401){
        if (showErr && !G_HasErr) {
          G_HasErr = true
          // 显示错误信息
          ElMessage({
            type: 'error',
            duration: 0,
            message: '登陆信息失效,请退出系统重新登陆!',
          })
        }
        return response
      }
    
    } else {
      if (config && config.isExport) return response
      // 其他错误
      // 显示错误信息
      const err = response.data.resp_msg ?? response.data.msg
      console.log('api请求错误:\nurl:%s\ndata:%o', response.request.responseURL, response.data)
      if (showErr) {
        ElMessage.error(err.slice(0, 2000))
      }
    }
    return response
  })

  // 显示loading
  let loadingInstance = undefined
  if (isLoading) loadingInstance = ElLoading.service({ lock: true, text: '加载中...' })

  const retData: DtHttpRet = {
    status: 1,
    data: null,
    msg: '请求报错或请求超时,请稍后重试!',
  }

  try {
    const res = await Axios(axiosConfig)
    if (loadingInstance) loadingInstance.close()
    if (config && config.isExport) {
      retData.status = 0
      if (res.headers['content-disposition']) {
        retData.fileName = decodeURIComponent(res.headers['content-disposition'].split('filename=')[1]) || ''
      } else {
        retData.fileName = ''
      }
      retData.data = res.data
      retData.msg = ''
    } else if (typeof res.data.resp_code !== 'undefined') {
      retData.status = res.data.resp_code
      retData.data = res.data.datas ?? null
      retData.msg = res.data.resp_msg
    } else if (typeof res.data.status !== 'undefined') {
      if (res.data.status === '200') {
        retData.status = 0
        retData.data = res.data.body ?? null
        retData.msg = res.data.msg
      } else {
        retData.status = 1
        retData.data = res.data.body ?? null
        retData.msg = res.data.msg
      }
    } else {
      retData.status = 2
      retData.data = res.data
      retData.msg = ''
    }
  } catch (error) {
    if (loadingInstance) loadingInstance.close()
    console.log(error)
    if (showErr) {
      // 显示错误信息
      ElMessage.error('请求报错或请求超时,请稍后重试!')
    }
  }
  return retData
}
