import { useUserStore } from '../stores/user'
import type { App } from 'vue'
/**
 * 按钮权限指令 v-auth
 * @param app vue 实例
 */
export const authDirective = (app: App<Element>): void => {
  app.directive('auth', {
    mounted(el: Element, binding) {
      const { hasAuth } = useUserStore()
      if (binding.value && el.parentNode) {
        const arr = binding.value.split(',')
        const authed = arr.some((key: string) => hasAuth(key))
        if (!authed) el.parentNode.removeChild(el)
      }
    },
  })
}
