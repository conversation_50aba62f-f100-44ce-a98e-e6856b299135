import { getSearchDicItems } from '@/api/index'
// 获取字典数据项
// dicCode 字典类型   force 是否强制刷新字典
let dicMap = sessionStorage.getItem('chatbot-dicMap') ? JSON.parse(sessionStorage.getItem('chatbot-dicMap')) : {}

export const getDicItemList = async (dicCodeList: string[], force = false) => {
  let arr = {}
  for (const i in dicCodeList) {
    if (!dicMap[dicCodeList[i]]) {
      return getDicItems(dicCodeList)
    } else {
      arr[dicCodeList[i]] = dicMap[dicCodeList[i]]
    }
  }
  return arr
}

const getDicItems = async (dicCodeList: string[]) => {
  let res = await getSearchDicItems(dicCodeList)
  if (res) {
    dicCodeList.forEach((item) => {
      dicMap[item] = res[item]
    })
    sessionStorage.setItem('chatbot-dicMap', JSON.stringify(dicMap))
  }
  return res
}
