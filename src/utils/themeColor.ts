const tintColor = (color: string, tint: number): string => {
  let red = parseInt(color.slice(0, 2), 16)
  let green = parseInt(color.slice(2, 4), 16)
  let blue = parseInt(color.slice(4, 6), 16)

  if (tint === 0) {
    // when primary color is in its rgb space
    return [red, green, blue].join(',')
  } else {
    red += Math.round(tint * (255 - red))
    green += Math.round(tint * (255 - green))
    blue += Math.round(tint * (255 - blue))

    return `#${red.toString(16)}${green.toString(16)}${blue.toString(16)}`
  }
}

/**
 * 设置系统主题颜色
 * @param {*} themeColor
 * @param {*} navTagColor
 */
export const setThemeColor = (
  themeColor: string,
  navTagColor: string
): void => {
  const el = document.documentElement
  /**
   * 主题颜色
   */
  el.style.setProperty('--el-color-primary', themeColor)
  // --el-color-primary-{i}
  for (let index = 1; index < 10; index++) {
    el.style.setProperty(
      `--el-color-primary-light-${index}`,
      tintColor(themeColor.replace('#', ''), Number((index / 10).toFixed(2)))
    )
  }
  /**
   * 表格 hover 颜色
   */
  el.style.setProperty('--el-fill-color-light', navTagColor)

  // set primary dark
  el.style.setProperty('--el-color-primary-dark-2', themeColor)
}
