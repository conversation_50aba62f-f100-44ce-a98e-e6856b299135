import axios from 'axios'
/**
 * 获取Url链接参数
 * @param {*} key 参数名称
 * @param {*} def 默认值
 * @returns
 */
export const getUrlParams = (key: string, def: string = '', url: string = ''): string => {
  if (!url) url = location.href

  const arr = url.split('?')
  const searchStr = arr.length > 1 ? arr[1] : arr[0]
  const searchParams = new URLSearchParams(searchStr)
  return searchParams.get(key) ?? def
}

/**
 * 下载文件
 * @param content 下载数据
 * @param fileName 下载文件名称
 */
export const exportData = (content: any, fileName = 'default.xlsx') => {
  const blob = new Blob([content])
  if ('download' in document.createElement('a')) {
    // 非IE下载
    const elink = document.createElement('a')
    elink.download = fileName
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href) // 释放URL 对象
    document.body.removeChild(elink)
  } else {
    // IE10+下载
    ;(navigator as any).msSaveBlob(blob, fileName)
  }
}

/**
 * 通过URL下载文件
 * @param url 链接
 * @param fileName 下载文件名称
 */
export const exportByUrl = async (url: string, fileName?: string) => {
  const res = await axios({
    url,
    method: 'GET',
    responseType: 'blob', // important
  })
  const pathname = new URL(url).pathname
  const paths = pathname.split('/')
  const fName = fileName || paths[paths.length - 1]
  exportData(res.data, fName)
}

/**
 * 字节单位转换
 * 注意: 会直接丢弃小数位
 * @param byte 字节 byte
 * @returns
 */
export const byteConvert = (byte: number): string => {
  const KB = 1024 * 1
  const MB = 1024 * KB
  const GB = 1024 * MB
  const TB = 1024 * GB
  if (byte >= TB) return `${Math.trunc(byte / TB)}TB`
  else if (byte >= GB && byte < TB) return `${Math.trunc(byte / GB)}GB`
  else if (byte >= MB && byte < GB) return `${Math.trunc(byte / MB)}MB`
  else if (byte >= KB && byte < MB) return `${Math.trunc(byte / KB)}KB`
  else return `${byte}B`
}

/**
 * 租户变更 删除session
 * @returns
 */
export const checkSession = () => {
  const newTenantId = getUrlParams('tenantId', 'T0001')
  if (!newTenantId) return
  const sTenantId = sessionStorage.getItem('cbs-gp-tenantId')
  if (!sTenantId) return
  const delKey: string[] = []
  if (newTenantId !== sTenantId) {
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i) || ''
      if (/^cbs-gp-/.test(key)) delKey.push(key)
    }
  }
  delKey.forEach((e) => sessionStorage.removeItem(e))
}

export function textWidth(text: string, fontSize?: string): number {
  const tag = document.createElement('div')
  tag.style.position = 'absolute'
  tag.style.left = '-99px'
  tag.style.whiteSpace = 'nowrap'
  if (fontSize) tag.style.fontSize = fontSize
  tag.innerHTML = text

  document.body.appendChild(tag)
  const result = tag.offsetWidth
  document.body.removeChild(tag)
  return result
}
export const isTextOverFlow = (text: string, elWidth: number, fontSize?: string) => {
  return textWidth(text, fontSize) > elWidth
}

/**
 * 格式化毫秒时间戳
 *
 * @param {Number} timestamp (单位为豪秒)
 * @param {String} format (格式)
 *    format='YYYY-MM-DD'
 *    format='MM/DD hh:mm'
 * @returns {String} default return YYYY/MM/DD hh:mm:ss
 */
export function formatTimestamp(format = 'YYYY/MM/DD HH:mm:ss', timestamp: number = Date.now()): string {
  const date = new Date(timestamp)
  const year: string = date.getFullYear() + ''
  let month: number | string = date.getMonth() + 1
  let day: number | string = date.getDate()
  let hour: number | string = date.getHours()
  let minute: number | string = date.getMinutes()
  let second: number | string = date.getSeconds()

  month = month > 9 ? `${month}` : `0${month}`
  day = day > 9 ? `${day}` : `0${day}`
  hour = hour > 9 ? `${hour}` : `0${hour}`
  minute = minute > 9 ? `${minute}` : `0${minute}`
  second = second > 9 ? `${second}` : `0${second}`
  return format.replace('YYYY', year).replace('MM', month).replace('DD', day).replace('HH', hour).replace('mm', minute).replace('ss', second)
}

/*
 * @param {Object} object
 * @returns {Boolean}
 */
const isFalsy /* So, it's not an object */ = (obj: any) => !obj

/**
 * Matches object type
 * @param {Object} object
 * @returns {Boolean}
 */
export const isObjectType = (obj: any) => !!(typeof obj).match(/obj/)
/**
 * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Cyclic_object_value#Examples
 * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/WeakSet
 */
const getCircularReplacer = () => {
  const seen = new WeakSet()
  return (_key: any, value: any) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return
      }
      seen.add(value)
    }
    return value
  }
}

/**
 * @param {Object} object The object to be cloned
 * @returns A copied value representing a new reference and discarding circular references
 */
export const deepClone = (object: Record<string, any>) => {
  if (isFalsy(object)) return {}
  if (!isObjectType(object)) return object
  return JSON.parse(JSON.stringify(object, getCircularReplacer()))
}

export const isEmpty = (obj: any) => {
  if (!obj) return true
  if (Array.isArray(obj)) return obj.length === 0
  return Object.keys(obj).length === 0
}

export const tbTimeStr = (str: string): string => {
  if (!str) return ''
  const arr = str.split(' ')

  return arr.join('<br />')
}

// 生成5位短随机id
// 注意非完全随机 追求完全随机慎用
export const makeid = () => (Math.random() + 1).toString(36).substring(7)
