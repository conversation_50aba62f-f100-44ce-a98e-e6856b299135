<template>
  <div class="channel-config-container">
    <!-- <DtNav :from="nav.from"  :fromPath="'/channelManage'" :name="nav.to" :back="onBack" /> -->
     <!-- 页面头部和导航区域 -->
    <SecondaryPageHeader
      title="配置"
      icon="Document"
      :show-actions="false"
      :breadcrumb-items="breadcrumbItems"
      @back="goBack"
    />

    <el-tabs  v-model="activeTab" @tab-change="onTabChange" class="config-tabs">
      <el-tab-pane label="基础配置" name="basic" lazy>
        <Basic :ref="(el) => (refs.basic = el)" />
      </el-tab-pane>
      <el-tab-pane label="智能体配置" name="Intelligent" >
        <Intelligent :ref="(el) => (refs.Intelligent = el)" />
      </el-tab-pane>
      <el-tab-pane label="常用卡片配置" name="common" lazy>
        <Common :ref="(el) => (refs.common = el)" />
      </el-tab-pane>
      <el-tab-pane label="猜你想问配置" name="qa" lazy>
        <Qa :ref="(el) => (refs.qa = el)" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup lang="ts">
import Basic from './components/basic.vue'
import Common from './components/common.vue'
import Qa from './components/qa.vue'
import Intelligent from './components/Intelligent.vue'
import ShortWords from './components/shortWords.vue'
import sensitiveWord from './components/sensitiveWord.vue';
import manualConfig from "./components/manualConfig.vue";

import { ref, reactive, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useRouterStore } from '@/stores/router'
import { onActivated,onMounted } from 'vue'
import { DataBoard, House, Document, Back } from '@element-plus/icons-vue'

const { delRouterCaches } = useRouterStore()
const activeTab = ref('basic')
const nav = reactive({
  from: '渠道管理',
  to: '配置',
})

const refs = reactive<Record<string, any>>({ common: null, qa: null, words: null, basic: null,manualConfig: null,sensitiveWord: null,Intelligent:null })

const onTabChange = (name: string) => {
  nextTick(() => {
    if (refs[name]) {
      if (typeof refs[name].init === 'function') {
        refs[name].init()
      }
      if (typeof refs[name].search === 'function') {
        refs[name].search()
      }
      if (typeof refs[name].loadData === 'function') {
        refs[name].loadData()
      }
    }
  })
}
const  breadcrumbItems = [
        {
          text: "渠道管理",
          icon: "Back",
          to: { name: "channelManage" }
        },
        {
          text: "配置",
          icon: "DataBoard"
        }
      ]
const router = useRouter()
const route = useRoute()
const channelId =route.query.channelId ?? ''
const onBack = () => {
  delRouterCaches((route?.name as string) ?? '')
  router.back()
}
 // 返回指标管理
const  goBack=()=> {
      router.push({ name: 'channelManage' });
  }
    
// onMounted(() => {
//  activeTab.value = ref('basic')
// })
const reset=()=> {
  activeTab.value = "basic";
}
onActivated(() => {
  nextTick(() => {
    reset()
    if (refs[activeTab.value]) {
      if (typeof refs[activeTab.value].init === 'function') {
        refs[activeTab.value].init()
      }
      if (typeof refs[activeTab.value].search === 'function') {
        refs[activeTab.value].search()
      }
      if (typeof refs[activeTab.value].loadData === 'function') {
        refs[activeTab.value].loadData()
      }
    }
  })
})

onMounted(() => {
  nextTick(() => {
    if (refs.basic) {
      if (typeof refs.basic.init === 'function') {
        refs.basic.init()
      }
      if (typeof refs.basic.search === 'function') {
        refs.basic.search()
      }
    }
  })
})

// 暴露给父组件的方法
defineExpose({
  // 初始化方法
  init() {
    // 初始化逻辑
    // loadData()
  },
  
  // 搜索方法
  search() {
    // 搜索逻辑
    // loadData()
  },
  
  // 加载数据方法
  loadData() {
    // 根据 channelId 加载数据
    if (channelId) {
      // 调用API获取数据
      // fetchChannelConfig(channelId).then(data => {
        // 回显数据到表单
        // formData.value = data
      // })
    }
  }
})
</script>

<style scoped>
/* 整体容器样式 */
.channel-config-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #D7A256;
  min-height: 100vh;
}

/* 标签页样式优化 */
.config-tabs {
  /* margin-top: 20px; */
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(215, 162, 86, 0.15);
  background-color: #fff;
  border: 1px solid rgba(215, 162, 86, 0.2);
  padding:28px;
}

.config-tabs :deep(.el-tabs__header) {
  /* background-color: #FFF6E8; */
  border-bottom: 1px solid rgba(215, 162, 86, 0.3);
  border-radius: 8px 8px 0 0;
  margin: 0;
}

.config-tabs :deep(.el-tabs__nav-wrap) {
  /* padding: 0 20px; */
}
.config-tabs :deep(.el-tabs__content) {
  /* padding: 24px; */
  background-color: #fff;
  border-radius: 0 0 8px 8px;
  padding-top: 24px;
  /* margin-left: 10px; */
  /* padding-left:24px; */
}

.config-tabs :deep(.el-tabs__item) {
  font-size: 15px; /* 比内容字体大 */
  font-weight: 550;
}

</style>
