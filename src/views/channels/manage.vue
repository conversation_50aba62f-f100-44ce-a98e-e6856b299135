<template>
  <div class="index-manage-container">
      <UniversalTable
        title="渠道列表"
        subtitle="管理和配置渠道"
        :title-icon="Document"
        :table-data="tableData"
        :noHeader="false"
        :loading="loading"
        :columns="tableColumns"
        :actions="tableActions"
        :search-form-config="searchForm"
        :search-params="initParam"
        :pagination-data="initParam"
        :total="total"
        add-button-text="新增"
        empty-title="暂无渠道数据"
        empty-description="点击上方新增渠道数据按钮开始创建"
        @search="normalSearch"
        @reset="normalResetQuery"
        @add="handleAdd"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @action-click="handleAction"
      >
        <!-- 渠道编码 -->
        <template #channelCode="{ row }">
          <div class="code-cell">
            <div class="code-icon">
              <!-- <i class="el-icon-document-copy"></i> -->
              <el-icon><Document /></el-icon>
            </div>
            <span class="code-text">{{ row.channelCode }}</span>
          </div>
        </template>
        <!-- 名称 -->
        <template #channelName="{ row }">
          <span>{{ row.channelName }}</span>
        </template>
        <!-- 执行类型 -->
        <template #executeType="{ row }">
          <div class="type-cell">
          <el-tooltip :content="row.executeType == '1' ? '工作流' : '自定义'" placement="top">
            <el-tag
            :class="row.executeType == '1' ? 'calculate-tag' : 'result-tag'"
            size="medium"
            effect="plain"
          >
            <el-icon style="margin-right: 4px;">
              <component :is="row.executeType == '1' ? Cpu : DataLine" />
            </el-icon>
            {{ row.executeType == '1' ? "工作流" : "自定义" }}
          </el-tag>
            
          </el-tooltip>
          </div>
        </template>
        <!-- 关联执行体 -->
        <template #bindRobots="{ row }">
          <el-tooltip
            v-if="row.bindRobots"
            :content="row.bindRobots"
            :enterable="false"
            effect="dark"
            :width="100"
            :showOverflowTooltip="true"
            class="awidthTooltip"
            placement="top"
          >
            <p class="awidth">{{ row.bindRobots }}</p>
          </el-tooltip>
          <span v-else>{{ row.bindRobots }}</span>
        </template>
        <!-- 所属项目 -->
        <template #projectName="{ row }">
          <span>{{ row.projectName }}</span>
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
        <div class="remark-cell">
          <el-tooltip
            v-if="row.remark"
            :content="row.remark"
            :enterable="false"
            effect="dark"
            placement="top"
            popper-class="modern-tooltip"
          >
            <span class="remark-text">{{ row.remark }}</span>
          </el-tooltip>
          <span v-else class="no-remark">
            <el-icon><InfoFilled /></el-icon>
            暂无备注
          </span>
        </div>
      </template>
        <!-- 创建时间 -->
        <template #createTime="{ row }">
          <div class="time-cell">
            <el-icon><Clock /></el-icon>
            <span>{{ row.createTime }}</span>
          </div>
        </template>
        <!-- 最后更新时间 -->
        <template #updateTime="{ row }">
          <div class="time-cell">
            <el-icon><Clock /></el-icon>
            <span>{{ row.updateTime }}</span>
          </div>
        </template>
        <!-- 操作人 -->
        <template #updateBy="{ row }">
        <div class="user-cell">
          <div class="user-avatar">
            <el-icon><User /></el-icon>
          </div>
          <span>{{ getNickName(row.updateBy) }}</span>
        </div>
      </template>
        <!-- 启用状态 -->
        <template #status="{ row }">
          <el-switch
            class="el-switch"
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="onSwitchChange(row.channelId, $event)"
          />
        </template>
      </UniversalTable>
    </div>
    <UniversalFormDialog
      ref="editDialogRef"
      v-model="showEditDialog"
      :form-data="editForm"
      :form-fields="computedFormFields"
      :form-rules="editRules"
      :is-edit="editForm.isEdit"
      :loading="saveLoading"
      add-title="新增数据源"
      edit-title="编辑数据源"
      @confirm="handleSave"
      @cancel="handleCancel"
      @close="handleDialogClose"
    />
   <!-- 删除确认弹窗 -->
   <ConfirmDialog
      ref="confirmDialog"
       v-model="showdel"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除该指标？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
       @cancel="delCancel"
      @confirm="confirmUpdate"
    />
</template>

<script setup lang="ts">
import type { ToolListProps, SearchFormTemp } from '@/components/types'
import add from './add.vue'
import { Document, Edit, Setting, Delete,Clock ,User,Cpu, DataLine,InfoFilled, Coin} from '@element-plus/icons-vue'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import { useRouterStore } from '@/stores/router'
import useChannel from '@/stores/channel'
import { useRouter } from 'vue-router'
import { ref, reactive, onActivated, onMounted,nextTick, computed } from 'vue'
import * as api from '@/api/channels'

const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const initParam = ref({
  pageNum: 1,
  pageSize: 10,
  // 其他查询参数
  param:
  {
    name:"",
    status:"",
  }
})

const getTableData = async () => {
  loading.value = true
  try {
    const res = await api.searchChannelPage(initParam.value)
    tableData.value = (res.data?.list || []).map(item => ({
      ...item,
      status: Number(item.status) // 保证 status 是数字
    }))
    total.value = res.data?.total || 0
  } finally {
    loading.value = false
  }
}

onMounted(getTableData)

const { setChannel } = useChannel()
const { getNickName } = useFilters()
const { getDic } = useUserStore()
const { routerCaches, exceptCaches } = useRouterStore()
const router = useRouter()
const channelTbRef = ref()
const pageTitle = '渠道列表'
const showAdd = ref<boolean>(false)
const showEditDialog = ref<boolean>(false)
const showdel=ref<boolean>(false)
const saveLoading = ref<boolean>(false)
let title=""
let channelId=""
let channelCode=""
const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: '',
    action: async () => {
       title="新增"
      //  showAdd.value = true;
      //   channelId=''
      //   channelCode=''
      router.push({ path: '/channelAdd' })
    },
  },
]
defineOptions({ name: 'channelManage' })

const editForm = reactive({
  projectId: "",
  channelName: "",
  channelCode: "",
  status: 1,
  executeType: "0",
  flowId: "",
  finalWords: "",
  isEdit: false // 如果需要区分编辑/新增状态可保留
})

const formFields = [
  {
    label: "所属项目",
    name: "projectId",
    type: "proselect",
    placeholder: "请输入所属项目",
    prop: "projectId",
    // disabled: isEdit.value ? true : false
  },
  {
    label: "渠道名称",
    name: "channelName",
    type: "input",
    placeholder: "请输入渠道名称",
    prop: "channelName"
  },
  {
    label: "渠道编码",
    name: "channelCode",
    type: "input",
    placeholder: "请输入渠道编码",
    prop: "channelCode",
    maxlength: 10
  },
  {
    label: "启用状态",
    name: "status",
    type: "switch",
    placeholder: "请选择启用状态",
    prop: "status",
    value: 1
  },
  {
    label: "执行类型",
    name: "executeType",
    type: "radio",
    prop: "executeType",
    value: '0',
    options: [
      { dicItemCode: '0', dicItemName: "自定义" },
      { dicItemCode: '1', dicItemName: "工作流" }
    ],
    optionLabel: 'dicItemName',
    optionValue: 'dicItemCode'
  },
  {
    label: "关联工作流",
    name: "flowId",
    type: "flowIdSelect",
    placeholder: "请选择关联工作流",
    prop: "flowId",
    except: true,
    visible: (formData) => formData.executeType === '1' // 只在executeType=1时显示
  },
  // {
  //   label: "结束语",
  //   name: "finalWords",
  //   type: "textarea",
  //   placeholder: "请输入结束语",
  //   prop: "finalWords",
  //   maxlength: 100,
  //   except: true
  // }
]

const computedFormFields = computed(() => {
  return formFields.map(field => {
    if (field.name === 'projectId') {
      return {
        ...field,
        disabled: editForm.isEdit // 编辑时禁用
      }
    }
    return field
  })
})

const editRules = {
  projectId: [{ required: true, message: '请输入所属项目', trigger: 'blur' }],
  status: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
  channelName: [{ required: true, message: '请输入渠道名称', trigger: 'change' }],
  channelCode: [{ required: true, message: '请输入渠道编码', trigger: 'change' }],
  executeType: [{ required: true, message: '请选择执行类型', trigger: 'blur' }],
  flowId: [{ required: true, message: '请选择关联工作流', trigger: 'blur' }],
}
const searchForm: SearchFormTemp[] = [
  {
  label: '渠道名称',
  name: 'name',
  type: 'input',
  placeholder: '请输入渠道名称',
  searchState: true
  },
  // {
  //   label: '类型',
  //   name: 'type',
  //   type: 'select',
  //   placeholder: '请选择类型',
  //   list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.cardType')],
  // },
  {
  label: '启用状态',
  name: 'status',
  type: 'select',
  placeholder: '请选择启用状态',
  list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.common.status')],
  searchState: true
  },
]


const tableColumns = [
  {
    prop: 'channelCode',
    label: '渠道编码',
    align: 'center',
    width: '150px',
  },
  {
    prop: 'channelName',
    label: '名称',
    align: 'center',
    minWidth: '100px',
  },
  {
    prop: 'executeType',
    label: '执行类型',
    align: 'center',
    minWidth: '120px',
    slot: 'executeType', // 需要自定义渲染
  },
  {
    prop: 'bindRobots',
    label: '关联执行体',
    align: 'center',
    minWidth: '200px',
    slot: 'bindRobots', // 需要自定义渲染
  },
  {
    prop: 'projectName',
    label: '所属项目',
    align: 'center',
    minWidth: '140px',
  },
  {
    prop: 'remark',
    label: '备注',
    align: 'center',
    minWidth: '120px',
    slot: 'remark', // 需要自定义渲染
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    minWidth: '175px',
  },
  {
    prop: 'updateTime',
    label: '最后更新时间',
    align: 'center',
    minWidth: '175px',
  },
  {
    prop: 'updateBy',
    label: '操作人',
    align: 'center',
    minWidth: '120px', // 或 '160px'
    slot: 'updateBy',
    showOverflowTooltip: true, // 可选
  },
  {
    prop: 'status',
    label: '启用状态',
    align: 'center',
    width: '80px',
    slot: 'status', // 需要自定义渲染
  }
]

const tableActions = [
  { key: "edit", label: "编辑", icon: Edit, class: "edit-btn" },
  { key: "config", label: "配置", icon: Setting, class: "config-btn" },
  { key: "delete", label: "删除", icon: Delete, class: "delete-btn" }
]
const onConfig = (data: any) => {
  setChannel(data)
  router.push({
    path: data.executeType && data.executeType=='1'?'/flowConfig':'/channelConfig',
    query: {
      channelId: data.channelId ?? '',
      channelCode:data.channelCode??'',
      channelBasicConfigId: data.channelBasicConfigId ?? ''
    },
  })
}
const confirmDialog = ref()
const deleteRow = ref(null)

const onDelete = (data: any) => {
  console.log('confirmDialog:', confirmDialog.value)
  deleteRow.value = data
  showdel.value=true
  confirmDialog.value.show()
}

const confirmUpdate = async () => {
  // 3. 执行删除逻辑
  if (deleteRow.value) {
    await api.delChannel(deleteRow.value.channelId)
    getTableData()
    showdel.value=false
    deleteRow.value = null
  }
}
 //关闭弹窗
  const delCancel=()=> {
    showdel.value = false;
        
  }
const onOpen = async (channelId: string, ...arg: any) => {
  await api.updateStatus({channelId, status: arg[0] })
  await channelTbRef.value?.onSearchBtn()
}
const submit = async (param: any) => {
  showAdd.value=false
  // router.back()
}
const cancel = async (val) => {
  console.log(1111)
  showAdd.value=false
}
onActivated(async () => {
  await channelTbRef.value?.onSearchBtn()
})

// 搜索、分页等事件
const normalSearch = (params) => {
  console.log('params',params)
  initParam.value.param = params
  initParam.value = { ...initParam.value, pageNum: 1 }
  getTableData()
}
const normalResetQuery = () => {
  initParam.value = {
    pageNum: 1,
    pageSize: 10,
    param: {
      name: "",
      status: "",
    }
  }
  getTableData()
}
const handleSizeChange = (size) => {
  initParam.value.pageSize = size
  getTableData()
}
const handleCurrentChange = (page) => {
  initParam.value.pageNum = page
  getTableData()
}

const handleAction = async (item) => {
  console.log(item)
  if (item.action.key === 'edit') {
    // 先获取更多详情
    const detail = await api.basicGet(item.row.channelCode)
    // 合并详情和原有数据
    Object.assign(editForm, {
      ...item.row,
      ...detail.data, // 假设接口返回的数据在 data 字段
      isEdit: true
    })
    showEditDialog.value = true
    nextTick(() => {
      editDialogRef.value?.clearFormValidate?.()
    })
  } else if (item.action.key === 'config') {
    onConfig(item.row)
  } else if (item.action.key === 'delete') {
    onDelete(item.row)
  }
}

const handleAdd = () => {
  resetEditForm()
  showEditDialog.value = true
  nextTick(() => {
    editDialogRef.value?.clearFormValidate?.()
  })
}

const resetEditForm = () => {
  Object.assign(editForm, {
    projectId: "",
    channelName: "",
    channelCode: "",
    status: 1,
    executeType: "0",
    flowId: "",
    finalWords: "",
    isEdit: false
  })
}

const editDialogRef = ref()

const handleSave = async (formData:any) => {
  saveLoading.value = true
  try {
    let res
    if (!formData.isEdit) {
      // 新增时将 status 转为 number
      const addData = { ...formData.formData, status: Number(formData.formData.status) }
      res = await api.add(addData)
      if (res && res.status === 0) {
        ElMessage.success('新增成功')
      }
    } else {
      // 编辑时带上 channelId
      const updateData = {
        ...formData.formData,
        status: Number(formData.formData.status),
        channelId: formData.formData.channelId // 确保有 channelId
      }
      res = await api.updateChannel(updateData)
      if (res && res.status === 0) {
        ElMessage.success('编辑成功')
      }
    }
    if (res && res.status === 0) {
      showEditDialog.value = false
      resetEditForm()
      editDialogRef.value?.clearFormValidate?.() // 重置表单校验
      getTableData()
    }
  } finally {
    saveLoading.value = false
  }
}

const handleCancel = () => {
   // 重置表单
   Object.assign(editForm, {
    projectId: "",
    channelName: "",
    channelCode: "",
    status: 1,
    executeType: "0",
    flowId: "",
    finalWords: "",
    isEdit: false
  })
  showEditDialog.value = false
  // TODO: 处理表单取消
}

const handleDialogClose = () => {
  // TODO: 处理表单关闭
}

const switchInitMap = ref({}) // 记录每个开关是否初始化过

const onSwitchChange = (channelId, value) => {
  if (!switchInitMap.value[channelId]) {
    // 第一次渲染，标记为已初始化，不请求
    switchInitMap.value[channelId] = true
    return
  }
  // 用户手动切换才会走到这里
  onOpen(channelId, value)
}
</script>

<style lang="less">

.awidth {
    width: 100%;
    height: 20px;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    left: -5px;
    right: -10px;
  }
  /deep/.awidthTooltip{
    white-space: wrap;
    max-width: 100px;
    display: block;
    max-height: 100px;
  }
</style>
<style lang="less" scoped>
.index-manage-container {
  .code-cell {
    display: flex;
    align-items: center;
    justify-content: center; // 改成左对齐
    gap: 8px;
    .code-icon {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        color: #d7a256;
        font-size: 12px;
      }
    }
    .code-text {
      font-family: "Courier New", monospace;
      font-weight: 500;
      color: #2c3e50;
      font-size: 13px;
    }
  }
  .name-cell {
    .name-text {
      font-weight: 500;
      color: #2c3e50;
    }
  }
  .type-cell {
    .calculate-tag {
      background: rgba(64, 158, 255, 0.1);
      color: #409eff;
      border: 1px solid rgba(64, 158, 255, 0.3);

      i {
        margin-right: 4px;
      }
    }

    .result-tag {
      background: rgba(103, 194, 58, 0.1);
      color: #67c23a;
      border: 1px solid rgba(103, 194, 58, 0.3);

      i {
        margin-right: 4px;
      }
    }
  }
  :deep(.el-switch) {
    .el-switch__core {
      border-color: #e8dcc0;
      background: #e8dcc0;
    }
  }
  :deep(.el-switch.is-checked) {
    .el-switch__core {
      border-color: #D7A256;
      background: #D7A256;
    }
  }
  .remark-cell {
    .remark-text {
      color: #606266;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .no-remark {
      color: #c0c4cc;
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      i {
        font-size: 12px;
      }
    }
  }
  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;
    i {
      color: #d7a256;
      font-size: 14px;
    }
  }
  
  .user-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;
    .user-avatar {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }
  }
}
// :deep(.modern-tooltip) {
//   background: #2c3e50 !important;
//   color: white !important;
//   border-radius: 8px !important;
//   padding: 8px 12px !important;
//   font-size: 12px !important;
//   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
// }
// :deep(.el-loading-mask) {
//   background-color: rgba(251, 246, 238, 0.8) !important;
// }
</style>
