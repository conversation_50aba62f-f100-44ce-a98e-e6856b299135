<template>
    <TableToolTemp :toolTitle="pageTitle" :toolList="toolList" />
    <PageTable :searchFormTemp="searchForm" apiUrl="/web/channel/page" ref="channelTbRef">
      <template #default="{ tableData }">
        <el-table :data="tableData" class="dt-table" stripe>
          <el-table-column prop="code" label="渠道编码" align="center" width="120px" />
          <el-table-column prop="name" label="渠道名称" align="center" min-width="100px" />
          <el-table-column prop="code" label="所属项目" align="center" width="120px" />
          <el-table-column prop="code" label="结束语" align="center" width="120px" />
          <el-table-column prop="code" label="默认回复" align="center" width="120px" />
          <el-table-column prop="robotName" label="状态" align="center" min-width="100px" />
          <el-table-column prop="remark" label="更新时间" align="center" min-width="120px">
            <template #default="scope">
              <el-tooltip :content="scope.row.remark" placement="top">
                <div class="truncate">{{ scope.row.remark }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="updateId" label="更新人" align="center" min-width="80px">
            <template #default="scope">
              {{ getNickName(scope.row.updateId) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="启用状态" align="center" width="80px">
            <template #default="scope">
              <el-switch v-model="scope.row.status" active-value="1" inactive-value="0"
                @change="onOpen(scope.row.id, $event)" />
            </template>
          </el-table-column>
          <el-table-column prop="code" label="操作" align="center" width="80px">
            <template #default="scope">
              <el-button link type="primary" @click="onConfig(scope.row)">配置机器人</el-button>
              <el-button link type="primary" @click="onConfig(scope.row)">更新</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>
     <!--新增弹窗-->
     <DtPopup
        :show="showAdd"
        @close="closePopup"
        size="mini"
        :title="title"
        :footer="false"
      >
        <el-form
          ref="addUserForm"
          :model="addForm"
          :rules="addRules"
          label-position="right"
          label-width="80px"
          :inline="true"
        >
          <el-form-item label="所属项目" prop="project_id">
            <el-select v-model="addForm.project_id" placeholder="请选择所属项目">
              <el-option
                v-for="item in initList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="渠道名称" prop="channel_name">
            <el-input
              v-model="addForm.channel_name"
              auto-complete="off"
              placeholder="请输入渠道名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="渠道编码" prop="channel_code">
            <el-input
              v-model="addForm.channel_code"
              auto-complete="off"
              placeholder="请输入渠道编码"
            ></el-input>
          </el-form-item>
          <el-form-item label="是否启用" prop="status">
            <el-select v-model="addForm.status">
              <el-option label="启用" value="0"></el-option>
              <el-option label="禁用" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="结束语" prop="final_words">
            <el-input
              v-model="addForm.final_words"
              type="textarea"
              :rows="5"
              auto-complete="off"
              placeholder="请输入结束语"
            ></el-input>
          </el-form-item>
          <el-form-item label="默认回复" prop="default_answer">
            <el-input
              v-model="addForm.default_answer"
              type="textarea"
              :rows="7"
              auto-complete="off"
              placeholder="请输入默认回复"
            ></el-input>
          </el-form-item>
        </el-form>
        <!-- <div class="popup-footer">
          <el-button
            size="large"
            @click="closePopup"
            class="dt-btn"
            type="primary"
            plain
            :style="{ color: themeObj.color }"
            >取消</el-button
          >
          <el-button type="primary" size="large" @click="toAddForm" class="dt-btn"
            >保存</el-button
          >
        </div> -->
      </DtPopup>
      <!--编辑弹窗-->
      <DtPopup
        :show="showEdit"
        @close="showEdit = false"
        size="mini"
        :title="title"
        :footer="false"
      >
        <el-form
          label-width="80px"
          label-position="right"
          :model="editForm"
          :rules="editRules"
          ref="editUserForm"
          :inline="true"
        >
          <el-form-item label="所属项目" prop="project_id">
            <el-select v-model="editForm.project_id" placeholder="请选择所属项目">
              <el-option
                v-for="item in initList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="渠道名称" prop="channel_name">
            <el-input
              v-model="editForm.channel_name"
              auto-complete="off"
              placeholder="请输入渠道名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="渠道名称" prop="channel_name">
            <el-input
              v-model="editForm.channel_name"
              auto-complete="off"
              placeholder="请输入渠道名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="是否启用" prop="status">
            <el-select v-model="editForm.status">
              <el-option label="启用" value="0"></el-option>
              <el-option label="禁用" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="渠道编码" prop="channel_code">
            <el-input
              :disabled="true"
              v-model="editForm.channel_code"
              auto-complete="off"
              placeholder="请输入渠道编码"
            ></el-input>
          </el-form-item>
          <el-form-item label="结束语" prop="final_words">
            <el-input
              v-model="editForm.final_words"
              type="textarea"
              :rows="5"
              auto-complete="off"
              placeholder="请输入结束语"
            ></el-input>
          </el-form-item>
          <el-form-item label="默认回复" prop="default_answer">
            <el-input
              v-model="editForm.default_answer"
              type="textarea"
              :rows="7"
              auto-complete="off"
              placeholder="请输入默认回复"
            ></el-input>
          </el-form-item>
        </el-form>
        <!-- <div class="popup-footer">
          <el-button
            type="primary"
            class="dt-btn"
            plain
            :style="{ color: $store.state.layoutStore.themeObj.color }"
            @click="editCancel"
            >取消</el-button
          >
          <el-button type="primary" class="dt-btn" @click="editSubmit"
            >保存</el-button
          >
        </div> -->
      </DtPopup>
  </template>
  
  <script setup lang="ts">
  import type { ToolListProps, SearchFormTemp } from '@/components/types'
  import useFilters from '@/hooks/useFilters'
  import { useUserStore } from '@/stores/user'
  import useChannel from '@/stores/channel'
  import { useRouter } from 'vue-router'
  import { ref, onActivated } from 'vue'
  import * as api from '@/api/channels'
  
  const { setChannel } = useChannel()
  const { getNickName } = useFilters()
  const { getDic } = useUserStore()
  const router = useRouter()
  const channelTbRef = ref()
  const pageTitle = '渠道列表'
  const title=""
  const initList = ref<Record<string, any>[]>([])
  const addRules= {
          project_id:  [{ required: true, message: '不能为空', trigger: 'blur' }],
          channel_name:  [{ required: true, message: '不能为空', trigger: 'blur' }],
          status:  [{ required: true, message: '不能为空', trigger: 'blur' }],
          channel_code:  [{ required: true, message: '不能为空', trigger: 'blur' }],
          default_answer:  [{ required: true, message: '不能为空', trigger: 'blur' }],
        }
  const  editRules= {
    project_id:  [{ required: true, message: '不能为空', trigger: 'blur' }],
          channel_name:  [{ required: true, message: '不能为空', trigger: 'blur' }],
          status:  [{ required: true, message: '不能为空', trigger: 'blur' }],
          channel_code:  [{ required: true, message: '不能为空', trigger: 'blur' }],
          default_answer:  [{ required: true, message: '不能为空', trigger: 'blur' }],
        }
  const  channel_id = ref()
  const  addForm= ref<Record<string, any>>({
          channel_name: "",
          channel_code: "",
          default_answer: "",
          final_words: "",
          project_id: "",
          status:""
        })
  const  editForm= ref<Record<string, any>>({
    channel_name: "",
          channel_code: "",
          default_answer: "",
          final_words: "",
          project_id: "",
          status: ""
        })
  const showAdd = ref<boolean>(false)
  const showEdit = ref<boolean>(false)
  const robitState = ref<boolean>(false)
  const toolList: ToolListProps[] = [
    {
      name: '新增',
      icon: 'add',
      btnCode: '',
      action: async () => {
        router.push({ path: '/channelAdd' })
      },
    },
  ]
  
  const searchForm: SearchFormTemp[] = [
    {
      label: '渠道名称',
      name: 'name',
      type: 'input',
      placeholder: '请输入渠道名称',
    },
    {
      label: '渠道编码',
      name: 'name',
      type: 'input',
      placeholder: '请输入渠道名称',
    },
    {
      label: '启用状态',
      name: 'status',
      type: 'select',
      placeholder: '请选择启用状态',
      list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.common.status')],
    },
  ]
  
  const onConfig = (data: any) => {
    setChannel(data)
  
    router.push({
      path: '/channelConfig',
      query: {
        channelId: data.id ?? '',
        channelBasicConfigId: data.channelBasicConfigId ?? '',
      },
    })
  }
  
  const onOpen = async (id: string, ...arg: any) => {
    await api.update({ id, status: arg[0] })
    await channelTbRef.value?.onSearchBtn()
  }
  const robotChange=(val:any)=> {
        // if (val) {
        //   this.robitState = false;
        //   this.addForm.robot_id = "";
        //   this.initRobotList();
        // }
      }
   //关闭新增弹窗
   const closePopup=()=> {
          showAdd.value = false;
          //  addForm.value = addForm.value;
        // console.log(addForm.value);
        // $nextTick(() => {
        //   $refs.addUserForm.clearValidate();
        // });
      }
     const handleEdit=(row:any)=> {
        // console.log(row);
        // this.showEdit = true;
        // this.title = "编辑机器人";
        // this.editForm.robot_id = row.robot_id;
        // this.editForm.channel_id = this.$route.query.channelId;
        // this.editForm.robot_type = row.robot_type;
        // this.editForm.id = row.id;
      }
      const handleTool=(val:any)=> {
        // if (val.name === "新增") {
        //   this.title = "新增机器人";
        //   this.showAdd = true;
        // }
      }
  onActivated(async () => {
    await channelTbRef.value?.onSearchBtn()
  })
  </script>
  
  <style lang="less"></style>
  