<template>
  <DtNav :from="nav.from" :fromPath="'/channelManage'" :subFrom="nav.subFrom" :name="nav.to" />
  <div class="p-[15px]">
    <TableToolTemp toolTitle="常用卡片信息" />
    <UpdateFormTemp v-if="finished" class="pt-5" :form-list="baseList" :rules="rules" show-submit label-position="left"
      :certainText="certainText" :cancelText="'返回'" @submit="submit">
      <template #default="{ form }">
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型" clearable @change="onTypeChange(form, $event)">
            <el-option v-for="(el, idx) in getDic('chatbot.cardType')" :key="idx" :label="el.dicItemName"
              :value="el.dicItemCode" />
          </el-select>
        </el-form-item>

        <el-form-item label="内容" prop="content" v-if="form?.type == 2" ref="contentRef">
          <el-input type="textarea" maxlength="50" v-model="form.content" rows="4" />
          <!-- <Editor v-model="form.content" :maxlength="300" trim @change="onContentChange" /> -->
        </el-form-item>

        <div v-else-if="form?.type == 3">
          <el-form-item label="知识类型" prop="knowledgeType">
            <el-radio-group v-model="form.knowledgeType">
              <el-radio label="1">QA</el-radio>
              <el-radio label="2">知识</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="内容" prop="content">
            <KnowsList v-model="form.content" ref="knowsRef"
              :param="{ robotId: channelConfig.robotId, type: 1, knowledgeType: form.knowledgeType, knowledgeTitle }" />
          </el-form-item>
        </div>

        <el-form-item label="内容" prop="content" v-else>
          <el-input v-model.trim="form.content" placeholder="请输入内容" clearable />
          <div class="text-[12px] text-gray-400 leading-normal mt-2">
            说明：跳转页面支持内置参数配置，包含token、userId。使用示例：https://www.dtinsure.com/index?token=${token}
          </div>
        </el-form-item>
      </template>
    </UpdateFormTemp>
  </div>
</template>

<script setup lang="ts">
import { reactive, onBeforeMount, computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { UpdateFormList } from '@/components/types'
import type { FormRules } from 'element-plus'
import * as api from '@/api/channels'
import { useUserStore } from '@/stores/user'
import useChannel from '@/stores/channel'
import { cardDetail } from '@/api/channels'
// import Editor from '@/components/Editor.vue'
import KnowsList from './knowsList.vue'
import { nextTick } from 'process'

defineOptions({ name: 'channelCommonAddAndEdit' })

const { channelConfig } = useChannel()
// sub post data
const subData = {
  channelId: '',
  id: '',
  imgPath: [],
  channelCode: '',
}

const contentRef = ref()
const route = useRoute()
const router = useRouter()
const finished = ref(false)
const { getDic } = useUserStore()
const isEdit = computed(() => {
  return !!route.query?.id
})
const toName = computed(() => `常用卡片${isEdit.value ? '编辑' : '新增'}`)
const certainText = computed(() => {
  return !isEdit.value ? '保存' : '更新'
})
const nav = reactive({
  from: '渠道管理',
  subFrom: '配置',
  to: toName,
})
const knowsRef = ref(null)
const knowledgeTitle = ref('')
const baseList: UpdateFormList[] = [
  {
    title: '标题',
    type: 'input',
    field: 'title',
  },
  {
    title: '图片',
    type: 'uploadimg',
    field: 'imgPath',
    value: [],
  },
  {
    title: '排序',
    type: 'numberInput',
    field: 'sort',
    min: 1,
    max: 127,
    precision: 0,
  },
  {
    title: '启用状态',
    type: 'switch',
    field: 'status',
    value: 0
  },
  {
    title: '类型',
    type: 'select',
    field: 'type',
    except: true,
  },
  {
    title: '内容',
    type: 'input',
    field: 'content',
    except: true,
  },
  {
    title: '知识类型',
    type: 'radio',
    field: 'knowledgeType',
    value: '1',
    isHidden: true
  }
]


const rules: FormRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  status: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
  imgPath: [{ required: true, message: '请上传图片', trigger:'change'}],
  sort: [{ required: true, message: '请选择排序', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型',  trigger: [ 'blur','change']  }],
  knowledgeType: [{ required: true, message: '请选择类型',  trigger: [ 'blur','change'] }],
  content: [
    { required: true, message: '请选择或填写内容', trigger: 'blur' },
    {
      max: 300,
      message: '内容不能超过300个字符',
      trigger: 'change',
    },
  ],
}

const submit = async (param: any) => {
  console.log('submit');

  const { channelId, id,channelCode } = subData
  const imgPath = param.imgPath[0].foreignPath

  let status: string | number
  let msg: string
  let knowledgeTitle = knowsRef.value?.knowTitle || ''
  if (!isEdit.value) {
    const res = await api.cardAdd({ ...param, imgPath, channelId, knowledgeTitle,channelCode })
    status = res.status
    msg = '保存成功'
    if(status==0){
      contentRef.value && contentRef.value.clearValidate()
      contentRef.value && contentRef.value.resetFields()
    }
  } else {
    const res = await api.cardUpdate({ ...param, imgPath, channelId, id, knowledgeTitle,channelCode })
    status = res.status
    msg = '更新成功'
    if(status==0){
      ElMessage.success(msg)
      contentRef.value && contentRef.value.clearValidate()
      contentRef.value && contentRef.value.resetFields()
    }
  }

  if (status != 0) return
  ElMessage.success(msg)

  router.back()
  return
}

async function init() {
  if (!isEdit.value) return

  const id = route.query.id as string
  const channelCode =route.query.channelCode
  const { status, data } = await cardDetail(id)
  if (status != 0) return

  subData.id = data?.id as string
  subData.channelCode = data?.channelCode as string
  subData.channelId = data?.channelId as string

  data.imgPath = [{ name: 'defualt.png', url: data.imgPath }]
  data.status = Number(data.status)

  baseList.forEach((tmp) => {
    if (Object.hasOwn(data, tmp.field) && data[tmp.field] !== null) {
      tmp.value = data[tmp.field]
    }
  })
  knowledgeTitle.value = data.knowledgeTitle
}

const onTypeChange = (form: any, args: string[]) => {
  if (args && args[0]) form.content = ''
}

const onContentChange = () => {
  if (contentRef.value) contentRef.value.validate()
}

onBeforeMount(async () => {
  subData.channelId = (route.query.channelId as string) ?? ''
  subData.channelCode = (route.query.channelCode as string) ?? ''
  await init()
  finished.value = true
})
</script>
