<template>
  <!-- <TableToolTemp :tool-title="pageTitle" /> -->
  <UpdateForm
    v-if="finished"
    ref="formTempRef"
    :form-list="baseList"
    :rules="rules"
    class="form-card"
    label-position="right"
    :certainText="certainText"
    :cancelText="'返回'"
    @submit="submit"
    @cancel="cancel"
  >
  <template #default="{ form }">
    <el-card class="form-section-card">
      <div  class="big-func-card">
       <div slot="header" class="card-header">基础配置</div>
        <el-row :gutter="20">
            <el-col :span="8">
          <el-form-item class="custom-form-item" label="渠道名称" prop="channelName">
            <el-input v-model="form.channelName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="custom-form-item" label="所属项目" prop="proselect">
            <DtprojectList v-model="form.projectId" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="custom-form-item" label="渠道类型" prop="channelType">
            <el-select v-model="form.channelType" :placeholder="'请选择'" clearable>
              <el-option v-for="el in getDic('chatbot.channelType')" :label="el.dicItemName" :value="el.dicItemCode" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
            <el-form-item class="custom-form-item" label="通道id" prop="passId">
              <el-input v-model="form.passId"  />
            </el-form-item>
        </el-col>
      </el-row>
      </div>
     <div  class="big-func-card">
        <div slot="header" class="card-header1">文案配置</div>
        <el-row :gutter="20">  
          <el-col :span="8" >
              <el-form-item class="custom-form-item" label="欢迎语" prop="welcomeReply">
                <el-input type="textarea" maxlength="50" v-model="form.welcomeReply"  />
              </el-form-item>
            </el-col>
            <el-col :span="8" >
              <el-form-item class="custom-form-item" label="输入框引导语" prop="inputPlaceHolder">
                <el-input type="textarea" maxlength="50" v-model="form.inputPlaceHolder"  />
              </el-form-item>
            </el-col>
        
          </el-row>
        </div>
      <div class="big-func-card">
        <div slot="header" class="card-header1">开关配置</div>
          <el-row :gutter="20">
            <el-col :span="8" v-for="item in functionList" :key="item.prop">
              <!-- <el-card class="big-func-card" shadow="hover"> -->
                <el-form-item
                  class="custom-form-item1"
                  :label="item.label"
                  label-width="135px"
                  :prop="item.prop"
                >
                  <el-switch
                    v-model="form[item.prop]"
                    :active-value="1"
                    :inactive-value="0"
                  ></el-switch>
                </el-form-item>
               
              <!-- </el-card> -->
            </el-col>
            <el-col :span="8">
              <el-form-item class="custom-form-item1" label="是否开启语音播报" label-width="165px" prop="type">
                <el-switch
                  v-model="form.voiceSynthesis"
                  :active-value="1"
                  :inactive-value="0"
                  @click="onTypeChange(form,  $event)"
                ></el-switch>
              </el-form-item>
              
            </el-col>
          </el-row>
          <!-- <div class="section-divider"></div> -->
        </div>
        <div  class="big-func-card">
          <div slot="header" class="card-header2">其他配置</div>
          <el-row :gutter="20">
            <el-col :span="8"  v-if="form?.voiceSynthesis == 1">
              <el-form-item class="custom-form-item" label="音色编码" prop="voiceSpeaker" ref="contentRef">
                <el-input type="textarea" maxlength="50" v-model="form.voiceSpeaker"  />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="custom-form-item" label="备注" prop="remark"  ref="contentRef">
                <el-input type="textarea" maxlength="50" v-model="form.remark"  />
              </el-form-item>
          </el-col>
        </el-row>
      </div>
  </el-card>
  </template>
</UpdateForm>
</template>

<script setup lang="ts">
import type { UpdateFormList ,DtprojectList} from '@/components/types'
import type { FormRules } from 'element-plus'
import { useRoute } from 'vue-router'
import * as api from '@/api/channels'
import { useRouterStore } from '@/stores/router'
// import DtprojectList from '@components/DtprojectList.vue'
  import { useUserStore } from '@/stores/user'
import { ref, computed,onMounted } from 'vue'
const { getDic } = useUserStore()
const formTempRef = ref()
const isEdit = ref(false)
const finished = ref(false)
const certainText = computed(() => {
  return !isEdit.value ? '保存' : '更新'
})

const subData = {
  id: '', // base config id
  channelCode: '', // channelCode
  channelId: ''
}

const route = useRoute()
const { delRouterCaches } = useRouterStore()
const pageTitle = '基础配置'
const baseList: UpdateFormList[] = [
  {
    title: '渠道名称',
    type: 'input',
    field: 'channelName',
    except: true,
  },
  {
    title: '所属项目',
    type: 'proselect',
    field: 'projectId',
    except: true,
  },
  {
    title: '渠道类型',
    type: 'select',
    field: 'channelType',
    except: true,
    dict: [ ...getDic('chatbot.channelType')],
  },
  {
    title: '通道Id',
    type: 'input',
    field: 'passId',
    except: true,
  },
  {
    title: '欢迎语',
    type: 'textarea',
    field: 'welcomeReply',
    maxlength:400,
    except: true,
  },
  {
    title: '输入框引导语',
    type: 'textarea',
    field: 'inputPlaceHolder',
    except: true,
  },
  {
    title: '无答案文案',
    type: 'textarea',
    field: 'noAnswerReply',
    except: true,
  },
  {
    title: '结束语文案',
    type: 'textarea',
    field: 'finalWords',
    except: true,
  },
  {
    title: '启用状态',
    type: 'switch',
    field: 'status',
    except: true,
  },
  {
    title: '是否开启常用卡片',
    type: 'switch',
    field: 'commonCardStatus',
    except: true,
  },
  {
    title: '是否开启猜你想问',
    type: 'switch',
    field: 'guessQuestionStatus',
    except: true,
  },
  {
    title: '是否开启常用短语',
    type: 'switch',
    field: 'commonPhraseStatus',
    except: true,
  },
  {
    title: '是否开启答案评价',
    type: 'switch',
    field: 'answerEvaluateStatus',
    except: true,
  },
  {
    title: '是否开启语音录入',
    type: 'switch',
    field: 'voiceStatus',
    except: true,
  },
  // {
  //   title: '是否开启转人工',
  //   type: 'switch',
  //   field: 'transferHumanStatus',
  // },
  {
    title: '是否开启问题补全',
    type: 'switch',
    field: 'contextualizeQuestionStatus',
    except: true,
  },
  {
    title: '是否开启上传图片',
    type: 'switch',
    field: 'uploadFileStatus',
    except: true,
    labelWidth: '90px'
  },
  {
    title: '是否开启语音播报',
    type: 'switch',
    field: 'voiceSynthesis',
    except: true,
     labelWidth:"135px"
  },
  {
    title: '音色编码',
    type: 'input',
    field: 'voiceSpeaker',
    except: true,
     labelWidth:"135px"
  },
  // {
  //   title: '对话缓存条数',
  //   type: 'input',
  //   field: 'memoryCacheNum',
  // },
  // {
  //   title: '备注',
  //   type: 'textarea',
  //   field: 'remark',
  // },
]

const functionList = [
  { label: '启用状态', prop: 'status' },
  // { label: '是否开启常用卡片', prop: 'commonCardStatus' },
  // { label: '是否开启猜你想问', prop: 'guessQuestionStatus' },
  // { label: '是否开启常用短语', prop: 'commonPhraseStatus' },
  // { label: '是否开启问题补全', prop: 'contextualizeQuestionStatus' },
  { label: '是否开启上传图片', prop: 'uploadFileStatus' },
  { label: '是否开启答案评价', prop: 'answerEvaluateStatus' },
  { label: '是否开启语音录入', prop: 'voiceStatus' },
]

const rules: FormRules = {
  channelName: [{ required: true, message: '请输入渠道名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
  projectId: [{ required: true, message: '请选择所属项目', trigger: 'blur' }],
  // passId: [{ required: true, message: '请输入通道Id', trigger: 'blur' }],
  // welcomeReply: [{ required: true, message: '请输入欢迎语', trigger: 'blur' }],
  // inputPlaceHolder: [{ required: true, message: '请输入输入框引导语', trigger: 'blur' }],
}
const onTypeChange = (form: any, args: string[]) => {
  console.log(form,args)
  if (args && args[0]) form.voiceSpeaker = ''
}
const submit = async (param: any) => {
  const { channelCode, id, channelId } = subData
  let status: string | number
  let msg: string
  // if (isEdit.value) {
    const res = await api.basicAdd({ ...param, channelCode, id,channelId})
    status = res.status
    msg = status == 0 ? '保存成功' : res.msg
    if(status==0){
      ElMessage.success(msg)
      formTempRef.value && formTempRef.value.clearValidate()
      formTempRef.value && formTempRef.value.resetFields()
    }
  // } else {
  //   const res = await api.basicAdd({ ...param, channelCode,id })
  //   status = res.status
  //   msg = status == 0 ? '保存成功' : res.msg
  //   if (status == 0) {
  //     subData.id = res.data.id
  //     isEdit.value = true
  //   }
  //   // todo
  // }

  if (status != 0) {
    if (/猜你想问问题数量/.test(msg)) {
      formTempRef.value.reset('guessQuestionStatus', 0)
    } else if (/常用短语数量/.test(msg)) {
      formTempRef.value.reset('commonPhraseStatus', 0)
    }

    return
  }
  ElMessage.success(msg)
}

const cancel = () => {
  delRouterCaches((route?.name as string) ?? '')
}

const init = async () => {
  const channelCode = route.query.channelCode ?? ''
  if (!channelCode) return
  const { status, data } = await api.basicGet(channelCode as string)
  if (status != 0 || !data) return
  isEdit.value = !!data.channelBasicConfigId
  data.guessQuestionStatus = Number(data.guessQuestionStatus)
  data.commonCardStatus = Number(data.commonCardStatus)
  data.commonPhraseStatus = Number(data.commonPhraseStatus)
  data.answerEvaluateStatus = Number(data.answerEvaluateStatus)
  data.voiceStatus =Number(data.voiceStatus)
  data.channelName = data.channelName
  data.status = Number(data.status)
  data.passId = data.passId
  data.projectId =  Number(data.projectId)
  data.sensitiveWordsReply = data.sensitiveWordsReply
  data.finalWords = data.finalWords
  data.defaultAnswer = data.defaultAnswer
  data.noAnswerReply = data.noAnswerReply
  data.transferHumanStatus =  Number(data.transferHumanStatus)
  data.contextualizeQuestionStatus = Number(data.contextualizeQuestionStatus)
  data.uploadFileStatus =Number(data.uploadFileStatus)
  data.memoryCacheNum = Number(data.memoryCacheNum)
  data.voiceSynthesis= Number(data.voiceSynthesis)
  data.voiceSpeaker=data.voiceSpeaker
  data.channelType = data.channelType
  data.remark=data.remark
  subData.channelCode = channelCode as string
  subData.id = data.channelBasicConfigId
  const channelId = route.query.channelId
  subData.channelId =  channelId as string 
  // rebind baseList value
  
  finished.value = true
  baseList.forEach((tmp) => {
    if (Object.hasOwn(data, tmp.field)) {
      tmp.value = data[tmp.field]
    }
  })
}

onMounted(async () => {
  await init()
})


defineExpose({
  search: async () => {
    finished.value = false
    await init()
    finished.value = true
  },
})
</script>

<style scoped>
:root {
  --theme-color: #D7A256;
  --theme-color-hover: #D7A256;
  --form-radius: 12px;
  --input-radius: 6px;
  --btn-radius: 6px;
  --page-bg: linear-gradient(135deg, #f9f6f1 0%, #f3e7d9 100%);
}

/* 页面外层背景色 */
body, #app {
  min-height: 100vh;
  background: var(--page-bg);
  /* 如果你有外层div，可以加上对应class选择器 */
}

/* 或者如果有外层div包裹，可以这样写 */
.page-bg {
  min-height: 100vh;
  background: var(--page-bg);
  width: 100vw;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 0;
}

/* 保证form-card有足够的z-index */
.form-card {
  position: relative;
  z-index: 1;
  background: #fff;
  max-width: 100vw;
  width: 100vw;
  padding: 0;
  margin: 0;
  border-radius: 0;
  box-sizing: border-box;
  /* padding-right:20px; */
  /* padding: 20px; */
  left: -20px;
  paddgin-right:0px;
  /* box-shadow: 0 12px 32px rgba(215,162,86,0.13); */
  /* transition: box-shadow 0.3s, border-color 0.3s, transform 0.25s; */
  /* box-shadow: 0 12px 32px rgba(215,162,86,0.22); */
}


.el-form-item {
  margin-bottom: 28px;
}

.el-form-item__label {
  font-weight: bold;
  color: var(--theme-color);
  font-size: 14px;
  letter-spacing: 1px;
}

.el-form-item__content {
  min-width: 220px;
}

.form-card .el-form-item:last-child {
  margin-bottom: 0;
}


.form-card .el-form-item__content {
  display: flex;
  justify-content: flex-end;
  max-width: 640px; /* 让按钮区域不会太靠右 */
  margin: 0 auto;
}

/* 输入框美化 */
.el-input__inner,
.el-textarea__inner {
  border-radius: var(--input-radius, 8px);
  padding: 10px 14px;
  border: 1.5px solid #dcdfe6;
  transition: border-color 0.2s, box-shadow 0.2s, background 0.2s;
  font-size: 16px;
  background: #fafdff;
  box-shadow: 0 1px 2px rgba(215,162,86,0.03) inset;
}
.el-input__inner:focus,
.el-textarea__inner:focus {
  border-color: var(--theme-color, #D7A256);
  box-shadow: 0 0 8px var(--theme-color-hover, #D7A256);
  background: #fff;
}

/* 响应式适配 */
@media (max-width: 600px) {
  .form-card {
    padding: 20px 8px 16px 8px;
    max-width: 98vw;
  }
  .el-form-item__label {
    font-size: 14px;
  }
  .form-card .el-form-item__content .el-button,
  .form-card .el-form-item__content .el-button + .el-button {
    padding: 8px 12px;
    font-size: 14px;
  }
}
/* .custom-form-item {
  
  margin-bottom:20px;
}  */
.custom-form-item,
.custom-form-item1 {
  min-height: 64px; /* 你可以根据实际需求调整数值 */
  /* 如果想绝对一致，也可以用 height: 64px; */
  display: flex;
  align-items: center;
} 
.custom-form-item1 {
  /* background: rgba(255,255,255,0.85); */
  border-radius: var(--input-radius, 8px);
  /* padding: 18px 30px 18px 30px;  原来左右30px */
  padding: 18px 20px 18px 0px; /* 左右加大一点，整体更居中 */
  margin-bottom: 12px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(215,162,86,0.04);
  /* border: 1px solid #f0f6ff; */
  transition: box-shadow 0.2s, background 0.2s, border-color 0.2s;
  position: relative;
  margin-top:20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.custom-form-item .el-form-item__label {
  font-weight: 600;
  color: var(--theme-color, #D7A256);
  font-size: 14px;
  letter-spacing: 1px;
  margin-bottom: 6px;
  text-shadow: 0 1px 0 #fff;
}
.custom-form-item .el-form-item__content {
  min-width: 180px;
  align-items: center;
  display: flex;
  min-height: 40px; /* 你可以根据实际情况调整 */
}
.custom-form-item .el-input__inner,
.custom-form-item .el-textarea__inner {
  border-radius: var(--input-radius, 8px);
  padding: 12px;
  border: 1.5px solid #dcdfe6;
  transition: border-color 0.2s, box-shadow 0.2s, background 0.2s;
  font-size: 16px;
  background: #fafdff;
  box-shadow: 0 1px 2px rgba(215,162,86,0.03) inset;
}
.custom-form-item .el-input__inner:focus,
.custom-form-item .el-textarea__inner:focus {
  border-color: var(--theme-color, #D7A256);
  box-shadow: 0 0 8px var(--theme-color-hover, #D7A256);
  background: #fff;
}
.custom-form-item .el-switch.is-checked .el-switch__core {
  border-color: var(--theme-color, #D7A256) !important;
  background-color: var(--theme-color, #D7A256) !important;
  box-shadow: 0 0 4px var(--theme-color-hover, #D7A256);
}


.form-section-card {
  width: 100%;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
  border:none;
  /* background: #fffdfa; */
}
.card-header {
  font-size: 14px;
  font-weight: bold;
  color: var(--theme-color, #D7A256);
  letter-spacing: 1px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f7ecdd;
  background: transparent;
  margin-bottom: 12px;
}
.card-header1 {
  font-size: 14px;
  font-weight: bold;
  color: var(--theme-color, #D7A256);
  letter-spacing: 1px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f7ecdd;
  background: transparent;
  margin-bottom: 12px;
  /* margin-top: 20px; */
}
.card-header2{
  font-size: 14px;
  font-weight: bold;
  color: var(--theme-color, #D7A256);
  letter-spacing: 1px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f7ecdd;
  background: transparent;
  margin-bottom: 12px;
  /* margin-top: 10px; */
}

.big-func-card {
  margin-bottom: 24px;
  border-radius: 16px;
  /* 渐变背景 */
  /* 更柔和的阴影 */
  box-shadow: 0 6px 24px rgba(215,162,86,0.13), 0 1.5px 8px rgba(215,162,86,0.08);
  /* 增加边框 */
  border: 1.5px solid #f7ecdd;
  /* 内边距让内容更舒展 */
  padding: 18px 28px 18px 28px;
  /* 过渡动画让交互更丝滑 */
  transition: box-shadow 0.3s, border-color 0.3s, background 0.3s;
  /* 鼠标悬浮时高亮 */
}
.big-func-card:hover {
  box-shadow: 0 12px 32px rgba(215,162,86,0.22), 0 2px 12px rgba(215,162,86,0.12);
  border-color: #d7a256;
  /* background: #f7ecdd; */
  /* 可根据需要调整高亮色 */
}

.form-section-card:first-child {
  /* border-left: none; */
}
.form-section-card:last-child {
  /* border-right: none; */
}

.el-col {
  overflow: visible;
  position: relative;
}
.el-col:last-child {
  padding-right: 0;
}

.row-padding-x {
  padding-left: 5x;
  padding-right: 5px;
}


.move-left {
  margin-left: -12px; /* 负值向左移动，数值可调整 */
}

/* 统一表单项宽度和对齐 */
:deep(.el-form-item) {
  width: 100%;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}
:deep(.el-form-item__label) {
  width: 140px !important;
  min-width: 140px;
  text-align: right;
  padding-right: 12px;
  font-size: 14px;
}
/* .custom-form-item1 .el-form-item__label {
  font-weight: 600;
  color: var(--theme-color, #D7A256);
  font-size: 17px;
  letter-spacing: 1px;
  margin-bottom: 6px;
  text-shadow: 0 1px 0 #fff;
  padding-left: 30px;
} */
:deep(.el-form-item__content) {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}
:deep(.el-input), :deep(.el-select), :deep(.el-textarea) {
  width: 100% !important;
  min-width: 180px;
}
:deep(.el-switch) {
  margin-left: 0;
}

/* 保证 input 和 select 宽度一致 */
:deep(.el-input), :deep(.el-select) {
  width: 100%;
}
.form-card, .form-section-card, .el-row, .el-col, .custom-form-item {
  box-sizing: border-box;
  max-width: 102%;
  overflow: hidden;
}
@media (max-width: 900px) {
  .el-col {
    min-width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }
}
.section-divider {
  border-bottom: 1.5px solid #f7ecdd;
  margin: 24px 0 0 0;
  width: 100%;
}
</style>
