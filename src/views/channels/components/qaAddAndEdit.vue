<template>
  <DtNav :from="nav.from" :fromPath="'/channelManage'" :subFrom="nav.subFrom" :name="nav.to" />
  <div class="p-[15px]">
    <TableToolTemp toolTitle="基本信息" />
    <UpdateFormTemp v-if="finished" class="pt-5" :form-list="baseList" :rules="rules" show-submit
      :certainText="certainText" :cancelText="'返回'" label-position="left" @submit="submit">
      <template #default="{ form }">
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型" clearable @change="onTypeChange(form, $event)">
            <el-option v-for=" (el, idx) in getDic('chatbot.cardType')" :key="idx" :label="el.dicItemName"
              :value="el.dicItemCode" />
          </el-select>
        </el-form-item>

        <el-form-item label="来源" prop="sourceType">
          <el-checkbox-group v-model="form.sourceType">
            <el-checkbox v-for="el in getDic('chatbot.guessQuestionType')" :label="el.dicItemCode">
              {{ el.dicItemName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="内容" prop="content" v-if="form?.type == 2" ref="contentRef">

          <el-input type="textarea" maxlength="50" v-model="form.content" rows="4" />
          <!-- <Editor v-model="form.content" :maxlength="300" trim @change="onContentChange" /> -->
        </el-form-item>

        <div v-else-if="form?.type == 3">
          <el-form-item label="知识类型" prop="knowledgeType">
            <el-radio-group v-model="form.knowledgeType">
              <el-radio label="1">QA</el-radio>
              <el-radio label="2">知识</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="内容" prop="content">
            <KnowsList v-model="form.content" ref="knowsRef"
              :param="{ robotId: channelConfig.robotId, type: 1, knowledgeType: form.knowledgeType, knowledgeTitle }" />
          </el-form-item>
        </div>

        <el-form-item label="内容" prop="content" v-else>
          <el-input v-model="form.content" placeholder="请输入内容" clearable />
          <div class="text-[12px] text-gray-400 leading-normal mt-2">
            说明：跳转页面支持内置参数配置，包含token、userId。使用示例：https://www.dtinsure.com/index?token=${token}
          </div>
        </el-form-item>
      </template>
    </UpdateFormTemp>
  </div>
</template>

<script setup lang="ts">
import type { UpdateFormList } from '@/components/types'
import type { FormRules } from 'element-plus'
import { reactive, onBeforeMount, computed, ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRoute, useRouter } from 'vue-router'
import useChannel from '@/stores/channel'
import * as api from '@/api/channels'
import KnowsList from './knowsList.vue'
// import Editor from '@/components/Editor.vue'

const { channelConfig } = useChannel()
const finished = ref(false)

const { query } = useRoute()
const { back } = useRouter()
const knowledgeTitle = ref('')
const contentRef = ref()
const isEdit = computed(() => {
  return !!query.id
})
const knowsRef = ref(null)

const { getDic } = useUserStore()
const nav = reactive({
  from: '渠道管理',
  subFrom: '配置',
  to: `猜你想问${isEdit.value ? '编辑' : '新增'}`,
})
const certainText = computed(() => {
  return !isEdit.value ? '保存' : '更新'
})
const baseList: UpdateFormList[] = [
  {
    title: '标题',
    type: 'input',
    field: 'title',
  },
  {
    title: '启用状态',
    type: 'switch',
    field: 'status',
    value: 1
  },
  {
    title: '类型',
    type: 'select',
    field: 'type',
    placeholder: '请选择类型',
    except: true,
  },
  {
    title: '内容',
    type: 'textarea',
    field: 'content',
    except: true,
  },
  {
    title: '知识类型',
    type: 'radio',
    field: 'knowledgeType',
    value: '1',
    isHidden: true
  },
  {
    title: '来源',
    type: 'checkbox',
    field: 'sourceType',
    value: ['01'],
    isHidden: true
  },
]

const rules: FormRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  status: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
  knowledgeType: [{ required: true, message: '请选择类型', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  sourceType: [{ required: true, message: '请选择来源', trigger: 'blur' }],
}

const submit = async (param: any) => {
  const params = JSON.parse(JSON.stringify(param))
  params.sourceType = params.sourceType.join(',')
  let res: any
  let knowledgeTitle = knowsRef.value?.knowTitle || ''
  if (!isEdit.value) {
    res = await api.qaAdd({ ...params, channelId: query.channelId as string, knowledgeTitle,channelCode: query.channelCode as string })
  } else {
    res = await api.qaUpdate({ ...params, channelId: query.channelId as string, id: query.id as string, knowledgeTitle, channelCode: query.channelCode as string })
  }

  if (res.status != 0) return
  ElMessage.success(`${isEdit.value ? '更新' : '保存'}成功`)

  back()
}

async function init() {
  if (!isEdit.value) return

  const { status, data } = await api.qaDetail(query.id as string)
  if (status != 0) return
  data.sourceType = data.sourceType.split(',')
  data.status = Number(data.status)
  baseList.forEach((tmp) => {
    if (Object.hasOwn(data, tmp.field) && data[tmp.field] !== null) {
      tmp.value = data[tmp.field]
    }
  })
  knowledgeTitle.value = data.knowledgeTitle
}

const onTypeChange = (form: any, args: string[]) => {
  if (args && args[0]) form.content = ''
}

const onContentChange = () => {
  if (contentRef.value) contentRef.value.validate()
}

onBeforeMount(async () => {
  await init()
  finished.value = true
})
</script>
