<template>
  <div>
    {{ knowTitle }}
    <el-button class="ml-20px" type="primary" @click="show = true">绑定</el-button>
  </div>

  <DtPopup v-model:show="show" @close="onClose" @close-by-icon="onClose" title="选择知识配置" :footer="false" width="1000px"
    :destroy="true">
    <PageTable :searchFormTemp="searchForm" apiUrl="/web/knowledgeconfig/pageSearch" ref="knowsRef" :param="param">
      <template #default="{ tableData }">
        <el-radio-group v-model="selectId" @change="onSelect" class="w-full">
          <el-table :data="tableData" class="dt-table" stripe>
            <el-table-column label="操作" width="70px" prop="knowledgeId" fixed>
              <template #default="scope" class="flex justify-center">
                <div class="flex justify-center">
                  <el-radio :label="scope.row.knowledgeId" size="large" @click="setName(scope.row.title)">{{ }}</el-radio>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="knowledgeId" label="编码" align="center" fixed width="200">
              <template #default="scope">
                <el-button type="text" @click="toKnowledge(scope.row)">{{ scope.row.knowledgeId }}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="标题" align="center" fixed width="340" />
            <el-table-column prop="state" label="状态" align="center" width="80">
              <template #default="scope">
                <span>{{ getDicItemName(scope.row.state, 'km.knowledge.state') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="updateName" label="作者" align="center" width="80" />
            <el-table-column prop="updateTime" label="更新时间" align="center" width="180" />
          </el-table>
        </el-radio-group>
      </template>
    </PageTable>
  </DtPopup>


  <!-- 知识库iframe -->
  <div class="iframe-dialog" v-if="showKnowledgeIframe">
    <DtIcon icon-name="icondt25" class="popup-icon" @click="closeKnowledgeIframe" />
    <iframe :src="iframeSrc" frameborder="0" height="100%" width="100%"></iframe>
  </div>
</template>

<script setup lang="ts">
import useFilters from '@/hooks/useFilters'
import { ref } from 'vue'
import type { SearchFormTemp } from '@/components/types'
import { getFuncId } from '@/api/channels'
import useSystemStore from '@/stores/system'
import { onMounted } from 'vue'

const systemStore = useSystemStore()
const selectId = ref('')
const { getDicItemName } = useFilters()
const props = withDefaults(
  defineProps<{
    modelValue: string
    param?: any
  }>(),
  {
    modelValue: '',
  }
)

const emits = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'close'): void
}>()


const show = ref(false)

const showKnowledgeIframe = ref(false)
const iframeSrc = ref('')

// TODO wait more filter conditions
const searchForm: SearchFormTemp[] = [
  {
label: '标题',
name: 'title',
type: 'input',
placeholder: '请输入标题',
searchState: true
},
  {
label: '编码',
name: 'knowledgeId',
type: 'input',
placeholder: '请输入编码',
searchState: true
},
]

const toKnowledge = async (row: any) => {
  const res = await getFuncId()
  const url = `${import.meta.env.VITE_KBC_PATH}/km-web/lookKnowledge?from=iframe&readOnly=true&id=${row.knowledgeId
    }&projectId=${row.projectId
    }&folderId=${row.folderId
    }&firstDirect=${row.firstDirect
    }&secondDirect=${row.secondDirect
    }&access_token=${systemStore.system.access_token
    }&tenantId=${systemStore.system.tenantId
    }&funcId=${res.data.paramValue
    }&themeColor=${systemStore.system.themeColor
    }&navTagColor=${systemStore.system.navTagColor}`
  console.log(url);
  iframeSrc.value = url
  showKnowledgeIframe.value = true
}
const closeKnowledgeIframe = () => {
  showKnowledgeIframe.value = false
}

const onClose = () => {
  emits('close')
}

const onSelect = (val: any) => {
  if (val) {
    emits('update:modelValue', val)
    show.value = false
  }
}

const knowTitle = ref<string>('')
const setName = (title: string) => {
  knowTitle.value = title
}

onMounted(() => {
  knowTitle.value = props.param.knowledgeTitle
  selectId.value = props.modelValue
})

defineExpose({ knowTitle })
</script>

<style lang="less" scoped>
.iframe-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 99999;

  .popup-icon {
    position: absolute;
    font-size: 65px;
    top: 7px;
    right: -61px;
    color: #b8b8b8;
    cursor: pointer;
  }
}
</style>