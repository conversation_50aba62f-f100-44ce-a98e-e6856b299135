<template>
    <div class="index-manage-container">
        <UniversalTable
          title="智能体列表"
          :title-icon="Document"
          :table-data="tableData"
          :loading="loading"
          :noHeader="true"
          :columns="tableColumns"
          :actions="tableActions"
          :search-form-config="searchForm"
          :search-params="initParam"
          :pagination-data="initParam"
          :total="total"
          empty-title="暂无智能体"
          add-button-text="新增"
          @search="getTableData"
          @add="handleAdd"
          @action-click="handleAction"
        >
        <!-- 机器人编码 -->
            <template #robotCode="{ row }">
            <div class="code-cell">
              <div class="code-icon">
                <!-- <i class="el-icon-document-copy"></i> -->
                <el-icon><Document /></el-icon>
              </div>
              <span class="code-text">{{ row.robotCode }}</span>
            </div>
          </template>
          <template #robotType="{ row }">
            <span>{{ getDicItemName(row.robotType, 'chatbot.botType') }}</span>
          </template>
          <template #createTime="{ row }">
                <div class="time-cell">
                  <el-icon><Clock /></el-icon>
                  <span>{{ row.createTime }}</span>
                </div>
              </template>
          <template #createBy="{ row }">
            <div class="user-cell">
                <div class="user-avatar">
                  <el-icon><User /></el-icon>
                </div>
                <span>{{ getNickName(row.createBy) }}</span>
              </div>
          </template>
          <template #status="{ row }">
            <el-switch   class="el-switch" v-model="row.status" active-value="1" inactive-value="0"
              @change="onOpen(row.id, $event)" />
          </template>
          <!-- <template #actions="{ row }">
            <el-button link type="primary" @click="onEdit(row)">配置提示词</el-button>
            <el-button link type="primary" @click="onRemove(row)">删除</el-button>
          </template> -->
        </UniversalTable>
        <UniversalFormDialog
          ref="editDialogRef"
          v-model="showEditDialog"
          :form-data="editForm"
          :form-fields="formFields"
          :is-edit="editForm.isEdit"
          :form-rules="editRules"
          :loading="saveLoading"
          add-title="新增智能体"
          edit-title="配置智能体"
          @confirm="handleSave"
          @cancel="() => showEditDialog = false"
        />
        <ConfirmDialog
          ref="confirmDialog"
          v-model="showDel"
          title="确认删除"
          message="删除后无法恢复，是否确认？"
          @confirm="confirmUpdate"
          @cancel="() => showDel = false"
        />
      </div>
</template>

<script setup lang="ts">
  import { ref, computed, reactive, onMounted, nextTick, isRef, watch,onBeforeMount} from 'vue'
  import type { SearchFormTemp, ToolListProps } from '@/components/types'
  import useFilters from '@/hooks/useFilters'
  import { useUserStore } from '@/stores/user'
  import { useRouterStore } from '@/stores/router'
  import * as api from '@/api/channels'
  import { useRouter,useRoute } from 'vue-router'
  import useChannel from '@/stores/channel'
  import UniversalTable from '@/components/UniversalTable.vue'
  import UniversalFormDialog from '@/components/UniversalFormDialog.vue'
  import ConfirmDialog from '@/components/ConfirmDialog.vue'
  import { Edit, Delete, Document,Clock,User } from '@element-plus/icons-vue'
  const route = useRoute()
  const { channelConfig } = useChannel()
  const { setId } = useChannel()
  const { push } = useRouter()
  const { getNickName, getDicItemName } = useFilters()
  const { getDic } = useUserStore()
  const pageTitle = '智能体配置'
  const intelligentRef = ref()
  const editDialogRef = ref()
  const toolList: ToolListProps[] = [
    {
      name: '添加',
      icon: 'add',
      btnCode: '',
      action: async () => {
        push({ path: '/IntelligentAdd', query: { channelCode: subData.channelCode, channelId:subData.channelId } })
      },
    },
  ]
  const subData = {
    channelId: '',
    id: '',
    channelCode: '',

  }
  const searchForm: SearchFormTemp[] = [
    {
    label: '机器人名称',
    name: 'robotName',
    type: 'input',
    placeholder: '请输入机器人名称',
    searchState: false
    },
    {
      label: '机器人编号',
      name: 'robotCode',
      type: 'input',
      placeholder: '请输入机器人编号',
      searchState: false
    },
  ]
  let switchReady = false
setTimeout(() => { switchReady = true }, 1000)
  const onOpen = async (id: string, ...arg: any) => {
    if (!switchReady) return
    await api.channelrobotUpdate({ id, status: arg[0], channelCode: subData.channelCode, channelId:subData.channelId })
  }
  const onEdit = (data: any) => {
    const { updateActiveParams } = useRouterStore()
    updateActiveParams({ name: 'channelShortWords', params: data })
    setId(data)
    push({ path: 'channelShortWords', query: { id: data.id,robotType:data.robotType} })
  }
  
  /**
   * @description 删除
   */
  const onRemove = async (data: any) => {
    ElMessageBox.confirm('确定删除该渠道机器人吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      await api.channelrobotDelete(data.id)
      await intelligentRef.value.onSearchBtn()
    })
  }
  
  const search = () => {
    if (!intelligentRef.value) return
    intelligentRef.value.onSearchBtn()
  }
  
  defineExpose({ search })

  const tableData = ref([])
  const loading = ref(false)
  const showDel= ref(false)
  const showEditDialog=ref(false)
  const saveLoading=ref(false)
  const total = ref(0)
  const confirmDialog = ref()
  const deleteRow = ref(null)
  const initParam = ref({
    pageNum: 1,
    pageSize: 10,
    param: { title: '', type: '', status: '', sourceTypeFuzzy: '', channelCode:subData.channelCode }
  })
  const getTableData = async (param = {}) => {
    loading.value = true
    try {
      const res = await api.channelrobotPage(
        {
           ...initParam.value,
            param:
          {
            channelCode:subData.channelCode,
            channelId: subData.channelId,
            ...param
          }
       }
      )
      tableData.value = (res.data?.list || []).map(item => ({
      ...item,
      status: Number(item.status) // 保证 status 是数字
    }))
      total.value = res.data?.total || 0
    } finally {
      loading.value = false
    }
  }
  const tableColumns = [
    { prop: 'robotCode', label: '编号', align: 'center', minWidth: '140px', slot: 'robotCode'},
    { prop: 'robotName', label: '名称', align: 'center', minWidth: '110px' },
    { 
      prop: 'robotType', 
      label: '类型', 
      align: 'center', 
      minWidth: '80px', 
      slot: 'robotType' // 需要自定义渲染
    },
    { prop: 'createTime', label: '创建时间', align: 'center', minWidth: '110px', slot: 'createTime' },
    { 
      prop: 'createBy', 
      label: '创建人', 
      align: 'center', 
      minWidth: '80px', 
      slot: 'createBy' // 需要自定义渲染
    },
    { 
      prop: 'status', 
      label: '状态', 
      align: 'center', 
      width: '80px', 
      slot: 'status' // 需要自定义渲染
    },
  ]
  const tableActions = [
    { key: "edit", label: "配置提示词", icon: Edit, class: "edit-btn" },
    { key: "delete", label: "删除", icon: Delete, class: "delete-btn" }
  ]
  const initList = ref<Record<string, any>[]>([])

  const editForm = reactive({
    robotType: "",
    robotId: "",
    isEdit: false
  })

  // 获取机器人名称列表
  async function robotInit(robotType: string) {
    if (!robotType) {
      initList.value = []
      return
    }
    const { status, data } = await api.maasRobotList({ robotType })
    if (status !== 0) return
    // 数据处理：转成 label/value 结构
    initList.value = (data || []).map(item => ({
      label: item.robotName, // 这里根据实际字段名调整
      value: item.id
    }))
  }

  // 机器人类型变更时，清空机器人名称并重新拉取
  function onTypeChange(value: string) {
    editForm.robotId = ""
    robotInit(value)
  }

  // formFields 配置
  const rawBotTypeList = getDic('chatbot.botType') || []
  const botTypeList = rawBotTypeList.map(item => ({
    label: item.dicItemName,
    value: item.dicItemCode
  }))

  const robotOptions = computed(() => initList.value)

  const formFields = [
    {
      label: '机器人类型',
      prop: 'robotType',
      type: 'select',
      placeholder: '请选择机器人类型',
      required: true,
      options: botTypeList,
      onChange: onTypeChange
    },
    {
      label: '机器人名称',
      prop: 'robotId',
      type: 'select',
      placeholder: '请选择机器人名称',
      required: true,
      options: []
    }
  ]

  watch(initList, (val) => {
    formFields[1].options = val
  })

  const editRules = {
    title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
    status: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
    sort: [{ required: true, message: '请选择排序', trigger: 'blur' }],
    robotType: [{ required: true, message: '请选择机器人类型', trigger: 'blur' }],
    robotId: [{ required: true, message: '请选择机器人', trigger: 'change' }],
    content: [
      { required: true, message: '请选择或填写内容', trigger: 'blur' },
      {
        max: 300,
        message: '内容不能超过300个字符',
        trigger: 'change',
      },
    ],
}
  // 新增时重置表单
  const handleAdd = async () => {
    resetEditForm()
    // 先拉取机器人类型的 options
    const defaultType = botTypeList[0]?.value || ""
    await robotInit(defaultType) // 拉取机器人名称
    nextTick(() => {
      editDialogRef.value?.clearFormValidate?.()
    })
    showEditDialog.value = true
  }
  const resetEditForm = () => {
  Object.assign(editForm, {
    robotType: "",
    robotId: "",
    isEdit: false
  })
}
  // 编辑时回显表单
  const handleEdit = async (row) => {
    // Object.assign(editForm, { ...row, isEdit: true })
    // await robotInit(row.robotType)
    // showEditDialog.value = true
    const { updateActiveParams } = useRouterStore()
    updateActiveParams({ name: 'channelShortWords', params: row })
    setId(row)
    push({ path: 'channelShortWords', query: { id: row.id,robotType:row.robotType} })
  }
  const handleDelete = (row) => {
    deleteRow.value = row
    showDel.value = true
    // confirmDialog.value.show()
  }
  const handleSave = async (formData) => {
    saveLoading.value = true
    try {
      let res
      if (!formData.isEdit) {
        const params = {
        robotType: formData.formData.robotType,
        robotId: formData.formData.robotId,
        channelCode:subData.channelCode,
        channelId: subData.channelId
      }
        res = await api.robotAdd(params)
      } else {
        res = await api.qaUpdate(formData)
      }
      if (res && res.status === 0) {
        showEditDialog.value = false
        getTableData()
      }
    } finally {
      saveLoading.value = false
    }
  }
  const confirmUpdate = async () => {
    if (deleteRow.value) {
      await api.channelrobotDelete(deleteRow.value.id)
      getTableData()
      showDel.value = false
      deleteRow.value = null
    }
  }
  const handleAction = (item) => {
    if (item.action.key === 'edit') handleEdit(item.row)
    if (item.action.key === 'delete') handleDelete(item.row)
  }
  const onSwitchChange = async (id, value) => {
    await api.qaUpdate({ id, status: value, channelCode: route.query.channelCode })
    getTableData()
  }
  onBeforeMount(async () => {
    subData.channelId = (route.query.channelId as string) ?? ''
    subData.channelCode = (route.query.channelCode as string) ?? ''
    getTableData()
    // await init()
    // finished.value = true
  })
</script>
<style lang="less" scoped>
.index-manage-container {
  .code-cell {
    display: flex;
    align-items: center;
    justify-content: center; // 改成左对齐
    gap: 8px;
    .code-icon {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        color: #d7a256;
        font-size: 12px;
      }
    }
    .code-text {
      font-family: "Courier New", monospace;
      font-weight: 500;
      color: #2c3e50;
      font-size: 13px;
    }
  }
  .name-cell {
    .name-text {
      font-weight: 500;
      color: #2c3e50;
    }
  }
  .type-cell {
    .calculate-tag {
      background: rgba(64, 158, 255, 0.1);
      color: #409eff;
      border: 1px solid rgba(64, 158, 255, 0.3);

      i {
        margin-right: 4px;
      }
    }

    .result-tag {
      background: rgba(103, 194, 58, 0.1);
      color: #67c23a;
      border: 1px solid rgba(103, 194, 58, 0.3);

      i {
        margin-right: 4px;
      }
    }
  }
  :deep(.el-switch) {
    .el-switch__core {
      border-color: #e8dcc0;
      background: #e8dcc0;
    }
  }
  :deep(.el-switch.is-checked) {
    .el-switch__core {
      border-color: #D7A256;
      background: #D7A256;
    }
  }
  .remark-cell {
    .remark-text {
      color: #606266;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .no-remark {
      color: #c0c4cc;
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      i {
        font-size: 12px;
      }
    }
  }
  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;
    i {
      color: #d7a256;
      font-size: 14px;
    }
  }
  
  .user-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;
    .user-avatar {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }
  }
}
// :deep(.modern-tooltip) {
//   background: #2c3e50 !important;
//   color: white !important;
//   border-radius: 8px !important;
//   padding: 8px 12px !important;
//   font-size: 12px !important;
//   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
// }
// :deep(.el-loading-mask) {
//   background-color: rgba(251, 246, 238, 0.8) !important;
// }
</style>