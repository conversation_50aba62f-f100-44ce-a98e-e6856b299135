<template>
  <TableToolTemp :tool-title="pageTitle" :tool-list="toolList" />
  <PageTable :search-form-temp="searchForm" apiUrl="/web/channelcommonphrase/page" ref="shortWordRef"
    :param="{ channelCode }">
    <template #default="{ tableData }">
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column prop="phrase" label="短语" align="center" min-width="110px" />
        <el-table-column prop="type" label="类型" align="center" min-width="110px">
          <template #default="scope">{{ getDicItemName(scope.row.type, 'chatbot.cardType') }}</template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" align="center" min-width="110px" />
        <el-table-column prop="createTime" label="创建时间" align="center" min-width="110px" />
        <el-table-column prop="updateTime" label="最后更新时间" align="center" min-width="110px" />
        <el-table-column prop="updateId" label="操作人" align="center" min-width="110px">
          <template #default="scope">
            {{ getNickName(scope.row.updateId) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="启用状态" align="center" width="80px">
          <template #default="scope">
            <el-switch v-model="scope.row.status" active-value="1" inactive-value="0"
              @change="onOpen(scope.row.id, $event)" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button link type="primary" @click="onEdit(scope.row)">配置</el-button>
            <el-button link type="primary" @click="onRemove(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { SearchFormTemp, ToolListProps } from '@/components/types'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import * as api from '@/api/channels'
import useChannel from '@/stores/channel'

const { channelConfig } = useChannel()
const channelCode = computed(() => channelConfig.channelCode)
const channelId = computed(() => channelConfig.channelId)
const { push } = useRouter()
const { getNickName, getDicItemName } = useFilters()
const { getDic } = useUserStore()
const pageTitle = '常用短语列表'
const shortWordRef = ref()
const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: '',
    action: async () => {
      push({ path: '/shortWordsAddAndEdit', query: { channelCode: channelCode.value,channelId : channelId.value} })
    },
  },
]

const searchForm: SearchFormTemp[] = [
  {
label: '短语',
name: 'phrase',
type: 'input',
placeholder: '请输入短语',
searchState: true
},
  {
label: '类型',
name: 'type',
type: 'select',
placeholder: '请选择类型',
list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.cardType')],
searchState: true
},
  {
label: '启用状态',
name: 'status',
type: 'select',
placeholder: '请选择启用状态',
list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.common.status')],
searchState: true
},
]

const onOpen = async (id: string, ...arg: any) => {
  const { status } = await api.wordUpdate({ id, status: arg[0], channelCode: channelCode.value })
  await shortWordRef.value.onSearchBtn()
  if (status != 0) return
}

const onEdit = (data: any) => {
  push({ path: '/shortWordsAddAndEdit', query: { id: data.id, channelCode: channelCode.value, channelId : channelId.value } })
}


/**
 * @description 删除
 */
const onRemove = async (data: any) => {
  ElMessageBox.confirm('确定删除该常用短语吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.wordDel(data.id)
    await shortWordRef.value?.onSearchBtn()
  })
}

const search = () => {
  if (!shortWordRef.value) return
  shortWordRef.value.onSearchBtn()
}

defineExpose({ search })
</script>
