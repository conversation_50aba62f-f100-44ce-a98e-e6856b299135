<template>
  <div class="menu-table">
    <TableToolTemp tool-title="敏感词列表" :tool-list="toolList"></TableToolTemp>
    <el-table :data="tableData" class="dt-table" stripe>
      <el-table-column prop="words" label="敏感词" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" />
      <el-table-column prop="createId" label="操作人" align="center">
        <template #default="scope">
          {{ getNickName(scope.row.createId) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="remove(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination ref="paginationRef" @change="onPageData" :total="total" />
  </div>

  <DtPopup :show="showSensitiveWord" @close="closeDialog" @close-by-icon="closeDialog" :title="`添加敏感词`" :footer="false">
    <UpdateFormTemp ref="formTempRef" :form-list="sensitiveList" :rules="rules" :showSubmit="false"></UpdateFormTemp>
    <div class="formBtn">
      <el-button class="dt-btn" @click="closeDialog">取消</el-button>
      <el-button class="dt-btn" type="primary" @click="submit">保存</el-button>
    </div>
  </DtPopup>
</template>



<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router';
import { ToolListProps, PageTableSearch, PageData } from '@/components/types';
import useFilters from '@/hooks/useFilters';
import {
  channelsensitivewordsPage,
  channelsensitivewordsAdd,
  deleteSensitiveWords
} from '@/api/channels'
import type { FormRules } from 'element-plus'

const emit = defineEmits<{
  (e: 'updateStatus'): void
}>()
const props = defineProps({
  versionIndex: Number
})
const formTempRef = ref(null)
const { getNickName, getDicItemName } = useFilters()
const route = useRoute()
const toolList: ToolListProps[] = [
  {
    name: '添加敏感词',
    btnCode: '',
    action: async () => {
      showSensitiveWord.value = true
    },
  },
]
const sensitiveList = ref<Record<string, any>[]>([
  {
    title: '敏感词',
    type: 'input',
    field: 'words',
  },
])
const rules: FormRules = {
  words: [{ required: true, message: '请输入敏感词', trigger: 'blur' }],
}
const submit = async () => {
  const { words } = await formTempRef.value.getFormData()
  if (!words) return
  const res = await channelsensitivewordsAdd({
    channelCode: channelCode,
    channelId: channelId,
    words,
  })
  if (res.status === 0) {
    closeDialog()
    init()
  }
}

const showSensitiveWord = ref<boolean>(false)
const tableData = ref<Record<string, any>[]>([])
const total = ref<number>(0)
const robotId = ref<number>(Number(route.query.id))
const channelId = route.query.channelId
const channelCode = route.query.channelCode
const pageParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    channelId: channelId,
    channelCode: channelCode,
    words: ''
  }
})

const remove = async (row: Record<string, any>) => {
  ElMessageBox.confirm('是否删除该敏感词？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await deleteSensitiveWords({
      id: row.id,
    })
    if (res.status === 0) {
      init()
    }
  })
}


const closeDialog = async () => {
  showSensitiveWord.value = false
  await formTempRef.value.reset('words')
}

const onPageData = async (data: PageData) => {
  pageParam.value.pageNum = data.pageNum
  pageParam.value.pageSize = data.pageSize
  init()
}


const init = async () => {
  const res = await channelsensitivewordsPage(pageParam.value)
  if (res.status === 0) {
    tableData.value = res.data.list
    total.value = res.data.total
  }
}

watch(() => props.versionIndex, (val) => {
  init()
})

onMounted(async () => {
  await init()
})
defineExpose({
  search: async () => {
    await init()
  },
})
</script>

<style lang="less" scoped>
.menu-content {
  display: flex;

  // 不可全选样式
  :deep(.el-tree-node) {
    .el-checkbox__input.is-disabled {
      display: none;
    }
  }

  .menu-tree {
    width: 300px;
  }

  .menu-table {
    flex: 1;
    overflow-y: auto;
  }
}

.iframe-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 99999;

  .popup-icon {
    position: absolute;
    font-size: 65px;
    top: 7px;
    right: -61px;
    color: #b8b8b8;
    cursor: pointer;
  }
}
</style>