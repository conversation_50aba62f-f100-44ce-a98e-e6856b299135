<template>
  <!-- <TableToolTemp :tool-title="pageTitle" :tool-list="toolList" /> -->
  <div class="index-manage-container">
  <UniversalTable
    title="猜你想问列表"
    subtitle="管理和配置猜你想问"
    :title-icon="Document"
    :table-data="tableData"
    :loading="loading"
    :noHeader="true"
    :columns="tableColumns"
    :actions="tableActions"
    :search-form-config="searchForm"
    :search-params="initParam"
    :pagination-data="initParam"
    :total="total"
    add-button-text="新增"
    empty-title="暂无猜你想问"
    empty-description="点击上方新增按钮开始创建"
    @search="normalSearch"
    @reset="normalResetQuery"
    @add="handleAdd"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
    @action-click="handleAction"
  >
    <!-- 这里可以自定义slot，比如类型、状态等 -->
    <template #type="{ row }">
      <div class="type-cell">
        <el-tooltip :content="getDicItemName(row.type, 'chatbot.cardType')" placement="top">
          <el-tag
            v-if="row.type=='1'"
            class="calculate-tag"
            size="medium"
            effect="plain"
          >
            <el-icon style="margin-right: 4px;">
              <component :is="Link" />
            </el-icon>
            {{ getDicItemName(row.type, 'chatbot.cardType') }}
          </el-tag>
          <el-tag
            v-if="row.type=='2'"
            class="result-tag"
            size="medium"
            effect="plain"
          >
            <el-icon style="margin-right: 4px;">
              <component :is="Document" />
            </el-icon>
            {{ getDicItemName(row.type, 'chatbot.cardType') }}
          </el-tag>
          <el-tag
            v-if="row.type=='3'"
            class="draft-tag"
            size="medium"
            effect="plain"
          >
            <el-icon style="margin-right: 4px;">
              <component :is="Files" />
            </el-icon>
            {{ getDicItemName(row.type, 'chatbot.cardType') }}
          </el-tag>
        </el-tooltip>
      </div>
    </template>
    <template #sourceType="{ row }">
      <span v-for="(item, index) in row.sourceType.split(',')" :key="item">
        <span>{{ getDicItemName(item, 'chatbot.guessQuestionType') }}</span>
        <span v-if="index !== row.sourceType.split(',').length - 1">，</span>
      </span>
    </template>
    <template #status="{ row }">
      <el-switch
        :model-value="row.status"
        :active-value="1"
        :inactive-value="0"
        @change="onSwitchChange(row, $event)"
      />
    </template>
    <template #updateId="{ row }">
      <div class="user-cell">
        <div class="user-avatar">
          <el-icon><User /></el-icon>
        </div>
        <span>{{ getNickName(row.updateId) }}</span>
      </div>
    </template>
    <template #createTime="{ row }">
      <div class="time-cell">
        <el-icon><Clock /></el-icon>
        <span>{{ row.createTime }}</span>
      </div>
    </template>
    <template #updateTime="{ row }">
      <div class="time-cell">
        <el-icon><Clock /></el-icon>
        <span>{{ row.updateTime }}</span>
      </div>
    </template>
  </UniversalTable>
  <UniversalFormDialog
    ref="editDialogRef"
    v-model="showEditDialog"
    :form-data="editForm"
    :form-fields="formFields"
    :form-rules="editRules"
    :is-edit="editForm.isEdit"
    :loading="saveLoading"
    add-title="新增猜你想问"
    edit-title="配置猜你想问"
    @confirm="handleSave"
    @cancel="() => showEditDialog = false"
  >
    <template #content="{ field, formData }">
      <el-input
        v-if="formData.type == '2'"
        type="textarea"
        maxlength="50"
        v-model="formData.content"
        rows="4"
      />
      <div v-else-if="formData.type == '3'">
        <el-radio-group v-model="formData.knowledgeType">
          <el-radio label="1">QA</el-radio>
          <el-radio label="2">知识</el-radio>
        </el-radio-group>
        <KnowsList v-model="formData.content" :param="{ robotId: route.query.robotId, type: 1, knowledgeType: formData.knowledgeType }" />
      </div>
      <el-input
        v-else
        v-model.trim="formData.content"
        placeholder="请输入内容"
        clearable
      />
      <div class="text-[12px] text-gray-400 leading-normal mt-2" v-if="formData.type !== '3' && formData.type !== '2'">
        说明：跳转页面支持内置参数配置，包含token、userId。使用示例：https://www.dtinsure.com/index?token=${token}
      </div>
    </template>
  </UniversalFormDialog>
  <ConfirmDialog
    ref="confirmDialogRef"
    v-model="showDelDialog"
    title="确认删除"
    message="删除后无法恢复，是否确认删除该条数据？"
    icon="el-icon-warning"
    confirm-text="删除"
    cancel-text="取消"
    @cancel="() => showDelDialog = false"
    @confirm="confirmDelete"
  />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, nextTick, onMounted, watch } from 'vue'
import type { SearchFormTemp, ToolListProps } from '@/components/types'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import * as api from '@/api/channels'
import { useRoute, useRouter } from 'vue-router'
import UniversalTable from '@/components/UniversalTable.vue'
import UniversalFormDialog from '@/components/UniversalFormDialog.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'

// 新增这一行，补充图标
import { Document, Edit, Setting, Delete,Clock ,User,Cpu, DataLine,InfoFilled, Coin,Files,Link} from '@element-plus/icons-vue'
import KnowsList from './knowsList.vue'

const route = useRoute()
const channelCode = computed(() => route.query.channelCode)
const channelId = computed(() => route.query.channelId)

const { push } = useRouter()
const { getNickName, getDicItemName } = useFilters()
const { getDic } = useUserStore()
const pageTitle = '猜你想问列表'
const qaRef = ref()
const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: '',
    action: async () => {
      push({ path: '/qaAddAndEdit', query: { channelCode: channelCode.value, channelId: channelId.value } })
    },
  },
]

const searchForm: SearchFormTemp[] = [
  {
    label: '标题',
    name: 'title',
    type: 'input',
    placeholder: '请输入标题',
   searchState: false
    },
      {
    label: '类型',
    name: 'type',
    type: 'select',
    placeholder: '请选择类型',
    list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.cardType')],
   searchState: false
    },
      {
    label: '启用状态',
    name: 'status',
    type: 'select',
    placeholder: '请选择启用状态',
    list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.common.status')],
   searchState: false
    },
      {
    label: '来源',
    name: 'sourceTypeFuzzy',
    type: 'select',
    placeholder: '请选择来源',
    list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.guessQuestionType')],
   searchState: false
    },
]

const tableColumns = [
  { prop: 'title', label: '标题', align: 'center', minWidth: '110px' },
  { prop: 'type', label: '类型', align: 'center', minWidth: '110px', slot: 'type' },
  { prop: 'sourceType', label: '来源', align: 'center', minWidth: '110px', slot: 'sourceType' },
  { prop: 'createTime', label: '创建时间', align: 'center', minWidth: '110px', slot: 'createTime' },
  { prop: 'updateTime', label: '最后更新时间', align: 'center', minWidth: '110px', slot: 'updateTime' },
  { prop: 'updateId', label: '操作人', align: 'center', minWidth: '110px', slot: 'updateId' },
  { prop: 'status', label: '启用状态', align: 'center', width: '80px', slot: 'status' }
]

const tableActions = [
  { key: "edit", label: "配置", icon: Edit, class: "edit-btn" },
  { key: "delete", label: "删除", icon: Delete, class: "delete-btn" }
]

const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const initParam = ref({
  pageNum: 1,
  pageSize: 10,
  param: {
    title: "",
    type: "",
    status: "",
    sourceTypeFuzzy: "",
    channelCode: channelCode.value
  }
})

const getTableData = async () => {
  loading.value = true
  try {
    const res = await api.qaPage({
      param: { ...initParam.value.param },
      pageNum: initParam.value.pageNum,
      pageSize: initParam.value.pageSize
    })
    tableData.value = (res.data?.list || []).map(item => ({
      ...item,
      status: Number(item.status) // 保证 status 是数字
    }))
    total.value = res.data?.total || 0
  } finally {
    loading.value = false
  }
}

const normalSearch = (params) => {
  initParam.value = { ...initParam.value, param: { ...initParam.value.param, ...params }, pageNum: 1 }
  getTableData()
}
const normalResetQuery = () => {
  initParam.value = {
    pageNum: 1,
    pageSize: 10,
    param: { title: "", type: "", status: "", sourceTypeFuzzy: "", channelCode: channelCode.value }
  }
  getTableData()
}
const handleSizeChange = (size) => {
  initParam.value.pageSize = size
  getTableData()
}
const handleCurrentChange = (page) => {
  initParam.value.pageNum = page
  getTableData()
}

// 用于存储待删除的数据
let delRow: any = null

const handleAdd = () => {
  // 清空表单，打开弹窗
  Object.assign(editForm, {
    id: '',
    title: '',
    type: '',
    sourceType: '',
    content: '',
    status: 1,
    knowledgeType: '1',
    isEdit: false
  })
  showEditDialog.value = true
}

const handleEdit = (row) => {
  // 回显数据到表单，打开弹窗
  Object.assign(editForm, {
    ...row,
    isEdit: true,
    knowledgeType: row.knowledgeType || '1'
  })
  showEditDialog.value = true
}

const handleSave = async (formData) => {
  saveLoading.value = true
  const submitData = { ...formData.formData }
  try {
    if (formData.isEdit) {
      await api.qaUpdate({ ...submitData, channelCode: channelCode.value })
    } else {
      await api.qaAdd({ ...submitData, channelCode: channelCode.value })
    }
    showEditDialog.value = false
    getTableData()
  } finally {
    saveLoading.value = false
  }
}

const handleDelete = (row) => {
  delRow = row
  showDelDialog.value = true
}

const confirmDelete = async () => {
  if (!delRow) return
  await api.qaDel(delRow.id)
  showDelDialog.value = false
  getTableData()
  delRow = null
}


let switchReady = false
setTimeout(() => { switchReady = true }, 1000)

const onSwitchChange = async (row, value) => {
  if (!switchReady) return
  await api.qaUpdate({ id: row.id, status: value, channelCode: channelCode.value})
  getTableData()
}

const handleAction = async (item) => {
  if (item.action.key === 'edit') {
    handleEdit(item.row)
  } else if (item.action.key === 'delete') {
    handleDelete(item.row)
  }
}

const search = () => {
  if (!qaRef.value) return
  qaRef.value.onSearchBtn()
}

defineExpose({ search })

// UniversalFormDialog 相关字段
const editDialogRef = ref() // 弹窗ref
const showEditDialog = ref(false) // 弹窗显示/隐藏
const saveLoading = ref(false) // 保存按钮loading

// 编辑表单数据
const editForm = reactive({
  id: '',
  title: '',
  type: '',
  sourceType: '',
  content: '',
  status: 1,
  knowledgeType: '1', // 默认为QA
  isEdit: false
})

// 字典转label-value工具
function dicToOptions(dicArr) {
  return (dicArr || []).map(item => ({
    label: item.dicItemName,
    value: item.dicItemCode
  }))
}

// 表单字段配置
const formFields = [
  {
    label: '标题',
    prop: 'title',
    type: 'input',
    placeholder: '请输入标题',
    required: true,
    maxlength: 50
  },
  {
    label: '类型',
    prop: 'type',
    type: 'select',
    placeholder: '请选择类型',
    options: dicToOptions(getDic('chatbot.cardType')),
    required: true
  },
  {
    label: '来源',
    prop: 'sourceType',
    type: 'select',
    placeholder: '请选择来源',
    options: dicToOptions(getDic('chatbot.guessQuestionType')),
    required: true
  },
  {
    label: '内容',
    prop: 'content',
    type: 'slot', // 用slot自定义
    required: true
  }
]

// 表单校验规则
const editRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  sourceType: [{ required: true, message: '请选择来源', trigger: 'change' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
}

const showDelDialog = ref(false)

onMounted(() => {
  getTableData()
})

watch(
  () => route.query.channelCode,
  (newVal, oldVal) => {
    if (newVal) {
      getTableData()
    }
  }
)

</script>
<style lang="less" scoped>
.index-manage-container {
  .code-cell {
    display: flex;
    align-items: center;
    justify-content: flex-start; // 改成左对齐
    gap: 8px;
    .code-icon {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        color: #d7a256;
        font-size: 12px;
      }
    }
    .code-text {
      font-family: "Courier New", monospace;
      font-weight: 500;
      color: #2c3e50;
      font-size: 13px;
    }
  }
  .name-cell {
    .name-text {
      font-weight: 500;
      color: #2c3e50;
    }
  }
  .type-cell {
    .el-tag {
      height: 28px !important;
      line-height: 26px !important;
      font-size: 14px !important;
      padding: 0 10px !important;
      display: inline-flex !important;
      align-items: center !important;
      box-sizing: border-box !important;
      min-width: 80px; // 可选，保证宽度一致
      justify-content: center; // 可选，内容居中
    }
    .calculate-tag {
      background: rgba(64, 158, 255, 0.1);
      color: #409eff;
      border: 1px solid rgba(64, 158, 255, 0.3);

      i {
        margin-right: 4px;
      }
    }

    .result-tag {
      background: rgba(103, 194, 58, 0.1);
      color: #67c23a;
      border: 1px solid rgba(103, 194, 58, 0.3);

      i {
        margin-right: 4px;
      }
    }
    .draft-tag {
      background: rgba(230, 162, 60, 0.1);
      color: #e6a23c;
      border: 1px solid rgba(230, 162, 60, 0.3);
      i {
        margin-right: 4px;
      }
    }
  }
  :deep(.el-switch) {
    .el-switch__core {
      border-color: #e8dcc0;
      background: #e8dcc0;
    }
  }
  :deep(.el-switch.is-checked) {
    .el-switch__core {
      border-color: #D7A256;
      background: #D7A256;
    }
  }
  .remark-cell {
    .remark-text {
      color: #606266;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .no-remark {
      color: #c0c4cc;
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      i {
        font-size: 12px;
      }
    }
  }
  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;
    i {
      color: #d7a256;
      font-size: 14px;
    }
  }
  
  .user-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;
    .user-avatar {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }
  }
}
</style>
