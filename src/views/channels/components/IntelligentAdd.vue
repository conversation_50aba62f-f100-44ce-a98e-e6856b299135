<template>
    <DtNav :from="nav.from" :fromPath="'/channelManage'" :subFrom="nav.subFrom" :name="nav.to" />
    <div class="p-[15px]">
      <TableToolTemp toolTitle="基本信息" />
      <UpdateFormTemp  class="pt-5" :form-list="baseList" :rules="rules" show-submit label-position="left"
        :certainText="certainText" :cancelText="'返回'" @submit="submit">
        <template #default="{ form }">
          <el-form-item label="机器人类型" prop="robotType">
            <!-- <el-select v-model="form.robotType" placeholder="请选择类型" clearable @change="onTypeChange(form, $event)">
              <el-option v-for="(el, idx) in getDic('chatbot.cardType')" :key="idx" :label="el.dicItemName"
                :value="el.dicItemCode" />
            </el-select> -->
            <el-select v-model="form.robotType" placeholder="请选择类型" clearable @change="onTypeChange(form, $event)">
              <el-option v-for="(item, idx) in getDic('chatbot.botType')" :key="idx" :label="item.dicItemName" :value="item.dicItemCode"></el-option>
            </el-select>
          </el-form-item>
  
          <el-form-item label="机器人名称"  prop="robotId">
          <el-select v-model="form.robotId"  :disabled="robitState" placeholder="请选择机器人名称">
            <el-option
              v-for="item in initList"
              :key="item.id"
              :label="item.robotName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        </template>
      </UpdateFormTemp>
    </div>
  </template>
  
  <script setup lang="ts">
  import { reactive, onBeforeMount, computed, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import type { UpdateFormList } from '@/components/types'
  import type { FormRules } from 'element-plus'
  import * as api from '@/api/channels'
  import { useUserStore } from '@/stores/user'
  import useChannel from '@/stores/channel'
  import { cardDetail,maasRobotList } from '@/api/channels'
  // import Editor from '@/components/Editor.vue'
  import KnowsList from './knowsList.vue'
  import { nextTick } from 'process'
import { couldStartTrivia } from 'typescript'
  
  defineOptions({ name: 'channelCommonAddAndEdit' })
  
  const { channelConfig } = useChannel()
  // sub post data
  const subData = {
    channelId: '',
    id: '',
    channelCode: '',

  }
  
  const contentRef = ref()
  const route = useRoute()
  const router = useRouter()
  const finished = ref(false)
  const robitState =ref(true)
  const robotList: robotList[] =  [
        {
          name: "闲聊",
          id: "1"
        },
        {
          name: "云知识库问答",
          id: "2"
        },
        {
          name: "产品检索",
          id: "3"
        },
        {
          name: "产品推荐",
          id: "4"
        },
        {
          name: "方案推荐",
          id: "5"
        },
        {
          name: "人机对练",
          id: "6"
        }
  ]
  const initList = ref<Record<string, any>[]>([])
  const { getDic } = useUserStore()
  const isEdit = computed(() => {
    return !!route.query?.id
  })
  const toName = computed(() => `智能体${isEdit.value ? '编辑' : '新增'}`)
  const certainText = computed(() => {
    return !isEdit.value ? '保存' : '更新'
  })
  const nav = reactive({
    from: '渠道管理',
    subFrom: '配置',
    to: toName,
  })
  const knowsRef = ref(null)
  const knowledgeTitle = ref('')
  const baseList: UpdateFormList[] = [
    {
      title: '提示词类型',
      type: 'select',
      field: 'type',
      except: true,
    },
    {
      title: '内容',
      type: 'input',
      field: 'content',
      except: true,
    }
  ]
  
  const rules: FormRules = {
    title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
    status: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
    sort: [{ required: true, message: '请选择排序', trigger: 'blur' }],
    robotType: [{ required: true, message: '请选择机器人类型', trigger: 'blur' }],
    robotId: [{ required: true, message: '请选择机器人', trigger: 'blur' }],
    content: [
      { required: true, message: '请选择或填写内容', trigger: 'blur' },
      {
        max: 300,
        message: '内容不能超过300个字符',
        trigger: 'change',
      },
    ],
  }
  
  const submit = async (param: any) => {
    console.log('submit');
  
    const { channelId, id ,channelCode} = subData
  
    let status: string | number
    let msg: string
    let knowledgeTitle = knowsRef.value?.knowTitle || ''
    // if (!isEdit.value) {
      const res = await api.robotAdd({ ...param,channelCode,channelId })
      status = res.status
      msg = '保存成功'
    // } 
    // else {
    //   const res = await api.cardUpdate({ ...param, id,channelCode})
    //   status = res.status
    //   msg = '更新成功'
    // }
  
    if (status != 0) return
    ElMessage.success(msg)
  
    router.back()
    return
  }
  async function robotInit(robotType:any) {
       console.log(robotType)
    const { status, data }= await maasRobotList({robotType:robotType})
    if (status != 0) return
    initList.value = data
  }
  async function init() {
    if (!isEdit.value) return
  
    const id = route.query.id as string
    const { status, data } = await cardDetail(id)
    if (status != 0) return
  
    subData.id = data?.id as string
    subData.channelId = data?.channelId as string
    subData.channelCode = data?.channelCode as string
    data.status = Number(data.status)
  
    baseList.forEach((tmp) => {
      if (Object.hasOwn(data, tmp.field) && data[tmp.field] !== null) {
        tmp.value = data[tmp.field]
      }
    })
  }
  
  const onTypeChange = (form: any, args: string[]) => {
    console.log(form,args)
    if (args && args[0]) form.robotId = ''
        robitState.value = false;
        // this.addForm.robot_id = "";
        robotInit(args)
  }
  
  const onContentChange = () => {
    if (contentRef.value) contentRef.value.validate()
  }
  
  onBeforeMount(async () => {
    subData.channelId = (route.query.channelId as string) ?? ''
    subData.channelCode = (route.query.channelCode as string) ?? ''
    
    await init()
    finished.value = true
  })
  </script>
  