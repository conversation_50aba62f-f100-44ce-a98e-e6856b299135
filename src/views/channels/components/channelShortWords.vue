<template>
    <!-- <DtNav :from="nav.from" :fromPath="'/channelManage'" :subFrom="nav.subFrom" :name="nav.to" /> -->
    <!-- <TableToolTemp :tool-title="pageTitle" :tool-list="toolList" /> -->
      <!-- 页面头部和导航区域 -->
      <!-- <SecondaryPageHeader
      title="配置"
      icon="Document"
      :show-actions="true"
      :breadcrumb-items="breadcrumbItems"
      @breadcrumb-click="onBreadcrumbClick"
      @back="goBack"
    /> -->
    <UniversalTable
        title="提示词列表"
        subtitle="管理和配置渠道"
        :title-icon="Document"
        :table-data="tableData"
        :noHeader="false"
        :loading="loading"
        :columns="tableColumns"
        :actions="tableActions"
        :search-form-config="searchForm"
        :search-params="initParam"
        :pagination-data="initParam"
        :showBreadcrumb="true"
        :total="total"
        add-button-text="新增"
        empty-title="暂无渠道数据"
        empty-description="点击上方新增渠道数据按钮开始创建"
        @search="normalSearch"
        @reset="normalResetQuery"
        @add="handleAdd"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @action-click="handleAction"
        :breadcrumb-items="breadcrumbItems"
        @breadcrumb-click="onBreadcrumbClick"
        @back="goBack"
      >
      <!-- 自定义列插槽 -->
      <template #promptType="{ row }">
        <!-- <el-tag
          :type="row.promptType === '1' ? 'success' : 'info'"
          effect="plain"
          size="small"
        > -->
          {{ getDicItemName(row.promptType, 'chatbot.promptType') }}
        <!-- </el-tag> -->
      </template>
      <!-- 提示词内容可加tooltip -->
      <template #prompt="{ row }">
        <el-tooltip :content="row.prompt" placement="top" popper-class="narrow-tooltip">
          <span class="remark-text">{{ row.prompt }}</span>
        </el-tooltip>
      </template>
    </UniversalTable>
    <!-- <PageTable :search-form-temp="searchForm"  apiUrl="/web/maas/channelrobot/prompt/page" ref="shortWordRef" 
    :param="{ channelRobotId}">
      <template #default="{ tableData }">
        <el-table :data="tableData" class="dt-table" stripe>
          <el-table-column
        align="center"
        prop="promptType"
        label="提示词类型"
        width="200"
      >
        <template  #default="scope">
          <span>{{ getDicItemName(scope.row.promptType, 'chatbot.promptType') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="prompt"
        label="提示词"
      ></el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-button link type="primary" @click="onRemove(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable> -->
    <!--新增弹窗-->
    <UniversalFormDialog
      ref="editDialogRef"
      v-model="showEditDialog"
      :form-data="editForm"
      :form-fields="formFields"
      :form-rules="editRules"
      :is-edit="editForm.isEdit"
      :loading="saveLoading"
      add-title="新增提示词"
      edit-title="编辑提示词"
      @confirm="handleSave"
      @cancel="handleCancel"
      @close="handleDialogClose"
    />
     <!-- 删除确认弹窗 -->
   <ConfirmDialog
      ref="confirmDialog"
       v-model="showdel"
      title="确认删除"
      message="删除后无法恢复，请确认是否删除该指标？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
       @cancel="delCancel"
      @confirm="confirmUpdate"
    />
  </template>
  
  <script setup lang="ts">
  import { ref, computed,onMounted,reactive } from 'vue'
  import type { SearchFormTemp, ToolListProps } from '@/components/types'
  import { Back, DataBoard, ArrowRight, Document, User, House, Plus, Grid, Edit, Delete } from '@element-plus/icons-vue'
  import useFilters from '@/hooks/useFilters'
  import { useUserStore } from '@/stores/user'
  import { useRouter, useRoute } from 'vue-router'
  import * as api from '@/api/channels'
  import useChannel from '@/stores/channel'
  const nav = reactive({
    from: '渠道管理',
    subFrom: '配置',
    to: '提示词列表',
  })
  const { channelConfig } = useChannel()
  const channelRobotId = computed(() => channelConfig.channelRobotId)
  console.log(channelRobotId)
  const { push } = useRouter()
  const route = useRoute()

  const { getNickName, getDicItemName } = useFilters()
  const { getDic } = useUserStore()
  const pageTitle = '提示词列表'
  const shortWordRef = ref()
  const addUserForm =ref()
  const editUserForm = ref()
  const initList = ref<Record<string, any>[]>([])
  const  breadcrumbItems = [
  {
    text: "渠道管理",
    icon: Back, // 这里直接用组件对象
    to: { name: "channelManage" }
  },
  {
    text: "配置",
    icon: Back,
    to: { name: "channelManage" }
  },
  {
    text: "提示词列表",
    icon: DataBoard
  }
]
const onBreadcrumbClick = (item) => {
  if (item.text === '配置') {
    router.back()
  }
}
  const showAdd = ref<boolean>(false)
  const showEdit = ref<boolean>(false)
  const addRules= {
      prompt:  [{ required: true, message: '不能为空', trigger: 'blur' }],
      promptType:  [{ required: true, message: '不能为空', trigger: 'blur' }],
    }
  const  editRules= {
    prompt:  [{ required: true, message: '不能为空', trigger: 'blur' }],
    promptType:  [{ required: true, message: '不能为空', trigger: 'blur' }],
        }
  const  channel_id = ref()
  const  addForm= ref<Record<string, any>>({
          promptType: "",
          prompt: "",
        })
 const editForm = reactive({
    promptType: "",
    prompt: "",
    id: "",
    isEdit: false
  })
  const toolList: ToolListProps[] = [
    {
      name: '新增',
      icon: 'add',
      btnCode: '',
      action: async () => {
        showAdd.value = true;
        // push({ path: '/shortWordsAddAndEdit', query: { channelRobotId: channelRobotId.value } })
      },
    },
  ]
  const router = useRouter()

  const goBack = () => {
    router.back()
  }
  // 1. 先定义 promptTypeOptions
  const promptTypeOptions = computed(() =>
    getDic('chatbot.promptType').map(item => ({
      label: item.dicItemName,
      value: item.dicItemCode
    }))
  )

  // 2. 再定义 searchForm 和 formFields
  const searchForm: SearchFormTemp[] = [
    {
    label: '类型',
    name: 'promptType',
    type: 'select',
    placeholder: '请选择提示词类型',
    list: getDic('chatbot.promptType'),
    searchState: false
},
    {
    label: "提示词",
    name: "prompt",
    placeholder: "请输入提示词",
    type: "input",
    searchState: false
}
  ]
  const onOpen = async (id: string, ...arg: any) => {
    const { status } = await api.wordUpdate({ id, status: arg[0], channelRobotId: channelRobotId.value?channelRobotId.value:route.query.id })
    await shortWordRef.value.onSearchBtn()
    if (status != 0) return
  }
  
  const onEdit = (row: any) => {
    // push({ path: '/shortWordsAddAndEdit', query: { id: data.id, channelRobotId: channelRobotId.value } })
    console.log(row)
      showEdit.value = true;
      editForm.promptType = row.promptType;
      editForm.prompt = row.prompt;
      editForm.id = row.id;
  }
   
  /**
   * @description 删除
   */
   const onRemove = async (data: any) => {
    ElMessageBox.confirm('确定删除该提示词吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      await api.channelrobotPromptDelete(data.id)
      await shortWordRef.value.onSearchBtn()
    })
  }
  const closePopup=()=> {
          showAdd.value = false;
          addForm.value= {
            promptType: "",
            prompt: "",
          }
          editForm = {
          promptType: "",
          prompt: "",
        }
          shortWordRef.value.onSearchBtn()
      }
  /**
   * @description 提交
   */
  const editSubmit = async () => {
    const data = {
      channelRobotId: channelRobotId.value?channelRobotId.value:route.query.id,
        ...editForm
      };
      const res = await api.channelRobotpromptUpdate(data);
      if (res.status == 0) {
        showEdit.value = false;
        editForm= {
          promptType: "",
          prompt: "",
        }
        await shortWordRef.value.onSearchBtn()
      }
  }
   /**
   * @description 删除
   */
   const editCancel = async (data: any) => {
      showEdit.value = false;
  }

   // 新增
   const toAddForm =async()=> {
    console.log(route, channelRobotId)
      const data = {
        channelRobotId: channelRobotId.value?channelRobotId.value:route.query.id,
        ...addForm.value
      };
      const res = await api.channelRobotpromptAdd(data);
      if (res.status == 0) {
        showAdd.value = false;
        addForm.value= {
          promptType: "",
          prompt: "",
        }
        await shortWordRef.value.onSearchBtn()
      }
    }
  
  const search = () => {
    if (!shortWordRef.value) return
    shortWordRef.value.onSearchBtn()
  }
  
  defineExpose({ search })
  const tableData = ref<any[]>([])
  const tableColumns = ref<any[]>([
    {
      prop: 'promptType',
      label: '提示词类型',
      width: 200,
      align: 'center',
      slot: 'promptType'
    },
    {
      prop: 'prompt',
      label: '提示词',
      align: 'center'
    }
  ])
  const tableActions = [
    { label: '编辑', key: 'edit', icon: Edit, class: 'edit-btn' },
    { label: '删除', key: 'delete', icon: Delete, class: 'delete-btn' }
  ]
  const loading = ref(false)
  const total = ref(0)
  const initParam = reactive({
    pageNum: 1,
    pageSize: 10,
    param:{
      promptType: '',
      prompt: '',
      channelRobotId: channelRobotId.value?channelRobotId.value:route.query.id,
    }
  })

  const fetchTableData = async () => {
    loading.value = true
    const res = await api.getPromptList(initParam)
    if (res.status === 0) {
      tableData.value = res.data.list
      total.value = res.data.total
    }
    loading.value = false
  }

  // const handleAdd = () => { showAdd.value = true }
  const handleAction = async (item:any) => {
    console.log(item)
    if (item.action.key === 'edit') {
      Object.assign(editForm, { ...item.row,isEdit:true }) // 不要 .value
      showEditDialog.value = true
    } else if (item.action.key === 'delete') {
      // 弹窗确认后
      deleteRow.value = item.row
      showdel.value = true
    }
  }
  const handleSizeChange = (size: number) => {
    initParam.pageSize = size
    fetchTableData()
  }
  const handleCurrentChange = (page: number) => {
    initParam.pageNum = page
    fetchTableData()
  }
  const normalSearch = (params: any) => {
    initParam.param = { ...params, channelRobotId: channelRobotId.value?channelRobotId.value:route.query.id, }
    fetchTableData()
  }
  const normalResetQuery = () => {
    initParam.param.promptType = ''
    initParam.param.prompt = ''
    initParam.param.channelRobotId = channelRobotId.value?channelRobotId.value:route.query.id
    fetchTableData()
  }

  onMounted(() => {
    fetchTableData()
  })

  const showEditDialog = ref(false)
  const saveLoading = ref(false)
 

  const formFields = [
    {
      label: "提示词类型",
      name: "promptType",
      type: "select",
      placeholder: "请选择提示词类型",
      options: promptTypeOptions.value, // 这里用新的 options
      prop: "promptType"
    },
    {
      label: "提示词",
      name: "prompt",
      type: "textarea",
      placeholder: "请输入内容",
      prop: "prompt"
    }
  ]

  const handleAdd = () => {
    Object.assign(editForm, { promptType: "", prompt: "", id: "" }) // 不要 .value
    showEditDialog.value = true
  }


  const handleSave = async (formData) => {
    saveLoading.value = true
    try {
      let res
      if (!formData.isEdit) {
        res = await api.channelRobotpromptAdd({ ...formData.formData, channelRobotId:channelRobotId.value?channelRobotId.value:route.query.id })
      } else {
        res = await api.channelRobotpromptUpdate({ ...formData.formData, channelRobotId:channelRobotId.value?channelRobotId.value:route.query.id })
      }
      if (res.status === 0) {
        showEditDialog.value = false
        fetchTableData()
      }
    } finally {
      saveLoading.value = false
    }
  }

  const handleCancel = () => {
    showEditDialog.value = false
    // 这里可以重置 editForm
    Object.assign(editForm, { promptType: "", prompt: "", id: "" }) // 不要 .value
  }

  const handleDialogClose = () => {
    showEditDialog.value = false
    Object.assign(editForm, { promptType: "", prompt: "", id: "" }) // 不要 .value
  }

  const showdel = ref(false)
  const deleteRow = ref<any>(null)

  const confirmUpdate = async () => {
    if (!deleteRow.value) return
    await api.channelrobotPromptDelete(deleteRow.value.id)
    showdel.value = false
    deleteRow.value = null
    fetchTableData()
  }

  const delCancel = () => {
    showdel.value = false
    deleteRow.value = null
  }
  </script>
  <style>
    .narrow-tooltip {
      max-width: 80%; /* tooltip弹窗最大宽度 */
      white-space: normal;
      word-break: break-all;
    }
  </style>
  