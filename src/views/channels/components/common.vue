<template>
  <!-- <PageTable :search-form-temp="searchForm" apiUrl="/web/channelcommoncard/page" ref="commonRef" :param="{ channelCode }" -->
    <!-- no-immediate> -->
    <!-- <template #default="{ tableData }">
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column prop="title" label="标题" align="center" min-width="110px" />
        <el-table-column prop="type" label="类型" align="center" min-width="80px">
          <template #default="scope">{{ getDicItemName(scope.row.type, 'chatbot.cardType') }}</template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" align="center" min-width="80px" />
        <el-table-column prop="createTime" label="创建时间" align="center" min-width="110px" />
        <el-table-column prop="updateTime" label="最后更新时间" align="center" min-width="110px" />
        <el-table-column prop="updateId" label="操作人" align="center" min-width="80px">
          <template #default="scope">
            {{ getNickName(scope.row.updateId) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="启用状态" align="center" width="80px">
          <template #default="scope">
            <el-switch v-model="scope.row.status" active-value="1" inactive-value="0"
              @change="onOpen(scope.row.id, $event)" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button link type="primary" @click="onEdit(scope.row)">编辑</el-button>
            <el-button link type="primary" @click="onRemove(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table> -->
      <div class="index-manage-container">
  <UniversalTable
    title="常用卡片列表"
    subtitle="管理和配置常用卡片"
     :title-icon="Document"
    :table-data="tableData"
    :loading="loading"
    :noHeader="true"
    :columns="tableColumns"
    :actions="tableActions"
    :search-form-config="searchForm"
    :search-params="initParam"
    :pagination-data="initParam"
    :total="total"
    add-button-text="新增"
    empty-title="暂无常用卡片"
    empty-description="点击上方新增按钮开始创建"
    @search="normalSearch"
    @reset="normalResetQuery"
    @add="handleAdd"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
    @action-click="handleAction"
  >
    <!-- 这里可以自定义slot，比如类型、状态等 -->
    <!-- <template #type="{ row }">
      {{ getDicItemName(row.type, 'chatbot.cardType') }}
    </template> -->
    <template #type="{ row }">
          <div class="type-cell">
          <el-tooltip :content="getDicItemName(row.type, 'chatbot.cardType')" placement="top">
            <el-tag
             v-if="row.type=='1'"
            class="calculate-tag"
            size="medium"
            effect="plain"
          >
            <el-icon style="margin-right: 4px;">
              <component :is="Link" />
            </el-icon>
            {{ getDicItemName(row.type, 'chatbot.cardType') }}
          </el-tag>
          <el-tag
           v-if="row.type=='2'"
            class="result-tag"
            size="medium"
            effect="plain"
          >
            <el-icon style="margin-right: 4px;">
              <component :is="Document" />
            </el-icon>
            {{ getDicItemName(row.type, 'chatbot.cardType') }}
          </el-tag>

          <el-tag
           v-if="row.type=='3'"
            class="draft-tag"
            size="medium"
            effect="plain"
          >
            <el-icon style="margin-right: 4px;">
              <component :is="Files" />
            </el-icon>
            {{ getDicItemName(row.type, 'chatbot.cardType') }}
          </el-tag>
          </el-tooltip>
          </div>
        </template>
    <template #status="{ row }">
      <el-switch
        :model-value="row.status"
        :active-value="1"
        :inactive-value="0"
        @change="onSwitchChange(row, $event)"
      />
    </template>
    <template #updateId="{ row }">
      <div class="user-cell">
        <div class="user-avatar">
          <el-icon><User /></el-icon>
        </div>
        <span>{{ getNickName(row.updateId) }}</span>
      </div>
    </template>
    <template #createTime="{ row }">
      <div class="time-cell">
        <el-icon><Clock /></el-icon>
        <span>{{ row.createTime }}</span>
      </div>
    </template>
    <template #updateTime="{ row }">
      <div class="time-cell">
        <el-icon><Clock /></el-icon>
        <span>{{ row.updateTime }}</span>
      </div>
    </template>
  </UniversalTable>
  <UniversalFormDialog
    ref="editDialogRef"
    v-model="showEditDialog"
    :form-data="editForm"
    :form-fields="formFields"
    :form-rules="editRules"
    :is-edit="editForm.isEdit"
    :loading="saveLoading"
    add-title="新增常用卡片"
    edit-title="配置常用卡片"
    @confirm="handleSave"
    @cancel="() => showEditDialog = false"
  >
    <template #content="{ field, formData }">
      <!-- 只写内容，不要包el-form-item -->
      <el-input v-if="formData.type == '2'" type="textarea" maxlength="50" v-model="formData.content" rows="4" />
      <div v-else-if="formData.type == '3'">
        <el-radio-group v-model="formData.knowledgeType">
          <el-radio label="1">QA</el-radio>
          <el-radio label="2">知识</el-radio>
        </el-radio-group>
        <knowsList v-model="formData.content" :param="{ robotId: channelConfig.robotId, type: 1, knowledgeType: formData.knowledgeType }" />
      </div>
      <el-input v-else v-model.trim="formData.content" placeholder="请输入内容" clearable />
      <div class="text-[12px] text-gray-400 leading-normal mt-2" v-if="formData.type !== '3' && formData.type !== '2'">
        说明：跳转页面支持内置参数配置，包含token、userId。使用示例：https://www.dtinsure.com/index?token=${token}
      </div>
    </template>
    
  </UniversalFormDialog>
  <ConfirmDialog
    ref="confirmDialogRef"
    v-model="showDelDialog"
    title="确认删除"
    message="删除后无法恢复，是否确认删除该卡片？"
    icon="el-icon-warning"
    confirm-text="删除"
    cancel-text="取消"
    @cancel="() => showDelDialog = false"
    @confirm="confirmDelete"
  />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, nextTick } from 'vue'
import type { SearchFormTemp, ToolListProps } from '@/components/types'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import { useRouterStore } from '@/stores/router'
import * as api from '@/api/channels'
import { useRoute, useRouter } from 'vue-router'
import UniversalFormDialog from '@/components/UniversalFormDialog.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import UniversalTable from '@/components/UniversalTable.vue'
import { Document, Edit, Setting, Delete,Clock ,User,Cpu, DataLine,InfoFilled, Coin,Files,Link} from '@element-plus/icons-vue'
import { onMounted } from 'vue'
import knowsList from './knowsList.vue' // 引入知识选择组件
import useSystemStore from '@/stores/system'
// import { useChannelConfigStore } from '@/stores/channelConfig' // 引入渠道配置store
import { watch } from 'vue'

const route = useRoute()
const channelCode = computed(() => route.query.channelCode)
const channelId = computed(() => route.query.channelId)
const { push } = useRouter()
const { getNickName, getDicItemName } = useFilters()
const { getDic } = useUserStore()
const pageTitle = '常用卡片列表'
const commonRef = ref()
const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: '',
    action: async () => {
      console.log(channelCode.value)
      push({ path: '/channelCommonAddAndEdit', query: { channelCode: channelCode.value, channelId: channelId.value } })
    },
  },
]

const searchForm: SearchFormTemp[] = [
  {
    label: '标题',
    name: 'title',
    type: 'input',
    placeholder: '请输入标题',
    searchState: false
  },
  {
    label: '类型',
    name: 'type',
    type: 'select',
    placeholder: '请选择类型',
    list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.cardType')],
    searchState: false
  },
  {
    label: '启用状态',
    name: 'status',
    type: 'select',
    placeholder: '请选择启用状态',
    list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.common.status')],
    searchState: false
  },
]

const onOpen = async (id: string, ...arg: any) => {
  await api.cardUpdate({ id, status: arg[0], channelCode: channelCode.value })
  await commonRef.value.onSearchBtn()
}

const onEdit = (data: any) => {
  const { updateActiveParams } = useRouterStore()
  updateActiveParams({ name: 'channelCommonAddAndEdit', params: data })
  push({ path: 'channelCommonAddAndEdit', query: { id: data.id } })
}

/**
 * @description 删除
 */
const onRemove = async (data: any) => {
  ElMessageBox.confirm('确定删除该常用卡片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.cardDel(data.id)
    await commonRef.value.onSearchBtn()
  })
}

const search = () => {
  if (!commonRef.value) return
  commonRef.value.onSearchBtn()
}

defineExpose({ search })

const editForm = reactive({
  id: "",
  title: "",
  imgPath: [],
  sort: "",
  status: 1,
  type: "",
  content: "",
  knowledgeType: "1",
  isEdit: false
})

const cardTypeOptions = getDic('chatbot.cardType').map(item => ({
  label: item.dicItemName,
  value: String(item.dicItemCode)
}))

const formFields = [
  { label: "标题", name: "title", type: "input", placeholder: "请输入标题", prop: "title" },
  { 
    label: "图片", 
    name: "imgPath", 
    type: "uploadimg", 
    prop: "imgPath", 
    value: [],
    tip: "推荐尺寸:200x200"
  },
  { label: "排序", name: "sort", type: "input", placeholder: "请输入排序", prop: "sort" },
  { label: "启用状态", name: "status", type: "switch", prop: "status", value: 1 },
  { 
    label: "类型", 
    name: "type", 
    type: "select", 
    placeholder: "请选择类型", 
    prop: "type", 
    options: cardTypeOptions
  },
  { label: "内容", name: "content", type: "slot", prop: "content", slotName: "content" }
]

const editRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  status: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
  imgPath: [{ required: true, message: '请上传图片', trigger:'change'}],
  sort: [{ required: true, message: '请选择排序', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型',  trigger: [ 'blur','change']  }],
  knowledgeType: [{ required: true, message: '请选择类型',  trigger: [ 'blur','change'] }],
  content: [
    { required: true, message: '请选择或填写内容', trigger: 'change' },
    {
      max: 300,
      message: '内容不能超过300个字符',
      trigger: 'change',
    },
  ],
}

const showEditDialog = ref(false)
const showDelDialog = ref(false)
const saveLoading = ref(false)
const editDialogRef = ref()
const confirmDialogRef = ref()
const deleteRow = ref(null)

const handleAdd = () => {
  Object.assign(editForm, { id: "", title: "", imgPath: [], sort: 1, status: 1, type: "", content: "", knowledgeType: "1", isEdit: false })
  showEditDialog.value = true
  nextTick(() => editDialogRef.value?.clearFormValidate?.())
}

const handleEdit = async (row) => {
  // 处理图片回显
  let imgPath = [];
  if (row.imgPath) {
    if (typeof row.imgPath === 'string') {
      imgPath = [{
        name: row.imgPath.split('/').pop(),
        url: row.imgPath,
        uid: Date.now() + '',
        status: 'done'
      }];
    } else if (Array.isArray(row.imgPath)) {
      imgPath = row.imgPath.map((url, idx) => ({
        name: typeof url === 'string' ? url.split('/').pop() : url.name,
        url: typeof url === 'string' ? url : url.url,
        uid: Date.now() + '-' + idx,
        status: 'done'
      }));
    } else if (typeof row.imgPath === 'object' && row.imgPath.url) {
      imgPath = [row.imgPath];
    }
  }
  Object.assign(editForm, { ...row, imgPath, isEdit: true });
  showEditDialog.value = true;
  nextTick(() => editDialogRef.value?.clearFormValidate?.());
}

const handleSave = async (formData) => {
  saveLoading.value = true
  
  try {
    // 处理图片
    const imgPath = Array.isArray(formData.imgPath) && formData.imgPath.length > 0
      ? (typeof formData.imgPath[0] === 'object'
          ? (formData.imgPath[0].foreignPath || formData.imgPath[0].url)
          : formData.imgPath[0])
      : ''
    const submitData = { ...formData.formData, imgPath }
    let res
    if (!formData.isEdit) {
      res = await api.cardAdd(submitData)
      if (res && res.status === 0) ElMessage.success('新增成功')
    } else {
      res = await api.cardUpdate(submitData)
      if (res && res.status === 0) ElMessage.success('配置成功')
    }
    if (res && res.status === 0) {
      showEditDialog.value = false
      getTableData()
    }
  } finally {
    saveLoading.value = false
  }
}

const handleDelete = (row) => {
  deleteRow.value = row
  showDelDialog.value = true
  confirmDialogRef.value.show()
}

const confirmDelete = async () => {
  if (deleteRow.value) {
    await api.cardDel(deleteRow.value.id)
    showDelDialog.value = false
    getTableData()
    deleteRow.value = null
  }
}

const tableColumns = [
  { prop: 'title', label: '标题', align: 'center', minWidth: '110px' },
  { prop: 'type', label: '类型', align: 'center', minWidth: '80px', slot: 'type' },
  { prop: 'sort', label: '排序', align: 'center', minWidth: '80px' },
  { prop: 'createTime', label: '创建时间', align: 'center', minWidth: '110px', slot: 'createTime' },
  { prop: 'updateTime', label: '最后更新时间', align: 'center', minWidth: '110px', slot: 'updateTime' },
  { prop: 'updateId', label: '操作人', align: 'center', minWidth: '80px', slot: 'updateId' },
  { prop: 'status', label: '启用状态', align: 'center', width: '80px', slot: 'status' }
]

const tableActions = [
  { key: "edit", label: "配置", icon: Edit, class: "edit-btn" },
  { key: "delete", label: "删除", icon: Delete, class: "delete-btn" }
]

const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const initParam = ref({
  pageNum: 1,
  pageSize: 10,
  param: {
    title: "",
    type: "",
    status: "",
    channelCode: channelCode.value
  }
})

const getTableData = async () => {
  loading.value = true
  try {
    const res = await api.cardList({
      param:{
        ...initParam.value.param,
      },
      pageNum: initParam.value.pageNum,
      pageSize: initParam.value.pageSize
    })
    tableData.value = (res.data?.list || []).map(item => ({
      ...item,
      status: Number(item.status) // 保证 status 是数字
    }))
    total.value = res.data?.total || 0
  } finally {
    loading.value = false
  }
}

onMounted(getTableData)

const normalSearch = (params) => {
  initParam.value = { 
    ...initParam.value, 
    param: { ...initParam.value.param, ...params }, 
    pageNum: 1 
  }
  getTableData()
}
const normalResetQuery = () => {
  initParam.value = { 
    pageNum: 1, 
    pageSize: 10, 
    param: { title: "", type: "", status: "", channelCode: channelCode.value } 
  }
  getTableData()
}
const handleSizeChange = (size) => {
  initParam.value.pageSize = size
  getTableData()
}
const handleCurrentChange = (page) => {
  initParam.value.pageNum = page
  getTableData()
}

const handleAction = async (item) => {
  console.log(item)
  if (item.action.key === 'edit') {
    handleEdit(item.row)
  } else if (item.action.key === 'delete') {
    handleDelete(item.row)
  }
}
let switchReady = false
setTimeout(() => { switchReady = true }, 1000)

const onSwitchChange = async (row, value) => {
  if (!switchReady) return
  await api.cardUpdate({ id: row.id, status: value, channelCode: channelCode.value })
  row.status = value
}

const channelConfig = useSystemStore() // 获取渠道配置

watch(() => editForm.type, (val) => {
  console.log('当前type:', val, typeof val)
})
</script>
<style lang="less" scoped>
.index-manage-container {
  .code-cell {
    display: flex;
    align-items: center;
    justify-content: flex-start; // 改成左对齐
    gap: 8px;
    .code-icon {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        color: #d7a256;
        font-size: 12px;
      }
    }
    .code-text {
      font-family: "Courier New", monospace;
      font-weight: 500;
      color: #2c3e50;
      font-size: 13px;
    }
  }
  .name-cell {
    .name-text {
      font-weight: 500;
      color: #2c3e50;
    }
  }
  .type-cell {
    .el-tag {
      height: 28px !important;
      line-height: 26px !important;
      font-size: 14px !important;
      padding: 0 10px !important;
      display: inline-flex !important;
      align-items: center !important;
      box-sizing: border-box !important;
      min-width: 80px; // 可选，保证宽度一致
      justify-content: center; // 可选，内容居中
    }
    .calculate-tag {
      background: rgba(64, 158, 255, 0.1);
      color: #409eff;
      border: 1px solid rgba(64, 158, 255, 0.3);

      i {
        margin-right: 4px;
      }
    }

    .result-tag {
      background: rgba(103, 194, 58, 0.1);
      color: #67c23a;
      border: 1px solid rgba(103, 194, 58, 0.3);

      i {
        margin-right: 4px;
      }
    }
    .draft-tag {
      background: rgba(230, 162, 60, 0.1);
      color: #e6a23c;
      border: 1px solid rgba(230, 162, 60, 0.3);
      i {
        margin-right: 4px;
      }
    }
  }
  :deep(.el-switch) {
    .el-switch__core {
      border-color: #e8dcc0;
      background: #e8dcc0;
    }
  }
  :deep(.el-switch.is-checked) {
    .el-switch__core {
      border-color: #D7A256;
      background: #D7A256;
    }
  }
  .remark-cell {
    .remark-text {
      color: #606266;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .no-remark {
      color: #c0c4cc;
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      i {
        font-size: 12px;
      }
    }
  }
  .time-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;
    i {
      color: #d7a256;
      font-size: 14px;
    }
  }
  
  .user-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #606266;
    font-size: 13px;
    .user-avatar {
      width: 24px;
      height: 24px;
      background: rgba(215, 162, 86, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: #D7A256;
        font-size: 12px;
      }
    }
  }
}
</style>
