<template>
  <!-- <DtNav :from="nav.from"  :fromPath="'/channelManage'" :name="nav.to" :back="onBack" /> -->
  <SecondaryPageHeader
      title="配置"
      icon="Document"
      :show-actions="false"
      :breadcrumb-items="breadcrumbItems"
      @back="goBack"
    />
  <el-tabs  type="border-card" v-model="activeTab" @tab-change="onTabChange">
    <el-tab-pane label="基础配置" name="flowBasic" lazy>
      <flowBasic  />
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import flowBasic from './components/flowBasic.vue'

import { ref, reactive, nextTick,watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useRouterStore } from '@/stores/router'
import { onActivated,onMounted } from 'vue'

const activeTab = ref('flowBasic')
defineOptions({ name: 'channelConfig' })
const finished = ref(false)
const { delRouterCaches } = useRouterStore()
const nav = reactive({
  from: '渠道管理',
  to: '配置',
})

const refs = reactive<Record<string, any>>({ common: null, qa: null, words: null,basic:null, flowBasic: null,manualConfig: null,sensitiveWord: null,Intelligent:null })
const onTabChange = (name: string) => {
  nextTick(() => {
    refs[name]?.search()
  })
}

const  breadcrumbItems = [
        {
          text: "渠道管理",
          icon: "Back",
          to: { name: "channelManage" }
        },
        {
          text: "配置",
          icon: "DataBoard"
        }
      ]
const router = useRouter()
const route = useRoute()
const onBack = () => {
  delRouterCaches((route?.name as string) ?? '')
  router.back()
}
const edit = route.query.edit

onMounted(async () => {
  // await init()
  finished.value = true // 确保加上这一句
  nextTick(() => {
    if (refs.flowBasic) {
      if (typeof refs.flowBasic.init === 'function') {
        refs.flowBasic.init()
      }
      if (typeof refs.flowBasic.search === 'function') {
        refs.flowBasic.search()
      }
    }
  })
})
const reset=()=> {
   activeTab.value = "flowBasic";
   console.log(activeTab.value)
}
onActivated(() => {
  nextTick(() => {
    reset()
    refs[activeTab.value]?.search()
  })
})
</script>
<style scoped>
/* 整体容器样式 */
.channel-config-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #D7A256;
  background-color: #FFF6E8;
  min-height: 100vh;
  padding: 20px;
}

/* 标签页样式优化 */
.config-tabs {
  /* margin-top: 20px; */
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(215, 162, 86, 0.15);
  background-color: #fff;
  border: 1px solid rgba(215, 162, 86, 0.2);
}

.config-tabs :deep(.el-tabs__header) {
  background-color: #FFF6E8;
  border-bottom: 1px solid rgba(215, 162, 86, 0.3);
  border-radius: 8px 8px 0 0;
  margin: 0;
}

.config-tabs :deep(.el-tabs__nav-wrap) {
  /* padding: 0 20px; */
}

.config-tabs :deep(.el-tabs__item) {
  font-size: 15px;
  font-weight: 500;
  color: #D7A256;
  padding: 12px 20px;
  transition: all 0.3s ease;
}

.config-tabs :deep(.el-tabs__item:hover) {
  color: #B8860B;
  background-color: rgba(215, 162, 86, 0.1);
}

.config-tabs :deep(.el-tabs__item.is-active) {
  color: #B8860B;
  font-weight: 600;
  background-color: #fff;
  border-bottom: 2px solid #D7A256;
}

.config-tabs :deep(.el-tabs__content) {
  padding: 24px;
  background-color: #fff;
  border-radius: 0 0 8px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .channel-config-container {
    padding: 10px;
  }
  
  .config-tabs :deep(.el-tabs__item) {
    font-size: 14px;
    padding: 10px 15px;
  }
  
  .config-tabs :deep(.el-tabs__content) {
    padding: 16px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .channel-config-container {
    background-color: #2A1F0F;
    color: #D7A256;
  }
  
  .config-tabs {
    background-color: #3D2E1A;
    box-shadow: 0 2px 12px 0 rgba(215, 162, 86, 0.2);
    border-color: rgba(215, 162, 86, 0.3);
  }
  
  .config-tabs :deep(.el-tabs__header) {
    background-color: #2A1F0F;
    border-bottom-color: rgba(215, 162, 86, 0.4);
  }
  
  .config-tabs :deep(.el-tabs__content) {
    background-color: #3D2E1A;
  }
}
</style>