<template>
  <!-- <DtNav :from="nav.from" :name="nav.to"  :fromPath="'/channelManage'" /> -->
  <TableToolTemp toolTitle="渠道信息" class="page-title-container"/>
  <NewUpdateForm  class="form-card" v-if="finished" :form-list="baseList" :rules="rules" :showSubmit="true" :showbtn="true" cancel-text="取消" @submit="submit" @cancel="cancel" ref="formTempRef" >
    <template #default="{ form }">
    <el-form-item label="执行类型" label-width="135px" prop="type" class="form-item-custom">
          <el-radio-group v-model="form.executeType"   @click="onTypeChange(form,  $event)" class="radio-group-custom">
             <el-radio v-for="el in dictList" :label="el.dicItemCode" :name="el.dicItemCode" :key="el.dicItemCode" class="radio-item-custom">{{ el.dicItemName }}</el-radio>
          </el-radio-group>
    </el-form-item>
    <el-form-item label="关联工作流" prop="flowId" v-if="form?.executeType == 1" ref="contentRef" class="form-item-custom">
       <el-select v-model="form.flowId" class="select-custom" placeholder="请选择工作流">
        <el-option v-for="(item, i) in flowDataList" :key="i" :label="item.flowName" :value="item.flowId" />
      </el-select>
    </el-form-item>
    <el-form-item label="结束语" prop="finalWords"  ref="contentRef" class="form-item-custom">
       <el-input type="textarea" maxlength="50" v-model="form.finalWords" class="textarea-custom" placeholder="请输入结束语" />
    </el-form-item>
    <!-- <el-form-item v-if="showSubmit" class="submit-buttons">
      <el-button type="primary" @click="submitForm(updateForm)">{{ props.certainText }}</el-button>
      <el-button @click="cancel">{{ props.cancelText }}</el-button>
    </el-form-item> -->
  </template>
</NewUpdateForm>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted,computed,onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { UpdateFormList } from '@/components/types'
import type { FormRules } from 'element-plus'
import * as api from '@/api/channels'
import { flowList } from '@/api/channels'
const formTempRef = ref()
const { query } = useRoute()
const props = defineProps<{
  channelCode: string
  channelId: string
}>()
const emit = defineEmits<{
  (e: 'submit'): void
  (e: 'cancel'): void
}>()
const router = useRouter()
const finished = ref(false)
const isEdit = computed(() => {
  return !!props?.channelCode
})
const nav = reactive({
  from: '渠道管理',
  to: '编辑',
})
let flowDataList=[]
const dictList=[
   {dicItemCode:'0',dicItemName:"自定义"},
   {dicItemCode:'1',dicItemName:"工作流"}
]
const baseList: UpdateFormList[] = [
  {
    title: '所属项目',
    type: 'proselect',
    field: 'projectId',
    disabled:isEdit.value?true:false
  },
  {
    title: '渠道名称',
    type: 'input',
    field: 'channelName',
  },
  {
    title: '渠道编码',
    type: 'input',
    field: 'channelCode',
    maxlength:10,
  },
  {
    title: '启用状态',
    type: 'switch',
    field: 'status',
    value: 1,
  },
  {
    title: '执行类型',
    type: 'radio',
    field: 'executeType',
    value: '0',
    dict: [
      {dicItemCode:'0',dicItemName:"自定义"},
      {dicItemCode:'1',dicItemName:"工作流"}
     ],
    except: true,
  },
   {
    title: '关联工作流',
    type: 'flowIdSelect',
    field: 'flowId',
    except: true,
  },
  {
    title: '结束语',
    type: 'textarea',
    field: 'finalWords',
    maxlength: 100,
    except: true,
  },
]
const subData = {
  id: '', // base config id
  channelCode: '', // channelCode
  channelId: ''
}
const rules: FormRules = {
  projectId: [{ required: true, message: '请输入所属项目', trigger: 'blur' }],
  status: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
  channelName: [{ required: true, message: '请输入渠道名称', trigger: 'change' }],
  channelCode: [{ required: true, message: '请输入渠道编码', trigger: 'change' }],
  executeType: [{ required: true, message: '请选择执行类型', trigger: 'blur' }],
  flowId: [{ required: true, message: '请选择关联工作流', trigger: 'blur' }],
  
}

const submit = async (param: any) => {
   const { channelCode, id, channelId } = subData
  let status: string | number
  let msg: string
  if (!isEdit.value) {
    const res = await api.add({ ...param})
    status = res.status
    msg = '保存成功'
    if(status==0){
      formTempRef.value && formTempRef.value.clearValidate()
      // formTempRef.value && formTempRef.value.resetFields()
    }
  } else {
    const res = await api.updateChannel({ ...param, id})
    status = res.status
    msg = '更新成功'
    if(status==0){
      ElMessage.success(msg)
      formTempRef.value && formTempRef.value.clearValidate()
      // formTempRef.value && formTempRef.value.resetFields()
    }
  }
  if (status != 0) return
  emit('submit')
  // router.back()
}
const cancel = async () => {
  // emit('cancel')
  router.back()
}
const onTypeChange = (form: any, args: string[]) => {
  console.log(form,args)
  if (args && args[0]) form.flowId = ''
}
const init = async () =>  {
  console.log('111',isEdit.value)
  if (!isEdit.value) return
  nav.to = isEdit.value ? '编辑' : '新增'
  const channelCode = props.channelCode ?? ''
  if (!channelCode) return
  const { status, data } = await api.basicGet(channelCode as string)
  console.log(status, data)
  if (status != 0 || !data) return
  // isEdit.value = !!data.channelBasicConfigId
  data.channelName = data.channelName
  data.channelCode = data.channelCode
  data.projectId =  Number(data.projectId)
  data.status= Number(data.status)
  data.finalWords = data.finalWords
  data.flowId = data.flowId
  data.executeType = data.executeType
  // rebind baseList value
  subData.channelCode = channelCode as string
  const channelId = props.channelId
  subData.channelId =  channelId as string
  subData.id = data.channelId
  console.log(data)
  baseList.forEach((tmp) => {
    if (Object.hasOwn(data, tmp.field)) {
      tmp.value = data[tmp.field]
    }
  })
  console.log(baseList)
}
const listData = async () =>  {
  const list = await flowList({})
  flowDataList=list.data
}
onBeforeMount(async () => {
  await init()
  await listData()
  finished.value = true
})
onMounted(() => {
  // init()
})
</script>
<style scoped>
/* 整体容器样式优化 */
/* .form-card {
  background: linear-gradient(135deg, #fffbe6 0%, #f7e7ff 40%, #f8fafc 100%);
  border-radius: 28px;
  box-shadow: 0 12px 48px 0 rgba(102,126,234,0.13), 0 2px 8px rgba(215,162,86,0.10), 0 0 0 4px rgba(215,162,86,0.04);
  border: 1.5px solid rgba(215, 162, 86, 0.10);
  padding: 48px 40px 36px 40px;
  width: 100%;
  margin: 0;
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  transition: box-shadow 0.3s, transform 0.3s;
}
.form-card:hover {
  box-shadow: 0 20px 64px 0 rgba(102,126,234,0.18), 0 4px 16px rgba(215,162,86,0.16), 0 0 0 8px rgba(215,162,86,0.06);
  transform: scale(1.015);
} */

/* .page-title-container {
  margin-bottom: 2rem;
}

.page-title-container :deep(.table-tool-title) {
  font-size: 2.1rem;
  font-weight: 800;
  color: #2d3748;
  padding: 1.3rem 0;
  margin: 0 auto;
  border-bottom: none;
  background: linear-gradient(90deg, #fdf6e3 0%, #f7e7ff 30%, #ffe9c7 70%, #fffbe6 100%);
  border-radius: 20px;
  box-shadow: 0 6px 24px 0 rgba(215,162,86,0.13), 0 2px 8px rgba(102,126,234,0.08);
  text-shadow: 0 2px 8px rgba(215,162,86,0.08), 1px 1px 2px rgba(0,0,0,0.05);
  position: relative;
  max-width: 480px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}
.page-title-container :deep(.table-tool-title)::before,
.page-title-container :deep(.table-tool-title)::after {
  content: '';
  display: block;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  background: radial-gradient(circle, #f7e7ff 0%, #fffbe6 80%, transparent 100%);
  opacity: 0.7;
  filter: blur(2px);
}
.page-title-container :deep(.table-tool-title)::before {
  left: -24px;
}
.page-title-container :deep(.table-tool-title)::after {
  right: -24px;
}
.page-title-container :deep(.table-tool-title) span {
  position: relative;
  z-index: 2;
}
.page-title-container :deep(.table-tool-title) .title-underline {
  display: block;
  margin: 0 auto;
  margin-top: 12px;
  width: 90px;
  height: 5px;
  background: linear-gradient(90deg, #dbab67 0%, #D7A256 100%);
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(215,162,86,0.18);
}

.form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #D7A256 0%, #dbab67 50%, #fbf6ee 100%);
  border-radius: 20px 20px 0 0;
}

@keyframes fadeInUp {
  from { 
    opacity: 0; 
    transform: translateY(30px) scale(0.95);
  }
  to { 
    opacity: 1; 
    transform: translateY(0) scale(1);
  }
} */

/* 表单项容器优化 */
/* .form-item-custom {
  margin-bottom: 32px;
  position: relative;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.form-item-custom:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px rgba(102, 126, 234, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.06);
  border-color: rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.9);
}

.form-item-custom:last-child {
  margin-bottom: 0;
} */

/* 标签样式优化 */
/* .el-form-item__label {
  font-weight: 700;
  color: #2d3748;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 12px;
  display: block;
  position: relative;
  padding-left: 16px;
}

.el-form-item__label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #D7A256;
  border-radius: 2px;
} */

/* 输入框样式全面优化 */
/* .el-input__wrapper,
.el-textarea__inner {
  border-radius: 12px;
  border: 2px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 
    0 2px 8px rgba(102, 126, 234, 0.06),
    inset 0 1px 2px rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.el-input__wrapper:hover,
.el-textarea__inner:hover {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 
    0 4px 16px rgba(102, 126, 234, 0.12),
    inset 0 1px 2px rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.el-input__wrapper.is-focus,
.el-textarea__inner:focus {
  border-color: #D7A256;
  box-shadow: 
    0 0 0 4px rgba(215, 162, 86, 0.15),
    0 4px 20px rgba(215, 162, 86, 0.2),
    inset 0 1px 2px rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
} */

/* 选择器样式优化 */
/* .select-custom {
  width: 100%;
}

.select-custom .el-input__wrapper {
  border-radius: 12px;
  border: 2px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
}

.select-custom .el-input__wrapper:hover {
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

.select-custom .el-input__wrapper.is-focus {
  border-color: #D7A256;
  box-shadow: 
    0 0 0 4px rgba(215, 162, 86, 0.15),
    0 4px 20px rgba(215, 162, 86, 0.2);
  transform: translateY(-2px);
} */

/* 文本域样式优化 */
/* .textarea-custom .el-textarea__inner {
  border-radius: 12px;
  border: 2px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.textarea-custom .el-textarea__inner:hover {
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

.textarea-custom .el-textarea__inner:focus {
  border-color: #D7A256;
  box-shadow: 
    0 0 0 4px rgba(215, 162, 86, 0.15),
    0 4px 20px rgba(215, 162, 86, 0.2);
  transform: translateY(-2px);
} */

/* 单选框组样式全面优化 */
/* .radio-group-custom {
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
  padding: 8px 0;
}

.radio-item-custom {
  margin-right: 0;
  margin-bottom: 12px;
  position: relative;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 2px solid rgba(102, 126, 234, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
} */

/* .radio-item-custom:hover {
  background: rgba(102, 126, 234, 0.05);
  border-color: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
} */

/* .radio-item-custom .el-radio__label {
  font-size: 15px;
  color: #4a5568;
  font-weight: 600;
  margin-left: 8px;
}

.radio-item-custom .el-radio__input.is-checked + .el-radio__label {
  color: #D7A256;
  font-weight: 700;
}

.radio-item-custom .el-radio__input .el-radio__inner {
  border-color: rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 20px;
  height: 20px;
}

.radio-item-custom .el-radio__input.is-checked .el-radio__inner {
  border-color: #D7A256;
  background: linear-gradient(135deg, #dbab67 0%, #D7A256 100%);
  box-shadow: 0 2px 8px rgba(215, 162, 86, 0.3);
}

.radio-item-custom .el-radio__input.is-checked .el-radio__inner::after {
  background-color: #ffffff;
  width: 8px;
  height: 8px;
  border-radius: 50%;
} */


/* 占位符样式优化 */
/* .el-input__inner::placeholder,
.el-textarea__inner::placeholder {
  color: rgba(160, 174, 192, 0.8);
  font-style: italic;
  font-weight: 400;
} */

/* 错误状态样式优化 */
/* .el-form-item.is-error .el-input__wrapper,
.el-form-item.is-error .el-textarea__inner {
  border-color: #e53e3e;
  box-shadow: 
    0 0 0 4px rgba(229, 62, 62, 0.15),
    0 4px 20px rgba(229, 62, 62, 0.2);
  background: rgba(255, 255, 255, 0.95);
}

.el-form-item.is-error .el-form-item__error {
  color: #e53e3e;
  font-size: 13px;
  margin-top: 8px;
  font-weight: 600;
  padding: 8px 12px;
  background: rgba(229, 62, 62, 0.1);
  border-radius: 8px;
  border-left: 3px solid #e53e3e;
} */

/* 响应式设计优化 */
/* @media (max-width: 768px) {
  .form-card {
    padding: 24px 16px 20px 16px;
    border-radius: 16px;
  }
  
  .form-item-custom {
    padding: 16px 20px;
    margin-bottom: 24px;
  }
  
  .radio-group-custom {
    gap: 16px;
  }
  
  .radio-item-custom {
    padding: 10px 16px;
  }
  
  .form-card :deep(.submit-buttons) .el-form-item__content {
    padding: 20px 16px;
    margin-top: 32px;
    flex-direction: column;
    gap: 16px;
  }
  
  .form-card :deep(.submit-buttons) .el-form-item__content .el-button {
    padding: 12px 24px;
    font-size: 14px;
    width: 100%;
    max-width: 200px;
  }
} */

/* 加载动画优化 */
/* @keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.form-card.loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.dialog-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 900;
  color: #fff;
  letter-spacing: 2px;
  position: relative;
  padding: 1.2rem 0 18px 0;
  margin-bottom: 10px;
  background: linear-gradient(90deg, #dbab67 0%, #D7A256 40%, #f7e7ff 100%);
  border-radius: 18px 18px 12px 12px;
  box-shadow: 0 6px 32px 0 rgba(215,162,86,0.18), 0 2px 8px rgba(102,126,234,0.10);
  text-shadow: 0 2px 12px rgba(215,162,86,0.18), 1px 1px 2px rgba(0,0,0,0.08);
  overflow: hidden;
}
.dialog-title::after {
  content: '';
  display: block;
  margin: 0 auto;
  margin-top: 12px;
  width: 90px;
  height: 6px;
  background: linear-gradient(90deg, #fffbe6 0%, #dbab67 50%, #D7A256 100%);
  border-radius: 3px;
  box-shadow: 0 2px 12px rgba(215,162,86,0.18);
} */
</style>
