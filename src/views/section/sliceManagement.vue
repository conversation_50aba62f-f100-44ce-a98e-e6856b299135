<template>
  <!--  <TableToolTemp tool-title="知识库" /> -->

  <PageTable ref="pageRef" :search-form-temp="searchFormTemp"   :param="param"  apiUrl="/web/maas/knowledge/chapter/page">
    <template #default="{ tableData }">
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column align="center" prop="title" label="标题"></el-table-column>
        <el-table-column align="center" prop="knowledgeId" label="知识ID" width="200"></el-table-column>

        <!--
        <el-table-column align="center" prop="projectCode" label="渠道" width="100">
          <template #default="scope">
            <span v-if="scope.row.projectCode && scope.row.project_name">{{ scope.row.projectCode + ' | ' + scope.row.project_name }}</span>
          </template>
        </el-table-column>
-->
        <el-table-column align="center" prop="dataType" label="文件格式" width="100"></el-table-column>
        <el-table-column align="center" prop="contentLength" label="字数长度" width="100"></el-table-column>
        <el-table-column align="center" prop="syncTime" label="同步时间" width="170"></el-table-column>
        <el-table-column align="center" prop="status" label="状态" width="100">
          <template #default="scope">
            <!--
            <span v-if="scope.row.status == '0'">上架</span>
            <span v-if="scope.row.status == '1'">下架</span>
-->
            <el-switch
              style="display: block"
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              :active-color="system.themeColor"
              @click="toChangeStatus(scope.row.status, scope.row.id)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="syncStatus" label="同步状态" width="100">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.syncStatus == '1'"
              :content="scope.row.message"
              :enterable="false"
              effect="dark"
              :width="100"
              :showOverflowTooltip="true"
              class="awidthTooltip"
              placement="top"
            >
              <span v-if="scope.row.syncStatus == '1'" style="color: red">失败</span>
            </el-tooltip>
            <span v-if="scope.row.syncStatus == '0'">成功</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleDetail(scope.row)">查看</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>
</template>

<script setup lang="ts">
import type { SearchFormTemp } from '@/components/types'
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import * as api from './api'
import useSystemStore from '@/stores/system'
import { useUserStore } from '@/stores/user'

const { system } = useSystemStore()

const { getDic } = useUserStore()
const router = useRouter()
const route = useRoute()
const pageRef = ref(null)
const param: Record<string, any> = {
  robotCode: route.query.robotCode
}

const searchFormTemp: SearchFormTemp[] = [
    {
      label: '文件名称',
      name: 'title',
      placeholder: '请输入文件名称',
      type: 'input',
      searchState: true
    },
    {
      label: '文件格式',
      name: 'dataType',
      placeholder: '请输入文件格式',
      type: 'select',
      list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.fileType')],
      searchState: true
    },
    {
        label: '知识ID',
        name: 'knowledgeId',
        placeholder: '请输入知识ID',
        type: 'input',
        searchState: true
    },
    {
        label: '同步状态',
        name: 'syncStatus',
        type: 'select',
        placeholder: '请选择同步状态',
        list: [
        { dicItemCode: '', dicItemName: '全部' },
        { dicItemCode: '0', dicItemName: '成功' },
        { dicItemCode: '1', dicItemName: '失败' },
        { dicItemCode: '2', dicItemName: '同步中' },
        ],
        searchState: true
    },
    {
      label: '启用状态',
      name: 'status',
      type: 'select',
      placeholder: '请选择同步状态',
      list: [
      { dicItemCode: '', dicItemName: '全部' },
      { dicItemCode: '0', dicItemName: '上架' },
      { dicItemCode: '1', dicItemName: '下架' },
      ],
      searchState: true
    },
]

function handleDetail(row: any) {
  //this.$store.commit("layoutStore/setLinks", "切片详情");
  router.push({
    path: '/sectionDetail',
    query: {
      productId: row.id,
    },
  })
}

function handleDelete(row: any) {
  ElMessageBox.confirm('是否删除?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res = await api.knowledgeDel({ dataId: row.id })
      if (res.status == 0) {
        ElMessage.success('删除成功')

        pageRef.value && pageRef.value.onSearchBtn()
      }
    })
    .catch(() => {})
}

function toChangeStatus(status: String, id: String) {
  const type = status == '1' ? 'down' : 'up'
  const typeName = type === 'up' ? '上架' : '下架'

  ElMessageBox.confirm(`是否${typeName}?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res = await api.knowledgeStatusUpdate({
        dataId: id,
        status: status,
      })
      if (res.status == 0) {
        ElMessage.success(typeName + '成功!')

        pageRef.value && pageRef.value.onSearchBtn()
      }
    })
    .catch(() => {
      // reset status
      const temp = pageRef.value.tableData.find((item: any) => (item.id = id))
      temp.status = status == '0' ? '1' : '0'
    })
}
</script>

<style lang="less" scoped></style>
