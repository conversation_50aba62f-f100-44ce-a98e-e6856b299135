<template>
  <TableToolTemp tool-title="智能体列表" :toolList="toolList" />

  <PageTable ref="pageRef" :search-form-temp="searchFormTemp" apiUrl="/web/maas/robot/page">
    <template #default="{ tableData }">
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column align="center" prop="robotCode" width="180" label="编号" />
        <el-table-column align="center" prop="robotName" label="名称" />
        <el-table-column align="center" prop="robotType" label="类型" width="200">
          <template #default="scope">
            <span>{{ getDicItemName(scope.row.robotType, 'chatbot.botType') }}</span>
          </template>
        </el-table-column>

        <!--
        <el-table-column align="center" prop="xxx" label="状态" width="100">
          <template #default="scope">
            <el-switch style="display: block" v-model="scope.row.status" :active-color="system.themeColor" @click="toChangeStatus(scope.row.status)">
            </el-switch>
          </template>
        </el-table-column>
-->

        <el-table-column align="center" prop="updateTime" label="发布时间" width="200"></el-table-column>
        <el-table-column align="center" prop="updateBy" label="发布人" width="100"></el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" width="200"></el-table-column>
        <el-table-column align="center" prop="createBy" label="创建人" width="100"></el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button type="text" @click="toConfig(scope.row)">配置</el-button>
            <el-button type="text" @click="onRemove(scope.row)">删除</el-button>
            <!--
            <el-button :disabled="scope.row.robotType == '2' ? false : true" type="text" @click="handleDetail(scope.row)">知识库</el-button>
-->
            <!--
            <el-button type="text" @click="handleEdit(scope.row)">更新</el-button>
-->
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>

  <!--新增弹窗-->
  <DtPopup v-model:show="showFormPop" :title="title" @close="closePopup" :footer="false">
    <el-form label-width="80px" label-position="right" :model="form" :rules="rules" ref="formRef" :inline="true">
      <el-form-item label="智能体类型" label-width="100px" prop="robotType" required>
        <el-select v-model="form.robotType" :disabled="formType === 'edit'">
          <el-option v-for="(item, idx) in getDic('chatbot.botType')" :key="idx" :label="item.dicItemName" :value="item.dicItemCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="智能体名称" label-width="100px" prop="robotName" required>
        <el-input v-model="form.robotName" auto-complete="off" placeholder="请输入智能体名称"></el-input>
      </el-form-item>
      <el-form-item label="智能体编码" label-width="100px" prop="robotCode" required>
        <el-input :disabled="formType === 'edit'" v-model="form.robotCode" auto-complete="off" placeholder="请输入智能体编码"></el-input>
      </el-form-item>

      <el-form-item label="唯一ID" prop="id" class="hidden"> </el-form-item>
    </el-form>

    <div class="popup-footer">
      <el-button type="primary" class="dt-btn" plain @click="closePopup(false)">取消</el-button>
      <el-button type="primary" class="dt-btn" @click="toSave">保存</el-button>
    </div>
  </DtPopup>
</template>

<script setup lang="ts">
import type { ToolListProps, SearchFormTemp } from '@/components/types'
import type { FormInstance } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import * as api from './api'
import { useRouterStore } from '@/stores/router'
import useFilters from '@/hooks/useFilters'

const { getDicItemName, getDic } = useFilters()
const { delRouterCaches } = useRouterStore()
const router = useRouter()

const formRef = ref<FormInstance>()
const pageRef = ref(null)
const showFormPop = ref(false)
const title = ref('')
const formType = ref('add') // add | edit

const form = reactive({
  robotName: '',
  robotCode: '',
  robotType: '',
  id: '',
})

const searchFormTemp: SearchFormTemp[] = [
  {
label: '编号',
name: 'robotCode',
placeholder: '请输入智能体编码',
type: 'input',
searchState: true
},
  {
label: '名称',
name: 'robotName',
placeholder: '请输入智能体名称',
type: 'input',
searchState: true
},
]

const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: '',
    action: async () => {
      title.value = '新增智能体'
      formType.value = 'add'
      showFormPop.value = true
    },
  },
]

const rules = {
  robotName: [{ required: true, message: '请输入智能体名称', trigger: 'blur' }],
  robotCode: [{ required: true, message: '请输入智能体编号', trigger: 'blur' }],
  robotType: [{ required: true, message: '选择智能体类型', trigger: 'blur' }],
}

function handleDetail(row: any) {
  // set top anchor
  //this.$store.commit("layoutStore/setLinks", "知识库");

  router.push({
    path: '/sliceManagement',
    query: {
      robotCode: row.robotCode,
    },
  })
}

function toConfig(data: Record<string, any>) {
  router.push({
    path: '/botConfig',
    query: {
      id: data.id,
      robotCode: data.robotCode,
      name: data.robotName,
      createTime: data.createTime,
      robotType: data.robotType
    },
  })
}

function handleEdit(row: any) {
  showFormPop.value = true
  title.value = '编辑智能体'
  formType.value = 'edit'
  form.robotName = row.robotName
  form.robotCode = row.robotCode
  form.robotType = row.robotType
  form.id = row.id
}
  /**
 * @description 删除
 */
 const onRemove = async (data: any) => {
  ElMessageBox.confirm('确定删除该模型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.robotDel(data.id)
    await pageRef.value.onSearchBtn()
  })
}
function closePopup(fresh: boolean = false) {
  formRef.value && formRef.value.clearValidate()
  formRef.value && formRef.value.resetFields()
  showFormPop.value = false

  if (fresh) {
    pageRef.value && pageRef.value.onSearchBtn()
  }
}

async function toSave() {
  const flag = formRef.value && (await formRef.value.validate())
  if (!flag) {
    return
  }

  if (formType.value === 'add') {
    const params = { ...form }
    delete params.id
    const res = await api.robotAdd(params)
    if (res.status) {
      ElMessage.error(res.msg)
      return
    }

    closePopup(true)
    return
  }

  const res = await api.robotUpdate(form)
  if (res.status) {
    ElMessage.error(res.msg)
    return
  }
  closePopup(true)
}

function toChangeStatus(status: any) {
  // todo
  alert('wait provide method ' + status)
}

onMounted(() => {
  delRouterCaches('botConfig')
})
</script>

<style lang="less" scoped>
.hidden {
  display: none;
}
.popup-footer {
  display: flex;
  justify-content: center;
}
</style>
