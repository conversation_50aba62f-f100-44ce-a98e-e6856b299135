import { useAxios } from '@/hooks/http'

const baseURL = import.meta.env.VITE_APP_MASS_API
const defalutConfig = { baseURL }

// 查询登录日志
export const knowledgeDataPage = async (data: any) => await useAxios("/bs/knowledge/data/page", data, defalutConfig);


// 上架下架
export const knowledgeStatusUpdate = async (data: any) => await useAxios("/web/maas/knowledge/data/status/update", data);
//删除知识列表
export const knowledgeDel = async (data: any) => await useAxios("/web/maas/knowledge/data/del", data);

//知识库
//export const knowledgeSliceList = async (data: any) => await useAxios("/bs/knowledge/slice/list", data, defalutConfig);

//新增切片
export const knowledgeSliceAdd = async (data: any) => await useAxios("/web/maas/knowledge/slice/add", data);
//更新切片内容
export const knowledgeSliceUpdate = async (data: any) => await useAxios("/web/maas/knowledge/slice/content/update", data);
// 更新切片状态
export const sliceStatusUpdate = async (data: any) => await useAxios("/web/maas/knowledge/slice/status/update", data);
// 删除切片
export const sliceDel = async (data: any) => await useAxios("/web/maas/knowledge/slice/del", data);

// 智能体列表-分页
//export const robotList = async (data: any) => await useAxios("/bs/robot/list", data, defalutConfig);
// 智能体列表
//export const robotPage = async (data: any) => await useAxios("/bs/robot/page", data, defalutConfig);
// 智能体列表更新
export const robotUpdate = async (data: any) => await useAxios("/web/maas/robot/update", data);
// 智能体列表新增
export const robotAdd = async (data: any) => await useAxios("/web/maas/robot/create", data);

// 机器人基础配置新
export const robotBaseInfo = async (data: any) => await useAxios("/web/maas/robot/find", data);
//删除能体列表
export const robotDel = async (id: any) => await useAxios("web/maas/robot/delete",  { id });
// 渠道智能体列表
//export const channelRobotList = async (data: any) => await useAxios("/bs/channelRobot/list", data, defalutConfig);
// 渠道智能体新增
//export const channelRobotAdd = async (data: any) => await useAxios("/bs/channelRobot/add", data, defalutConfig);
// 渠道智能体更新
//export const channelUpdateStatus = async (data: any) => await useAxios("/bs/channelRobot/updateStatus", data, defalutConfig);
