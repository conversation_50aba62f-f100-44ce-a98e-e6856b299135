<template>
  <div class="menu-table">
    <TableToolTemp tool-title="问答记录" :tool-list="toolList"></TableToolTemp>
    <SearchForm :searchFormTemp="searchFormTemp" @handleSearch="handleSearch" @handleResetQuery="resetSearch">
    </SearchForm>
    <el-table :data="tableData" class="dt-table" stripe>
      <el-table-column prop="words" label="敏感词" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" />
      <el-table-column prop="createId" label="操作人" align="center">
        <template #default="scope">
          {{ getNickName(scope.row.createId) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="username"
        label="用户名"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="channel_name"
        width="200"
        label="渠道"
      ></el-table-column>
      <!-- <el-table-column
        align="center"
        prop="channel_code"
        width="200"
        label="渠道编码"
      ></el-table-column> -->
      <el-table-column
        align="center"
        prop="robot_name"
        width="200"
        label="机器人"
      ></el-table-column>
      <!-- <el-table-column
        align="center"
        prop="robot_code"
        width="200"
        label="机器人编码"
      ></el-table-column> -->
      <el-table-column
        align="center"
        width="120"
        prop="model"
        label="模型"
      ></el-table-column>
      <el-table-column
        align="center"
        width="120"
        prop="intention"
        label="意图"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="question"
        width="200"
        label="问题"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="contextualize_question"
        width="200"
        label="AI补全问题"
      ></el-table-column>
      <el-table-column align="center" prop="answer" width="150" label="答案">
        <template #default="scope">
          <el-tooltip
            v-if="scope.row.answer"
            :content="
              scope.row.answer.replace(/<strong>(.*?)<\/strong>/g, '$1')
            "
            :enterable="false"
            effect="dark"
            :width="100"
            :showOverflowTooltip="true"
            class="awidthTooltip"
            placement="top"
          >
            <p
              class="awidth"
              v-html="
                scope.row.answer.replace(/<strong>(.*?)<\/strong>/g, '$1')
              "
            ></p>
          </el-tooltip>
          <span v-else>""</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="answer_type"
        width="200"
        label="回复类型"
      >
        <template #default="scope">
          <span v-if="scope.row.answer_type == '0'">系统默认回复</span>
          <span v-if="scope.row.answer_type == '1'">知识问答</span>
          <span v-if="scope.row.answer_type == '2'">闲聊</span>
          <span v-if="scope.row.answer_type == '3'">转人工</span>
          <span v-if="scope.row.answer_type == '4'">产品列表</span>
          <span v-if="scope.row.answer_type == '5'"> 产品资料</span>
          <span v-if="scope.row.answer_type == '6'"> 方案推荐</span>
          <span v-if="scope.row.answer_type == '7'"> 产品选择</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="cost"
        width="200"
        label="问答时长(秒)"
      ></el-table-column>
      <el-table-column
        align="center"
        width="200"
        prop="create_time"
        label="创建时间"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="user_id"
        label="用户ID"
        width="200"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="session_id"
        width="200"
        label="会话ID"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="question_id"
        width="200"
        label="问题ID"
      ></el-table-column>
      <!-- <el-table-column label="操作" align="center" fixed="right" width="180px">
        <template #default="scope">
          <el-button
            type="text"
            :disabled="scope.row.has_references == 1 ? false : true"
            @click="handleDetail(scope.row)"
            >引用知识列表</el-button
          >
        </template>
      </el-table-column> -->
        <el-table-column label="操作" align="center" width="150px" fixed="right">
          <template #default="scope">
            <el-button link type="primary" @click="remove(scope.row)">引用知识列表</el-button>
          </template>
        </el-table-column>
    </el-table>
    <Pagination ref="paginationRef" @change="onPageData" :total="total" />
  </div>

  <DtPopup :show="showSensitiveWord" @close="closeDialog" @close-by-icon="closeDialog" :title="`添加敏感词`" :footer="false">
    <UpdateFormTemp ref="formTempRef" :form-list="sensitiveList" :rules="rules" :showSubmit="false"></UpdateFormTemp>
    <div class="formBtn">
      <el-button class="dt-btn" @click="closeDialog">取消</el-button>
      <el-button class="dt-btn" type="primary" @click="submit">保存</el-button>
    </div>
  </DtPopup>
</template>



<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router';
import { ToolListProps, PageTableSearch, PageData } from '@/components/types';
import type { SearchFormTemp } from '@/components/types'
import useFilters from '@/hooks/useFilters';
// import {
//   qaRecordList,
//   channelsensitivewordsAdd,
//   deleteSensitiveWords
// } from '@/api/answerLog'
import type { FormRules } from 'element-plus'

const emit = defineEmits<{
  (e: 'updateStatus'): void
}>()
const props = defineProps({
  versionIndex: Number
})
const searchFormTemp: SearchFormTemp[] = [
  {
label: '智能体编号',
name: 'code',
type: 'input',
placeholder: '请输入智能体编码',
searchState: true
},
  {
label: '智能体名称',
name: 'name',
type: 'input',
placeholder: '请输入智能体名称',
searchState: true
},
  // {
  //   label: '发布状态',
  //   name: 'status',
  //   type: 'select',
  //   placeholder: '请选择',
  //   /**
  //    * @description 智能体状态
  //    * 0、未发布 1、已发布 2、发布中 3、编辑中 9、已下线
  //    */
  //   list: getDic('chatbot.status')
  // }
]
const formTempRef = ref(null)
const { getNickName, getDicItemName } = useFilters()
const route = useRoute()
const toolList: ToolListProps[] = [
  {
    name: '添加敏感词',
    btnCode: '',
    action: async () => {
      showSensitiveWord.value = true
    },
  },
]
const sensitiveList = ref<Record<string, any>[]>([
  {
    title: '敏感词',
    type: 'input',
    field: 'words',
  },
])
const rules: FormRules = {
  words: [{ required: true, message: '请输入敏感词', trigger: 'blur' }],
}
const submit = async () => {
  // const { words } = await formTempRef.value.getFormData()
  // if (!words) return
  // const res = await channelsensitivewordsAdd({
  //   channelCode: channelCode,
  //   channelId: channelId,
  //   words,
  // })
  // if (res.status === 0) {
  //   closeDialog()
  //   init()
  // }
}

const showSensitiveWord = ref<boolean>(false)
const tableData = ref<Record<string, any>[]>([])
const total = ref<number>(0)
const robotId = ref<number>(Number(route.query.id))
const channelId = route.query.channelId
const channelCode = route.query.channelCode
const pageParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    channelId: channelId,
    channelCode: channelCode,
    words: ''
  }
})

const handleSearch = (data: any) => {
  pageParam.value.param = data
  init()
}

const resetSearch = (data: any) => {
  pageParam.value.param = data
  init()
}
const remove = async (row: Record<string, any>) => {
  // ElMessageBox.confirm('是否删除该敏感词？', '提示', {
  //   confirmButtonText: '确定',
  //   cancelButtonText: '取消',
  //   type: 'warning',
  // }).then(async () => {
  //   const res = await deleteSensitiveWords({
  //     id: row.id,
  //   })
  //   if (res.status === 0) {
  //     init()
  //   }
  // })
}


const closeDialog = async () => {
  showSensitiveWord.value = false
  await formTempRef.value.reset('words')
}

const onPageData = async (data: PageData) => {
  pageParam.value.pageNum = data.pageNum
  pageParam.value.pageSize = data.pageSize
  init()
}


const init = async () => {
  // const res = await qaRecordList(pageParam.value)
  // if (res.status === 0) {
  //   tableData.value = res.data.list
  //   total.value = res.data.total
  // }
}

watch(() => props.versionIndex, (val) => {
  init()
})

onMounted(async () => {
  await init()
})
defineExpose({
  search: async () => {
    await init()
  },
})
</script>

<style lang="less" scoped>
.menu-content {
  display: flex;

  // 不可全选样式
  :deep(.el-tree-node) {
    .el-checkbox__input.is-disabled {
      display: none;
    }
  }

  .menu-tree {
    width: 300px;
  }

  .menu-table {
    flex: 1;
    overflow-y: auto;
  }
}

.iframe-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 99999;

  .popup-icon {
    position: absolute;
    font-size: 65px;
    top: 7px;
    right: -61px;
    color: #b8b8b8;
    cursor: pointer;
  }
}
</style>