<template>
  <div>
    <TableToolTemp toolTitle="智能体列表" :toolList="toolList" />
    <SearchForm :searchFormTemp="searchFormTemp" @handleSearch="handleSearch" @handleResetQuery="resetSearch">
    </SearchForm>
    <el-table :data="tableData" stripe class="dt-table">
      <el-table-column prop="code" label="智能体编号" align="center" />
      <el-table-column prop="name" label="智能体名称" align="center" />
      <el-table-column prop="status" label="发布状态" align="center" width="80">
        <template #default="scope">
          <!-- 0、未发布 1、已发布 2、发布中 3、编辑中 9、已下线 -->
          {{ getDicItemName(scope.row.status, 'chatbot.status') }}
        </template>
      </el-table-column>
      <el-table-column prop="version" label="线上版本号" align="center" width="100" />
      <el-table-column prop="publishTime" label="发布时间" align="center" width="170" />
      <el-table-column prop="publisher" label="发布人" align="center">
        <template #default="scope">
          {{ getNickName(scope.row.publisher) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" width="350" fixed="right">
        <template #default="scope">
          <el-button type="primary" text size="small" @click="handleConfig(scope.row)">
            配置
          </el-button>
          <el-button type="primary" text size="small" @click="editName(scope.row)">
            编辑名称
          </el-button>
          <el-button v-if="scope.row.status == 0 || scope.row.status == 3" type="primary" text size="small"
            @click="publishBot(scope.row)">
            发布
          </el-button>
          <el-button v-if="scope.row.status != 0" type="primary" text size="small" @click="publishRecord(scope.row)">
            发布记录
          </el-button>
          <el-button v-if="scope.row.status == 1 || scope.row.status == 3" type="primary" text size="small"
            @click="downOnline(scope.row)">
            下线
          </el-button>
          <el-button v-if="scope.row.status == 9" type="primary" text size="small" @click="upOnline(scope.row)">
            上线
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination ref="paginationRef" @change="onPageData" :total="total" />
  </div>

  <!-- 新增智能体弹框 -->
  <DtPopup :show="addShow" @close="closeAdd" @close-by-icon="closeAdd" :title="addRobotTitle" :footer="false">
    <el-form ref="ruleFormRef" :rules="addRules" :model="addRuleForm" label-position="left" label-width="110px">
      <el-form-item label="智能体名称" prop="name">
        <el-input maxlength="10" v-model="addRuleForm.name"></el-input>
      </el-form-item>
      <div class="formBtn">
        <el-button class="dt-btn" @click="closeAdd">取消</el-button>
        <el-button class="dt-btn" type="primary" @click="saveRobot(ruleFormRef)">保存</el-button>
      </div>
    </el-form>
  </DtPopup>

  <!-- 发布记录弹框 -->
  <DtPopup :show="publishShow" @close="closePublish" @close-by-icon="closePublish" title="发布记录" :footer="false"
    width="900">
    <el-table :data="publishTableData" stripe class="dt-table">
      <el-table-column prop="publishTime" label="发布时间" align="center" />
      <el-table-column prop="publisher" label="发布人" align="center">
        <template #default="scope">
          {{ getNickName(scope.row.publisher) }}
        </template>
      </el-table-column>
      <el-table-column prop="version" label="版本号" align="center" />
      <el-table-column prop="status" label="发布状态" align="center">
        <template #default="scope">
          <!-- 0、未发布 1、已发布 2、发布中 3、编辑中 9、已下线 -->
          {{ getDicItemName(scope.row.status, 'chatbot.status') }}
        </template>
      </el-table-column>
      <el-table-column prop="errorMsg" label="发布失败原因" align="center" />
    </el-table>
    <Pagination ref="paginationRef" @change="publishChange" :total="publishTotal" />
  </DtPopup>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import type { ToolListProps, PageData, PageTableSearch } from '@/components/types'
import type { SearchFormTemp } from '@/components/types'
import { useRouter } from 'vue-router'
import { onActivated } from 'vue'
import { FormInstance } from 'element-plus'
import { useUserStore } from '@/stores/user'
import useFilters from '@/hooks/useFilters'
import {
  getRobotPage,
  addRobot,
  publish,
  downOnlineBot,
  editRobot,
  upOnlineBot,
  releaseRecord
} from './api'
defineOptions({ name: 'botManage' })
const { getNickName, getDicItemName } = useFilters()
const { getDic } = useUserStore()
const router = useRouter()
const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: '',
    action: async () => {
      addRobotTitle.value = '新增智能体'
      addShow.value = true
    }
  }
]
const searchFormTemp: SearchFormTemp[] = [
  {
label: '智能体编号',
name: 'code',
type: 'input',
placeholder: '请输入智能体编码',
searchState: true
},
  {
label: '智能体名称',
name: 'name',
type: 'input',
placeholder: '请输入智能体名称',
searchState: true
},
  {
label: '发布状态',
name: 'status',
type: 'select',
placeholder: '请选择',
/**
* @description 智能体状态
* 0、未发布 1、已发布 2、发布中 3、编辑中 9、已下线
*/
list: getDic('chatbot.status'),
searchState: true
}
]
const total = ref<number>(0)
const pageParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    code: '',
    name: '',
    status: ''
  }
})
const tableData = ref<Record<string, any>[]>([])
const addShow = ref<boolean>(false)
const addRuleForm = ref<Record<string, any>>({ name: '' })
const addRules = {
  name: [{ required: true, message: '请输入智能体名称', trigger: 'blur' }]
}
const ruleFormRef = ref<FormInstance>()
const addRobotTitle = ref<string>('新增智能体')
const publishShow = ref<boolean>(false)
const publishTableData = ref<Record<string, any>[]>([])
const publishTotal = ref<number>(0)
const publishParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    robotId: ''
  }
})


const closeAdd = () => {
  addShow.value = false
  addRuleForm.value = { name: '' }
}

const closePublish = () => {
  publishShow.value = false
  publishTableData.value = []
}

const publishChange = (data: PageData) => {
  pageParam.value.pageNum = data.pageNum
  pageParam.value.pageSize = data.pageSize
  getPublishList()
}

const saveRobot = (formEl: FormInstance | undefined) => {
  formEl.validate(async (valid: boolean) => {
    if (valid) {
      let res: any
      if (addRobotTitle.value == '编辑智能体') {
        res = await editRobot(addRuleForm.value)
      } else {
        res = await addRobot(addRuleForm.value)
      }
      if (res.status === 0) {
        closeAdd()
        initTableData()
      }
    }
  })
}

const publishBot = async (row: any) => {
  ElMessageBox.confirm(
    `确定要发布吗？`,
    '请确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const res = await publish({ id: row.id })
      if (res.status === 0) {
        initTableData()
      }
    })
}

const upOnline = async (row: any) => {
  ElMessageBox.confirm(
    `确定要上线吗？`,
    '请确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const res = await upOnlineBot({ id: row.id })
      if (res.status === 0) {
        initTableData()
      }
    })
}

const downOnline = async (row: any) => {

  ElMessageBox.confirm(
    `确定要下线吗？`,
    '请确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const res = await downOnlineBot({ id: row.id })
      if (res.status === 0) {
        initTableData()
      }
    })
}

const publishRecord = async (row: any) => {
  publishParam.value.param.robotId = row.id
  await getPublishList()
  publishShow.value = true
}

const getPublishList = async () => {
  const res = await releaseRecord(publishParam.value)
  if (res.status === 0) {
    publishTableData.value = res.data.list
    publishTotal.value = res.data.total
  }
}

const editName = (row: any) => {
  addRobotTitle.value = '编辑智能体'
  addRuleForm.value = { name: row.name, id: row.id }
  addShow.value = true
}

const handleSearch = (data: any) => {
  pageParam.value.param = data
  initTableData()
}

const resetSearch = (data: any) => {
  pageParam.value.param = data
  initTableData()
}

const onPageData = (data: PageData) => {
  pageParam.value.pageNum = data.pageNum
  pageParam.value.pageSize = data.pageSize
  initTableData()
}

const handleConfig = (data: Record<string, any>) => {
  router.push({
    path: '/botConfig',
    query: {
      id: data.id,
      name: data.name,
      status: data.status,
      version: data.version,
      createTime: data.createTime,
      preStatus: data.preStatus
    }
  })
}

const initTableData = async () => {
  const res = await getRobotPage(pageParam.value)
  if (res.status === 0) {
    tableData.value = res.data.list
    total.value = res.data.total
  }
}

onActivated(async () => {
  initTableData()
})
</script>


<style>
.formBtn {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
