<template>
     <TableToolTemp tool-title="模型配置" :tool-list="toolList"></TableToolTemp>
     <el-form  :model="form"  class="form"  :rules="rules" label-position="left" :inline="true" @submit="submitForm">
        <div  v-for=" (item, index) in form.formList" :key="index">
            <span style="color: red;position: relative;top:-5px">*&nbsp;</span><el-form-item label="模型"  prop="modelId" >
                <el-select v-model="item.modelId" placeholder="请选择模型" clearable @change="onTypeChange(index,item, $event)">
                    <el-option v-for=" (el, idx) in initList" :key="idx" :disabled="el.disabled" :label="el.modelName"
                    :value="el.id" />
                </el-select>
            </el-form-item>
            <span style="color: red;position: relative;top:-5px">*&nbsp;</span><el-form-item label="权重"  prop="weight"  ref="contentRef">
                <el-input 
                type="number"
                v-model="item.weight" />
            </el-form-item>
            <el-form-item label=""  ref="contentRef">
                <el-button  @click="del(index,item)">删除</el-button>
            </el-form-item>
            </div>
       </el-form>
       <div v-if="form.formList.length>0" class="btn" >
            <el-button type="primary" @click="submitForm()">提交</el-button>
            <el-button @click="cancel">取消</el-button>
        </div>
        <div v-else class="btn1" >
           暂无数据
        </div>
  </template>
  
  <script lang="ts" setup>
  import { ref, onMounted, watch, reactive } from 'vue';
  import type { ToolListProps, UpdateFormList } from '../../../components/types'
  import type { FormRules } from 'element-plus'
  import { useRoute } from 'vue-router'
  import useFilters from '@/hooks/useFilters'
import * as api from '@/api/model'
  const initList = ref<Record<string, any>[]>([])
  import router from '@/router'
const form: Record<string, any> = reactive({
    formList:[
      {
        weight:'',
        modelId:''
      }
    ]
})
  const props = defineProps<{
    versionIndex: Number
  }>()
  const baseFormRef = ref()
  const toolList: ToolListProps[] = [
  {
    name: '添加模型',
    btnCode: '',
    action: async () => {
        form.formList.push({
            weight:'',
            modelId:'' 
        })
    },
  },
]
  const route = useRoute()
  
  const rules: FormRules = {
    // modelId: [{ required: true, message: '不能为空', trigger: 'blur' }],
    // weight: [{ required: true, message: '不能为空', trigger: 'blur' }],
  }
  const submitForm = async () => {
   let data=[]
   if(form.formList && form.formList.length>0){
    for(let i=0;i<form.formList.length;i++){
        if(!form.formList[i].modelId){
            return  ElMessage.error('第'+(i+1)+'行模型值不能为空')  
        }
        if(!form.formList[i].weight){
            return  ElMessage.error('第'+(i+1)+'行权重值不能为空')  
        }
        data.push({
            modelId: Number(form.formList[i].modelId),
            weight:Number(form.formList[i].weight)
        })

    }
   }
  
    const res = await api.robotmodelSave({ models:data,robotId: route.query.id, })
    if (res.status === 0) {
        initBaseConfig()
      ElMessage.success('保存成功')
    }
  }
    
 // 去重不让选中
  const listDisabled=(val:any)=> {
    console.log(val)
    for (let i = 0; i < initList.value.length; i++) {
      if (initList.value[i].id == Number(val)) {
         initList.value[i].disabled = true;
      }
    }
  }
   // 更换选中
   const changelistDisabled=(val:any)=> {
    console.log(form.formList,initList.value)
    for(let j = 0; j < form.formList.length; j++){
    for (let i = 0; i < initList.value.length; i++) {
        initList.value[i].disabled = false;
        if(initList.value[i].id ==  form.formList[j].modelId){
          initList.value[i].disabled = true;
        }
      }
    }
    
    console.log(initList.value)
  }
  const del = async (index:any,val:any) => {
    console.log(val)
    if(form.formList.length==1){
      return  ElMessage.error('必须保留一个模型')  
    }
    form.formList.splice(index, 1);
    console.log(initList.value)
    for (let i = 0; i < initList.value.length; i++) {
      if (initList.value[i].id == Number(val.modelId)) {
        initList.value[i].disabled = false;
      }
    }
  }
  async function Init() {
    const { status, data }= await  api.modelList()
    if (status != 0) return
    initList.value = data
  }
  
  const cancel = () => {
    router.back()
  }
  const onTypeChange = (index:any,item: any, args: string[]) => {
    // initList
    console.log(index,item,args)
    console.log(initList.value,form.formList)
    if(args){
      changelistDisabled(args)//回显设置disabled
    }else{
      initList.value[index].disabled = false;
    }
  }
  const initBaseConfig = async () => {
    const data = {
        robotId: route.query.id,
    }
    const res = await api.robotmodelList(data)
    const formdata=[]
    if (res.status === 0 && res.data) {
        for(let i=0;i<res.data.length;i++){
            formdata.push({
                modelId: Number(res.data[i].modelId),
                weight: res.data[i].weight
            })
            listDisabled(res.data[i].modelId)//回显设置disabled
        }
        form.formList=formdata
    } else {
      form.formList=[{
            weight:'',
            modelId:'' 
      }]
    }
  }
  
  watch(
    () => props.versionIndex,
    () => {
      initBaseConfig()
    }
  )
  
  onMounted(async () => {
    Init()
    initBaseConfig()
  })
  </script>
  <style lang="less">
   .form{
    margin-left: 20px;
   }
   .btn{
    margin-left: 200px;
   }
   .btn1{
    margin-left: 200px;
    font-size: 14px;;
   }
 </style>