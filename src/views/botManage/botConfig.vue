<template>
  <DtNav from="智能体管理" :fromPath="'/botManage'" name="智能体配置"></DtNav>

  <!-- <el-tabs v-model="versionIndex" type="border-card" class="tabs" @tab-change="versionSelect">
    <el-tab-pane v-for="item, index in versionList" :name="item.id" :label="item.title" :key="item.id">
    </el-tab-pane>
  </el-tabs> -->

  <div class="temp-content">
    <!-- <el-radio-group v-model="versionIndex" size="large" class="radio-btn">
      <el-radio-button v-for="item, index in versionList" :label="item.id" :key="item.id">
        {{ item.title }}
      </el-radio-button>
    </el-radio-group> -->
    <el-tabs v-model="configIndex" class="tabs bottom-tabs" @tab-change="configSelect">
      <el-tab-pane :key="0" :name="0" label="基础配置">
        <baseConfig ref="baseRef" :versionIndex="versionIndex" v-if="configIndex === 0" @updateStatus="updateStatus"> </baseConfig>
      </el-tab-pane>

      <el-tab-pane :key="1" :name="1" label="知识配置" v-if="showKnowledge">
        <knowledgeConfig ref="knowledgeRef" :versionIndex="versionIndex" v-if="configIndex === 1" @updateStatus="updateStatus"></knowledgeConfig>
      </el-tab-pane>

      <el-tab-pane :key="2" :name="2" label="切片管理"  v-if="showKnowledge" >
        <sliceManagement v-if="configIndex === 2" />
      </el-tab-pane>
      <el-tab-pane :key="3" :name="3" label="模型配置"  >
        <modelConfig v-if="configIndex === 3" ></modelConfig>
      </el-tab-pane>
      <!--
      <el-tab-pane :key="2" :name="2" label="转人工配置">
        <manualConfig ref="manualRef" :versionIndex="versionIndex" v-if="configIndex === 2" @updateStatus="updateStatus">
        </manualConfig>
      </el-tab-pane>
      <el-tab-pane :key="3" :name="3" label="敏感词配置">
        <sensitiveWord ref="sensitiveRef" :versionIndex="versionIndex" v-if="configIndex === 3"
          @updateStatus="updateStatus">
        </sensitiveWord>
      </el-tab-pane>
-->
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import baseConfig from './components/baseConfig.vue'
import knowledgeConfig from './components/knowledgeConfig.vue'
import modelConfig from './components/modelConfig.vue'
//import sensitiveWord from './components/sensitiveWord.vue'
import sliceManagement from '@/views/section/sliceManagement.vue'

const route = useRoute()
const robotId = ref<number>(null)
const knowledgeRef = ref(null)
const baseRef = ref()

const showKnowledge = route.query.robotType == "2"

/**
 * 更新状态
 */
const updateStatus = () => {
  //if (route.query.status !== '0') {
  //  route.query.status = '3'
  //}

  knowledgeRef.value && knowledgeRef.value.init()
}

/**
 * 当前选中的版本
 * @description 0: 测试版 1: 正式版
 */
const versionIndex = ref<number>(1)

const configIndex = ref<number>(0)
const configSelect = (index: number) => {
  configIndex.value = index
}

onMounted(() => {
  robotId.value = Number(route.query.id)
})
</script>

<style lang="less" scoped>
.temp-content {
  position: relative;

  .radio-btn {
    position: absolute;
    top: 0;
    right: 0;
    margin-top: 20px;
    margin-right: 10px;
    z-index: 999;
  }
}

.el-menu-demo {
  :deep(.el-menu-item) {
    width: 120px;
  }
}

.tabs {
  margin-bottom: 10px;
  border-bottom: none;

  :deep(.el-tabs__content) {
    padding: 0;
  }

  :deep(.el-tabs__item) {
    width: 120px;
  }
}

.bottom-tabs {
  :deep(.el-tabs__item) {
    // border-bottom: 1px solid #DCDFE6;
    height: 80px;
    padding: 20px;
  }
}
</style>

<style lang="less">
.submit-btn {
  padding-left: 50px;
  padding-top: 30px;
}
</style>
