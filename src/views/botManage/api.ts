import { useAxios } from '@/hooks/http'

/**
 * @description 智能体列表
 */
export const getRobotPage = async (data: any) => await useAxios('/web/robot/page', data)

/**
 * @description 添加智能体
 */
export const addRobot = async (data: any) => await useAxios('/web/robot/add', data)

/**
 * @description 发布智能体
 */
export const publish = async (data: any) => await useAxios('/web/robot/release', data)

/**
 * @description 上线智能体
 */
export const upOnlineBot = async (data: any) => await useAxios('/web/robot/up', data)

/**
 * @description 下线智能体
 */
export const downOnlineBot = async (data: any) => await useAxios('/web/robot/down', data)

/**
 * @description 编辑智能体名称
 */
export const editRobot = async (data: any) => await useAxios('/web/robot/update', data)

/**
 * @description 获取智能体基础配置
 */
// export const getRobotConfig = async (data: any) => await useAxios('/web/robotbasicconfig/get', data)

// 机器人基础配置新
export const robotBaseInfo = async (data: any) => await useAxios("/web/maas/robot/find", data);


/**
 * @description 保存智能体基础配置
 */
//export const saveRobotConfig = async (data: any) => await useAxios('/web/robotbasicconfig/save', data)
export const saveRobotConfig = async (data: any) => await useAxios('/web/maas/robot/update', data)

/**
 * @description 保存转人工配置
 */
export const saveManualConfig = async (data: any) => await useAxios('/web/manualserviceconfig/save', data)

/**
 * @description 查询转人工配置
 */
export const getManualConfig = async (data: any) => await useAxios('/web/manualserviceconfig/get', data)

/**
 * @description 查询知识配置目录
 */
export const knowledgeList = async (data: any) => await useAxios('/web/knowledgeconfig/list', data)

/** @description 查询已绑定知识目录
 */
export const bindKnowLedgeList = async (data: any) => await useAxios('/web/knowledgeconfig/bindList', data)

/**
 * @description 绑定知识目录
 */
export const saveBind = async (data: any) => await useAxios('/web/knowledgeconfig/bind', data, { loading: false, msg: true })

/**
 * @description 解绑知识目录
 */
export const unbindKnowLedge = async (data: any) => await useAxios('/web/knowledgeconfig/unbind', data, { loading: false, msg: true })

/**
 * @description 刷新知识
 */
export const refreshKnowledge = async (data: any) => await useAxios('/web/knowledgeconfig/refresh', data)

/**
 * @description 知识操作记录
 */
export const knowledgeRecord = async (data: any) => await useAxios('/web/knowledgerecord/page', data)

/**
 * @description 智能体发布记录
 */
export const releaseRecord = async (data: any) => await useAxios('/web/releaserecord/page', data)

/**
 * @description 知识明细列表
 */
//export const knowledgeDetailList = async (data: any) => await useAxios('/web/knowledgeconfig/pageSearch', data)
export const knowledgeDetailList = async (data: any) => await useAxios('/web/maas/knowledge/data/page', data)

/**
 * @description 获取FuncId
 */
export const getFuncId = async () => await useAxios('/web/knowledgeconfig/getFuncId')

/**
 * @description 敏感词列表
 */
export const sensitiveWordsPage = async (data) => await useAxios('/web/sensitivewords/page', data)

/**
 * @description 添加敏感词
 */
export const addSensitiveWords = async (data) => await useAxios('/web/sensitivewords/save', data)

/**
 * @description 删除敏感词
 */
export const deleteSensitiveWords = async (data) => await useAxios('/web/sensitivewords/delete', data)

/**
 * @description 获取directoryList
 */
export const directoryList = async (data) => await useAxios('/web/knowledgeconfig/directoryList', data)

/**
 * @description 更新知识目录
 */
export const updateKnowLedgeDetail = async (data: any) => await useAxios('/web/knowledgeconfig/update', data)


/**
 * @description 知识章节列表
 */
//export const knowledgeDetailList = async (data: any) => await useAxios('/web/knowledgeconfig/pageSearch', data)
export const knowledgeChapterPage = async (data: any) => await useAxios('/web/maas/knowledge/chapter/page', data)