<template>
  <div class="flow-record-page">
    <div class="breadcrumb-nav-underline">
      <DtNav :from="'执行记录列表'" :name="'详情'" :fromPath="'/flowRecordManage'" />
    </div>
    <div class="flow-main">
      <!-- 左侧节点列表 -->
      <div class="flow-node-list">
        <div class="flow-nodes-container">
          <div v-for="(node, idx) in mainNodes" :key="node.id || idx" class="flow-node-wrapper">
            <!-- 节点连接线 -->
            <div class="node-connector" v-if="idx > 0"></div>
            
            <div :class="['node-list-item', { active: isSelected('main', idx) }]" @click="selectMainNode(idx)">
              <!-- 节点图标 -->
              <div class="node-icon">
                <el-icon v-if="node.data.node_type=='start'" class="start-icon"><CaretTop /></el-icon>
                <el-icon v-else-if="node.data.node_type=='end'" class="end-icon"><CaretBottom /></el-icon>
                <el-icon v-else class="process-icon"><Connection /></el-icon>
              </div>
              
              <div class="node-content">
                <span class="node-title">{{ node.data.title }}</span>
                <span class="node-type">{{ nodeTypeText(node) }}</span>
              </div>
              
              <!-- 迭代节点入口 -->
              <template v-if="node.event === 'iteration_completed' && getIterationLength(node) > 0">
                <span class="node-iteration" @click.stop="toggleIteration(idx, node)">
                  {{ getIterationLength(node) }}个迭代
                  <el-icon class="arrow-icon"> <ArrowDown v-if="iterationExpanded[idx]" /> <ArrowRight v-else /> </el-icon>
                </span>
              </template>
            </div>
            
            <!-- 迭代展开区 -->
            <transition name="fade">
              <div v-show="iterationExpanded[idx]" v-if="node.event === 'iteration_completed' && getIterationLength(node) > 0" class="iteration-container">
                <div v-for="(group, iterIdx) in iterationDetailMap[node.data.node_id] || []" :key="iterIdx" class="iteration-group">
                  <div class="node-list-item child" :class="{ active: isSelected('iteration', idx, iterIdx) }" @click.stop="toggleIterationGroup(node.data.node_id, iterIdx)">
                    <div class="iteration-icon">
                      <el-icon><RefreshRight /></el-icon>
                    </div>
                    <div class="node-content">
                      <span class="node-title">迭代{{ iterIdx + 1 }}</span>
                      <span class="node-type">({{ group.childlist.length }} 步)</span>
                    </div>
                    <el-icon class="arrow-icon"> <ArrowDown v-if="group.childup" /> <ArrowRight v-else /> </el-icon>
                  </div>
                  
                  <!-- 迭代子节点列表 -->
                  <div v-show="group.childup" class="child-nodes">
                    <div v-for="(child, cidx) in group.childlist" :key="cidx" class="node-list-item child2" :class="{ active: isSelected('iterationChild', idx, iterIdx, cidx) }" @click.stop="selectIterationChildNode(idx, iterIdx, cidx)">
                      <div class="child-node-icon">
                        <el-icon><Paperclip /></el-icon>
                      </div>
                      <div class="node-content">
                        <span class="node-title">{{ child.data.title }}</span>
                        <span class="node-type">{{ nodeTypeText(child) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </transition>
          </div>
        </div>
      </div>
      
      <!-- 右侧节点详情 -->
      <div class="flow-node-detail" v-if="currentNode">
        <div class="node-card">
          <div class="node-header">
            <span class="node-title">
              <el-icon v-if="currentNode.data.node_type=='start'"><HomeFilled /></el-icon>
              <el-icon v-else-if="currentNode.data.node_type=='end'"><Finished /></el-icon>
              <el-icon v-else><Document /></el-icon>
              {{ currentNode.data.title }}
            </span>
            <span class="node-status">{{ currentNode.data.elapsed_time }}s</span>
          </div>
          <div class="node-section" v-if="currentNode.data.inputs">
            <div class="section-header">
              输入
              <el-icon class="copy-btn" @click.stop="onCopy(currentNode.data.inputs)" style="margin-right:8px;"><DocumentCopy /></el-icon>
              <el-icon class="copy-btn" @click.stop="openDialog('输入', currentNode.data.inputs)"><Rank /></el-icon>
            </div>
            <div class="json-content">
              <vue-json-pretty :data="currentNode.data.inputs" :deep="3" />
            </div>
          </div>
          <div class="node-section" v-if="currentNode.data.process_data">
            <div class="section-header">
              数据处理
              <el-icon class="copy-btn" @click.stop="onCopy(currentNode.data.process_data)" style="margin-right:8px;"><DocumentCopy /></el-icon>
              <el-icon class="copy-btn" @click.stop="openDialog('数据处理', currentNode.data.process_data)"><Rank /></el-icon>
            </div>
            <div class="json-content">
              <vue-json-pretty :data="currentNode.data.process_data" :deep="3" />
            </div>
          </div>
          <div class="node-section" v-if="currentNode.data.outputs">
            <div class="section-header">
              输出
              <el-icon class="copy-btn" @click.stop="onCopy(currentNode.data.outputs)" style="margin-right:8px;"><DocumentCopy /></el-icon>
              <el-icon class="copy-btn" @click.stop="openDialog('输出', currentNode.data.outputs)"><Rank /></el-icon>
            </div>
            <div class="json-content">
              <vue-json-pretty :data="currentNode.data.outputs" :deep="3" />
            </div>
          </div>
        </div>
      </div>
      <!-- 内容弹窗放大 -->
      <el-dialog
        v-model="showDialog"
        :title="dialogTitle"
        width="60vw"
        top="5vh"
        :close-on-click-modal="true"
        :show-close="true"
        :draggable="false"
        class="custom-json-dialog"
      >
        <template #header>
          <div class="dialog-header">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </div>
        </template>
        <div class="dialog-json-content">
          <vue-json-pretty v-if="dialogData" :data="dialogData" />
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="onCopy(dialogData)" type="primary" :icon="DocumentCopy">复制</el-button>
            <el-button @click="showDialog = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { HomeFilled, Finished, Document, DocumentCopy, ArrowDown, ArrowRight, Rank, 
  CaretTop, CaretBottom, Connection, RefreshRight, Paperclip } from '@element-plus/icons-vue'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import copy from 'copy-text-to-clipboard'
import { getFlowDetail } from './api'
import DtNav from '@/components/DtNav.vue'

const route = useRoute()
const workflowRunId = route.params.workflowRunId

const mainNodes = ref<any[]>([])
const iterationStarts = ref<any[]>([])
const iterationExpanded = ref<{ [k: number]: boolean }>({})
const iterationDetailMap = ref<Record<string, any[]>>({}) // node_id -> [{childup, childlist:[]}, ...]

// 弹窗放大相关
const showDialog = ref(false)
const dialogTitle = ref('')
const dialogData = ref<any>(null)

// 当前选中节点类型与索引
const selected = ref<{ type: 'main' | 'iteration' | 'iterationChild', mainIdx?: number, iterIdx?: number, childIdx?: number }>({ type: 'main', mainIdx: 0 })

const currentNode = computed(() => {
  if (selected.value.type === 'main') {
    return mainNodes.value[selected.value.mainIdx!]
  } else if (selected.value.type === 'iterationChild') {
    const node = mainNodes.value[selected.value.mainIdx!]
    const group = iterationDetailMap.value[node.data.node_id]?.[selected.value.iterIdx!]
    return group?.childlist[selected.value.childIdx!] || null
  }
  return null
})

const isSelected = (type: string, mainIdx: number, iterIdx?: number, childIdx?: number) => {
  if (type === 'main') return selected.value.type === 'main' && selected.value.mainIdx === mainIdx
  if (type === 'iteration') return selected.value.type === 'iteration' && selected.value.mainIdx === mainIdx && selected.value.iterIdx === iterIdx
  if (type === 'iterationChild') return selected.value.type === 'iterationChild' && selected.value.mainIdx === mainIdx && selected.value.iterIdx === iterIdx && selected.value.childIdx === childIdx
  return false
}

const selectMainNode = (idx: number) => {
  selected.value = { type: 'main', mainIdx: idx }
}

const toggleIteration = async (mainIdx: number, node: any) => {
  iterationExpanded.value[mainIdx] = !iterationExpanded.value[mainIdx]
  // 首次展开时加载子迭代数据
  if (iterationExpanded.value[mainIdx] && !iterationDetailMap.value[node.data.node_id]) {
    const res = await getFlowDetail({ workflowRunId, nodeId: node.data.node_id })
    if (res.data && res.status === 0) {
      // 分组
      const groups: any[] = []
      const maxIndex = Math.max(...res.data.map(i => i.data.execution_metadata?.iteration_index ?? 0), 0)
      for (let i = 0; i <= maxIndex; i++) {
        groups.push({
          childup: false,
          childlist: res.data.filter(item => item.data.execution_metadata?.iteration_index === i)
        })
      }
      iterationDetailMap.value[node.data.node_id] = groups
    }
  }
}

const toggleIterationGroup = (nodeId: string, iterIdx: number) => {
  const group = iterationDetailMap.value[nodeId]?.[iterIdx]
  if (group) group.childup = !group.childup
}

const selectIterationChildNode = (mainIdx: number, iterIdx: number, childIdx: number) => {
  selected.value = { type: 'iterationChild', mainIdx, iterIdx, childIdx }
}

const nodeTypeText = (node: any) => {
  return ''
}

const onCopy = (data: any) => {
  let text = data
  if (typeof data === 'object') {
    text = JSON.stringify(data, null, 2)
  }
  copy(text)
  ElMessage.success('复制成功')
}

// 获取迭代次数
const getIterationLength = (node: any) => {
  const start = iterationStarts.value.find(i => i.data.node_id === node.data.node_id)
  return start?.data?.metadata?.iterator_length || 0
}

const openDialog = (title: string, data: any) => {
  dialogTitle.value = title
  dialogData.value = data
  showDialog.value = true
}

onMounted(async () => {
  const res = await getFlowDetail({ workflowRunId })
  if (res.data && res.data.length > 0 && res.status === 0) {
    mainNodes.value = res.data.filter(item => item.event === 'node_finished' || item.event === 'iteration_completed')
    iterationStarts.value = res.data.filter(item => item.event === 'iteration_started')
  } else {
    mainNodes.value = []
    iterationStarts.value = []
    ElMessage.warning('暂无数据')
  }
})
</script>

<style lang="less" scoped>
.flow-record-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f7f8fa;
}

.breadcrumb-nav-underline {
  background: #fff;
  border-bottom: 1.5px solid #ffe7ba;
  padding: 0 32px 0 32px;
}

.flow-main {
  display: flex;
  flex: 1;
  min-height: 0;
}

.flow-node-list {
  width: 320px;
  background: #fff;
  border-right: 1.5px solid #ffe7ba;
  padding: 24px 0;
  overflow-y: auto;
}

.flow-nodes-container {
  position: relative;
  padding: 0 16px;
}

.flow-node-wrapper {
  position: relative;
  margin-bottom: 8px;
}

.node-connector {
  position: absolute;
  top: -8px;
  left: 20px;
  width: 2px;
  height: 24px;
  background-color: #D7A256;
  z-index: 1;
}

.node-list-item {
  position: relative;
  padding: 12px 16px;
  cursor: pointer;
  border-left: 4px solid transparent;
  transition: background 0.2s, border-color 0.2s;
  display: flex;
  align-items: center;
  border-radius: 6px;
  
  &:hover, &.active {
    background: #FFF6E8;
    border-left: 4px solid #D7A256;
  }
  
  .node-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: rgba(215, 162, 86, 0.08);
    margin-right: 12px;
    border: 1px solid rgba(215, 162, 86, 0.2);
    color: #D7A256;
    flex-shrink: 0;
    position: relative;
    z-index: 2;
    
    .start-icon {
      color: #D7A256;
    }
    
    .end-icon {
      color: #b47a1a;
    }
    
    .process-icon {
      color: #D7A256;
    }
  }
  
  .node-content {
    flex: 1;
    overflow: hidden;
  }
  
  .node-title {
    font-size: 15px;
    color: #222;
    font-weight: 500;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .node-type {
    font-size: 12px;
    color: #D7A256;
    margin-left: 0;
    display: block;
  }
  
  .node-iteration {
    display: flex;
    align-items: center;
    color: #D7A256;
    font-size: 13px;
    margin-left: 8px;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(215,162,86,0.1);
    
    .arrow-icon {
      margin-left: 4px;
      color: #D7A256;
    }
    
    &:hover {
      background: rgba(215,162,86,0.2);
    }
  }
}

.iteration-container {
  margin-left: 20px;
  padding-left: 16px;
  border-left: 1px dashed #D7A256;
}

.iteration-group {
  margin-bottom: 4px;
}

.node-list-item.child {
  padding: 10px 12px;
  background: transparent;
  margin-top: 4px;
  
  .iteration-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(215, 162, 86, 0.08);
    margin-right: 10px;
    color: #D7A256;
    flex-shrink: 0;
  }
  
  .arrow-icon {
    margin-left: auto;
    color: #D7A256;
  }
}

.child-nodes {
  padding-left: 16px;
  margin-left: 12px;
  border-left: 1px dotted rgba(215, 162, 86, 0.4);
}

.node-list-item.child2 {
  padding: 8px 12px;
  background: transparent;
  margin-top: 4px;
  border-radius: 4px;
  
  .child-node-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: rgba(215, 162, 86, 0.1);
    margin-right: 8px;
    color: #D7A256;
    flex-shrink: 0;
  }
}

.flow-node-detail {
  flex: 1;
  padding: 32px 48px;
  overflow: visible;
}

.node-card {
  background: #fff;
  border: 1.5px solid #ffe7ba;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px 0 rgba(215,162,86,0.04);
  padding: 24px 32px 18px 32px;
  
  .node-header {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    
    .node-title {
      font-size: 16px;
      font-weight: bold;
      color: #D7A256;
      margin-right: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .node-status {
      font-size: 13px;
      color: #D7A256;
      margin-left: auto;
    }
  }
  
  .node-section {
    margin-bottom: 16px;
    
    .section-header {
      display: flex;
      align-items: center;
      font-weight: 600;
      color: #222;
      margin-bottom: 6px;
      
      .copy-btn {
        margin-left: 10px;
        color: #888;
        cursor: pointer;
        &:hover { color: #D7A256; }
      }
    }
  }
}

// JSON 内容样式（合并重复样式）
:deep(.json-content),
:deep(.dialog-json-content) {
      background: #f8fafc;
      border-radius: 12px;
      padding: 24px 20px 24px 20px;
      font-size: 15px;
      color: #606266;
      overflow: auto;
      border: 1.5px solid #f0e6d6;
      box-shadow: 0 2px 8px 0 rgba(215,162,86,0.06);
  
      // 滚动条美化
      scrollbar-width: thin;
      scrollbar-color: #b3c0d1 #f4f8fb;
  
      &::-webkit-scrollbar {
        width: 8px;
        background: #f4f8fb;
        border-radius: 4px;
      }
  
      &::-webkit-scrollbar-thumb {
        background: #b3c0d1;
        border-radius: 4px;
        transition: background 0.2s;
      }
  
      &::-webkit-scrollbar-thumb:hover {
        background: #D7A256;
      }
  
  .vjs-key { color: #a31515 !important; }
  .vjs-value, .vjs-value-string { color: #0451a5 !important; }
  
  // JSON 行悬停高亮效果
  .vjs-item {
    transition: background 0.2s ease;
    border-radius: 4px;
    padding: 2px 4px;
    margin: 1px 0;
    
    &:hover {
      background: #fff6e8; // 主题色的淡色
    }
  }
}

:deep(.json-content) {
  max-height: 260px;
}

:deep(.dialog-json-content) {
  max-height: 60vh;
  margin-bottom: 0;
}

// 弹窗样式
:deep(.custom-json-dialog) {
  border-radius: 18px !important;
  box-shadow: 0 8px 32px 0 rgba(215,162,86,0.18);
  background: #fff !important;
}

:deep(.custom-json-dialog .el-dialog__header) {
  font-size: 20px;
  font-weight: bold;
  color: #b47a1a;
  padding-bottom: 8px;
  border-bottom: 1.5px solid #ffe7ba;
  background: transparent;
  display: flex;
  align-items: center;
  min-height: 56px;
}

:deep(.custom-json-dialog .el-dialog__headerbtn) {
  top: 18px;
  right: 18px;
  color: #b47a1a;
  &:hover {
    color: #D7A256;
    background: none;
    border-radius: 0;
  }
}

:deep(.custom-json-dialog .el-dialog__body) {
  background: #fff;
  border-radius: 0 0 18px 18px;
  padding: 32px 40px 24px 40px;
  font-size: 15px;
}

:deep(.custom-json-dialog .el-dialog__footer) {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  background: transparent;
  padding: 18px 40px 18px 40px;
}

.dialog-header {
  display: flex;
  align-items: center;
  min-height: 40px;
}

.dialog-title {
  font-size: 20px;
  font-weight: bold;
  color: #b47a1a;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  background: transparent;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 