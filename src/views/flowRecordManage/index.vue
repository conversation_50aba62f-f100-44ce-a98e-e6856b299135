<template>
  <!-- 标题模块移到筛选组件上方 -->
  <TableToolTemp toolTitle="执行记录列表" :tool-list="toolConfig" />
  <!-- 主表格组件，包含搜索表单和数据表格 -->
  <PageTable
    :search-form-temp="temp"
    :hiddenState="true"
    @spread="spread"
    @pack="pack"
    api-url="/web/databoard/pageFlowRecord"
    ref="flowRef"
  >
    <!-- 表格插槽 -->
    <template #default="{ tableData }">
      <!-- el-table 显示工作流数据 -->
      <el-table :data="tableData" class="dt-table" stripe>
        <!-- 新增会话ID字段，放在渠道编码前面 -->
        <el-table-column prop="sessionId" label="会话ID" align="center" min-width="200px" />
        <!-- 各种字段列 -->
        <el-table-column prop="channelCode" label="渠道编码" align="center" width="190px" />
        <el-table-column prop="channelName" label="渠道名称" align="center" min-width="180px" />
        <el-table-column prop="createTime" label="开始时间" align="center" min-width="210px" />
        <el-table-column prop="username" label="用户" align="center" min-width="180px"/>
        <!-- 输入内容，使用tooltip组件支持markdown格式 -->
        <el-table-column prop="question" label="输入内容" align="center" min-width="180px">
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.question" />
          </template>
        </el-table-column>
        <!-- 输出内容，使用tooltip组件支持markdown格式 -->
        <el-table-column prop="answer" label="输出内容" align="center" min-width="180px">
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.answer" />
          </template>
        </el-table-column>
        <el-table-column prop="cost" label="输出时间" align="center" min-width="180px" />
        <!-- 类型字段，字典转换显示 -->
        <el-table-column prop="flowType" label="类型" align="center" width="120">
          <template #default="scope">
            <!-- 0、未发布 1、已发布 2、发布中 3、编辑中 9、已下线 -->
            {{ getDicItemName(scope.row.flowType, 'chatbot.workflow.type') }}
          </template>
        </el-table-column>
        <!-- 是否复制 -->
        <el-table-column prop="isCopy" label="复制" align="center" min-width="100px" >
          <template #default="scope">
            <span v-if="scope.row.isCopy == '0'">否</span>
            <span v-if="scope.row.isCopy == '1'">是</span>
          </template>
        </el-table-column>
        <!-- 用户反馈 -->
        <el-table-column prop="voteResult" label="用户反馈" align="center" width="110px">
          <template #default="scope">
            {{ getCustomDicitemName(scope.row.voteResult, 'voteResult') }}
          </template>
        </el-table-column>
        <el-table-column prop="voteRemark" label="反馈内容" align="center" min-width="190px" />
        <el-table-column prop="userId" label="用户ID" align="center" min-width="200px" />
        <!-- 操作列，点击可查看详情 -->
        <el-table-column label="操作" align="center" fixed="right" width="180px">
          <template #default="scope">
            <el-button type="text" @click="goToFlowRecord(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>
</template>

<script setup lang="ts">
import { HomeFilled, ArrowDown, ArrowUp, ZoomIn,CircleCheckFilled,ArrowRight,Document,Finished,Check,DocumentCopy,Rank,Refresh} from '@element-plus/icons-vue'
import type { SearchFormTemp, ToolListProps, DoubleDate } from '@/components/types'
import PageTable from '@/components/PageTable.vue'
import { ref, reactive } from 'vue'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import useChannel from '@/stores/channel'
import { getFlowDetail } from './api'
import { formatTimestamp } from '@/utils'
import tooltipWithFn from '@/views/dashboardManage/components/tooltipWithFn.vue'
import { micromark } from 'micromark'
import { gfmTable, gfmTableHtml } from 'micromark-extension-gfm-table'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import copy from 'copy-text-to-clipboard'
import { useRouter } from 'vue-router'

// 控制执行记录抽屉显示
const showEdit = ref(false)
// 控制迭代记录抽屉显示
const showChild = ref(false)
// 弹窗标题
const title = ref(null)
// 控制"全部输出"弹窗显示
const showEdit1 = ref(false)
const showChild1= ref(false)
// 获取字典方法
const { getDic } = useUserStore()
// 获取字典项名称方法
const { getDicItemName } = useFilters()
// 渠道列表
const { channels } = useChannel()
// 表格ref
const flowRef = ref()
// 工作流主流程数据
const flwoData = ref<Record<string, any>[]>([])
// 详情数据
const detailData = ref<Record<string, any>[]>([])
// 迭代子节点数据
const childData= ref<Record<string, any>[]>([])
// 迭代开始节点数据
const iterationStart  = ref<Record<string, any>[]>([])
// 弹窗json数据
const jsonData = ref(null)
// 迭代次数
const reLength= ref(0)
// 迭代节点展开数据
const reLengthData= ref<Record<string, any>[]>([])
// 当前节点id
const nodeId = ref(null)

const router = useRouter()
const goToFlowRecord = (row: any) => {
  router.push({ name: 'FlowRecordDetail', params: { workflowRunId: row.workflowRunId } })
}

// 获取搜索参数
function getParams() {
  if (!flowRef.value) return {}
  const p = flowRef.value.getSearchQuery()
  return p
}

// 工具栏配置（可扩展）
const toolConfig: ToolListProps[] = [
]

// 字典数据
const dics = {
  userVoteResult: [
    { dicItemName: '全部', dicItemCode: '' },
    { dicItemName: '点踩', dicItemCode: '0' },
    { dicItemName: '点赞', dicItemCode: '1' },
  ],
  answerType: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.answerType')],
  answerSource: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.answerSource')],
  intention: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.intentionType')],
}
defineOptions({ name: 'flowRecordManage' })
// 搜索表单配置
const temp: SearchFormTemp[] = reactive([
  {
    label: '来源渠道',
    name: 'channelCode',
    type: 'select',
    placeholder: '请选择',
    list: [{ dicItemCode: '', dicItemName: '全部' }, ...channels],
    searchState: true,
  },
  {
    label: '用户姓名',
    name: 'username',
    type: 'input',
    placeholder: '请输入内容',
    searchState: true,
  },
  {
    label: '会话ID',
    name: 'sessionId',
    type: 'input',
    placeholder: '请输入会话ID',
    searchState: true,
  },
  {
    label: '时间',
    type: 'doubleDate',
    searchState: true,
    doubleDate: {
      elType: 'DateTimePicker',
      startTime: { name: 'startDate', value: formatTimestamp('YYYY-MM-DD HH:mm:ss', getFirstDayOfMonth()) } as { name: string; value?: string },
      endTime: { name: 'endDate', value: formatTimestamp('YYYY-MM-DD HH:mm:ss', getEnd()) } as { name: string; value?: string },
    } as DoubleDate,
  },
])

// 获取本月第一天时间戳
function getFirstDayOfMonth() {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth();
  const firstDayOfMonth = new Date(year, month, 1).getTime();
  return firstDayOfMonth;
}

// 获取当天结束时间
function getEnd() {
  const now = new Date();
  const endOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59).getTime();
  return endOfToday;
}

// 获取自定义字典项名称
const getCustomDicitemName = (value: any, key: string) => {
  return dics[key]?.find((item: any) => item.dicItemCode === value)?.dicItemName
}

// 展开/收起主流程节点
const flwoDataDown = (index: any) => {
  reLength.value=0
  // 判断是否为迭代节点
  if(flwoData.value[index].event=='iteration_completed'){
    for(let i=0;i<iterationStart.value.length;i++){
      if(flwoData.value[index].data.node_id=iterationStart.value[i].data.node_id){
        reLength.value=iterationStart.value[i].data.metadata.iterator_length
        nodeId.value=iterationStart.value[i].data.node_id
      }
    }
  }
  let data=[]
  // 生成迭代节点展开数据
  if(reLength.value>0){
     for(let k=0;k<reLength.value;k++){
        data.push({
          childup:false,
          childlist:[],
          index:k
        })
      }
  }
  reLengthData.value= data
  flwoData.value[index].isUp=!flwoData.value[index].isUp
}

// 展开/收起某个迭代节点
const rightReshDown = (index: any) => {
  reLengthData.value[index].childup=!reLengthData.value[index].childup
  let data=[]
  // 筛选出属于该迭代的子节点
  detailData.value.forEach(item => {
    if(item.data.execution_metadata.iteration_index==index){
          data.push({
              ...item
            })
        }
    })
  childData.value=data
}

// 展开/收起某个迭代子节点
const rightChildDown = (index1: any,index:any) => {
  reLengthData.value[index1].childlist[index].isUp=!reLengthData.value[index1].childlist[index].isUp
}

// 关闭迭代抽屉
const handleClose = () => {
   showChild.value=false
}

// 加载迭代节点详情
const rightChild = async (itemData:any) => {
  let data= []
  // 获取迭代详情
  const res = await getFlowDetail({ workflowRunId: itemData.workflow_run_id,nodeId:itemData.data.node_id});
  if(res.data&&res.data.length>0 &&res.status==0){
    // 先清空所有childlist
    reLengthData.value.forEach(item => item.childlist = []);
    res.data.forEach(item => {
      // 找到对应的reLengthData
      const idx = item.data.execution_metadata?.iteration_index;
      if (typeof idx === 'number') {
        const target = reLengthData.value.find(d => d.index === idx);
        if (target) {
          target.childlist.push({
            ...item,
            isUp: false,
          });
        }
      }
    });
  }
  showChild.value=true
}

const outputAll=(content:any,num:number)=> {
  jsonData.value=null
  if(!content) return
  title.value = num === -1 ? "数据处理" : (num === 0 ? "输入" : "输出")
  let data = content
  if (typeof content === 'string') {
    try {
      data = JSON.parse(content)
    } catch (e) {
      // 不是合法 JSON 字符串，直接显示原内容
      data = content
    }
  }
  jsonData.value = data
  showEdit1.value = true
}

// 复制内容到剪贴板
const onCopy = (str) => {
  let text = str
  if (typeof str === 'object') {
    text = JSON.stringify(str, null, 2)
  }
  copy(text)
  ElMessage.success('复制成功')
}

// 格式化输出内容，关键value高亮
const outPutData=(content:any)=> {
  if(!content) return
  // 将对象转换为格式化的JSON字符串
  const jsonString = JSON.stringify(content, null, 2)
  // 用正则给 key 加红色，value 加蓝色
  const prettyJson = jsonString.replace(
    /"([^"]+)"\s*:\s*("[^"]*"|\d+|true|false|null|\[[\s\S]*?\])/g,
    (match, key, value) => `<span style="color: #a31515">"${key}"</span>: <span style="color: #0451a5">${value}</span>`
  ).replace(/\n/g, '<br>');
  const prettyJson1 = prettyJson.replace(/{|}|,|$|$/g, match => match + '<br>');
  return prettyJson1
}

// 展开搜索表单
const spread = (value: any) => {
  for (let i = 0; i < temp.length; i++) {
    if (i > 3) {
      temp[i].searchState = value
    }
  }
}

// 收起搜索表单
const pack = (value: any) => {
  for (let i = 0; i < temp.length; i++) {
    if (i > 3) {
      temp[i].searchState = value
    }
  }
  flowRef.value
}

// 轮询获取工作流执行详情
const  checkWorkflowStatus=async(workflowRunId:any)=> {
  let data=[]
  let iterationStartData=[]
    try {
      const res = await getFlowDetail({ workflowRunId: workflowRunId});
       if(res.data&&res.data.length>0 &&res.status==0){
        res.data.forEach(item => {
          if(item.event === 'iteration_started'){//迭代节点
            iterationStartData.push({
              ...item
            });
          }
          if (item.event === 'node_finished' || item.event === 'iteration_completed') {//节点完成
            data.push({
              ...item,
              isUp:false,
            });
          } 
        });
        // 主流程数据
        flwoData.value= data
        // 迭代节点数据
        iterationStart.value = iterationStartData
        // 判断是否已完成
        const isCompleted = data.some(item => item.event === 'workflow_completed');
        if (isCompleted) {
          // 工作流已完成，停止轮询
          return true;
        }
        return false;
      }
    } catch (error) {
      console.error('获取工作流状态失败:', error);
      return true; // 出错时停止轮询
    }
}
</script>

<style lang="less" scoped>
.flow-list-container {
  padding: 20px;
}

.dt-table {
  margin-top: 20px;
}

// 抽屉样式
.flow-drawer {
  .drawer-header {
    padding: 20px;
    border-bottom: 1px solid #e4e7ed;
    
    .drawer-title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .drawer-content {
    padding: 20px;
    
    .node-item {
      margin-bottom: 15px;
      padding: 15px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      background: #fafafa;
      
      .node-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .node-title {
          font-weight: bold;
          color: #303133;
        }
        
        .node-status {
          color: #409eff;
          font-size: 14px;
        }
      }
      
      .node-content {
        .content-section {
          margin-bottom: 15px;
          
          .section-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #606266;
          }
          
          .section-content {
            background: #fff;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            max-height: 200px;
            overflow-y: auto;
          }
        }
      }
    }
  }
}

// 迭代节点样式
.iteration-node {
  margin-left: 20px;
  border-left: 2px solid #409eff;
  padding-left: 15px;
  
  .iteration-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f0f9ff;
    border-radius: 4px;
    margin-bottom: 10px;
    
    .iteration-title {
      font-weight: bold;
      color: #409eff;
    }
    
    .iteration-count {
      color: #909399;
      font-size: 14px;
    }
  }
}

// 子节点样式
.child-node {
  margin-left: 20px;
  border-left: 2px solid #67c23a;
  padding-left: 15px;
  margin-bottom: 10px;
  
  .child-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: #f0f9ff;
    border-radius: 4px;
    
    .child-title {
      font-weight: bold;
      color: #67c23a;
    }
    
    .child-status {
      color: #909399;
      font-size: 12px;
    }
  }
}

// 弹窗样式
.json-dialog {
  .dialog-content {
    max-height: 60vh;
    overflow-y: auto;
    
    .json-display {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}
</style> 