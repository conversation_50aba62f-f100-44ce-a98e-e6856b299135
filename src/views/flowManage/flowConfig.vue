<template>
  <DtNav from="智能体管理" :fromPath="'/botManage'" name="智能体配置"></DtNav>

  <!-- <el-tabs v-model="versionIndex" type="border-card" class="tabs" @tab-change="versionSelect">
    <el-tab-pane v-for="item, index in versionList" :name="item.id" :label="item.title" :key="item.id">
    </el-tab-pane>
  </el-tabs> -->

  <div class="temp-content">
    <!-- <el-radio-group v-model="versionIndex" size="large" class="radio-btn">
      <el-radio-button v-for="item, index in versionList" :label="item.id" :key="item.id">
        {{ item.title }}
      </el-radio-button>
    </el-radio-group> -->
    <el-tabs v-model="configIndex" class="tabs bottom-tabs" @tab-change="configSelect">
      <el-tab-pane :key="0" :name="0" label="基础配置">
        <baseConfig ref="baseRef" :versionIndex="versionIndex" v-if="configIndex === 0" @updateStatus="updateStatus"> </baseConfig>
      </el-tab-pane>

      <el-tab-pane :key="1" :name="1" label="知识配置" v-if="showKnowledge">
        <knowledgeConfig ref="knowledgeRef" :versionIndex="versionIndex" v-if="configIndex === 1" @updateStatus="updateStatus"></knowledgeConfig>
      </el-tab-pane>

      <el-tab-pane :key="2" :name="2" label="切片管理"  v-if="showKnowledge" >
        <sliceManagement v-if="configIndex === 2" />
      </el-tab-pane>
      <el-tab-pane :key="3" :name="3" label="模型配置"  >
        <modelConfig v-if="configIndex === 3" ></modelConfig>
      </el-tab-pane>
      <!--
      <el-tab-pane :key="2" :name="2" label="转人工配置">
        <manualConfig ref="manualRef" :versionIndex="versionIndex" v-if="configIndex === 2" @updateStatus="updateStatus">
        </manualConfig>
      </el-tab-pane>
      <el-tab-pane :key="3" :name="3" label="敏感词配置">
        <sensitiveWord ref="sensitiveRef" :versionIndex="versionIndex" v-if="configIndex === 3"
          @updateStatus="updateStatus">
        </sensitiveWord>
      </el-tab-pane>
-->
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import baseConfig from './components/baseConfig.vue'
import knowledgeConfig from './components/knowledgeConfig.vue'
import modelConfig from './components/modelConfig.vue'
//import sensitiveWord from './components/sensitiveWord.vue'
import sliceManagement from '@/views/section/sliceManagement.vue'

const route = useRoute()
const robotId = ref<number>(null)
const knowledgeRef = ref(null)
const baseRef = ref()

const showKnowledge = route.query.robotType == "2"

/**
 * 更新状态
 */
const updateStatus = () => {
  //if (route.query.status !== '0') {
  //  route.query.status = '3'
  //}

  knowledgeRef.value && knowledgeRef.value.init()
}

/**
 * 当前选中的版本
 * @description 0: 测试版 1: 正式版
 */
const versionIndex = ref<number>(1)

const configIndex = ref<number>(0)
const configSelect = (index: number) => {
  configIndex.value = index
}

onMounted(() => {
  robotId.value = Number(route.query.id)
})
</script>

<style scoped>
:root {
  --theme-color: #D7A256;
  --theme-color-hover: #D7A256;
  --form-radius: 12px;
  --input-radius: 6px;
  --btn-radius: 6px;
  --page-bg: linear-gradient(135deg, #f9f6f1 0%, #f3e7d9 100%);
}

/* 页面外层背景色 */
body, #app {
  min-height: 100vh;
  background: var(--page-bg);
  /* 如果你有外层div，可以加上对应class选择器 */
}

/* 或者如果有外层div包裹，可以这样写 */
.page-bg {
  min-height: 100vh;
  background: var(--page-bg);
  width: 100vw;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 0;
}

/* 保证form-card有足够的z-index */
.form-card {
  position: relative;
  z-index: 1;
  background: #fff;
  max-width: 100vw;
  width: 100vw;
  padding: 0;
  margin: 0;
  border-radius: 0;
  box-sizing: border-box;
  /* padding-right:20px; */
  /* padding: 20px; */
  /* left: -20px; */
  paddgin-right:0px;
  /* box-shadow: 0 12px 32px rgba(215,162,86,0.13); */
  /* transition: box-shadow 0.3s, border-color 0.3s, transform 0.25s; */
  /* box-shadow: 0 12px 32px rgba(215,162,86,0.22); */
}


.el-form-item {
  margin-bottom: 28px;
}

.el-form-item__label {
  font-weight: bold;
  color: var(--theme-color);
  font-size: 14px;
  letter-spacing: 1px;
}

.el-form-item__content {
  min-width: 220px;
}

.form-card .el-form-item:last-child {
  margin-bottom: 0;
}


.form-card .el-form-item__content {
  display: flex;
  justify-content: flex-end;
  max-width: 640px; /* 让按钮区域不会太靠右 */
  margin: 0 auto;
}

/* 输入框美化 */
.el-input__inner,
.el-textarea__inner {
  border-radius: var(--input-radius, 8px);
  padding: 10px 14px;
  border: 1.5px solid #dcdfe6;
  transition: border-color 0.2s, box-shadow 0.2s, background 0.2s;
  font-size: 16px;
  background: #fafdff;
  box-shadow: 0 1px 2px rgba(215,162,86,0.03) inset;
}
.el-input__inner:focus,
.el-textarea__inner:focus {
  border-color: var(--theme-color, #D7A256);
  box-shadow: 0 0 8px var(--theme-color-hover, #D7A256);
  background: #fff;
}

/* 响应式适配 */
@media (max-width: 600px) {
  .form-card {
    padding: 20px 8px 16px 8px;
    max-width: 98vw;
  }
  .el-form-item__label {
    font-size: 14px;
  }
  .form-card .el-form-item__content .el-button,
  .form-card .el-form-item__content .el-button + .el-button {
    padding: 8px 12px;
    font-size: 14px;
  }
}
/* .custom-form-item {
  
  margin-bottom:20px;
}  */
.custom-form-item,
.custom-form-item1 {
  min-height: 64px; /* 你可以根据实际需求调整数值 */
  /* 如果想绝对一致，也可以用 height: 64px; */
  display: flex;
  align-items: center;
} 
.custom-form-item1 {
  /* background: rgba(255,255,255,0.85); */
  border-radius: var(--input-radius, 8px);
  /* padding: 18px 30px 18px 30px;  原来左右30px */
  padding: 18px 20px 18px 0px; /* 左右加大一点，整体更居中 */
  margin-bottom: 12px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(215,162,86,0.04);
  /* border: 1px solid #f0f6ff; */
  transition: box-shadow 0.2s, background 0.2s, border-color 0.2s;
  position: relative;
  margin-top:20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.custom-form-item .el-form-item__label {
  font-weight: 600;
  color: var(--theme-color, #D7A256);
  font-size: 14px;
  letter-spacing: 1px;
  margin-bottom: 6px;
  text-shadow: 0 1px 0 #fff;
  margin-left: -20px;
}
.custom-form-item .el-form-item__content {
  min-width: 180px;
  align-items: center;
  display: flex;
  min-height: 40px; /* 你可以根据实际情况调整 */
}
.custom-form-item .el-input__inner,
.custom-form-item .el-textarea__inner {
  border-radius: var(--input-radius, 8px);
  padding: 12px;
  border: 1.5px solid #dcdfe6;
  transition: border-color 0.2s, box-shadow 0.2s, background 0.2s;
  font-size: 16px;
  background: #fafdff;
  box-shadow: 0 1px 2px rgba(215,162,86,0.03) inset;
}
.custom-form-item .el-input__inner:focus,
.custom-form-item .el-textarea__inner:focus {
  border-color: var(--theme-color, #D7A256);
  box-shadow: 0 0 8px var(--theme-color-hover, #D7A256);
  background: #fff;
}
.custom-form-item .el-switch.is-checked .el-switch__core {
  border-color: var(--theme-color, #D7A256) !important;
  background-color: var(--theme-color, #D7A256) !important;
  box-shadow: 0 0 4px var(--theme-color-hover, #D7A256);
}


.form-section-card {
  width: 100%;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
  border:none;
  /* background: #fffdfa; */
}
.card-header {
  font-size: 14px;
  font-weight: bold;
  color: var(--theme-color, #D7A256);
  letter-spacing: 1px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f7ecdd;
  background: transparent;
  margin-bottom: 12px;
}
.card-header1 {
  font-size: 14px;
  font-weight: bold;
  color: var(--theme-color, #D7A256);
  letter-spacing: 1px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f7ecdd;
  background: transparent;
  margin-bottom: 12px;
  /* margin-top: 20px; */
}
.card-header2{
  font-size: 14px;
  font-weight: bold;
  color: var(--theme-color, #D7A256);
  letter-spacing: 1px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f7ecdd;
  background: transparent;
  margin-bottom: 12px;
  /* margin-top: 10px; */
}

.big-func-card {
  margin-bottom: 24px;
  border-radius: 16px;
  /* 渐变背景 */
  /* 更柔和的阴影 */
  box-shadow: 0 6px 24px rgba(215,162,86,0.13), 0 1.5px 8px rgba(215,162,86,0.08);
  /* 增加边框 */
  border: 1.5px solid #f7ecdd;
  /* 内边距让内容更舒展 */
  padding: 18px 28px 18px 28px;
  /* 过渡动画让交互更丝滑 */
  transition: box-shadow 0.3s, border-color 0.3s, background 0.3s;
  /* 鼠标悬浮时高亮 */
}
.big-func-card:hover {
  box-shadow: 0 12px 32px rgba(215,162,86,0.22), 0 2px 12px rgba(215,162,86,0.12);
  border-color: #d7a256;
  /* background: #f7ecdd; */
  /* 可根据需要调整高亮色 */
}

.form-section-card:first-child {
  /* border-left: none; */
}
.form-section-card:last-child {
  /* border-right: none; */
}

.el-col {
  overflow: visible;
  position: relative;
}
.el-col:last-child {
  padding-right: 0;
}

.row-padding-x {
  padding-left: 5x;
  padding-right: 5px;
}


.move-left {
  margin-left: -12px; /* 负值向左移动，数值可调整 */
}

/* 统一表单项宽度和对齐 */
:deep(.el-form-item) {
  width: 100%;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}
:deep(.el-form-item__label) {
  /* width:0px !important;
  min-width: 0px; */
  text-align: left !important; /* 强制右对齐 */
  padding-right: 12px;
  font-size: 14px;
}
/* .custom-form-item1 .el-form-item__label {
  font-weight: 600;
  color: var(--theme-color, #D7A256);
  font-size: 17px;
  letter-spacing: 1px;
  margin-bottom: 6px;
  text-shadow: 0 1px 0 #fff;
  padding-left: 30px;
} */
:deep(.el-form-item__content) {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}
:deep(.el-input), :deep(.el-select), :deep(.el-textarea) {
  width: 100% !important;
  min-width: 180px;
}
:deep(.el-switch) {
  margin-left: 0;
}

/* 保证 input 和 select 宽度一致 */
:deep(.el-input), :deep(.el-select) {
  width: 100%;
}
.form-card, .form-section-card, .el-row, .el-col, .custom-form-item {
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}
@media (max-width: 900px) {
  .el-col {
    min-width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }
}
.section-divider {
  border-bottom: 1.5px solid #f7ecdd;
  margin: 24px 0 0 0;
  width: 100%;
}
</style>

<style lang="less">
.submit-btn {
  padding-left: 50px;
  padding-top: 30px;
}
</style>
