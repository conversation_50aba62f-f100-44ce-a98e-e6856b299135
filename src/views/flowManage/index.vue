<template>
  <div>
    <TableToolTemp toolTitle="流程管理" :toolList="toolList" />
    <SearchForm :searchFormTemp="searchFormTemp" @handleSearch="handleSearch" @handleResetQuery="resetSearch">
    </SearchForm>
    <el-table :data="tableData" class="dt-table" stripe>
      <el-table-column prop="flowId" label="流程ID" align="center"   width="300" />
      <el-table-column prop="flowName" label="流程名称" align="center"  />
      <el-table-column prop="flowType" label="类型" align="center" width="100">
        <template #default="scope">
          <!-- 0、未发布 1、已发布 2、发布中 3、编辑中 9、已下线 -->
          {{ getDicItemName(scope.row.flowType, 'chatbot.workflow.type') }}
        </template>
      </el-table-column>
      <el-table-column prop="apiKey" label="API_KEY" align="center"   />
      <el-table-column prop="createTime" label="创建时间" align="center" />
       <el-table-column prop="updateTime" label="更新时间" align="center" />
      <el-table-column prop="updateBy" label="更新人" align="center" width="120" >
        <template #default="scope">
          {{ getNickName(scope.row.updateBy) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center"  width="200" >
        <template #default="scope">
          <!-- <el-button type="primary" text size="small" @click="handleConfig(scope.row)">
            新增
          </el-button> -->
          <el-button type="primary" text size="small" @click="editName(scope.row)">
            编辑
          </el-button>
          <el-button type="primary" text size="small"
            @click="publishBot(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination ref="paginationRef" @change="onPageData" :total="total" />
  </div>

  <!-- 新增智能体弹框 -->
  <DtPopup :show="addShow" @close="closeAdd" @close-by-icon="closeAdd" :title="addRobotTitle" :footer="false"  :center="true">
    <el-form ref="ruleFormRef" :rules="addRules" :model="addRuleForm" label-position="left" label-width="110px"  class="form-card">
      <el-form-item label="流程名称" prop="flowName"  placeholder="请输入">
        <el-input maxlength="10" v-model="addRuleForm.flowName"></el-input>
      </el-form-item>
      <el-form-item label="流程类型" prop="flowType"  placeholder="请输入">
        <el-select v-model="addRuleForm.flowType" clearable>
              <el-option v-for="(el, idx) in getDic('chatbot.workflow.type')" :key="idx" :label="el.dicItemName"
                :value="el.dicItemCode" />
         </el-select>
       </el-form-item>
      <el-form-item label="apiKey" prop="apiKey"  placeholder="请输入">
        <el-input  v-model="addRuleForm.apiKey"></el-input>
      </el-form-item>
      <div class="formBtn">
        <el-button class="dt-btn" @click="closeAdd">取消</el-button>
        <el-button class="dt-btn" type="primary" @click="saveRobot(ruleFormRef)">保存</el-button>
      </div>
    </el-form>
  </DtPopup>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import type { ToolListProps, PageData, PageTableSearch } from '@/components/types'
import type { SearchFormTemp } from '@/components/types'
import { useRouter } from 'vue-router'
import { onActivated } from 'vue'
import { FormInstance } from 'element-plus'
import { useUserStore } from '@/stores/user'
import useFilters from '@/hooks/useFilters'
import {
  getFlowPage,
  addFlow,
  delFlow,
  uodateFlow,
} from './api'
defineOptions({ name: 'botManage' })
const { getNickName, getDicItemName } = useFilters()
const { getDic } = useUserStore()
console.log(getDic('chatbot.workflow.type'))
const router = useRouter()
const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: '',
    action: async () => {
      addRobotTitle.value = '新增智能体'
      addShow.value = true
    }
  }
]
const searchFormTemp: SearchFormTemp[] = [
  {
label: '流程名称',
name: 'flowName',
type: 'input',
placeholder: '请输入流程名称',
searchState: true
}
]
const total = ref<number>(0)
const pageParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    flowName: '',
  }
})
const tableData = ref<Record<string, any>[]>([])
const addShow = ref<boolean>(false)
const addRuleForm = ref<Record<string, any>>({ flowName: '', flowType:'', apiKey:''})
const addRules = {
  flowName: [{ required: true, message: '请输入流程', trigger: 'blur' }]
}
const ruleFormRef = ref<FormInstance>()
const addRobotTitle = ref<string>('新增智能体')
const publishShow = ref<boolean>(false)
const publishTableData = ref<Record<string, any>[]>([])
const publishTotal = ref<number>(0)
const publishParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    robotId: ''
  }
})


const closeAdd = () => {
  addShow.value = false
  addRuleForm.value = {  flowName: '', flowType:'', apiKey:'' }
}

const publishBot = async (row: any) => {
  ElMessageBox.confirm(
    `确定要删除吗？`,
    '请确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const res = await delFlow({ id: row.id })
      if (res.status === 0) {
        initTableData()
      }
    })
}

const editName = (row: any) => {
  addRobotTitle.value = '编辑流程'
  addRuleForm.value = { flowName: row.flowName, id: row.id, flowType: row.flowType, apiKey: row.apiKey }
  addShow.value = true
}

const handleSearch = (data: any) => {
  pageParam.value.param = data
  initTableData()
}

const resetSearch = (data: any) => {
  pageParam.value.param = data
  initTableData()
}

const onPageData = (data: PageData) => {
  pageParam.value.pageNum = data.pageNum
  pageParam.value.pageSize = data.pageSize
  initTableData()
}

const handleConfig = (data: Record<string, any>) => {
  router.push({
    path: '/botConfig',
    query: {
      id: data.id,
      name: data.name,
      status: data.status,
      version: data.version,
      createTime: data.createTime,
      preStatus: data.preStatus
    }
  })
}

const saveRobot = (formEl: FormInstance | undefined) => {
  formEl.validate(async (valid: boolean) => {
    if (valid) {
      let res: any
      if (addRobotTitle.value == '编辑流程') {
        res = await uodateFlow(addRuleForm.value)
      } else {
        res = await addFlow(addRuleForm.value)
      }
      if (res.status === 0) {
        closeAdd()
        initTableData()
      }
    }
  })
}
const initTableData = async () => {
  const res = await getFlowPage(pageParam.value)
  if (res.status === 0) {
    tableData.value = res.data.list
    total.value = res.data.total
  }
}

onActivated(async () => {
  initTableData()
})
</script>
<style lang="less">

</style>