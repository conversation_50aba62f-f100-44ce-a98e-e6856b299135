import { useAxios } from '@/hooks/http'

/**
 * @description 流程列表
 */
export const getFlowPage = async (data: any) => await useAxios('/web/flow/page', data)

/**
 * @description 添加流程
 */
export const addFlow = async (data: any) => await useAxios('/web/flow/save', data)

/**
 * @description 更新流程
 */
export const uodateFlow = async (data: any) => await useAxios('/web/flow/update', data)

/**
 * @description 删除流程
 */
export const delFlow= async (data: any) => await useAxios('/web/flow/delete', data)

