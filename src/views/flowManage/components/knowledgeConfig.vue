<template>
  <TableToolTemp tool-title="绑定知识目录">
    <template #default>
      <div class="menu-content">
        <el-tree
          ref="treeRef"
          class="menu-tree"
          :data="treeData"
          :props="defaultProps"
          show-checkbox
          :default-expanded-keys="expanded"
          @node-expand="expandedChange"
          @node-collapse="expandedClose"
          node-key="directId"
          @check="checkChange"
        ></el-tree>
        <div class="menu-table">
          <TableToolTemp tool-title="已绑定知识目录" :tool-list="toolList"></TableToolTemp>
          <div class="search-box">
            <div class="search-item">
              <div class="search-label">一级知识目录名称：</div>
              <el-select
                v-model="pageParam.param.firstDirectId"
                filterable
                clearable
                remote
                reserve-keyword
                placeholder="知识目录名称"
                @change="selectFirstDirectName"
              >
                <el-option v-for="item in firstDirectNameList" :key="item.directoryId" :label="item.directoryName" :value="item.directoryId" />
              </el-select>
            </div>
            <div class="search-item">
              <div class="search-label">二级知识目录名称：</div>
              <el-select v-model="pageParam.param.directoryId" filterable clearable remote reserve-keyword placeholder="知识目录名称">
                <el-option v-for="item in directNameList" :key="item.directoryId" :label="item.directoryName" :value="item.directoryId" />
              </el-select>
            </div>
            <div class="search-item">
              <div class="search-label">操作人：</div>
              <el-select v-model="pageParam.param.updateId" filterable clearable remote reserve-keyword placeholder="请输入操作人">
                <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </div>
            <el-button type="primary" @click="initBindKnowLedge">搜索</el-button>
          </div>
          <el-table :data="tableData" class="dt-table" stripe>
            <el-table-column prop="directoryName" label="知识目录名称" align="center" min-width="120">
              <template #default="scope">
                <span>{{ `${scope.row.firstDirectName}-${scope.row.directoryName}` }}</span>
                <span style="color: red" v-if="scope.row.hasRemoved">[已删除]</span>
              </template>
            </el-table-column>
            <el-table-column prop="projectName" label="项目" align="center" />
            <el-table-column prop="folderName" label="知识库" align="center" />
            <el-table-column prop="updateTime" label="操作时间" width="170" align="center" />
            <el-table-column prop="bindId" label="操作人" align="center">
              <template #default="scope">
                {{ getNickName(scope.row.updateId) }}
              </template>
            </el-table-column>
            <el-table-column prop="syncStatus" label="同步状态" align="center">
              <!-- 字典 -->
              <template #default="scope">
                <span>{{ getDicItemName(scope.row.syncStatus, 'chatbot.syncStatus') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="failMsg" label="失败原因" align="center" />
            <el-table-column prop="refreshTime" label="刷新时间" width="170" align="center" />
            <el-table-column label="操作" align="center" width="160" fixed="right">
              <template #default="scope">
                <el-button link type="primary" @click="unbind(scope.row)">解绑</el-button>
                <el-button link type="primary" @click="refresh(scope.row)">同步</el-button>
                <el-button link type="primary" @click="detail(scope.row)">明细</el-button>
              </template>
            </el-table-column>
          </el-table>
          <Pagination ref="paginationRef" @change="onPageData" :total="total" />
        </div>
      </div>
    </template>
  </TableToolTemp>

  <!-- 历史操作记录弹框 -->
  <DtPopup :show="showOldRecord" @close="closeDialog" @close-by-icon="closeDialog" title="历史操作记录" :footer="false" :isFullscreen="true">
    <el-table :data="oldRecordTableData" class="dt-table" stripe>
      <el-table-column prop="directoryName" label="知识目录名称" align="center">
        <template #default="scope">
          <span>{{ `${scope.row.firstDirectName}-${scope.row.directoryName}` }}</span>
          <span style="color: red" v-if="scope.row.hasRemoved">[已删除]</span>
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="项目" align="center" />
      <el-table-column prop="operateStatus" label="同步状态" align="center">
        <!-- 字典 -->
        <template #default="scope">
        <span>{{ getDicItemName(scope.row.operateStatus, 'chatbot.knowledge.operateStatus') }}</span>
      </template>
    </el-table-column>
      <el-table-column prop="type" label="操作" align="center">
        <template #default="scope">
          <span>{{ getDicItemName(scope.row.type, 'chatbot.operationType') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="bindTime" label="操作时间" align="center" />
      <el-table-column prop="bindId" label="操作人" align="center">
        <template #default="scope">
          {{ getNickName(scope.row.bindId) }}
        </template>
      </el-table-column>
    </el-table>
    <Pagination ref="paginationRef" @change="oldRecordPageData" :total="oldRecordTotal" />
  </DtPopup>
<!-- 知识章节弹框 -->
<DtPopup :show="showChapter" @close="closeChapter" @close-by-icon="closeChapter" title="章节明细" :footer="false" :isFullscreen="true">
    <SearchForm ref="ChapterFormRef" :searchFormTemp="searchFormTemp1" @handleSearch="handleSearchChapter" @handleResetQuery="resetSearchChapter"> </SearchForm>
    <el-table :data="knowledgeChapterData" class="dt-table" stripe>
      <el-table-column align="center" prop="knowledgeId" label="知识ID" width="200"></el-table-column>
      <el-table-column align="center" prop="title" label="标题"></el-table-column>
      <el-table-column align="center" prop="dataType" label="数据类型" width="100"></el-table-column>
      <el-table-column align="center" prop="contentLength" label="字数长度" width="100"></el-table-column>
      <el-table-column align="center" prop="syncTime" label="同步时间" width="200"></el-table-column>
      <el-table-column align="center" prop="syncStatus" label="同步状态" width="100">
        <template #default="scope">
          <span>{{ getDicItemName(scope.row.syncStatus, 'chatbot.knowledge.syncStatus') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="status" label="启用状态" width="100">
        <template #default="scope">
          <span>{{ getDicItemName(scope.row.status, 'chatbot.common.status') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="showsliceList(scope.row)">查看切片</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination ref="paginationRef" @change="knowledgeChapter" :total="chapterTotal" />
  </DtPopup>
  <!-- 知识列表弹框 -->
  <DtPopup :show="showKnowledge" @close="closeKnowledge" @close-by-icon="closeKnowledge" title="知识列表" :footer="false" :isFullscreen="true">
    <SearchForm ref="knowledgeFormRef" :searchFormTemp="searchFormTemp" @handleSearch="handleSearch" @handleResetQuery="resetSearch"> </SearchForm>
    <el-table :data="knowledgeTableData" class="dt-table" stripe>
      <el-table-column align="center" prop="knowledgeId" label="知识ID" width="200"></el-table-column>
      <el-table-column align="center" prop="title" label="标题"></el-table-column>
      <el-table-column align="center" prop="contentLength" label="字数" width="100"></el-table-column>
      <el-table-column align="center" prop="syncTime" label="同步时间" width="200"></el-table-column>
        <el-table-column align="center" prop="syncStatus" label="同步状态" width="100">
          <template #default="scope">
          <span>{{ getDicItemName(scope.row.syncStatus, 'chatbot.knowledge.syncStatus') }}</span>
        </template>
        </el-table-column>

      <el-table-column label="操作" align="center"  width="250px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="knowledgeUpdate(scope.row)">更新</el-button>
          <el-button link type="primary" @click="showChapterList(scope.row)">知识切片</el-button>
          <el-button link type="primary" @click="knowledgeDetail(scope.row)">知识原文</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination ref="paginationRef" @change="knowledgePageData" :total="knowledgeTotal" />
  </DtPopup>
  <!-- 切片 -->

  <DtPopup :show="showslice" @close="closeslice" @close-by-icon="closeslice" title="切片管理" :footer="false" :isFullscreen="true">
      <!-- <sliceManagement  /> -->
      <sectionDetail   v-if="showslice" :sliceId="sliceId" />
  </DtPopup>
  <!-- 知识库iframe -->
  <div class="iframe-dialog" v-if="showKnowledgeIframe">
    <DtIcon icon-name="icondt25" class="popup-icon" @click="closeKnowledgeIframe" />
    <iframe :src="iframeSrc" frameborder="0" height="100%" width="100%"></iframe>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ToolListProps, PageTableSearch, PageData, SearchFormTemp } from '@/components/types'
import sliceManagement from '@/views/section/sliceManagement.vue'
import sectionDetail from'@/views/section/sectionDetail.vue'
import useFilters from '@/hooks/useFilters'
import useSystemStore from '@/stores/system'
import { useUserStore } from '@/stores/user'
import {
  knowledgeList,
  bindKnowLedgeList,
  saveBind,
  unbindKnowLedge,
  refreshKnowledge,
  knowledgeRecord,
  knowledgeDetailList,
  updateKnowLedgeDetail,
  getFuncId,
  directoryList,
  knowledgeChapterPage
} from '../api'
import { FormInstance } from 'element-plus'

const { getAllUser } = useUserStore()
const emit = defineEmits<{
  (e: 'updateStatus'): void
}>()
const props = defineProps({
  versionIndex: Number,
})
const router = useRouter()
const route = useRoute()
const userList = getAllUser()
const firstDirectNameList = ref<Record<string, any>[]>([])
const directNameList = ref<Record<string, any>[]>([])
const systemStore = useSystemStore()
const { getNickName, getDicItemName } = useFilters()
const sliceId = ref()
const defaultProps = {
  children: 'childs',
  label: 'directName',
}
const toolList: ToolListProps[] = [
  {
    name: '历史操作记录',
    btnCode: '',
    action: async () => {
      await initOldRecord()
      showOldRecord.value = true
    },
  },
  {
    name: '知识列表',
    btnCode: '',
    action: async () => {
      // await init()
      knowledgeParam.value.param.title = ''
      knowledgeParam.value.param.knowledgeId = ''
      knowledgeParam.value.param.directoryCode =''
      await initKnowLedgeDetail()
      showKnowledge.value = true
    },
  },
]
const treeData = ref<any[]>([])
const tableData = ref<Record<string, any>[]>([])
const treeRef = ref()
const total = ref<number>(0)
const robotCode = ref<string>(route.query.robotCode.toString() || '')
const searchFormTemp: SearchFormTemp[] = [
  {
    label: '知识ID',
    name: 'knowledgeId',
    type: 'input',
    placeholder: '请输入知识ID',
  },
  {
    label: '标题',
    name: 'title',
    type: 'input',
    placeholder: '请输入标题',
  },
]
const searchFormTemp1: SearchFormTemp[] = [
  // {
  //   label: '知识ID',
  //   name: 'knowledgeId',
  //   type: 'input',
  //   placeholder: '请输入知识ID',
  // },
  {
    label: '标题',
    name: 'title',
    type: 'input',
    placeholder: '请输入标题',
  },
]
const pageParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    robotCode: robotCode.value,
    type: props.versionIndex,
    firstDirectId: '',
    directoryId: '',
    updateId: '',
  },
})
const oldRecordPageParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    robotCode: robotCode.value,
  },
})
const expanded = ref<string[]>([])
const showOldRecord = ref<boolean>(false)
const oldRecordTableData = ref<Record<string, any>[]>([])
const oldRecordTotal = ref<number>(0)
const showKnowledge = ref<boolean>(false)
const showChapter = ref<boolean>(false)
const showslice= ref<boolean>(false)
const knowledgeParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    robotCode: robotCode.value,
    //type: 0,
    directoryCode: '',
    //knowledgeType: 1,
    title: '',
    knowledgeId: '',
    syncStatus: '',
    dataType: '',
    status: '',
  },
})
const knowledgeTotal = ref<number>(0)
const knowledgeTableData = ref<Record<string, any>[]>([])
const chapterTotal = ref<number>(0)
const knowledgeChapterData = ref<Record<string, any>[]>([])
const showKnowledgeIframe = ref<boolean>(false)
const iframeSrc = ref<string>('')
  
const knowledgeDetail = async (row: any) => {
  const res = await getFuncId()
  const url = `${import.meta.env.VITE_KBC_PATH}/km-web/lookKnowledge?from=iframe&readOnly=true&id=${row.knowledgeId}&projectId=${
    row.projectId
  }&folderId=${row.folderId}&firstDirect=${row.firstDirect}&secondDirect=${row.secondDirect}&access_token=${
    systemStore.system.access_token
  }&tenantId=${systemStore.system.tenantId}&funcId=${res.data.paramValue}&themeColor=${systemStore.system.themeColor}&navTagColor=${
    systemStore.system.navTagColor
  }`
  console.log(url)
  iframeSrc.value = url
  showKnowledgeIframe.value = true
}
const knowledgeUpdate = async (row: any) => {
  ElMessageBox.confirm(`确定要更新吗？`, '请确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await updateKnowLedge(row)
    })
    .finally(() => {
      init()
    })
}

const updateKnowLedge = async (value: Record<string, any>, id?: string[]) => {
  const data = {
    robotCode: value.robotCode,
    knowledgeId: value.knowledgeId,
    knowledgeType: value.knowledgeType,
    directoryCode: value.directoryCode,
  }

  const res = await updateKnowLedgeDetail(data)
  if (res.status === 0) {
    await init()
  }
}

const closeKnowledgeIframe = () => {
  showKnowledgeIframe.value = false
}

const oldRecordPageData = async (data: PageData) => {
  oldRecordPageParam.value.pageNum = data.pageNum
  oldRecordPageParam.value.pageSize = data.pageSize
  initOldRecord()
}

const handleSearch = async (data: any) => {
  // knowledgeParam.value.param.directoryId = data.directoryId
  // knowledgeParam.value.param.type = props.versionIndex

  knowledgeParam.value.param.title = data.title
  knowledgeParam.value.param.knowledgeId = data.knowledgeId

  await initKnowLedgeDetail()
}

const resetSearch = async (data: any) => {
  // knowledgeParam.value.param.directoryId = data.directoryId
  //knowledgeParam.value.param.type = props.versionIndex

  knowledgeParam.value.param.title = data.title
  knowledgeParam.value.param.knowledgeId = data.knowledgeId

  await initKnowLedgeDetail()
}
const initOldRecord = async () => {
  const res = await knowledgeRecord(oldRecordPageParam.value)
  if (res.status === 0) {
    oldRecordTableData.value = res.data.list
    oldRecordTotal.value = res.data.total
  }
}

const setCheckedKeys = (list: Record<string, any>): string[] | number[] => {
  let currentNodeKey = []
  list.forEach((el: Record<string, any>) => {
    el.disabled = true
    el.childs.forEach((item: Record<string, any>) => {
      item.disabled = true
      item.childs.forEach((key: Record<string, any>) => {
        key.childs.forEach((value: Record<string, any>) => {
          if (value.isBind) {
            currentNodeKey.push(value.directId)
          }
        })
      })
    })
  })

  return currentNodeKey
}

const closeDialog = () => {
  showOldRecord.value = false
}

const expandedChange = (data: Record<string, any>) => {
  if (expanded.value.findIndex((item: any) => item === data.directId) === -1) {
    expanded.value.push(data.directId)
  }
}

const expandedClose = (data: Record<string, any>) => {
  const index = expanded.value.findIndex((item: any) => item === data.directId)
  if (index !== -1) {
    expanded.value.splice(index, 1)
  }
}

const checkChange = (data: Record<string, any>) => {
  if (route.query.status === '2') {
    ElMessage.warning('发布中，无法修改')
    init()
    return
  }
  const param = []
  if (data.directType === '3') {
    data.childs.forEach((item: Record<string, any>) => {
      param.push({
        directoryName: item.directName,
        directoryId: item.directId,
      })
    })
  } else {
    param.push({
      directoryName: data.directName,
      directoryId: data.directId,
    })
  }
  const isBind = data.childs.every((item: Record<string, any>) => {
    return item.isBind
  })
  if (data.isBind || (data.directType === '3' && isBind)) {
    unbind(data, param)
  } else {
    ElMessageBox.confirm(`确定要绑定吗？`, '请确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        await bindKnowLedge(data, param)
      })
      .finally(() => {
        init()
      })
  }
}

const bindKnowLedge = async (value: Record<string, any>, id?: string[]) => {
  const data = {
    robotCode: robotCode.value,
    folderId: value.folderId,
    folderName: value.folderName,
    projectId: value.projectId,
    projectName: value.projectName,
    directVOS: id,
    firstDirectId: value.firstDirectId,
    firstDirectName: value.firstDirectName,
  }
  const res = await saveBind(data)
  if (res.status === 0) {
    initKnowLedge()
    emit('updateStatus')
  }
}

const unbind = (value: Record<string, any>, id?: string[]) => {
  if (route.query.status === '2') {
    ElMessage.warning('发布中，无法修改')
    init()
    return
  }
  if (value.syncStatus == 1) {
    ElMessage.warning('该目录同步中，无法解绑')
    return
  }
  ElMessageBox.confirm('确定要解绑吗？', '请确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const data = {
        robotCode: robotCode.value,
        folderId: value.folderId,
        folderName: value.folderName,
        projectId: value.projectId,
        projectName: value.projectName,
        directVOS: id || [{ directoryId: value.directoryId, directoryName: value.directoryName }],
        firstDirectId: value.firstDirectId,
        firstDirectName: value.firstDirectName,
      }
      const res = await unbindKnowLedge(data)
      if (res.status === 0) {
        init()
        emit('updateStatus')
      }
    })
    .catch(() => {
      if (id) {
        init()
      }
    })
}

const onPageData = async (data: PageData) => {
  pageParam.value.pageNum = data.pageNum
  pageParam.value.pageSize = data.pageSize
  initBindKnowLedge()
}

const refresh = async (data: Record<string, any>) => {
  if (route.query.status === '2') {
    ElMessage.warning('发布中，无法修改')
    return
  }
  const res = await refreshKnowledge({ robotCode: robotCode.value, type: props.versionIndex, directoryId: data.directoryId })
  if (res.status === 0) {
    init()
    emit('updateStatus')
  }
}

const detail = async (data: Record<string, any>) => {
  //knowledgeParam.value.param.directoryId = data.directoryId
  //knowledgeParam.value.param.type = props.versionIndex
  knowledgeParam.value.param.title = ''
  knowledgeParam.value.param.knowledgeId = ''
  knowledgeParam.value.param.directoryCode = data.directoryCode
  // await ChapterDetail()
  // showChapter.value = true
  await initKnowLedgeDetail()
  showKnowledge.value = true
}

const initKnowLedgeDetail = async () => {
  const res = await knowledgeDetailList(knowledgeParam.value)

  console.log('res: ', res)
  if (res.status === 0) {
    knowledgeTableData.value = res.data.list
    knowledgeTotal.value = res.data.total
  }
}
const knowledgeFormRef = ref<any>()
const closeKnowledge = () => {
  showKnowledge.value = false
  knowledgeFormRef.value && knowledgeFormRef.value.normalReset()
}
const knowledgePageData = async (data: PageData) => {
  knowledgeParam.value.pageNum = data.pageNum
  knowledgeParam.value.pageSize = data.pageSize
  initKnowLedgeDetail()
}

const init = async () => {
  initKnowLedge()
  initBindKnowLedge()
}

const initBindKnowLedge = async () => {
  pageParam.value.param.type = props.versionIndex
  const res = await bindKnowLedgeList(pageParam.value)
  if (res.status === 0) {
    tableData.value = res.data.list
    total.value = res.data.total
  }
}

const initKnowLedge = async () => {
  const data = {
    robotCode: robotCode.value,
    type: props.versionIndex,
  }
  const res = await knowledgeList(data)
  if (res.status === 0) {
    const list = res.data
    list.forEach((el: Record<string, any>) => {
      el.disabled = true
      el.childs.forEach((item: Record<string, any>) => {
        item.disabled = true
      })
    })
    treeData.value = list
    treeRef.value.setCheckedKeys(setCheckedKeys(list))
  }
}

const getDirectoryList = async (id?: string) => {
  const data = {
    robotCode: robotCode.value,
    type: props.versionIndex,
    firstDirectId: id,
  }
  const res = await directoryList(data)
  if (id) {
    directNameList.value = res.data
  } else {
    firstDirectNameList.value = res.data
  }
}

const selectFirstDirectName = (val: string) => {
  directNameList.value = []
  pageParam.value.param.directoryId = ''
  getDirectoryList(val)
}

const tabChange = async () => {
  knowledgeParam.value.pageNum = 1
  knowledgeParam.value.pageSize = 10
  await initKnowLedgeDetail()
}

// 知识章节列表

const ChapterFormRef = ref<any>()
const closeChapter = () => {
  showChapter.value = false
  ChapterFormRef.value && ChapterFormRef.value.normalReset()
}
const knowledgeChapter = async (data: PageData) => {
  knowledgeParam.value.pageNum = data.pageNum
  knowledgeParam.value.pageSize = data.pageSize
  ChapterDetail()
}
// 知识章节明细
const ChapterDetail = async () => {
  const res = await knowledgeChapterPage(knowledgeParam.value)
  console.log('res: ', res)
  if (res.status === 0) {
    knowledgeChapterData.value = res.data.list
    chapterTotal.value = res.data.total
  }
}

const handleSearchChapter = async (data: any) => {
  console.log(data)
  // knowledgeParam.value.param.directoryId = data.directoryId
  // knowledgeParam.value.param.type = props.versionIndex
 
  knowledgeParam.value.param.title = data.title
  // knowledgeParam.value.param.knowledgeId = data.knowledgeId

  await ChapterDetail()
}

const resetSearchChapter = async (data: any) => {
  console.log(data)
  // knowledgeParam.value.param.directoryId = data.directoryId
  //knowledgeParam.value.param.type = props.versionIndex

  knowledgeParam.value.param.title = data.title
  // knowledgeParam.value.param.knowledgeId = data.knowledgeId

  await ChapterDetail()
}
// 知识章节明细
const showChapterList = async (data:any) => {
  knowledgeParam.value.param.title = ''
  knowledgeParam.value.param.knowledgeId =  data.knowledgeId
  knowledgeParam.value.param.directoryCode = data.directoryCode
  await ChapterDetail()
  showChapter.value = true
  // await initKnowLedgeDetail()
  // showKnowledge.value = true
}
// 切片管理
const showsliceList = async (row:any) => {
  showslice.value = true
  sliceId.value = row.id
  // router.push({
  //   path: '/sectionDetail',
  //   query: {
  //     productId: row.id,
  //   }
  // })
}
// 切片管理
const closeslice = async () => {
  showslice.value = false
}


watch(
  () => props.versionIndex,
  (val) => {
    init()
  }
)

onMounted(async () => {
  await init()
  await getDirectoryList()
})

defineExpose({ init })
</script>

<style lang="less" scoped>
.menu-content {
  display: flex;

  // 不可全选样式
  :deep(.el-tree-node) {
    .el-checkbox__input.is-disabled {
      display: none;
    }
  }

  .menu-tree {
    width: 300px;
  }

  .menu-table {
    flex: 1;
    overflow-y: auto;
  }
}

.iframe-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 99999;

  .popup-icon {
    position: absolute;
    font-size: 65px;
    top: 7px;
    right: -61px;
    color: #b8b8b8;
    cursor: pointer;
  }
}

.search-box {
  display: flex;
  padding: 20px;
  flex-wrap: wrap;

  .search-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;

    :deep(.el-input) {
      width: 200px;
    }

    .search-label {
      font-size: 14px;
    }

    .el-input {
      flex: 1;
    }
  }
}
</style>
