<template>
  <TableToolTemp tool-title="触发转人工条件">
    <template #default>
      <el-form label-position="right" label-width="120">
        <el-form-item label="智能体无回复时">
          <div>
            <div>
              <el-radio-group v-model="updateForm.noReplyTransferStatus">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0" @click="updateForm.noReplyTransferTimes = 0">否</el-radio>
              </el-radio-group>
            </div>
            <div v-if="updateForm.noReplyTransferStatus">
              连续无回复
              <el-input-number class="input-number" v-model="updateForm.noReplyTransferTimes" :precision="0" :min="1"
                :max="100" :controls="false"></el-input-number>
              次后，转人工
            </div>
          </div>
        </el-form-item>
        <el-form-item label="澄清重复时">
          <div>
            <div>
              <el-radio-group v-model="updateForm.clearTransferStatus">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0" @click="updateForm.clearTransferTimes = 0">否</el-radio>
              </el-radio-group>
            </div>
            <div v-if="updateForm.clearTransferStatus">
              连续重复
              <el-input-number class="input-number" v-model="updateForm.clearTransferTimes" :precision="0" :min="1"
                :max="100" :controls="false"></el-input-number>
              次后，转人工
            </div>
          </div>
        </el-form-item>
        <el-form-item label="用户重复提问时">
          <div>
            <div>
              <el-radio-group v-model="updateForm.sameQuestionTransferStatus">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0" @click="updateForm.sameQuestionTransferTimes = 0">否</el-radio>
              </el-radio-group>
            </div>
            <div v-if="updateForm.sameQuestionTransferStatus">
              连续重复
              <el-input-number class="input-number" v-model="updateForm.sameQuestionTransferTimes" :precision="0" :min="1"
                :max="100" :controls="false"></el-input-number>
              次后，转人工
            </div>
          </div>
        </el-form-item>
        <el-form-item label="触发关键词时">
          <div>
            <div>
              <el-radio-group v-model="updateForm.keywordsTransferStatus">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0" @click="updateForm.keywords = []">否</el-radio>
              </el-radio-group>
            </div>
            <div v-if="updateForm.keywordsTransferStatus">
              <div>转人工关键字：</div>
              <div class="input-box" v-for="item, index in updateForm.keywords">
                <el-input v-model="updateForm.keywords[index]"></el-input>
                <DtIcon icon-name="icondt23" class="icon" @click="removeKeywords(index)" />
              </div>
              <el-button type="primary" link @click="addKeywords">
                <DtIcon icon-name="icondt8" />新增关键词
              </el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </template>
  </TableToolTemp>

  <TableToolTemp tool-title="人工客服平台配置">
    <template #default>
      <el-form label-position="right" label-width="120">
        <el-form-item label="对接方式">
          <el-radio-group v-model="updateForm.custServPlatAccessType">
            <el-radio :label="2" @click="updateForm.custServPlatAddr = ''">系统集成</el-radio>
            <el-radio :label="1">网页跳转</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="跳转地址" v-if="updateForm.custServPlatAccessType == 1">
          <el-input class="href-input" v-model="updateForm.custServPlatAddr"></el-input>
        </el-form-item>
        <el-form-item  v-if="updateForm.custServPlatAccessType == 2" label="客服技能组编码">
          <el-input
            type="text"
            placeholder="请输入客服技能组编码"
            v-model="updateForm.custServPlatAddr"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </template>
  </TableToolTemp>

  <div class="submit-btn">
    <el-button type="primary" @click="submitForm(updateForm)">提交</el-button>
    <el-button @click="cancel">返回</el-button>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { saveManualConfig, getManualConfig } from '../api'
const props = defineProps<{
  versionIndex: Number
}>()

const emit = defineEmits<{
  (e: 'updateStatus'): void
}>()

const router = useRouter()
const route = useRoute()
const updateForm = ref<Record<string, any>>({
  robotId: '',
  noReplyTransferStatus: 0,
  noReplyTransferTimes: 0,
  clearTransferStatus: 0,
  clearTransferTimes: 0,
  sameQuestionTransferStatus: 0,
  sameQuestionTransferTimes: 0,
  keywordsTransferStatus: 0,
  custServPlatAccessType: 2,
  custServPlatAddr: "",
  keywords: []
})

const submitForm = async (data: Record<string, any>) => {
  if (route.query.status === '2') {
    ElMessage.warning('发布中，无法修改')
    return
  }
  if (!validate()) return
  updateForm.value.robotId = Number(route.query.id)
  const res = await saveManualConfig(updateForm.value)
  if (res.status === 0) {
    ElMessage.success('保存成功')
    // router.back()
    emit('updateStatus')
  }
}

const validate = (): boolean => {
  if (updateForm.value.noReplyTransferStatus && !updateForm.value.noReplyTransferTimes) {
    ElMessage.warning('请填写连续无回复次数')
    return false
  }
  if (updateForm.value.clearTransferStatus && !updateForm.value.clearTransferTimes) {
    ElMessage.warning('请填写连续重复次数')
    return false
  }
  if (updateForm.value.sameQuestionTransferStatus && !updateForm.value.sameQuestionTransferTimes) {
    ElMessage.warning('请填写连续重复次数')
    return false
  }
  if (updateForm.value.keywordsTransferStatus) {
    if (updateForm.value.keywords.length) {
      if (!updateForm.value.keywords.every((item: string) => item)) {
        ElMessage.warning('请完善关键词')
        return false
      }
    } else {
      ElMessage.warning('请添加关键词')
      return false
    }
  }
  if (updateForm.value.custServPlatAccessType == 1 && !updateForm.value.custServPlatAddr) {
    ElMessage.warning('请填写跳转地址')
    return false
  }
  return true
}

const addKeywords = () => {
  updateForm.value.keywords.push('')
}

const removeKeywords = (index: number) => {
  updateForm.value.keywords.splice(index, 1)
}

const cancel = () => {
  router.back()
}

const initManualConfig = async () => {
  const data = {
    robotId: Number(route.query.id),
    type: props.versionIndex
  }
  const res = await getManualConfig(data)
  if (res.status == 0 && res.data) {
    updateForm.value = res.data
  }
}

watch(() => props.versionIndex, async (val) => {
  await initManualConfig()
})

onMounted(async () => {
  await initManualConfig()
})

defineExpose({ initManualConfig })
</script>

<style lang="less" scoped>
.input-number {
  width: 60px;
}

.input-box {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .icon {
    cursor: pointer;
    margin-left: 10px;
  }
}

.href-input {
  width: 300px;
}
</style>
