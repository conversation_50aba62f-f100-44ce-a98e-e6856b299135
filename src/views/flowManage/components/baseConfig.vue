<template>
  <TableToolTemp :tool-title="baseTitle">
    <template #default>
      <UpdateFormTemp ref="baseFormRef" :formParams="baseParams" :form-list="baseList" :rules="rules" :showSubmit="false" />
    </template>
  </TableToolTemp>
  <TableToolTemp :tool-title="policyTitle">
    <template #default>
      <UpdateFormTemp ref="updateFormRef" :formParams="updateForm" :form-list="policyList" :rules="rules" :showSubmit="false" />
    </template>
  </TableToolTemp>

  <div class="submit-btn">
    <el-button type="primary" @click="submitForm">提交</el-button>
    <el-button @click="cancel">返回</el-button>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import UpdateFormTemp from '../../../components/UpdateFormTemp.vue'
import type { UpdateFormList } from '../../../components/types'
import type { FormRules } from 'element-plus'
import { useRoute } from 'vue-router'
import useFilters from '@/hooks/useFilters'
import { saveRobotConfig, robotBaseInfo } from '../api'

import router from '@/router'

const props = defineProps<{
  versionIndex: Number
}>()

const emit = defineEmits<{
  (e: 'updateStatus'): void
}>()

const baseFormRef = ref()
const updateFormRef = ref()
const { getDicItemName } = useFilters()
const route = useRoute()
const baseTitle = ref<string>('基础配置')
const updateForm = ref<Record<string, any>>({
  similarityThreshold: '',
  retrievalThreshold: '',
})
const baseList = ref<UpdateFormList[]>([])
const baseParams = ref<Record<string, any>>({})

const policyTitle = ref<string>('策略信息')
const policyList = ref<UpdateFormList[]>([
  {
    title: '相似度阈值',
    type: 'numberInput',
    field: 'similarityThreshold',
    // require: true,
    min: 0.1,
    max: 1,
    precision: 1,
  },
  {
    title: '检索量阈值',
    type: 'numberInput',
    field: 'retrievalThreshold',
    // require: true,
    min: 1,
    stepStrictly: true
  },
])

const rules: FormRules = {
  robotName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  // similarityThreshold: [{ required: true, message: '不能为空', trigger: 'blur' }],
  // retrievalThreshold: [{ required: true, message: '不能为空', trigger: 'blur' }],
}

const submitForm = async () => {
  // todo 上线无法修改
  //if (route.query.status === '2') {
  //  ElMessage.warning('发布中，无法修改')
  //  return
  //}

  const base = await baseFormRef.value.getFormData()
  if (!base) return
  const update = await updateFormRef.value.getFormData()
  if (!update) return

  const res = await saveRobotConfig({ ...update, robotName: base.robotName, id: base.id })
  if (res.status === 0) {
    ElMessage.success('保存成功')
    emit('updateStatus')
  }
}

const cancel = () => {
  router.back()
}

const initBaseConfig = async () => {
  const data = {
    robotCode: route.query.robotCode,
  }
  const res = await robotBaseInfo(data)
  if (res.status === 0 && res.data) {
    updateForm.value = {
      similarityThreshold: res.data.similarityThreshold || "",
      retrievalThreshold: res.data.retrievalThreshold || ""
    }
    clearStatusChange()
  } else {
    updateForm.value = {
      similarityThreshold: "",
      retrievalThreshold: "",
    }
  }
}

const setBaseList = () => {
  baseList.value = [
    {
      title: '名称',
      type: 'input',
      field: 'robotName',
      require: true,
    },
    {
      title: '发布时间',
      type: 'text',
      field: 'createTime',
    },
  ]

  baseParams.value = {
    robotName: route.query.name,
    createTime: route.query.createTime,
    id: route.query.id,
  }
}

const clearStatusChange = async () => {
  await baseFormRef.value.clearValidate()
  await updateFormRef.value.clearValidate()
}

watch(
  () => props.versionIndex,
  () => {
    setBaseList()
    initBaseConfig()
  }
)

onMounted(async () => {
  setBaseList()
  await initBaseConfig()
})
</script>
