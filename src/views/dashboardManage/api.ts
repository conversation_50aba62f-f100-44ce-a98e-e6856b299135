import { useAxios } from '@/hooks/http'

/**
 * @description 接待概况
 */
// export const receptionList = async (data: any) => await useAxios('/web/databoard/page', data)
export const receptionList = async (data: any) => await useAxios('/web/databoard/page', data)
/**
 * @description 历史会话
 */
export const pageRecord = async (data: any) => await useAxios('/web/databoard/pageRecord', data)

/**
 * @description 智能体列表
 */
// export const searchRobotPage = async (data: any) => await useAxios('/web/robot/page', data)
export const searchRobotPage = async ( params: any ) => await useAxios('/web/maas/robot/list',params)

/**
 * @description 渠道列表
 */
// export const searchChannelPage = async (data: any) => await useAxios('/web/channel/page', data)
export const searchChannelPage = async (data: any) => await useAxios('/web/maas/channel/page', data)

/**
 * @description 获取FuncId
 */
export const getFuncId = async () => await useAxios('/web/knowledgeconfig/getFuncId')


// 知识列表
export const qaRecordReferences = async (data: any) => await useAxios('/web/maas/qaRecord/references', data)
// 日志报文
export const qaRecordInfo = async (data: any) => await useAxios('/web/maas/qaRecord/info', data)

//查询执行列表
export const pageFlowRecord = async (data: any) => await useAxios('/web/databoard/pageFlowRecord', data)
/**
 * //查询执行明细
 */
export const getFlowDetail= async (data: any) => await useAxios('/web/databoard/getFlowDetail', data)