<template>
  <el-tooltip :effect="props.effect" show-overflow-tooltip placement="top">
    <template #content>
      <div class="inner">
        <div class="inner__icons">
          <el-icon v-for="name in props.fns" :key="fn" :size="20" class="inner__icons__copy"
            ><CopyDocument v-show="name == 'copy'" @click="onCopy(props.content)"
          /></el-icon>
        </div>

        <div class="inner__content" v-html="strToHTML(props.content)"></div>
        <div class="inner__urls" v-if="props.images && props.images.length">
          <img class="inner__urls__images" :src="img" v-for="(img, idx) in props.images" :key="idx" />
        </div>
      </div>
    </template>


    <div class="out" v-if="props.format === 'str'">{{ props.content }}</div>
    <div class="out" v-else v-html="props.content"></div>
  </el-tooltip>
</template>

<script setup lang="ts">
import { reactive, toRefs, watch, ref, computed } from 'vue'
import { micromark } from 'micromark'
import { CopyDocument } from '@element-plus/icons-vue'
import copy from 'copy-text-to-clipboard'
import { gfmTable, gfmTableHtml } from 'micromark-extension-gfm-table'

const props = withDefaults(
  defineProps<{
    format?: string // "md" | html | str
    content: string
    fns?: string[] // "copy"
    effect?: string
    images?: string[]
  }>(),
  {
    format: 'md',
    effect: 'light',
    fns: ['copy'], // copy | others
    images: [],
  }
)

const strToHTML = (str) => {
  if (props.format === 'md') {
    str = micromark(str, {
      extensions: [gfmTable()],
      htmlExtensions: [gfmTableHtml()],
    })
  }
  return str
}

const onCopy = (str) => {
  copy(str)
  ElMessage.success('复制成功')
}
</script>

<style lang="less">
.out {
  position: relative;
  cursor: pointer;

  display: -webkit-box; /* Required for line-clamp */
  -webkit-box-orient: vertical; /* Vertical orientation */
  -webkit-line-clamp: 1; /* Number of lines to show */
  overflow: hidden; /* Hide overflowed content */
  text-overflow: ellipsis; /* Add ellipsis for truncated text */
  &:hover {
    color: var(--el-color-primary);
  }
}

.inner {
  position: relative;

  white-space: wrap;
  display: inline-block;
  max-width: 700px;
  max-height: 400px;
  overflow-y: auto;

  margin-bottom: 20px;

  &__content {
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 1em;
    }

    th,
    td {
      border: 1px solid #ccc;
      padding: 0.5em;
      text-align: left;
    }

    th {
      background-color: #f0f0f0;
      font-weight: bold;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }

  &__icons__copy {
    position: fixed;
    z-index: 1;
    bottom: 10px;
    right: 10px;

    cursor: pointer;
    &:active {
      opacity: 0.6;
    }
  }

  &__urls {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-top: 10px;

    &__images {
      width: 160px;
      object-fit: contain;
      border-radius: 10px;
    }
  }
}
</style>
