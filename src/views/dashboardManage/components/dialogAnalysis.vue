<template>
  <PageTable
    :search-form-temp="temp"
    :hiddenState="true"
    @spread="spread"
    @pack="pack"
    api-url="/web/databoard/pageConversation"
    ref="dialogAnalysisRef"
  >
    <template #default="{ tableData }">
      <TableToolTemp toolTitle="对话明细" :tool-list="toolConfig" />
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column prop="startTime" label="开始时间" align="center" width="190px" />
        <el-table-column prop="userName" label="用户" align="center" min-width="180px" />

        <el-table-column prop="intention" label="意图" align="center" min-width="180px">
          <template #default="scope">
            {{ getDicItemName(scope.row.intention, 'chatbot.intentionType') }}
          </template>
        </el-table-column>
        <el-table-column prop="userContent" label="提问内容" align="center" min-width="180px">
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.userContent" />
          </template>
        </el-table-column>
        <el-table-column prop="answerContent" label="回复内容" align="center" min-width="180px">
          <template #default="scope">
            <tooltip-with-fn format="md" :content="formatAnwser(scope.row)" :images="formatAnwserImages(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="time" label="输出秒数" align="center" width="190px" />
        <!--       <el-table-column prop="robotAnswerType" label="回复类型" align="center" width="110px">-->
        <!--          <template #default="scope">-->
        <!--            {{ getDicItemName(scope.row.robotAnswerType, 'chatbot.answerType') }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column prop="answerSource" label="回复类型" align="center" width="80px">
          <template #default="scope">
            <!-- {{scope.row.answerSource=='1'?'知识问答':'闲聊'}} -->
            {{ getDicItemName(scope.row.answerSource, 'chatbot.answerSource') }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceType" label="设备终端" align="center" min-width="100px" >
        <template #default="scope">
          <span v-if="scope.row.deviceType == '0'">APP</span>
          <span v-if="scope.row.deviceType == '1'">PC</span>
          </template>
        </el-table-column>
        <el-table-column prop="userVoteResult" label="用户反馈" align="center" width="80px">
          <template #default="scope">
            {{ getCustomDicitemName(scope.row.userVoteResult, 'userVoteResult') }}
          </template>
        </el-table-column>
        <el-table-column prop="userVoteRemark" label="反馈内容" align="center" min-width="100px" />
        <el-table-column prop="isCopy" label="复制" align="center" min-width="100px" >
        <template #default="scope">
          <span v-if="scope.row.isCopy == '0'">否</span>
          <span v-if="scope.row.isCopy == '1'">是</span>
          </template>
        </el-table-column>
        <el-table-column prop="robotName" label="智能体" align="center" min-width="110px" />
        <el-table-column prop="channelName" label="渠道来源" align="center" min-width="190px" />
        <el-table-column prop="model" label="大模型" align="center" width="180px" />
        <el-table-column prop="contextualizeQuestion" label="补全内容" align="center" min-width="180px">
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.contextualizeQuestion" />
          </template>
        </el-table-column>
        <el-table-column prop="convId" label="对话ID" align="center" width="160px" />
        <el-table-column prop="sessionId" label="会话ID" align="center" width="180px" />
        <el-table-column prop="userId" label="用户ID" align="center" min-width="200px" />
        <el-table-column label="操作" align="center" fixed="right" width="180px">
          <template #default="scope">
            <el-button type="text" :disabled="scope.row.answerSource == 1 ? false : true" @click="handleDetail(scope.row, 0)">知识列表</el-button>
            <el-button type="text" @click="handleDetail(scope.row, 1)">日志报文</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>
  <!--知识列表-->
  <DtPopup :show="showEdit" @close="showEdit = false" :title="title" width="73%" :footer="false">
    <el-table :data="tableDetailData" border stripe v-hover style="width: 100%; margin-bottom: 70px" class="dt-table">
      <el-table-column align="center" prop="id" label="切片ID" width="200"></el-table-column>
      <el-table-column align="center" prop="knowledgeId" label="知识ID" width="200"></el-table-column>
      <el-table-column align="center" prop="itemId" width="200" label="知识章节ID"></el-table-column>
      <el-table-column align="center" prop="title" width="250" label="知识章节标题"></el-table-column>
      <el-table-column align="center" prop="slice" label="内容" width="200">
        <template #default="scope">
          <el-tooltip
            v-if="scope.row.slice"
            :content="scope.row.slice"
            effect="dark"
            :width="100"
            :showOverflowTooltip="true"
            class="awidthTooltip"
            placement="top"
          >
            <p class="awidth" v-html="scope.row.slice"></p>
          </el-tooltip>
          <span v-else>""</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="score" width="200" label="相似度"></el-table-column>
    </el-table>
  </DtPopup>
  <!--日志报文-->
  <DtPopup :show="showEdit1" @close="showEdit1 = false" width="65%" :title="title" :footer="false">
    <div v-for="(value, key) in jsonData" :key="key">
      <strong>{{ key }}:</strong> {{ value }}
    </div>
  </DtPopup>
</template>

<script setup lang="ts">
import type { SearchFormTemp, ToolListProps } from '@/components/types'
import PageTable from '@/components/PageTable.vue'
import { ref, reactive } from 'vue'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import useChannel from '@/stores/channel'
import { qaRecordReferences, qaRecordInfo } from '../api'

import tooltipWithFn from '@/views/dashboardManage/components/tooltipWithFn.vue'

const showEdit = ref(false)
const title = ref(null)
const jsonData = ref(null)
const showEdit1 = ref(false)
const { getDic } = useUserStore()
const { getDicItemName } = useFilters()
const { channels } = useChannel()
const dialogAnalysisRef = ref()
const tableDetailData = ref<Record<string, any>[]>([])
const tableDetailData1 = ref<Record<string, any>[]>([])
function getParams() {
  if (!dialogAnalysisRef.value) return {}
  const p = dialogAnalysisRef.value.getSearchQuery()
  //console.debug(p)
  return p
}

const toolConfig: ToolListProps[] = [
  {
    name: '导出',
    icon: 'down',
    btnCode: 'chatbot:board:analysis:export',
    download: {
      url: '/web/databoard/exportConversation',
      params: getParams,
      isPost: true,
      check: () => {
        return true
      },
    },
  },
]
const dics = {
  userVoteResult: [
    { dicItemName: '全部', dicItemCode: '' },
    { dicItemName: '点踩', dicItemCode: '0' },
    { dicItemName: '点赞', dicItemCode: '1' },
  ],
  answerType: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.answerType')],
  answerSource: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.answerSource')],
  intention: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.intentionType')],
}
const temp: SearchFormTemp[] = reactive([
  {
    label: '来源渠道',
    name: 'channelCode',
    type: 'select',
    placeholder: '请选择',
    list: [{ dicItemCode: '', dicItemName: '全部' }, ...channels],
    searchState: true,
  },
  {
    label: '对话ID',
    name: 'convId',
    type: 'input',
    placeholder: '请输入内容',
    searchState: true,
  },
  {
    label: '会话ID',
    name: 'sessionId',
    type: 'input',
    placeholder: '请输入内容',
    searchState: true,
  },
  {
        label: '设备终端',
        name: 'deviceType',
        type: 'select',
        placeholder: '请选择',
        list: [
        { dicItemCode: '', dicItemName: '全部' },
        { dicItemCode: '0', dicItemName: 'APP' },
        { dicItemCode: '1', dicItemName: 'PC' },
        ],
        searchState: true
    },
  {
    label: '用户反馈',
    name: 'userVoteResult',
    type: 'select',
    placeholder: '请选择',
    list: dics.userVoteResult,
    searchState: false,
  },
  {
        label: '复制',
        name: 'isCopy',
        type: 'select',
        placeholder: '请选择',
        list: [
        { dicItemCode: '', dicItemName: '全部' },
        { dicItemCode: '0', dicItemName: '否' },
        { dicItemCode: '1', dicItemName: '是' },
        ],
        searchState: false
    },
  {
    label: '用户',
    name: 'userName',
    type: 'input',
    placeholder: '请输入内容',
    searchState: false,
  },
  {
    label: '提问内容',
    name: 'userContent',
    type: 'input',
    placeholder: '请输入内容',
    searchState: false,
  },
  // {
  //   label: '回复类型',
  //   name: 'robotAnswerType',
  //   type: 'select',
  //   placeholder: '请选择',
  //   list: dics.answerType,
  // },
  {
    label: '大模型类型',
    name: 'model',
    type: 'input',
    placeholder: '请输入内容',
    searchState: false,
  },
  {
    label: '回复类型',
    name: 'answerSource',
    type: 'select',
    placeholder: '请选择',
    list: dics.answerSource,
    searchState: false,
  },
  {
    label: '意图',
    name: 'intention',
    type: 'select',
    placeholder: '请选择',
    list: dics.intention,
    searchState: false,
  },
  {
    label: '时间',
    name: 'name',
    type: 'doubleDateWithQuick',
    doubleDate: {
      elType: 'DateTimePicker',
      startTime: {
        name: 'startTime',
        value: '',
      },
      endTime: {
        name: 'endTime',
        value: '',
      },
    },
    searchState: false,
  },
])

const getCustomDicitemName = (value: any, key: string) => {
  return dics[key]?.find((item: any) => item.dicItemCode === value)?.dicItemName
}
// 展开
const spread = (value: any) => {
  console.log(value)
  for (let i = 0; i < temp.length; i++) {
    if (i > 3) {
      temp[i].searchState = value
    }
  }
  console.log(temp)
  // dialogAnalysisRef.value.temp
}
// 收起
const pack = (value: any) => {
  console.log(value)
  for (let i = 0; i < temp.length; i++) {
    if (i > 3) {
      temp[i].searchState = value
    }
  }
  dialogAnalysisRef.value
}
const handleDetail = async (data: any, type: number) => {
  if (type == 0) {
    let res = await qaRecordReferences({ recordId: data.recordId })
    console.log(res)
    if (res.data && res.data.length > 0) {
      tableDetailData.value = res.data
      // this.total = res.total / 1 || 0;
      showEdit.value = true
      title.value = '知识列表'
    } else {
      return ElMessage.warning('列表无数据')
    }
  } else {
    let res = await qaRecordInfo({ recordId: data.recordId })
    if (res.data && res.data.question) {
      showEdit1.value = true
      title.value = '日志报文'
      jsonData.value = res.data
      // this.total = res.total / 1 || 0;
    } else {
      return ElMessage.warning('无日志报文')
    }
  }

  // this.tableDetailData = data;
}

const formatAnwser = (row) => {
  let { robotAnswerType, answerContent } = row
  if (robotAnswerType == '18') {
    try {
      answerContent = JSON.parse(answerContent || "")[0].text
      const str = answerContent.replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      //console.debug('answerContent: ', str)
      return str
    } catch (e) {
      return answerContent || ""
    }
  }
  return answerContent || ""
}

const formatAnwserImages = (row) => {
  let { robotAnswerType, answerContent } = row
  if (robotAnswerType == '18') {
    try {
      const data = JSON.parse(answerContent)[0]
      if (data && data.urls) {
        return data.urls
      }
      return []
    } catch (e) {
      return []
    }
  }

  return []
}

defineExpose({
  search: () => {
    if (dialogAnalysisRef.value) dialogAnalysisRef.value.onSearchBtn()
  },
})
</script>
<style lang="less" >
.awidth {
  width: 100%;
  display: block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  left: -5px;
  right: -10px;
  cursor: pointer;
  &:hover {
    color: var(--el-color-primary);
  }
}
.awidthTooltip {
  white-space: wrap;
  display: inline-block;

  max-width: 700px;
  max-height: 400px;
  overflow-y: auto;
}

.answer-pop {
  position: relative;
  padding-bottom: 20px;

  &__copy {
    position: absolute;
    z-index: 1;

    right: 10px;
    bottom: 10px;
    cursor: pointer;
    &:active {
      opacity: 0.6;
    }
  }
}
</style>
