<template>
  <!-- 主表格组件，包含搜索表单和数据表格 -->
  <PageTable
    :search-form-temp="temp"
    :hiddenState="true"
    @spread="spread"
    @pack="pack"
    api-url="/web/databoard/pageFlowRecord"
    ref="flowRef"
  >
    <!-- 表格工具栏和数据表格插槽 -->
    <template #default="{ tableData }">
      <!-- 工具栏，显示"工作流列表"标题 -->
      <TableToolTemp toolTitle="工作流列表" :tool-list="toolConfig" />
      <!-- el-table 显示工作流数据 -->
      <el-table :data="tableData" class="dt-table" stripe>
        <!-- 新增会话ID字段，放在渠道编码前面 -->
        <el-table-column prop="sessionId" label="会话ID" align="center" min-width="200px" />
        <!-- 各种字段列 -->
        <el-table-column prop="channelCode" label="渠道编码" align="center" width="190px" />
        <el-table-column prop="channelName" label="渠道名称" align="center" min-width="180px" />
        <el-table-column prop="createTime" label="开始时间" align="center" min-width="210px" />
        <el-table-column prop="username" label="用户" align="center" min-width="180px"/>
        <!-- 输入内容，使用tooltip组件支持markdown格式 -->
        <el-table-column prop="question" label="输入内容" align="center" min-width="180px">
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.question" />
          </template>
        </el-table-column>
        <!-- 输出内容，使用tooltip组件支持markdown格式 -->
        <el-table-column prop="answer" label="输出内容" align="center" min-width="180px">
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.answer" />
          </template>
        </el-table-column>
        <el-table-column prop="cost" label="输出时间" align="center" min-width="180px" />
        <!-- 类型字段，字典转换显示 -->
        <el-table-column prop="flowType" label="类型" align="center" width="120">
          <template #default="scope">
            <!-- 0、未发布 1、已发布 2、发布中 3、编辑中 9、已下线 -->
            {{ getDicItemName(scope.row.flowType, 'chatbot.workflow.type') }}
          </template>
        </el-table-column>
        <!-- 是否复制 -->
        <el-table-column prop="isCopy" label="复制" align="center" min-width="100px" >
          <template #default="scope">
            <span v-if="scope.row.isCopy == '0'">否</span>
            <span v-if="scope.row.isCopy == '1'">是</span>
          </template>
        </el-table-column>
        <!-- 用户反馈 -->
        <el-table-column prop="voteResult" label="用户反馈" align="center" width="110px">
          <template #default="scope">
            {{ getCustomDicitemName(scope.row.voteResult, 'voteResult') }}
          </template>
        </el-table-column>
        <el-table-column prop="voteRemark" label="反馈内容" align="center" min-width="190px" />
        <el-table-column prop="userId" label="用户ID" align="center" min-width="200px" />
        <!-- 操作列，点击可查看执行记录 -->
        <el-table-column label="操作" align="center" fixed="right" width="180px">
          <template #default="scope">
            <el-button type="text" @click="handleDetail(scope.row)">执行记录</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>

  <!-- 执行记录抽屉，显示主流程和迭代节点 -->
  <el-drawer
    title="执行记录"
    v-model="showEdit"
    :with-header="true"
    size="35%"
  >
    <!-- 主流程步骤循环 -->
    <div class="step-top" v-for="item,index in flwoData" :key=index>
      <!-- 步骤节点（主流程/迭代完成） -->
      <div class="step" v-if="item.event=='node_finished' || item.event=='iteration_completed'"  @click="flwoDataDown(index)">
        <!-- 展开/收起图标 -->
        <el-icon class="step-left" v-if="item.isUp" ><ArrowDown /></el-icon>
        <el-icon class="step-left" v-if="!item.isUp" ><ArrowRight /></el-icon>
        <div class="step-content">
          <!-- 节点类型图标和标题 -->
          <div  v-if="item.event=='node_finished'" class="step-title">
            <el-icon  v-if="item.data.node_type=='start'" class="step-icon"><HomeFilled /></el-icon>
            <el-icon v-if="item.data.node_type!=='end' && item.data.node_type!=='start'" class="step-cone"><Document /></el-icon>
            <el-icon v-if="item.data.node_type=='end'"    class="step-finish"><Finished /></el-icon>
            <span class="">{{item.data.title}}</span>
          </div>
          <!-- 迭代完成节点 -->
          <div  v-if="item.event=='iteration_completed'" class="step-title">
            <el-icon  class="step-icon"><Refresh /></el-icon>
            <span class="">{{item.data.title}}</span>
          </div>             
        </div>
        <div class="step-right">
          <div class="step-description">{{ Number(item.data.elapsed_time).toFixed(2) }}s</div>
          <el-icon  class="step-suc"><Check /></el-icon>
        </div>
      </div>
      <!-- 迭代节点入口，点击可展开右侧抽屉 -->
      <div class="resh" v-if="reLength>0 && item.event=='iteration_completed' && item.isUp"  @click.stop="rightChild(item)">
        <div  class="resh-left"><el-icon><Refresh /></el-icon><span style="margin-left: 10px;">{{reLength}}个迭代</span></div>
        <div class="resh-right">
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
      <!-- 数据处理内容块 -->
      <div v-if="item.isUp && item.data.process_data && item.data.process_data!=='null'" class="content-input">
        <div  class="input-tit">
          <div class="input-tit-text">数据处理</div>
          <div class="tool">
            <el-icon class="tool-icon" @click.stop="onCopy(item.data.process_data)"><DocumentCopy /></el-icon>
            <el-icon  class="tool-icon" @click.stop="outputAll(item.data.process_data,-1)"><Rank /></el-icon>
          </div>
        </div>
        <!-- <div  class="output" v-html="outPutData(item.data.process_data)"></div> -->
        <div class="output">
              <vue-json-pretty  :data="item.data.process_data"  :deep="3"/>
        </div>
      </div>
      <!-- 输入内容块 -->
      <div v-if="item.isUp && item.data.inputs && item.data.inputs!=='null'" class="content-input">
        <div  class="input-tit">
          <div class="input-tit-text">输入</div>
          <div class="tool">
            <el-icon class="tool-icon" @click.stop="onCopy(item.data.inputs)"><DocumentCopy /></el-icon>
            <el-icon  class="tool-icon" @click.stop="outputAll(item.data.inputs,0)"><Rank /></el-icon>
          </div>
        </div>
        <!-- 这里可以用json美化组件或自定义格式化 -->
        <!-- <div  class="output" v-html="outPutData(item.data.inputs)"></div> -->
        <div class="output">
              <vue-json-pretty  :data="item.data.inputs"  :deep="3"/>
        </div>
      </div>
      <!-- 输出内容块 -->
      <div v-if="item.isUp && item.data.outputs  && item.data.outputs!=='null'" class="content-input">
        <div class="input-tit">
          <div class="input-tit-text">输出</div>
          <div class="tool">
            <el-icon class="tool-icon" @click.stop="onCopy(item.data.outputs)"><DocumentCopy /></el-icon>
            <el-icon  class="tool-icon" @click.stop="outputAll(item.data.outputs,1)"><Rank /></el-icon>
          </div>
        </div>
        <!-- <div class="output" v-html="outPutData(item.data.outputs)"></div> -->
        <div class="output">
              <vue-json-pretty  :data="item.data.outputs"  :deep="3"/>
        </div>
      </div>
    </div>
    <!-- 右侧迭代记录抽屉 -->
    <el-drawer  v-model="showChild" title="迭代记录" size="35%">
      <!-- 迭代节点数循环 -->
      <div class="resh"  v-for="item,index1 in reLengthData" @click="rightReshDown(index1)" v-if="reLength>0">
        <div  class="resh-left"  ><el-icon><Refresh /></el-icon><span style="margin-left: 10px;">迭代{{index1+1}}</span></div>
        <div class="resh-right">
          <!-- 展开/收起图标 -->
          <el-icon v-if="item.childup"><ArrowDown/></el-icon>
          <el-icon v-if="!item.childup"><ArrowRight /></el-icon>
        </div>
        <!-- 迭代子节点循环 -->
        <div class="resh-topT" v-if="item.childup" v-for="item,index in item.childlist" :key="index"  @click.stop="rightChildDown(index1,index)">
          <div class="resh-step" v-if="item.event='node_finished'">
            <el-icon class="step-left" v-if="item.isUp" ><ArrowDown /></el-icon>
            <el-icon class="step-left" v-if="!item.isUp"><ArrowRight /></el-icon>
            <div class="step-content">
              <div class="step-title">
                <el-icon  v-if="item.data.node_type=='start'" class="step-icon"><HomeFilled /></el-icon>
                <el-icon v-if="item.data.node_type!=='end' && item.data.node_type!=='start'" class="step-cone"><Document /></el-icon>
                <el-icon v-if="item.data.node_type=='end'"    class="step-finish"><Finished /></el-icon>
                <span class="">{{item.data.title}}</span>
              </div>         
            </div>
            <div class="step-right">
              <div class="step-description">{{ Number(item.data.elapsed_time).toFixed(2) }}s</div>
              <el-icon  class="step-suc"><Check /></el-icon>
            </div>
          </div>
          <!-- 迭代子节点数据处理 -->
          <div v-if="item.isUp && item.data.process_data && item.data.process_data!=='null'" class="content-resh">
            <div  class="input-tit">
              <div class="input-tit-text">数据处理</div>
              <div class="tool">
                <el-icon class="tool-icon" @click.stop="onCopy(item.data.process_data)"><DocumentCopy /></el-icon>
                <el-icon  class="tool-icon" @click.stop="outputAll(item.data.process_data,-1)"><Rank /></el-icon>
              </div>
            </div>
            <!-- <div  class="output-resh output" v-html="outPutData(item.data.process_data)"> </div> -->
            <div class="output-resh output">
              <vue-json-pretty  :data="item.data.process_data"  :deep="3"/>
            </div>
          </div>
          <!-- 迭代子节点输入 -->
          <div v-if="item.isUp && item.data.inputs && item.data.inputs!=='null'" class="content-resh">
            <div  class="input-tit">
              <div class="input-tit-text">输入</div>
              <div class="tool">
                <el-icon class="tool-icon" @click.stop="onCopy(item.data.inputs)"><DocumentCopy /></el-icon>
                <el-icon  class="tool-icon" @click.stop="outputAll(item.data.inputs,0)"><Rank /></el-icon>
              </div>
            </div>
            <!-- <div  class="output-resh output" v-html="outPutData(item.data.inputs)"></div> -->
            <div class="output-resh output">
              <vue-json-pretty  :data="item.data.inputs"  :deep="3"/>
            </div>
          </div>
          <!-- 迭代子节点输出 -->
          <div v-if="item.isUp && item.data.outputs  && item.data.outputs!=='null'" class="content-resh">
            <div class="input-tit">
              <div class="input-tit-text">输出</div>
              <div class="tool">
                <el-icon class="tool-icon" @click.stop="onCopy(item.data.outputs)"><DocumentCopy /></el-icon>
                <el-icon  class="tool-icon" @click.stop="outputAll(item.data.outputs,1)"><Rank /></el-icon>
              </div>
            </div>
            <!-- <div class="output-resh output" v-html="outPutData(item.data.outputs)"></div> -->
            <div class="output-resh output">
              <vue-json-pretty  :data="item.data.outputs"  :deep="3"/>
            </div>
          </div>
        </div>
      </div>
      
    </el-drawer>
    <el-drawer  v-model="showEdit1" :title="title" size="35%">
        <div class="content-all">
          <div class="input-all">
            <!-- <div>{{ title }}</div> -->
            <div class="tool">
              <el-icon class="tool-icon" @click.stop="onCopy(jsonData)"><DocumentCopy /></el-icon>
            </div>
          </div>
          <div class="out-All">
            <vue-json-pretty v-if="jsonData" :data="jsonData" :deep="3"/>
            </div>
        </div>
     
      </el-drawer>
  </el-drawer>

  <!-- 全部输入/输出内容弹窗 -->
  <!-- <DtPopup :show="showEdit1" @close="showEdit1 = false" width="100%" :title="title" :isFullscreen="true" :footer="false">
    <vue-json-pretty v-if="jsonData" :data="jsonData" :deep="3"/>
    <div class="dtpopup-footer-btns">
      <el-button
        type="primary"
        @click="onCopy(jsonData)"
        class="dt-btn-copy"
        :icon="DocumentCopy"
      >复制</el-button>
    </div>
  </DtPopup> -->
</template>

<script setup lang="ts">
import { HomeFilled, ArrowDown, ArrowUp, ZoomIn,CircleCheckFilled,ArrowRight,Document,Finished,Check,DocumentCopy,Rank,Refresh} from '@element-plus/icons-vue'
import type { SearchFormTemp, ToolListProps } from '@/components/types'
import PageTable from '@/components/PageTable.vue'
import { ref, reactive } from 'vue'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import useChannel from '@/stores/channel'
import { getFlowDetail } from '../api'
import { formatTimestamp } from '@/utils'
import tooltipWithFn from '@/views/dashboardManage/components/tooltipWithFn.vue'
import { micromark } from 'micromark'
import { gfmTable, gfmTableHtml } from 'micromark-extension-gfm-table'
  import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import copy from 'copy-text-to-clipboard'
// 控制执行记录抽屉显示
const showEdit = ref(false)
// 控制迭代记录抽屉显示
const showChild = ref(false)
// 弹窗标题
const title = ref(null)
// 控制"全部输出"弹窗显示
const showEdit1 = ref(false)
const showChild1= ref(false)
// 获取字典方法
const { getDic } = useUserStore()
// 获取字典项名称方法
const { getDicItemName } = useFilters()
// 渠道列表
const { channels } = useChannel()
// 表格ref
const flowRef = ref()
// 工作流主流程数据
const flwoData = ref<Record<string, any>[]>([])
// 详情数据
const detailData = ref<Record<string, any>[]>([])
// 迭代子节点数据
const childData= ref<Record<string, any>[]>([])
// 迭代开始节点数据
const iterationStart  = ref<Record<string, any>[]>([])
// 弹窗json数据
const jsonData = ref(null)
// 迭代次数
const reLength= ref(0)
// 迭代节点展开数据
const reLengthData= ref<Record<string, any>[]>([])
// 当前节点id
const nodeId = ref(null)

// 获取搜索参数
function getParams() {
  if (!flowRef.value) return {}
  const p = flowRef.value.getSearchQuery()
  //console.debug(p)
  return p
}

// 工具栏配置（可扩展）
const toolConfig: ToolListProps[] = [
]

// 字典数据
const dics = {
  userVoteResult: [
    { dicItemName: '全部', dicItemCode: '' },
    { dicItemName: '点踩', dicItemCode: '0' },
    { dicItemName: '点赞', dicItemCode: '1' },
  ],
  answerType: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.answerType')],
  answerSource: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.answerSource')],
  intention: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.intentionType')],
}

// 搜索表单配置
const temp: SearchFormTemp[] = reactive([
  {
    label: '来源渠道',
    name: 'channelCode',
    type: 'select',
    placeholder: '请选择',
    list: [{ dicItemCode: '', dicItemName: '全部' }, ...channels],
    searchState: true,
  },
  {
    label: '用户姓名',
    name: 'username',
    type: 'input',
    placeholder: '请输入内容',
    searchState: true,
  },
  {
    label: '会话ID',
    name: 'sessionId',
    type: 'input',
    placeholder: '请输入会话ID',
    searchState: true,
  },
  {
    label: '时间范围',
    type: 'doubleDate',
    searchState: true,
    doubleDate: {
      elType: 'DateTimePicker',
      startTime: { name: 'startDate', value: formatTimestamp('YYYY-MM-DD HH:mm:ss', getFirstDayOfMonth()) } as { name: string; value?: string },
      endTime: { name: 'endDate', value: formatTimestamp('YYYY-MM-DD HH:mm:ss', getEnd()) } as { name: string; value?: string },
    } as import('@/components/types').DoubleDate,
  },
])

// 获取本月第一天时间戳
function getFirstDayOfMonth() {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth();
  const firstDayOfMonth = new Date(year, month, 1).getTime();
  return firstDayOfMonth;
}

// 获取当天结束时间
function getEnd() {
  const now = new Date();
  const endOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59).getTime();
  return endOfToday;
}

// 获取自定义字典项名称
const getCustomDicitemName = (value: any, key: string) => {
  return dics[key]?.find((item: any) => item.dicItemCode === value)?.dicItemName
}

// 展开/收起主流程节点
const flwoDataDown = (index: any) => {
  reLength.value=0
  // 判断是否为迭代节点
  if(flwoData.value[index].event=='iteration_completed'){
    for(let i=0;i<iterationStart.value.length;i++){
      if(flwoData.value[index].data.node_id=iterationStart.value[i].data.node_id){
        reLength.value=iterationStart.value[i].data.metadata.iterator_length
        nodeId.value=iterationStart.value[i].data.node_id
      }
    }
  }
  let data=[]
  // 生成迭代节点展开数据
  if(reLength.value>0){
     for(let k=0;k<reLength.value;k++){
        data.push({
          childup:false,
          childlist:[],
          index:k
        })
      }
  }
  reLengthData.value= data
  flwoData.value[index].isUp=!flwoData.value[index].isUp
}

// 展开/收起某个迭代节点
const rightReshDown = (index: any) => {
  reLengthData.value[index].childup=!reLengthData.value[index].childup
  let data=[]
  // 筛选出属于该迭代的子节点
  detailData.value.forEach(item => {
    if(item.data.execution_metadata.iteration_index==index){
          data.push({
              ...item
            })
        }
    })
  childData.value=data
}

// 展开/收起某个迭代子节点
const rightChildDown = (index1: any,index:any) => {
  reLengthData.value[index1].childlist[index].isUp=!reLengthData.value[index1].childlist[index].isUp
}

// 关闭迭代抽屉
const handleClose = () => {
   showChild.value=false
}

// 加载迭代节点详情
const rightChild = async (itemData:any) => {
  let data= []
  // 获取迭代详情
  const res = await getFlowDetail({ workflowRunId: itemData.workflow_run_id,nodeId:itemData.data.node_id});
  if(res.data&&res.data.length>0 &&res.status==0){
    // 先清空所有childlist
    reLengthData.value.forEach(item => item.childlist = []);
    res.data.forEach(item => {
      // 找到对应的reLengthData
      const idx = item.data.execution_metadata?.iteration_index;
      if (typeof idx === 'number') {
        const target = reLengthData.value.find(d => d.index === idx);
        if (target) {
          target.childlist.push({
            ...item,
            isUp: false,
          });
        }
      }
    });
  }
  showChild.value=true
}

// const outputAll=(content:any,num:number)=> {
//   jsonData.value=null
//   if(!content) return
//   title.value = num === -1 ? "数据处理" : (num === 0 ? "输入" : "输出")
//    let jsonStr = content.replace(/([\u4e00-\u9fa5\w]+)\s*:\s*([^\n]+)/g, (match, key, value) => {
//     // value 里如果有引号要转义
//     value = value.replace(/"/g, '\\"');
//     return `"${key}": "${value.trim()}"`;
//   });
//   // 2. 用大括号包裹
//   jsonStr = `${jsonStr}`;
//   const formattedJSON = JSON.stringify(jsonStr, null, 2)
//   const str = JSON.parse(formattedJSON);
//   // 用正则给 key 加红色，value 加蓝色
//   const prettyJson = str.replace(
//     /"([^"]+)"\s*:\s*("[^"]*"|\d+|true|false|null|\[[\s\S]*?\])/g,
//     (match, key, value) => `<span style="color: #a31515">"${key}"</span>: <span style="color: #0451a5">${value}</span>`
//   ).replace(/\n/g, '<br>');
//   const prettyJson1 = prettyJson.replace(/{|}|,|$|$/g, match => match + '<br>');
//   jsonData.value=prettyJson1
//   showEdit1.value=true
// }
const outputAll=(content:any,num:number)=> {
  jsonData.value=null
  if(!content) return
  title.value = num === -1 ? "数据处理" : (num === 0 ? "输入" : "输出")
  let data = content
  if (typeof content === 'string') {
    try {
      data = JSON.parse(content)
    } catch (e) {
      // 不是合法 JSON 字符串，直接显示原内容
      data = content
    }
  }
  jsonData.value = data
  showEdit1.value = true
}

// 复制内容到剪贴板
const onCopy = (str) => {
  let text = str
  if (typeof str === 'object') {
    text = JSON.stringify(str, null, 2)
  }
  copy(text)
  ElMessage.success('复制成功')
}
// 格式化输出内容，关键value高亮
const outPutData=(content:any)=> {
  if(!content) return
  // 1. 每行的 key: value 转成 "key": "value"
  // 将对象转换为格式化的JSON字符串
  const jsonString = JSON.stringify(content, null, 2)
  // let jsonStr = content.replace(/([\u4e00-\u9fa5\w]+)\s*:\s*([^\n]+)/g, (match, key, value) => {
  //   // value 里如果有引号要转义
  //   value = value.replace(/"/g, '\\"');
  //   return `"${key}": "${value.trim()}"`;
  // });
  // // 2. 用大括号包裹
  // jsonStr = `${jsonStr}`;
  // const formattedJSON = JSON.stringify(jsonStr, null, 2);
  // const str= JSON.parse(formattedJSON);
  // 用正则给 key 加红色，value 加蓝色
  const prettyJson = jsonString.replace(
    /"([^"]+)"\s*:\s*("[^"]*"|\d+|true|false|null|\[[\s\S]*?\])/g,
    (match, key, value) => `<span style="color: #a31515">"${key}"</span>: <span style="color: #0451a5">${value}</span>`
  ).replace(/\n/g, '<br>');
  const prettyJson1 = prettyJson.replace(/{|}|,|$|$/g, match => match + '<br>');
  return prettyJson1
}


// 展开搜索表单
const spread = (value: any) => {
  for (let i = 0; i < temp.length; i++) {
    if (i > 3) {
      temp[i].searchState = value
    }
  }
}

// 收起搜索表单
const pack = (value: any) => {
  for (let i = 0; i < temp.length; i++) {
    if (i > 3) {
      temp[i].searchState = value
    }
  }
  flowRef.value
}

// 轮询获取工作流执行详情
const  checkWorkflowStatus=async(workflowRunId:any)=> {
  let data=[]
  let iterationStartData=[]
    try {
      const res = await getFlowDetail({ workflowRunId: workflowRunId});
       if(res.data&&res.data.length>0 &&res.status==0){
        res.data.forEach(item => {
          if(item.event === 'iteration_started'){//迭代节点
            iterationStartData.push({
              ...item
            });
          }
          if (item.event === 'node_finished' || item.event === 'iteration_completed') {//节点完成
            data.push({
              ...item,
              isUp:false,
            });
          } 
        });
        // 主流程数据
        flwoData.value= data
        // 迭代节点数据
        iterationStart.value = iterationStartData
        // 判断是否已完成
        const workfinished = res.data.find(u => u.event === "workflow_finished");
        if (workfinished) {
          showEdit.value = true
          //  工作流完成时的处理
          return;
        }
        // 未完成则继续轮询（间隔2秒）
        setTimeout(() => checkWorkflowStatus(workflowRunId), 2000);
      }else{
        flwoData.value=[]
        ElMessage.warning('暂无数据')
      }
    } catch (error) {
      console.error('Polling failed:', error);
      // 错误处理后可选择重试
    }
  }

// 点击"执行记录"按钮，拉取详情
const handleDetail = async (data: any) => {
    let workflowRunId=data.workflowRunId
    checkWorkflowStatus(workflowRunId)
}

// 格式化答案内容（富文本转md）
const formatAnwser = (row) => {
  let { robotAnswerType, answerContent } = row
  if (robotAnswerType == '18') {
    try {
      answerContent = JSON.parse(answerContent || "")[0].text
      const str = answerContent.replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      return str
    } catch (e) {
      return answerContent || ""
    }
  }
  return answerContent || ""
}

// 格式化图片答案
const formatAnwserImages = (row) => {
  let { robotAnswerType, answerContent } = row
  if (robotAnswerType == '18') {
    try {
      const data = JSON.parse(answerContent)[0]
      if (data && data.urls) {
        return data.urls
      }
      return []
    } catch (e) {
      return []
    }
  }
  return []
}

// 暴露search方法给父组件调用
defineExpose({
  search: () => {
    if (flowRef.value) flowRef.value.onSearchBtn()
  },
})
// ... existing code ...

// 自定义 JSON 样式
const customJsonStyle = {
  '--vjs-value-color': '#409eff',
  '--vjs-key-color': '#333',
  '--vjs-bracket-color': '#666',
  '--vjs-colon-color': '#666',
  '--vjs-comma-color': '#666'
}
</script>
<style lang="less">
.awidth {
  width: 100%;
  display: block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  left: -5px;
  right: -10px;
  cursor: pointer;
  &:hover {
    color: var(--el-color-primary);
  }
}
.awidthTooltip {
  white-space: wrap;
  display: inline-block;

  max-width: 700px;
  max-height: 400px;
  overflow-y: auto;
}

.answer-pop {
  position: relative;
  padding-bottom: 20px;

  &__copy {
    position: absolute;
    z-index: 1;

    right: 10px;
    bottom: 10px;
    cursor: pointer;
    &:active {
      opacity: 0.6;
    }
  }
}
.step {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 15px;
    width: 100%;
    max-width: 600px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position:relative;
}
.resh-topT{
  margin-top: 10px;;
}
.resh-topT .resh-step{
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 15px;
    width: 100%;
    max-width: 600px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position:relative;
}

.resh-topT .step-icon {
    color:  #D7A256;
    text-align: center;
    font-size: 10px;
    // margin-top: 5px;
}
.step-cone{
   color: rgb(0, 255, 234);
    text-align: center;
    // margin-top: 5px;
}
.step-finish{
   color: rgb(255, 208, 0);
    text-align: center;
    // margin-top: 5px;
}

.resh-topT  .step-content {
    // flex-grow: 1;
    display:flex;
    justify-content: center;
    padding:5px;
    font-size:12px;
}
.resh-topT  .step-title {
    font-size: 10px;
    color: #333;
    margin-left: 6px;
    display: flex;
    align-items: center;
}
.resh-topT  .step-title span {
    font-size: 10px;
    color: #D7A256;
    margin-left: 6px;
    display: flex;
    align-items: center;
}
.step .step-title {
    font-size: 14px;
    color: #333;
    margin-left: 6px;
    display: flex;
    align-items: center;
}
.step .step-title span {
    margin-left: 4px;
    // color: #D7A256;
    /* 强制为inline，防止换行 */
    display: inline;
}
.step-description {
    color: #666;
    font-size: 12px;
}
.step-left{
  // margin-left: 10px;
  // color: #D7A256;
  color:#909399;
}
.step-right{
  position: absolute;
  right: 10px;
  display: flex;
}
.input-tit{
  // display: flex;
  // justify-content: space-around;
  padding:10px 0px;
  position:relative;
  // padding-left: 15px;;
  .tool{
    position: absolute;
    right:0px;
    top:12px;
    .tool-icon{
      margin-left: 10px;
    }
  }
}
.content-input{
  background: #eaecf0;
  height: 320px;
  // overflow-y: scroll;
  width: 100%;
  // margin-top:10px;
  overflow: hidden; /* 水平方向隐藏 */
  white-space: wrap;
  display: inline-block;
  border: 1px solid #d0d7de;
  border-radius: 10px;
  // max-width: 600px;
  margin-bottom: 10px;
}

.content-resh{
  background: #eaecf0;
  height: 320px;
  // overflow-y: scroll;
  width: 100%;
  // margin-top:10px;
   overflow: hidden; /* 水平方向隐藏 */
   white-space: wrap;
  display: inline-block;
  border: 1px solid #d0d7de;
  border-radius: 10px;
  // max-width: 600px;
  margin-bottom: 10px;
}
.resh {
  background: #eaecf0;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
  margin-bottom: 20px;
  padding: 10px 24px;
  transition: box-shadow 0.2s, border-color 0.2s;
  border: 1px solid #eef0f4;
  position: relative;
  width: 100%;
  // max-width: 600px;
}
// .resh:hover, .resh.active {
//   box-shadow: 0 6px 20px 0 rgba(215,162,86,0.22);
//   // border-color: #D7A256;
// }
.resh-left{
  margin-left: 10px;
  font-size:13px;
  display: flex;
  align-items: center;
}
.resh-right{
  position:absolute;
  right:30px;
  top:13px;
}
.output {
  background: #f8fafc !important;
  color: #222;
  font-size: 14px;
  border-radius: 8px;
  min-height: 80px;
  max-height: 230px;
  margin-bottom: 10px;
  padding-bottom:10px;
  word-break: break-all;
  overflow-y: auto;   // 默认隐藏滚动条
  overflow-x: hidden; /* 水平方向隐藏 */
}
.outAll{
  width: 100%;
  display: block;
}
.output-resh{
  background: #eaecf0;
}
/* hover时显示滚动条 */
// .output:hover {
//   overflow-y: auto; /* 垂直滚动条 */
//   overflow-x: hidden; /* 水平方向隐藏 */
// }

/* 可选：自定义滚动条样式 */
// .output::-webkit-scrollbar {
//   width: 8px;
// }

.output::-webkit-scrollbar-thumb {
  // background: #D7A256;
  border-radius: 4px;
}

// .output::-webkit-scrollbar-thumb:hover {
//   background: #D7A256;
// }

/* 弹窗内容整体美化 */
.el-drawer__body {
  // background: #f7f8fa;
  min-height: 100vh;
  padding: 24px 24px 32px 24px;
  box-sizing: border-box;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  border-radius: 0 0 10px 10px;
  box-shadow: none;
  font-size: 13px;
}

/* 步骤条美化 */
.step, .resh-step {
  display: flex;
  align-items: center;
  background: #FFF6E8;
  border: 1px solid #f0e6d6;
  border-radius: 10px;
  box-shadow: none;
  margin-bottom: 14px;
  width: 100%;
  max-width: 700px;
  transition: border 0.2s;
  cursor: pointer;
  padding: 0 16px;
  min-height: 36px;
  position: relative;
  font-size: 13px;
}
.step:hover, .resh-step:hover {
  border-left: 3px solid #D7A256;
  background: #FFF6E8;
}

/* 步骤内容区 */
.step-content, .step-title {
  font-size: 14px;
  color: #222;
  display: flex;
  align-items: center;
}

/* 步骤标题高亮 */
.step-title span {
  margin-left: 6px;
  color: #D7A256;
  font-size: 14px;
}

/* 步骤右侧描述 */
.step-right {
  align-items: center;
  position: absolute;
  right: 18px;
  display: flex;
  height: 100%;
}

/* 步骤描述文字 */
.step-description {
  color: #888;
  font-size: 12px;
}

/* 图标统一风格 */
.step-icon, .step-cone, .step-finish, .step-suc, .tool-icon {
  font-size: 14px !important;
  vertical-align: middle;
}
.step-icon { color: #D7A256; }
.step-cone { color: #D7A256; }
.step-finish { color: #D7A256; }
.step-suc { background: #D7A256; color: #fff; border-radius: 50%; width:14px; height: 14px;padding:2px; text-align: center; font-size: 14px; margin-left: 15px; }

/* 让内容区块更有呼吸感 */
.content-input, .content-resh {
  background: #f8fafc;
  border: 1.5px solid #ffe7ba;
  border-radius: 10px;
  margin-bottom: 14px;
  padding: 18px 30px 14px 30px;
  min-height: 120px;
  max-height: 320px;
  font-size: 14px;
  color: #333;
  box-shadow: 0 2px 8px 0 rgba(215,162,86,0.04);
  transition: border 0.2s;
  padding-bottom: 30px;
}
.content-input:hover, .content-resh:hover,.content-all:hover {
  // border: 1px solid #D7A256;
  // background: #FFF6E8;
}
.content-input{
  background: #eaecf0;
  max-height: 320px;
  width: 100%;
  white-space: wrap;
  display: inline-block;
  border: 1px solid #d0d7de;
  border-radius: 10px;
  margin-bottom: 10px;
  padding-bottom: 50px;
}
/* 输入输出标题 */
.input-tit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #222;
  margin-bottom: 8px;
}
.input-tit-text{
  font-size: 13px;
  // font-weight: 600;
  color: #222;
}
.input-tit .tool {
  display: flex;
  gap: 8px;
}
.input-tit .tool-icon {
  color: #888;
  cursor: pointer;
  transition: color 0.2s;
}
.input-tit .tool-icon:hover {
  color: #D7A256;
}


.resh-topT {
  // background-color: #fff9f0; /* 新的、不会冲突的背景色 */
  // border-radius: 8px;
  // margin: 16px -16px -8px -16px; /* 负外边距使其与卡片边缘对齐 */
  // padding: 16px;
  // border-top: 1.5px solid #ffe7ba; /* 顶部细分隔线 */
}

/* 滚动条美化 */
// .output, .output-resh, .content-input, .content-resh {
//   scrollbar-width: thin;
//   scrollbar-color: #b3c0d1 #f4f8fb;
// }
// .output::-webkit-scrollbar, .output-resh::-webkit-scrollbar,
// .content-input::-webkit-scrollbar, .content-resh::-webkit-scrollbar {
//   width: 8px;
// }
// .output::-webkit-scrollbar-thumb, .output-resh::-webkit-scrollbar-thumb,
// .content-input::-webkit-scrollbar-thumb, .content-resh::-webkit-scrollbar-thumb {
//   // background: #D7A256;
//   border-radius: 4px;
// }
// .output::-webkit-scrollbar-thumb:hover, .output-resh::-webkit-scrollbar-thumb:hover,
// .content-input::-webkit-scrollbar-thumb:hover, .content-resh::-webkit-scrollbar-thumb:hover {
//   // background: #D7A256;
// }

/* 弹窗标题美化 */
.el-drawer__header {
  // background:#FFF6E8;
  // border-bottom: 1px solid #797c81;
  box-shadow: 0 2px 8px 0 rgba(215,162,86,0.10);
  // border-radius: 0 0 18px 18px;
  padding: 10px 42px 10px 30px !important;
  min-height: unset;
  display: flex;
  align-items: center;
  // padding-bottom: 0 !important;
  margin-bottom: 0 !important;

  .drawer-title, .el-drawer__title {
    font-size:15px;
    // color:#D7A256;
    color:#888;
    letter-spacing: 1.5px;
    line-height: 1.2;
    margin: 0;
    padding: 0;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.drawer-title-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 32px;
}

.drawer-title {
  font-size: 22px;
  font-weight: bold;
  color: #b47a1a; /* 更深的金色，和背景区分 */
  letter-spacing: 1px;
  line-height: 1;
}



/* DtPopup 弹窗内容美化 */
.DtPopup .el-dialog {
  width: 520px !important;      /* 更窄，和 el-drawer 小号一致 */
  max-width: 90vw;
  min-width: 320px;
  right: 0;
  left: auto;
  margin: 0;
  position: fixed;
  top: 0;
  height: 100vh;
  border-radius: 0;
  box-shadow: -4px 0 32px 0 rgba(215,162,86,0.13);
  background: #FFF6E8;
  display: flex;
  flex-direction: column;
  transition: none;
}
.DtPopup .el-dialog__header {
  // background: linear-gradient(90deg, #ffe7ba 0%, #ffd591 80%, #FFF6E8 100%);
  border-bottom: 1px solid #D7A256;
  color: #b47a1a;
  font-size: 22px;
  font-weight: bold;
  padding: 20px 32px 12px 32px;
  letter-spacing: 1px;
  border-radius: 0;
}
.DtPopup .el-dialog__body {
  flex: 1 1 auto;
  overflow-y: auto;
  border-radius: 0;
  background: linear-gradient(135deg, #fdf6ec 0%, #f7f8fa 100%);
  padding: 32px 40px 32px 40px;
}

.drawer-footer-btns {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 24px 40px 24px 40px;
  background: transparent;
  margin-top: 16px;
  border-top: 1px solid #f0e6d6;
}

.dt-btn-back {
  background: #FFF6E8;
  color: #b47a1a;
  border: 1.5px solid #ffd591;
  border-radius: 8px;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
}
.dt-btn-back:hover {
  background: #ffe7ba;
  color:#D7A256;
}

.dt-btn-copy {
  background: linear-gradient(90deg, #ffd591 0%, #ffe7ba 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(215,162,86,0.10);
  transition: background 0.2s, color 0.2s;
}
.dt-btn-copy:hover {
  background: linear-gradient(90deg, #D7A256 0%, #ffd591 100%);
  color: #fff;
}

.el-drawer__title, .drawer-title {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* 适配 vjs-tree 背景色和主色 */
.vjs-tree,
.vue-json-pretty {
  background: #f8fafc !important; /* 与内容区主色区分，偏浅灰 */
  border-radius: 8px;
  padding: 12px 16px;
  /* 可选：加点阴影让区块更有层次 */
  box-shadow: 0 2px 8px 0 rgba(215,162,86,0.04);
}

/* key、value 颜色可根据主色微调 */
.vjs-key {
  white-space: nowrap;
}
.vjs-value {
  color: #0451a5 !important; /* 蓝色，和主色区分 */
}
.vjs-tree .vjs-tree__brackets {
  color: #D7A256 !important; /* 括号用金色点缀 */
}
.content-all {
  background: #eaecf0;
  border: 1px solid #d0d7de;
  border-radius: 10px;
  margin-bottom: 30px;
  // padding: 18px 22px 14px 22px;
  box-shadow: 0 2px 8px 0 rgba(215,162,86,0.04);
  width: 100%;
  // height: 100%;
  // max-height: 100%;
  display: flex;
  flex-direction: column;
}
.input-all {
  padding:10px;
  position:relative;
  padding-top:40px;
  // padding-left: 15px;;
  .tool{
    position: absolute;
    right: 10px;
    top:12px;
    .tool-icon{
      margin-right: 10px;
    }
  }
}
.input-all .tool-icon:hover {
  color: #D7A256;
}
</style>
<style>
.vjs-value {
  color: #0451a5; /* 修改为你想要的颜色值 */
}
.vjs-key {
  color: #a31515 !important;  /* 黄色 */
}
</style>
