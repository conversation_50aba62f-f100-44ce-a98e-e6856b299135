<template>
  <!-- <DtNav from="数据看板" name="数据看板"></DtNav> -->
  <el-tabs v-model="versionIndex" type="border-card" class="tabs" @tab-change="versionSelect">
    <el-tab-pane :name="0" label="接待概况" v-auth="'chatbot:board:recepTab:show'">
      <SearchForm
        ref="recepSearchRef"
        :searchFormTemp="receptionSearchParams"
        @handleSearch="setReceptionParam"
        @handleResetQuery="receptionResetSearch"
      >
        <template #btns>
          <el-button v-auth="'chatbot:board:recepTab:export'" type="primary" plain class="!bg-white hover:!text-dt !text-dt" @click="exportTb"
            >导出</el-button
          >
        </template>
      </SearchForm>
      <el-table :data="receptionData" stripe class="dt-table">
        <el-table-column prop="date" label="日期" align="center" width="140"> </el-table-column>
        <el-table-column prop="receptionNum" label="接待人次" align="center" >
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="一个session记为一人次">
              <template #reference>
                <div class="tb-header">
                  <span>接待人次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="totalRound" label="对话轮次" align="center" >
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="一问一答记为一轮">
              <template #reference>
                <div class="tb-header">
                  <span>对话轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="averageTotalRound" label="平均对话轮次" align="center" width="130">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="对话轮次/接待人次">
              <template #reference>
                <div class="tb-header">
                  <span>平均对话轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="resolveNum" label="解决量" align="center" >
          <template #header>
            <el-popover
              trigger="hover"
              width="300"
              placement="top-start"
              content="排除转人工、点踩的对话轮次"
            >
              <template #reference>
                <div class="tb-header">
                  <span>解决量</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="resolvePercent" label="解决率" align="center" >
          <template #default="scope"> {{ scope.row.resolvePercent }}% </template>
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="解决量/对话轮次">
              <template #reference>
                <div class="tb-header">
                  <span>解决率</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="knowledgeRound" label="知识对话轮次" align="center" width="135">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="知识库答案回答的轮次">
              <template #reference>
                <div class="tb-header">
                  <span>知识对话轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="chatRound" label="闲聊对话轮次" align="center" width="135">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="闲聊回答的轮次">
              <template #reference>
                <div class="tb-header">
                  <span>闲聊对话轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="manMachineRound" label="人机对练轮次" align="center" width="135">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="人机对练轮次">
              <template #reference>
                <div class="tb-header">
                  <span>人机对练轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="productAnalyzeRound" label="产品解读轮次" align="center" width="135">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="产品解读轮次">
              <template #reference>
                <div class="tb-header">
                  <span>产品解读轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="chatRound" label="澄清对话轮次" align="center" width="135">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="澄清回答的轮次">
              <template #reference>
                <div class="tb-header">
                  <span>澄清对话轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="chatRound" label="澄清点击量" align="center" width="135">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="澄清点击量">
              <template #reference>
                <div class="tb-header">
                  <span>澄清点击量</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="chatRound" label="澄清点击率" align="center" width="135">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="澄清点击量/澄清对话轮次">
              <template #reference>
                <div class="tb-header">
                  <span>澄清点击率</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="chatRound" label="无答案对话轮次" align="center" width="135">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="机器人无回复的轮次">
              <template #reference>
                <div class="tb-header">
                  <span>无答案对话轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column> -->
        <el-table-column prop="transformCount" label="转人工人次" align="center" width="130">
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="转人工的session量">
              <template #reference>
                <div class="tb-header">
                  <span>转人工人次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="transformPercent" label="转人工率" align="center" width="130">
          <template #default="scope"> {{ scope.row.transformPercent }}% </template>
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="转人工人次/接待人次">
              <template #reference>
                <div class="tb-header">
                  <span>转人工率</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="upVoteRoundCount" label="点赞轮次" align="center"  >
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="被点赞的对话轮次">
              <template #reference>
                <div class="tb-header">
                  <span>点赞轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="upVoteRoundPercent" label="点赞率" align="center" >
          <template #default="scope"> {{ scope.row.upVoteRoundPercent }}% </template>
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="点赞轮次/对话轮次">
              <template #reference>
                <div class="tb-header">
                  <span>点赞率</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="downVoteRoundCount" label="点踩轮次" align="center" >
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="被点踩的对话轮次">
              <template #reference>
                <div class="tb-header">
                  <span>点踩轮次</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="downVoteRoundPercent" label="点踩率" align="center" >
          <template #default="scope"> {{ scope.row.downVoteRoundPercent }}% </template>
          <template #header>
            <el-popover trigger="hover" placement="top-start" content="点踩轮次/知识对话轮次">
              <template #reference>
                <div class="tb-header">
                  <span>点踩率</span>
                  <el-icon class="tb-header__icon"><QuestionFilled /></el-icon>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
      <Pagination ref="receptionRef" @change="receptionChange" :total="receptionTotal" />
    </el-tab-pane>
    <el-tab-pane :name="1" label="知识分析" v-if="false"> 敬请期待 </el-tab-pane>
    <el-tab-pane :name="2" label="问题分析" v-if="false"> 敬请期待 </el-tab-pane>

    <el-tab-pane :name="4" label="对话分析" v-auth="'chatbot:board:analysis:show'">
      <dialogAnalysis ref="dialogAnalysisRef" />
    </el-tab-pane>
<!--    <el-tab-pane :name="5" label="工作流列表" v-auth="'chatbot:board:analysis:show'">-->
<!--      <flowList ref="flowRef" />-->
<!--    </el-tab-pane>-->
  </el-tabs>

  <!-- 知识库iframe -->
  <div class="iframe-dialog" v-if="showKnowledgeIframe">
    <DtIcon icon-name="icondt25" class="popup-icon" @click="closeKnowledgeIframe" />
    <iframe :src="iframeSrc" frameborder="0" height="100%" width="100%"></iframe>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import type { SearchFormTemp } from '@/components/types'
import { receptionList, pageRecord, searchRobotPage, searchChannelPage, getFuncId } from './api'
import { onMounted } from 'vue'
import useFilters from '@/hooks/useFilters'
import useSystemStore from '@/stores/system'
import dialogAnalysis from './components/dialogAnalysis.vue'
import flowList from './components/flowList.vue'
import { httpServe } from '@/utils/httpServe'
import { exportData } from '@/utils'

const dialogAnalysisRef = ref()
const flowRef = ref()
const recepSearchRef = ref()
const systemStore = useSystemStore()
const { getDicItemName } = useFilters()
const versionIndex = ref<number>(0)
const versionSelect = async (index: number) => {
  versionIndex.value = index
  if (index === 0) {
    initReception()
  } else if (index === 1) {
    initKnowledge()
  } else if (index === 2) {
    initQuestion()
  } else if (index === 3) {
    initHistory()
  } else if (index === 4) {
    dialogAnalysisRef.value.search()
  } else if (index === 5) {
    flowRef.value.search()
  }
}
const receptionData = ref<Record<string, any>[]>([])
const historyData = ref<Record<string, any>[]>([])
const receptionSearchParams: SearchFormTemp[] = [
  {
label: '聊天智能体',
name: 'robotCode',
type: 'select',
placeholder: '请选择',
list: [],
searchState: true
},
  {
label: '来源渠道',
name: 'channelCode',
type: 'select',
placeholder: '请选择',
searchState: true
},
  {
label: '',
name: 'name',
type: 'doubleDate',
doubleDate: {
elType: 'DatePicker',
startTime: {
name: 'startDate',
value: '',
},
endTime: {
name: 'endDate',
value: '',
},
},
searchState: true
},
]
const historySearchParams: SearchFormTemp[] = [
  {
label: '提问内容',
name: 'content',
type: 'input',
placeholder: '请输入内容',
searchState: true
},
  {
label: '会话Id',
name: 'sessionId',
type: 'input',
placeholder: '请输入会话Id',
searchState: true
},
]
const receptionTotal = ref<number>(0)
const historyTotal = ref<number>(0)
const receptionParams = ref<Record<string, any>>({
  pageNum: 1,
  pageSize: 10,
  param: {},
})
const historyParams = ref<Record<string, any>>({
  pageNum: 1,
  pageSize: 10,
  param: {},
})

const receptionRef = ref(null)
const historyRef = ref(null)

const iframeSrc = ref<string>('')
const showKnowledgeIframe = ref<boolean>(false)
const toDetail = async (row: Record<string, any>) => {
  const res = await getFuncId()
  const url = `${import.meta.env.VITE_KBC_PATH}/km-web/lookKnowledge?from=iframe&readOnly=true&id=${row.relatedArticles}&access_token=${
    systemStore.system.access_token
  }&tenantId=${systemStore.system.tenantId}&funcId=${res.data.paramValue}&themeColor=${systemStore.system.themeColor}&navTagColor=${
    systemStore.system.navTagColor
  }`
  console.log(url)
  iframeSrc.value = url
  showKnowledgeIframe.value = true
}

const closeKnowledgeIframe = () => {
  showKnowledgeIframe.value = false
}

const setReceptionParam = (param: Record<string, any>) => {
  receptionParams.value.param = param
  initReception()
}

const receptionResetSearch = async (data: any) => {
  receptionParams.value.param = data
  await receptionRef.value.normalReset()
  initReception()
}

const receptionChange = (val: Record<string, any>) => {
  receptionParams.value.pageNum = val.pageNum
  receptionParams.value.pageSize = val.pageSize
  initReception()
}

const setHistoryParam = (param: Record<string, any>) => {
  historyParams.value.param = param
  initHistory()
}

const historyChange = (val: Record<string, any>) => {
  historyParams.value.pageNum = val.pageNum
  historyParams.value.pageSize = val.pageSize
  initHistory()
}

const historyResetSearch = async (data: any) => {
  historyParams.value.param = data
  await historyRef.value.normalReset()
  initHistory()
}

const initReception = async () => {
  const res = await receptionList(receptionParams.value)
  receptionData.value = res.data.list
  receptionTotal.value = res.data.total
  initSearchValue()
}

const initKnowledge = () => {
  console.log('initKnowledge')
}

const initQuestion = () => {
  console.log('initQuestion')
}

const initHistory = async () => {
  const res = await pageRecord(historyParams.value)
  historyData.value = res.data.list
  historyTotal.value = res.data.total
}

const initSearchValue = async () => {
  const robotIdList = await searchRobotPage({ pageNum: 1, pageSize: 999999, param: {} })
  console.log(robotIdList)
  receptionSearchParams[0].list = robotIdList.data.map((item: any) => {
    return {
      dicItemName: item.robotName,
      dicItemCode: item.robotCode,
    }
  })
  const channelIdList = await searchChannelPage({ pageNum: 1, pageSize: 999999, param: {} })
  console.log(channelIdList)
  receptionSearchParams[1].list = channelIdList.data.list.map((item: any) => {
    return {
      dicItemName: item.channelName,
      dicItemCode: item.channelCode,
    }
  })
}

const exportTb = async () => {
  const res = await httpServe(
    '/web/databoard/export',
    {
      ...recepSearchRef.value.getSearchQuery(),
    },
    {
    isExport: true,
    method: 'post',
    customPrefix: ''
}
  )
  if (res.status === 0) {
    exportData(res.data, res.fileName)
  }
}

onMounted(async () => {
  initReception()
})
</script>

<style lang="less" scoped>
.tb-header {
  display: flex;
  align-items: center;
  justify-content: center;
  &__icon {
    margin-left: 5px;
  }
}
.tabs {
  border-bottom: none;
}

.iframe-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 99999;

  .popup-icon {
    position: absolute;
    font-size: 65px;
    top: 7px;
    right: -61px;
    color: #b8b8b8;
    cursor: pointer;
  }
}
</style>
