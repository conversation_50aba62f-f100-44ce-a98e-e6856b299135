import { useAxios } from '@/hooks/http'

const baseURL = import.meta.env.VITE_APP_MASS_API

// 查询项目列表
export const qaProjectPage = async (data: any) => await useAxios("/web/maas/project/list", data);

//查询可用项目列表
//export const qaProjectList = async (data: any) => await useAxios("/bs/project/list", data, {
//  baseURL
//});

// 新增项目
export const qaProjectCreate = async (data: any) => await useAxios("web/maas/project/create", data);

// 编辑项目
export const qaProjectUpdate = async (data: any) => await useAxios("/web/maas/project/update", data);

// 更新启用项目状态
export const qaProjectUpdateStatus = async (data: any) => await useAxios("/web/maas/project/updateStatus", data);

// 删除项目
export const qaProjectDelete = async (data: any) => await useAxios("/web/maas/project/delete", data);
