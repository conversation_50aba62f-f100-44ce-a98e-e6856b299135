<template>
  <TableToolTemp :toolTitle="pageTitle" :toolList="toolList" />

  <PageTable ref="pageRef" :search-form-temp="searchFormTemp" apiUrl="/web/maas/project/page">
    <template #default="{ tableData }">
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column prop="code" label="项目编码" align="center" width="100" />
        <el-table-column prop="name" label="项目名称" align="center" />
        <el-table-column prop="secretKey" label="项目密钥" align="center" />
        <el-table-column prop="status" label="状态" align="center" width="80px">
          <template #default="scope">
            <!--
            <span v-if="scope.row.status == '0'">启用</span>
            <span v-else>禁用</span>
            -->

            <el-switch active-value="1" inactive-value="0" @click.native="toChangeStatus(scope.row.status, scope.row.id)" v-model="scope.row.status">
            </el-switch>
          </template>
        </el-table-column>

        <el-table-column prop="updateBy" label="更新人" align="center" min-width="100px" />
        <el-table-column prop="updateTime" label="更新时间" align="center" width="170" />
        <el-table-column label="操作" align="center" width="100px">
          <template #default="scope">
            <el-button type="text" v-auth="'chatbot:project:edit'" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" v-auth="'chatbot:project:del'" @click="handleDelete(scope.row)">删除</el-button>

            <!--
            <el-button v-if="scope.row.status == '1'" type="text" @click="handleOn(scope.row)">启用</el-button>
            <el-button v-else type="text" @click="handleOff(scope.row)">禁用</el-button>
-->
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>

  <!--新增弹窗-->
  <DtPopup v-model:show="showEdit" :title="title" @close="closePopup" :footer="false">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="right" label-width="80px" inline>
      <el-form-item label="项目名称" prop="name">
        <el-input v-model="form.name" auto-complete="off" placeholder="请输入项目名称"></el-input>
      </el-form-item>
      <el-form-item label="项目编码" prop="code">
        <el-input v-model="form.code" auto-complete="off" placeholder="请输入项目编码"></el-input>
      </el-form-item>
      <el-form-item label="项目秘钥" prop="secretKey">
        <el-input v-model="form.secretKey" auto-complete="off" placeholder="请输入项目秘钥"></el-input>
      </el-form-item>
      <el-form-item label="是否启用" prop="status">
        <el-select v-model="form.status">
          <el-option label="启用" value="1"></el-option>
          <el-option label="禁用" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用" prop="id" class="hidden"> </el-form-item>
    </el-form>

    <div class="popup-footer">
      <el-button size="large" @click="showEdit = false" class="dt-btn" plain :style="{ color: 'var(--el-color-primary)' }">取消</el-button>
      <el-button type="primary" size="large" @click="toSaveOrUpdate" class="dt-btn">保存</el-button>
    </div>
  </DtPopup>
</template>

<script setup lang="ts">
import * as api from './api'

import type { ToolListProps, SearchFormTemp } from '@/components/types'
import { resolve4 } from 'dns'
import type { FormInstance } from 'element-plus'
import { ref, reactive } from 'vue'

const formRef = ref<FormInstance>()
const pageRef = ref(null)
let title = ''
const dialogFormVisible = ref(false)
const showEdit = ref(false)
const currentPopType = ref<string>('add') // add or edit

const pageTitle = '项目列表'
const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: 'chatbot:project:add',
    action: async () => {
      currentPopType.value = 'add'
      showEdit.value = true
    },
  },
]

const rules = {
  name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入项目编码', trigger: 'blur' }],
  secretKey: [{ required: true, message: '请输入项目密钥', trigger: 'blur' }],
}
const searchFormTemp: SearchFormTemp[] = [
  {
label: '项目名称',
name: 'name',
placeholder: '请输入名称',
type: 'input',
searchState: true
},
  {
label: '项目编码',
name: 'code',
placeholder: '请输入项目编码',
type: 'input',
searchState: true
},
  // {
  //   lable: "启用状态",
  //   name: "status",
  //   type: "select",
  //   placeholder: "请选择同步状态",
  //   list: [
  //     { dicItemCode: "", dicItemName: "全部" },
  //     { dicItemCode: "0", dicItemName: "上架" },
  //     { dicItemCode: "1", dicItemName: "下架" }
  //   ]
  // }
]

const form = reactive({
  name: '',
  code: '',
  secretKey: '',
  status: '0',
  id: '',
})

/***************************** methods *****************************/

function reset() {
  form.name = ''
  form.code = ''
  form.status = '0'
  form.secretKey = ''
  form.id = ''
}

function handleEdit(row: any) {
  form.name = row.name
  form.code = row.code
  form.secretKey = row.secretKey
  form.status = row.status
  form.id = row.id

  currentPopType.value = 'edit'
  showEdit.value = true
  title = '编辑项目'
}

function handleDelete(row: any) {
  ElMessageBox.confirm(`是否删除？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await api.qaProjectDelete({ id: row.id })
    if (res.status == 0) {
      ElMessage.success('删除成功!')
      dialogFormVisible.value = false

      pageRef.value && pageRef.value.onSearchBtn()
    }
  })
}

function toChangeStatus(status: String, id: String) {
  const typeName = status == '1' ? '启用' : '禁用'

  ElMessageBox.confirm(`是否${typeName}?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res = await api.qaProjectUpdateStatus({
        id,
        status,
      })
      if (res.status == 0) {
        ElMessage.success(typeName + '成功!')
        pageRef.value && pageRef.value.onSearchBtn()
      }
    })
    .catch(() => {
      const tmp = pageRef.value.tableData.find((item: any) => item.id === id)
      if (tmp) {
        tmp.status = tmp.status == '0' ? '1' : '0'
      }
    })
}

function closePopup() {
  try {
    formRef.value && formRef.value.resetFields()
    formRef.value && formRef.value.clearValidate()
    reset()

    //console.log(formRef.value)
    showEdit.value = false
  } catch (e) {
    console.error(e)
  }
}

async function toSaveOrUpdate() {
  const flag = formRef.value && (await formRef.value.validate())
  if (!flag) return
  if (currentPopType.value === 'add') {
    const res = await api.qaProjectCreate(form)
    if (res.status == 0) {
      closePopup()
      pageRef.value && pageRef.value.onSearchBtn()
    }
    return
  }

  const res = await api.qaProjectUpdate(form)
  if (res.status == 0) {
    closePopup()
    pageRef.value && pageRef.value.onSearchBtn()
  }
}
</script>

<style lang="less" scoped>
.log-tool {
  margin-bottom: 10px;
}
.popup-footer {
  display: flex;
  justify-content: center;
}
.hidden {
  display: none;
}
</style>
