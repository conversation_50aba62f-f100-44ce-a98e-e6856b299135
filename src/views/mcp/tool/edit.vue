<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getToolDetail, getToolDetailByCode, createTool, updateTool } from '@/api/mcp/tool'
import { getServerList } from '@/api/mcp/server'
import { getAppServerList } from '@/api/mcp/appserver'

export default {
  name: 'McpToolEdit',
  components: {
    EditPageContainer,
    UniversalForm
  },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      serverOptions: [],
      appServerOptions: [],
      form: {
        toolCode: '',
        toolName: '',
        serverId: null,
        appServerId: null,
        apiPath: '',
        toolType: '',
        toolStatus: 1,
        version: '1.0.0',
        toolDescription: '',
        inputSchema: '',
        outputSchema: '',
        createTime: '',
        updateTime: '',
        createUser: '',
         updateUser: ''
       },
       formGroups: [
         {
           title: '基本信息',
           icon: 'el-icon-setting',
           fields: [
             [
               {
                 prop: 'toolName',
                 label: '工具名称',
                 type: 'input',
                 placeholder: '请输入工具名称',
                 maxlength: 100,
                 showWordLimit: true
               },
               {
                 prop: 'serverId',
                 label: '所属服务器',
                 type: 'select',
                 placeholder: '请选择所属服务器',
                 options: []
               }
             ],
             [
               {
                 prop: 'appServerId',
                 label: '应用服务器',
                 type: 'select',
                 placeholder: '请选择应用服务器',
                 options: []
               },
               {
                 prop: 'apiPath',
                 label: '接口路径',
                 type: 'input',
                 placeholder: '请输入接口路径，如：/api/v1/tools',
                 maxlength: 500,
                 showWordLimit: true
               }
             ],
             [
               {
                 prop: 'toolType',
                 label: '工具类型',
                 type: 'input',
                 placeholder: '请输入工具类型',
                 maxlength: 50,
                 showWordLimit: true
               },
               {
                 prop: 'toolStatus',
                 label: '工具状态',
                 type: 'radio',
                 options: [
                   { label: '启用', value: 1 },
                   { label: '禁用', value: 0 }
                 ]
               }
             ],
             [
               {
                 prop: 'version',
                 label: '版本号',
                 type: 'input',
                 placeholder: '请输入版本号',
                 maxlength: 20,
                 showWordLimit: true
               }
             ],
             [
               {
                 prop: 'toolDescription',
                 label: '描述信息',
                 type: 'textarea',
                 placeholder: '请输入工具描述信息',
                 maxlength: 500,
                 rows: 4,
                 showWordLimit: true,
                 span: 24
               }
             ]
           ]
         },
         {
           title: '参数配置',
           icon: 'el-icon-document',
           fields: [
             [
               {
                 prop: 'inputSchema',
                 label: '输入参数结构',
                 type: 'textarea',
                 placeholder: '请输入JSON格式的输入参数结构',
                 rows: 6,
                 span: 24
               }
             ],
             [
               {
                 prop: 'outputSchema',
                 label: '输出参数结构',
                 type: 'textarea',
                 placeholder: '请输入JSON格式的输出参数结构',
                 rows: 6,
                 span: 24
               }
             ]
           ]
         }
       ],
       formRules: {
         toolName: [
           { required: true, message: '请输入工具名称', trigger: 'blur' },
           { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
           { validator: this.validateToolName, trigger: 'blur' }
         ],
         serverId: [
           { required: true, message: '请选择所属服务器', trigger: 'change' }
         ],
         appServerId: [
           { required: true, message: '请选择应用服务器', trigger: 'change' }
         ],
         apiPath: [
           { required: true, message: '请输入接口路径', trigger: 'blur' },
           { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
         ],
         toolType: [
           { required: true, message: '请输入工具类型', trigger: 'blur' },
           { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
         ],
         toolStatus: [
           { required: true, message: '请选择工具状态', trigger: 'change' }
         ],
         version: [
           { required: true, message: '请输入版本号', trigger: 'blur' },
           { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
         ],
         toolDescription: [
           { max: 500, message: '描述信息不能超过 500 个字符', trigger: 'blur' }
         ],
         inputSchema: [
           { validator: this.validateJsonSchema, trigger: 'blur' }
         ],
         outputSchema: [
           { validator: this.validateJsonSchema, trigger: 'blur' }
         ]
       }
     }
   },
   computed: {
     pageTitle() {
       if (this.isView) {
         return '查看工具'
       }
       return this.isEdit ? '编辑工具' : '新增工具'
     },
     pageIcon() {
       return 'el-icon-setting'
     },
     breadcrumbItems() {
       const items = [
         { text: '工具管理', to: { name: 'Tool' } }
       ]
       
       if (this.isView) {
         items.push({ text: '工具详情', to: null })
       } else if (this.isEdit) {
         items.push({ text: '编辑工具', to: null })
       } else {
         items.push({ text: '新增工具', to: null })
       }
       
       return items
     }
   },
   async created() {
     await this.initPage()
   },
   methods: {
     async initPage() {
       const { id } = this.$route.params
       const { mode } = this.$route.query
       
       // 支持查看模式：当mode=view或路由名称包含detail时进入查看模式
       this.isView = mode === 'view' || this.$route.name === 'ToolDetail'
       this.isEdit = !!id && !this.isView
       
       // 加载服务器选项
       await this.loadServerOptions()
       // 加载应用服务器选项
       await this.loadAppServerOptions()
       
       if (id) {
         await this.loadToolDetail(id)
       }
     },

     async loadServerOptions() {
       try {
         const response = await getServerList({})
         if (response && response.length > 0) {
           this.serverOptions = response.map(server => ({
             label: server.serverName,
             value: server.serverId
           }))
           // 更新formGroups中的服务器选项
           const serverField = this.formGroups[0].fields[0][1]
           serverField.options = this.serverOptions
         }
       } catch (error) {
         console.error('加载服务器列表失败:', error)
       }
     },

     async loadAppServerOptions() {
       try {
         const response = await getAppServerList({})
         if (response && response.length > 0) {
           this.appServerOptions = response.map(appServer => ({
             label: appServer.appServerName,
             value: appServer.appServerId
           }))
           // 更新formGroups中的应用服务器选项
           const appServerField = this.formGroups[0].fields[1][0]
           appServerField.options = this.appServerOptions
         }
       } catch (error) {
         console.error('加载应用服务器列表失败:', error)
       }
     },

     async loadToolDetail(id) {
        this.loading = true
        try {
          // 优先尝试通过编码查询，如果失败则通过ID查询
          let response
          try {
            response = await getToolDetailByCode(id)
          } catch (codeError) {
            response = await getToolDetail(id)
          }
          
          if (response) {
            this.form = {
              ...this.form,
              ...response
            }
          } else {
            this.$message.error('加载工具详情失败')
          }
        } catch (error) {
          console.error('加载工具详情失败:', error)
          this.$message.error('加载工具详情失败')
        } finally {
          this.loading = false
        }
      },

      async handleSave() {
        try {
          const valid = await this.$refs.universalForm.validate()
          if (!valid) {
            this.$message.warning('请检查表单数据')
            return
          }

          this.loading = true
          
          const formData = { ...this.form }
          
          if (this.isEdit) {
            await updateTool(formData)
            this.$message.success('更新成功')
          } else {
            await createTool(formData)
            this.$message.success('新增成功')
          }
          
          this.handleBack()
        } catch (error) {
          console.error('保存失败:', error)
          this.$message.error('保存失败，请检查表单数据')
        } finally {
          this.loading = false
        }
      },

      handleBack() {
        // 优先返回到工具列表页
        const from = this.$route.query.from
        if (from === 'list') {
          this.$router.push({ name: 'McpTool' })
        } else {
          this.$router.go(-1)
        }
      },

      handleBreadcrumbClick(item) {
        // 导航逻辑由BreadcrumbNavigation组件统一处理
      },

      // 自定义验证方法
      validateToolName(rule, value, callback) {
        if (!value) {
          callback()
          return
        }
        
        // 工具名称格式验证
        const namePattern = /^[a-zA-Z0-9_\u4e00-\u9fa5\s-]+$/
        if (!namePattern.test(value)) {
          callback(new Error('工具名称只能包含字母、数字、中文、下划线、连字符和空格'))
          return
        }
        
        callback()
      },

      validateJsonSchema(rule, value, callback) {
        if (!value) {
          callback()
          return
        }
        
        try {
          JSON.parse(value)
          callback()
        } catch (error) {
          callback(new Error('请输入有效的JSON格式'))
        }
      }
    }
  }
</script>

<style lang="less" scoped>
// 页面样式由EditPageContainer和UniversalForm组件提供
</style>