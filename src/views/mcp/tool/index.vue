<template>
  <div class="tool-container">
    <UniversalTable
      ref="universalTable"
      :loading="loading"
      :table-data="tableData"
      :columns="tableColumns"
      :pagination-data="pagination"
      :total="pagination.total"
      :search-params="searchParams"
      :search-form-config="searchFormConfig"
      :actions="tableActions"
      :action-column-width="200"
      :load-table-data="loadTableData"
      title="MCP工具管理"
      subtitle="管理MCP工具配置，支持工具注册、配置、启用/禁用等功能"
      :title-icon="'el-icon-s-tools'"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @action-click="handleAction"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    
    <ConfirmDialog
      ref="confirmDialog"
      :title="confirmTitle"
      :message="confirmContent"
      @confirm="confirmDelete"
      @cancel="handleCancelDelete"
    />
  </div>
</template>

<script>
import { getToolPage, deleteTool } from '@/api/mcp/tool'
import { getServerList } from '@/api/mcp/server'
import { getAppServerList } from '@/api/mcp/appserver'
import UniversalTable from '@/components/layouts/UniversalTable'
import ConfirmDialog from '@/components/layouts/ConfirmDialog'

export default {
  name: 'McpTool',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchParams: {
        param: {
          toolName: '',
          serverId: '',
          toolStatus: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      confirmTitle: '',
      confirmContent: '',
      deleteRow: null,
      appServerOptions: [], // 应用服务器选项
      tableColumns: [
        {
          prop: 'toolCode',
          label: '工具编号',
          width: 120,
          align: 'center'
        },
        {
          prop: 'toolName',
          label: '工具名称',
          width: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'serverName',
          label: '所属服务器',
          width: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'appServerName',
          label: '应用服务器',
          width: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'apiPath',
          label: '接口路径',
          width: 200,
          showOverflowTooltip: true
        },
        {
          prop: 'toolType',
          label: '工具类型',
          width: 120,
          align: 'center'
        },
        {
          prop: 'toolStatus',
          label: '状态',
          width: 100,
          align: 'center',
          dicCode: 'gen.useStatus',
          tagType: (row) => {
            return row.toolStatus === 1 ? 'success' : 'danger'
          }
        },
        {
          prop: 'toolDescription',
          label: '描述',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center'
        }
      ],
      tableActions: [
        {
          label: '查看',
          key: 'view',
          permission: 'mcp:tool:view'
        },
        {
          label: '编辑',
          key: 'edit',
          permission: 'mcp:tool:edit'
        },
        {
          label: '删除',
          key: 'delete',
          permission: 'mcp:tool:delete',
          danger: true
        }
      ],
      searchFormConfig: [
        {
          label: '工具名称',
          name: 'toolName',
          type: 'input',
          placeholder: '请输入工具名称',
          fixedShow: true
        },
        {
          label: '所属服务器',
          name: 'serverId',
          type: 'select',
          placeholder: '请选择服务器',
          fixedShow: true,
          list: [],
          clearable: true
        },
        {
          label: '工具状态',
          name: 'toolStatus',
          type: 'select',
          placeholder: '请选择状态',
          fixedShow: true,
          dicCode: 'gen.useStatus',
          clearable: true
        }
      ]
    }
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"] || { color: '#D7A256' }
    }
  },
  async mounted() {
    // 加载服务器列表数据
    await this.loadServerList()
    // 加载应用服务器列表数据
    await this.loadAppServerList()
    // 工具列表数据由UniversalTable组件自动加载
  },
  methods: {
    // 加载服务器列表
    async loadServerList() {
      try {
        const res = await getServerList({})
        const serverField = this.searchFormConfig.find(field => field.name === 'serverId')
        if (serverField && res) {
          serverField.list = res.map(server => ({
            dicItemName: server.serverName,
            dicItemCode: server.serverId
          }))
        }
      } catch (error) {
        console.error('获取服务器列表失败:', error)
      }
    },
    
    // 加载应用服务器列表
    async loadAppServerList() {
      try {
        const res = await getAppServerList({})
        if (res && Array.isArray(res)) {
          this.appServerOptions = res.map(appServer => ({
            dicItemName: appServer.appServerName,
            dicItemCode: appServer.appServerId
          }))
        }
      } catch (error) {
        console.error('获取应用服务器列表失败:', error)
        this.appServerOptions = []
      }
    },
    
    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const params = {
          param: this.searchParams.param,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        }
        const res = await getToolPage(params)
        
        // 转换服务器ID为服务器名称和应用服务器ID为应用服务器名称
        const toolList = (res && res.list) ? res.list : []
        const serverField = this.searchFormConfig.find(field => field.name === 'serverId')
        const serverList = serverField ? serverField.list : []
        
        this.tableData = toolList.map(tool => {
          const server = serverList.find(s => s.dicItemCode === tool.serverId)
          const appServer = this.appServerOptions.find(a => a.dicItemCode === tool.appServerId)
          return {
            ...tool,
            serverName: server ? server.dicItemName : '未知服务器',
            appServerName: appServer ? appServer.dicItemName : (tool.appServerId ? '未知应用服务器' : '')
          }
        })
        
        this.pagination.total = (res && res.total) ? res.total : 0
      } catch (error) {
        console.error('获取工具列表失败:', error)
        this.$message.error('获取工具列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.pageNum = 1
      this.loadTableData()
    },
    
    // 重置
    handleReset() {
      this.searchParams = {
        param: {
          toolName: '',
          serverId: '',
          toolStatus: ''
        }
      }
      this.pagination.pageNum = 1
      this.loadTableData()
    },

    
    // 新增
    handleAdd() {
      this.$router.push({ name: 'ToolAdd' })
    },
    
    // 操作处理
    handleAction(actionData) {
      const { action, row } = actionData
      switch (action) {
        case 'view':
          this.handleView(row)
          break
        case 'edit':
          this.handleEdit(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },
    
    // 查看详情
    handleView(row) {
      this.$router.push({
        name: 'ToolDetail',
        params: { id: row.toolCode }
      })
    },
    
    // 编辑
    handleEdit(row) {
      this.$router.push({
        name: 'ToolEdit',
        params: { id: row.toolCode }
      })
    },
    
    // 删除
    handleDelete(row) {
      this.deleteRow = row
      this.confirmTitle = '删除确认'
      this.confirmContent = `确定要删除工具"${row.toolName}"吗？此操作不可撤销。`
      this.$refs.confirmDialog.show()
    },
    
    // 确认删除
    async confirmDelete() {
      try {
        await deleteTool(this.deleteRow.toolId)
        this.$message.success('删除成功')
        this.$refs.confirmDialog.hide()
        this.loadTableData()
      } catch (error) {
        console.error('删除失败:', error)
        this.$message.error('删除失败')
      }
    },
    
    // 取消删除
    handleCancelDelete() {
      this.$refs.confirmDialog.hide()
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.loadTableData()
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.loadTableData()
    }
  }
}
</script>

<style lang="less">

</style>