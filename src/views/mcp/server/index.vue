<template>
  <div class="server-container">
    <UniversalTable
      ref="universalTable"
      :loading="loading"
      :table-data="tableData"
      :columns="tableColumns"
      :pagination-data="pagination"
      :total="pagination.total"
      :search-params="searchParams"
      :search-form-config="searchFormConfig"
      :actions="tableActions"
      :action-column-width="180"
      :load-table-data="loadTableData"
      title="MCP服务器管理"
      subtitle="管理MCP服务器配置，支持服务器连接、状态监控等功能"
      :title-icon="'el-icon-monitor'"
      @search="handleSearch"
      @reset="handleReset"
      @add="handleAdd"
      @action-click="handleAction"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    
    <ConfirmDialog
      ref="confirmDialog"
      :title="confirmTitle"
      :message="confirmContent"
      @confirm="confirmDelete"
      @cancel="handleCancelDelete"
    />
  </div>
</template>

<script>
import { getServerPage, deleteServer } from '@/api/mcp/server/index'
import UniversalTable from '@/components/layouts/UniversalTable'
import ConfirmDialog from '@/components/layouts/ConfirmDialog'

export default {
  name: 'McpServer',
  components: {
    UniversalTable,
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchParams: {
        param: {
          serverName: '',
          serverStatus: ''
        }
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      confirmTitle: '',
      confirmContent: '',
      deleteRow: null,
      tableColumns: [
        {
          prop: 'serverCode',
          label: '服务器编号',
          width: 120,
          align: 'center'
        },
        {
          prop: 'serverName',
          label: '服务器名称',
          width: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'serverUrl',
          label: '服务器地址',
          minWidth: 200,
          showOverflowTooltip: true
        },
        {
          prop: 'serverStatus',
          label: '状态',
          width: 100,
          align: 'center',
          dicCode: 'gen.useStatus',
          tagType: (row) => {
            return row.serverStatus === 1 ? 'success' : 'danger'
          }
        },
        {
          prop: 'description',
          label: '描述',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          align: 'center'
        }
      ],
      tableActions: [
        {
          label: '查看',
          key: 'view',
          permission: 'mcp:server:view'
        },
        {
          label: '编辑',
          key: 'edit',
          permission: 'mcp:server:edit'
        },
        {
          label: '删除',
          key: 'delete',
          permission: 'mcp:server:delete',
          danger: true
        }
      ],
      searchFormConfig: [
        {
          label: '服务器名称',
          name: 'serverName',
          type: 'input',
          placeholder: '请输入服务器名称',
          fixedShow: true
        },
        {
          label: '服务器状态',
          name: 'serverStatus',
          type: 'select',
          placeholder: '请选择状态',
          fixedShow: true,
          dicCode: 'gen.useStatus',
          clearable: true
        }
      ]
    }
  },
  computed: {
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"] || { color: '#D7A256' }
    }
  },
  mounted() {
    // 数据加载由UniversalTable组件统一管理，先加载字典再加载数据
  },
  methods: {
    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const params = {
          param: this.searchParams.param,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        }
        const res = await getServerPage(params)
        this.tableData = res.list || []
        this.pagination.total = res.total || 0
      } catch (error) {
        console.error('获取服务器列表失败:', error)
        this.$message.error('获取服务器列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.pageNum = 1
      this.loadTableData()
    },
    
    // 重置
    handleReset() {
      this.searchParams = {
        param: {
          serverName: '',
          serverStatus: ''
        }
      }
      this.pagination.pageNum = 1
      this.loadTableData()
    },

    
    // 新增
    handleAdd() {
      this.$router.push({ name: 'ServerAdd' })
    },
    
    // 操作处理
    handleAction(actionData) {
      const { action, row } = actionData
      switch (action) {
        case 'view':
          this.handleView(row)
          break
        case 'edit':
          this.handleEdit(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },
    
    // 查看详情
    handleView(row) {
      this.$router.push({
        name: 'ServerDetail',
        params: { id: row.serverCode }
      })
    },
    
    // 编辑
    handleEdit(row) {
      this.$router.push({
        name: 'ServerEdit',
        params: { id: row.serverCode }
      })
    },
    
    // 删除
    handleDelete(row) {
      this.deleteRow = row
      this.confirmTitle = '删除确认'
      this.confirmContent = `确定要删除服务器"${row.serverName}"吗？此操作不可撤销。`
      this.$refs.confirmDialog.show()
    },
    
    // 确认删除
    async confirmDelete() {
      try {
        await deleteServer(this.deleteRow.serverId)
        this.$message.success('删除成功')
        this.$refs.confirmDialog.hide()
        this.loadTableData()
      } catch (error) {
        console.error('删除失败:', error)
        this.$message.error('删除失败')
      }
    },
    
    // 取消删除
    handleCancelDelete() {
      this.$refs.confirmDialog.hide()
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.loadTableData()
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.loadTableData()
    }
  }
}
</script>

<style lang="less">

</style>