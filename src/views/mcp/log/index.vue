<template>
  <div class="log-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="服务器">
          <el-select
            v-model="searchForm.serverId"
            placeholder="请选择服务器"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="server in serverList"
              :key="server.serverId"
              :label="server.serverName"
              :value="server.serverId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工具">
          <el-select
            v-model="searchForm.toolId"
            placeholder="请选择工具"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="tool in toolList"
              :key="tool.toolId"
              :label="tool.toolName"
              :value="tool.toolId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="调用状态">
          <el-select
            v-model="searchForm.callStatus"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="成功" value="SUCCESS" />
            <el-option label="失败" value="FAILED" />
            <el-option label="进行中" value="RUNNING" />
          </el-select>
        </el-form-item>
        <el-form-item label="调用时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-area">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column prop="logId" label="日志ID" width="100" />
        <el-table-column prop="serverName" label="服务器" width="120" />
        <el-table-column prop="toolName" label="工具名称" width="120" />
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="callStatus" label="调用状态" width="100">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusType(scope.row.callStatus)"
            >
              {{ getStatusText(scope.row.callStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="requestData" label="请求参数" min-width="150">
          <template slot-scope="scope">
            <el-tooltip
              v-if="scope.row.requestData"
              :content="scope.row.requestData"
              placement="top"
            >
              <span class="text-ellipsis">{{ scope.row.requestData }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="responseData" label="响应结果" min-width="150">
          <template slot-scope="scope">
            <el-tooltip
              v-if="scope.row.responseData"
              :content="scope.row.responseData"
              placement="top"
            >
              <span class="text-ellipsis">{{ scope.row.responseData }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="errorMessage" label="错误信息" min-width="150">
          <template slot-scope="scope">
            <el-tooltip
              v-if="scope.row.errorMessage"
              :content="scope.row.errorMessage"
              placement="top"
            >
              <span class="text-ellipsis error-text">{{ scope.row.errorMessage }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="callTime" label="调用时间" width="180" />
        <el-table-column prop="duration" label="耗时(ms)" width="100" />
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              @click="handleView(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-area">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      title="调用日志详情"
      :visible.sync="detailVisible"
      width="800px"
    >
      <div class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志ID">{{ logDetail.logId }}</el-descriptions-item>
          <el-descriptions-item label="服务器">{{ logDetail.serverName }}</el-descriptions-item>
          <el-descriptions-item label="工具名称">{{ logDetail.toolName }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ logDetail.userId }}</el-descriptions-item>
          <el-descriptions-item label="调用状态">
            <el-tag :type="getStatusType(logDetail.callStatus)">
              {{ getStatusText(logDetail.callStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="调用时间">{{ logDetail.callTime }}</el-descriptions-item>
          <el-descriptions-item label="耗时">{{ logDetail.duration }}ms</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ logDetail.createTime }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-section">
          <h4>请求参数</h4>
          <el-input
            v-model="logDetail.requestData"
            type="textarea"
            :rows="6"
            readonly
            placeholder="无请求参数"
          />
        </div>
        
        <div class="detail-section">
          <h4>响应结果</h4>
          <el-input
            v-model="logDetail.responseData"
            type="textarea"
            :rows="6"
            readonly
            placeholder="无响应结果"
          />
        </div>
        
        <div class="detail-section" v-if="logDetail.errorMessage">
          <h4>错误信息</h4>
          <el-input
            v-model="logDetail.errorMessage"
            type="textarea"
            :rows="4"
            readonly
            class="error-input"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLogPage, deleteLog } from '@/api/mcp/log'
import { getServerList } from '@/api/mcp/server'
import { getToolList } from '@/api/mcp/tool'

export default {
  name: 'McpLog',
  data() {
    return {
      loading: false,
      tableData: [],
      serverList: [],
      toolList: [],
      searchForm: {
        serverId: null,
        toolId: null,
        callStatus: '',
        dateRange: null
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      detailVisible: false,
      logDetail: {}
    }
  },
  mounted() {
    this.getServerList()
    this.getToolList()
    this.getLogList()
  },
  methods: {
    // 获取服务器列表
    async getServerList() {
      try {
        const res = await getServerList({})
        this.serverList = res || []
      } catch (error) {
        console.error('获取服务器列表失败:', error)
      }
    },
    
    // 获取工具列表
    async getToolList() {
      try {
        const res = await getToolList({})
        this.toolList = res || []
      } catch (error) {
        console.error('获取工具列表失败:', error)
      }
    },
    
    // 获取调用日志列表
    async getLogList() {
      this.loading = true
      try {
        const params = {
          ...this.searchForm,
          current: this.pagination.current,
          size: this.pagination.size
        }
        
        // 处理时间范围
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.startTime = this.searchForm.dateRange[0]
          params.endTime = this.searchForm.dateRange[1]
        }
        delete params.dateRange
        
        const res = await getLogPage(params)
        this.tableData = res.list || []
        this.pagination.total = res.total || 0
      } catch (error) {
        console.error('获取调用日志列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.getLogList()
    },
    
    // 重置
    handleReset() {
      this.searchForm = {
        serverId: null,
        toolId: null,
        callStatus: '',
        dateRange: null
      }
      this.pagination.current = 1
      this.getLogList()
    },
    
    // 查看详情
    handleView(row) {
      this.logDetail = { ...row }
      this.detailVisible = true
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm('此操作将永久删除该调用日志, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteLog(row.logId)
          this.$message.success('删除成功')
          this.getLogList()
        } catch (error) {
          console.error('删除失败:', error)
        }
      })
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'SUCCESS': 'success',
        'FAILED': 'danger',
        'RUNNING': 'warning'
      }
      return statusMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'SUCCESS': '成功',
        'FAILED': '失败',
        'RUNNING': '进行中'
      }
      return statusMap[status] || status
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.current = 1
      this.getLogList()
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getLogList()
    }
  }
}
</script>

<style lang="less" scoped>
.log-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  
  .search-area {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-area {
    margin-bottom: 20px;
    
    .text-ellipsis {
      display: inline-block;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      vertical-align: top;
    }
    
    .error-text {
      color: #f56c6c;
    }
  }
  
  .pagination-area {
    text-align: right;
  }
}

.log-detail {
  .detail-section {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 10px;
      color: #303133;
    }
    
    .error-input {
      /deep/ .el-textarea__inner {
        color: #f56c6c;
        background-color: #fef0f0;
      }
    }
  }
}
</style>