<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getAppServerDetail, getAppServerDetailByCode, createAppServer, updateAppServer } from '@/api/mcp/appserver/index'

export default {
  name: 'McpAppServerEdit',
  components: {
    EditPageContainer,
    UniversalForm
  },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      form: {
        appServerCode: '',
        appServerName: '',
        domainUrl: '',
        appServerStatus: 1,
        description: '',
        createTime: '',
        updateTime: '',
        createUser: '',
        updateUser: ''
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-s-platform',
          fields: [
            [
              {
                prop: 'appServerName',
                label: '应用服务器名称',
                type: 'input',
                placeholder: '请输入应用服务器名称',
                maxlength: 100,
                showWordLimit: true
              },
              {
                prop: 'domainUrl',
                label: '域名地址',
                type: 'input',
                placeholder: '请输入域名地址，如：http://localhost',
                maxlength: 500,
                showWordLimit: true
              }
            ],
            [
              {
                prop: 'appServerStatus',
                label: '服务器状态',
                type: 'select',
                dicCode: 'gen.useStatus'
              }
            ],
            [
              {
                prop: 'description',
                label: '描述信息',
                type: 'textarea',
                placeholder: '请输入应用服务器描述信息',
                maxlength: 500,
                rows: 4,
                showWordLimit: true,
                span: 24
              }
            ]
          ]
        }
      ],
      formRules: {
        appServerName: [
          { required: true, message: '请输入应用服务器名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        domainUrl: [
          { required: true, message: '请输入域名地址', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' },
          { 
            pattern: /^(https?:\/\/|wss?:\/\/|tcp:\/\/|udp:\/\/).+/,
            message: '请输入有效的域名地址（需包含协议前缀）',
            trigger: 'blur'
          }
        ],

        appServerStatus: [
          { required: true, message: '请选择服务器状态', trigger: 'change' }
        ],
        description: [
          { max: 500, message: '描述信息不能超过 500 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.isView) {
        return '查看应用服务器'
      }
      return this.isEdit ? '编辑应用服务器' : '新增应用服务器'
    },
    pageIcon() {
      return 'el-icon-s-platform'
    },
    breadcrumbItems() {
      const items = [
        { text: '应用服务器管理', to: { name: 'AppServer' } }
      ]
      
      if (this.isView) {
        items.push({ text: '应用服务器详情', to: null })
      } else if (this.isEdit) {
        items.push({ text: '编辑应用服务器', to: null })
      } else {
        items.push({ text: '新增应用服务器', to: null })
      }
      
      return items
    }
  },
  async created() {
    await this.initPage()
  },
  methods: {
    async initPage() {
      const { id } = this.$route.params
      const { mode } = this.$route.query
      
      // 支持查看模式：当mode=view或路由名称包含detail时进入查看模式
      this.isView = mode === 'view' || this.$route.name === 'AppServerDetail'
      this.isEdit = !!id && !this.isView
      
      if (id) {
        await this.loadAppServerDetail(id)
      }
    },

    async loadAppServerDetail(id) {
      this.loading = true
      try {
        // 优先尝试通过编码查询，如果失败则通过ID查询
        let response
        try {
          response = await getAppServerDetailByCode(id)
        } catch (codeError) {
          response = await getAppServerDetail(id)
        }
        
        if (response) {
          this.form = {
            ...this.form,
            ...response
          }
        } else {
          this.$message.error('加载应用服务器详情失败')
        }
      } catch (error) {
        console.error('加载应用服务器详情失败:', error)
        this.$message.error('加载应用服务器详情失败')
      } finally {
        this.loading = false
      }
    },

    async handleSave() {
      try {
        const valid = await this.$refs.universalForm.validate()
        if (!valid) {
          this.$message.warning('请检查表单数据')
          return
        }

        this.loading = true
        
        const formData = { ...this.form }
        
        if (this.isEdit) {
          await updateAppServer(formData)
          this.$message.success('更新成功')
        } else {
          await createAppServer(formData)
          this.$message.success('新增成功')
        }
        
        this.handleBack()
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败，请检查表单数据')
      } finally {
        this.loading = false
      }
    },

    handleBack() {
      // 优先返回到应用服务器列表页
      const from = this.$route.query.from
      if (from === 'list') {
        this.$router.push({ name: 'AppServer' })
      } else {
        this.$router.go(-1)
      }
    },

    handleBreadcrumbClick(item) {
      // 导航逻辑由BreadcrumbNavigation组件统一处理
    }
  }
}
</script>

<style lang="less" scoped>
// 页面样式由EditPageContainer和UniversalForm组件提供
</style>