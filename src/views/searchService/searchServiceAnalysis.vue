<template>
  <DtNav from="索引表管理" name="查询分析配置"></DtNav>
  <TableToolTemp toolTitle="查询分析配置" :toolList="toolList"></TableToolTemp>
  <el-table :data="tableData" stripe class="dt-table">
    <el-table-column prop="fieldNames" label="检索字段" align="center" />
    <el-table-column prop="queryAnalysis" label="查询词解析" align="center" />
    <el-table-column prop="analysisStatus" label="是否启用" align="center" />
    <el-table-column prop="updateId" label="配置人" align="center" />
    <el-table-column prop="updateTime" label="维护时间" align="center" />
    <el-table-column label="操作" align="center" width="230">
      <template #default="scope">
        <el-button type="primary" text @click="toggle(scope.row)">
          {{ scope.row.analysisStatus == 1 ? "停用" : "启用" }}
        </el-button>
        <el-button type="primary" text @click="editApp(scope.row)">编辑</el-button>
        <el-button type="primary" text @click="deleteApp(scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <Pagination ref="paginationRef" @change="onPageData" :total="total" />


  <DtPopup :show="addShow" @close="closeAdd" @close-by-icon="closeAdd" :title="addPopupTitle" :footer="false">
    <div class="formWarp">
      <el-form ref="ruleFormRef" :rules="rules" :model="ruleForm" label-position="left" label-width="110px">
        <el-form-item label="检索字段:" prop="queryColumn">
          <el-select v-model="ruleForm.queryColumn" placeholder="检索字段" clearable multiple style="width: 460px">
            <el-option :label="item.fieldName" :value="item.columnId" v-for="(item, index) in queryColumnList"
              :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="解析器:" prop="queryAnalysis">
          <el-select v-model="ruleForm.queryAnalysis" placeholder="解析器" clearable multiple style="width: 460px">
            <el-option label="停用词" value="01"></el-option>
            <el-option label="拼音纠错" value="02"></el-option>
            <el-option label="同义词" value="03"></el-option>
          </el-select>
        </el-form-item>
        <div class="formBtn">
          <el-button class="dt-btn" type="primary" @click="createAnalysis">保存</el-button>
        </div>
      </el-form>
    </div>
  </DtPopup>

  <DtPopup :show="deletePopupShow" @close="deletePopupShow = false" @close-by-icon="deletePopupShow = false" title="删除"
    @confirm="deleteConfirm">
    <div class="popup-text">删除后无法恢复，请确认是否删除？</div>
  </DtPopup>
</template>

<script lang="ts" setup>
import { ToolListProps, PageTableSearch } from '@/components/types';
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  analysisPage,
  queryAnalysisListColumn,
  insertQueryAnalysis,
  updateQueryAnalysis,
  deleteQueryAnalysis,
  queryAnalysisdisableQueryAnalysis,
  queryAnalysisenableQueryAnalysis
} from './api'
const router = useRouter()
const route = useRoute()
const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: '',
    action: async () => {
      addPopupTitle.value = '新增'
      addShow.value = true
    }
  }
]
const tableData = ref<Record<string, any>[]>([])
const total = ref<number>(0)
const pageParam: PageTableSearch = {
  pageNum: 1,
  pageSize: 10,
  param: {}
}
const analyId = ref<string>('')
const deletePopupShow = ref<boolean>(false)
const addShow = ref<boolean>(false)
const addPopupTitle = ref<string>('新增')
const rules = {
  queryColumn: [{ required: true, message: "请选择", trigger: "blur" }],
  queryAnalysis: [{ required: true, message: "请选择", trigger: "blur" },],
}
const ruleFormRef = ref()
const ruleForm = ref({
  indId: route.query.indId,
  queryColumn: [],
  queryAnalysis: [],
})
const queryColumnList = ref<Record<string, any>[]>([])


const onPageData = (page: number) => {
  console.log(page)
}

const toggle = async (row: any) => {
  analyId.value = row.analyId
  const queryColumn = row.queryColumn
  let res: any
  if (row.analysisStatus == 1) {
    res = await queryAnalysisdisableQueryAnalysis({ analyId: analyId.value, queryColumn })
  } else {
    res = await queryAnalysisenableQueryAnalysis({ analyId: analyId.value, queryColumn })
  }
  if (res.status === 0) {
    ElMessage.success('操作成功')
    initTableData()
  }
}

const editApp = (row: any) => {
  analyId.value = row.analyId
  addPopupTitle.value = '编辑'
  addShow.value = true
  analyId.value = row.analyId
  ruleForm.value.queryColumn = row.queryColumn.split(",");
  ruleForm.value.queryAnalysis = row.queryAnalysis.split(",");
}

const deleteApp = (row: any) => {
  analyId.value = row.analyId
  deletePopupShow.value = true
}

const deleteConfirm = async () => {
  const res = await deleteQueryAnalysis({ analyId: analyId.value })
  if (res.status === 0) {
    ElMessage.success('删除成功')
    deletePopupShow.value = false
    initTableData()
  }
}

const createAnalysis = () => {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      const data = {
        indId: ruleForm.value.indId,
        queryColumn: ruleForm.value.queryColumn.join(","),
        queryAnalysis: ruleForm.value.queryAnalysis.join(",")
      }
      if (addPopupTitle.value == '新增') {
        const res = await insertQueryAnalysis(data)
        if (res.status === 0) {
          ElMessage.success('新增成功')
          addShow.value = false
          initTableData()
        }
      } else {
        const res = await updateQueryAnalysis(data)
        if (res.status === 0) {
          ElMessage.success('编辑成功')
          addShow.value = false
          initTableData()
        }
      }
    }
  });
}

const closeAdd = () => {
  addShow.value = false
  ruleForm.value = {
    indId: route.query.indId as string,
    queryColumn: [],
    queryAnalysis: [],
  }
}



const initTableData = async () => {
  const res = await analysisPage(pageParam)
  if (res.status === 0) {
    tableData.value = res.data.list
    total.value = Number(res.data.total)
  }
}

const initAllColumn = async () => {
  const res = await queryAnalysisListColumn({ indId: route.query.indId, isAnalysis: "Y" })
  if (res.status === 0) {
    queryColumnList.value = res.data
  }
}

const init = async () => {
  await initTableData()
  await initAllColumn()
  // await initDict()
}

onMounted(() => {
  init()
})
</script>

<style>
.popup-text {
  text-align: center;
}

.formBtn {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>