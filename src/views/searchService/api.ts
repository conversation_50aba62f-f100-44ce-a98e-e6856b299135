import { useAxios } from '@/hooks/http'

/**
 * @description 搜索表管理列表
 */
export const searchServicePage = async (data: any) => await useAxios('/web/es/index/page', data)

/**
 * @description 索引字段管理列表
 */
export const searchServiceColumn = async (data: any) => await useAxios('/web/es/column/page', data)

/**
 * @description 编辑字段管理
 */
export const editFiled = async (data: any) => await useAxios('/web/es/column/update', data)

/**
 * @description 查询分析配置列表
 */
export const analysisPage = async (data: any) => await useAxios('/web/es/queryAnalysis/page', data)

/**
 * @description 获取所有检索字段
 */
export const queryAnalysisListColumn = async (data: any) => await useAxios('/web/es/column/list', data)

/**
 * @description 查询分析配置  新增
 */
export const insertQueryAnalysis = async (data: any) => await useAxios('/web/es/queryAnalysis/insert', data)

/**
 * @description 查询分析配置  编辑
 */
export const updateQueryAnalysis = async (data: any) => await useAxios('/web/es/queryAnalysis/update', data)

/**
 * @description 查询分析配置  删除
 */
export const deleteQueryAnalysis = async (data: any) => await useAxios('/web/es/queryAnalysis/delete', data)

/**
 * @description 启用
 */
export const queryAnalysisenableQueryAnalysis = async (data: any) => await useAxios('/web/es/queryAnalysis/enable', data)

/**
 * @description 停用
 */
export const queryAnalysisdisableQueryAnalysis = async (data: any) => await useAxios('/web/es/queryAnalysis/disable', data)

/**
 * @description 重建索引
 */
export const reloadIndex = async (data: any) => await useAxios('/web/es/index/reindex', data)
