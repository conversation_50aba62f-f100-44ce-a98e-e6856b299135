<template>
  <div>
    <TableToolTemp toolTitle="索引表管理" />

    <el-table :data="tableData" stripe class="dt-table">
      <el-table-column prop="name" label="索引表名称" align="center" />
      <el-table-column prop="tableName" label="关联表名" align="center" />
      <el-table-column prop="indexName" label="搜索引擎索引名称" align="center" />
      <el-table-column prop="aliasName" label="搜索引擎索引别名" align="center" />
      <el-table-column prop="updateId" label="配置人" align="center" />
      <el-table-column prop="updateTime" label="维护时间" align="center" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="primary" text @click="gotoColumn(scope.row)">索引字段配置</el-button>
          <el-button type="primary" text @click="gotoAnalysis(scope.row)">查询分析配置</el-button>
          <el-button type="primary" text @click="reindex(scope.row)">重建索引</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination ref="paginationRef" @change="onPageData" :total="total" />
  </div>
</template>
<script lang="ts" setup>
import { searchServicePage, reloadIndex } from './api'
import { ref, onMounted } from 'vue'
import type { PageData, PageTableSearch } from '@/components/types'
import { useRouter } from 'vue-router'
const router = useRouter()
const total = ref<number>(0)
const pageParam: PageTableSearch = {
  pageNum: 1,
  pageSize: 10,
  param: {}
}
const tableData = ref<Record<string, any>[]>([])

const onPageData = (data: PageData) => {
  pageParam.pageNum = data.pageNum
  pageParam.pageSize = data.pageSize
  initTableData()
}

const gotoColumn = (row: Record<string, any>) => {
  router.push({ path: '/searchServiceColumn', query: { indId: row.indId } })
}

const gotoAnalysis = (row: Record<string, any>) => {
  router.push({ path: '/searchServiceAnalysis', query: { indId: row.indId } })
}

const reindex = async (row: Record<string, any>) => {
  const indId = row.indId
  const res = await reloadIndex({ indId })
  if (res.status === 0) {
    ElMessage.success('重建索引成功')
    initTableData()
  }
}

const init = async () => {
  await initTableData()
}

const initTableData = async () => {
  const res = await searchServicePage(pageParam)
  if (res.status === 0) {
    tableData.value = res.data.list
    total.value = Number(res.data.total)
  }
}


onMounted(() => {
  init()
})
</script>