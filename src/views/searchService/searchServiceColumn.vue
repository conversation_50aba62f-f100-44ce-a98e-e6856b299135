<template>
  <!-- 索引 字段-->
  <div class="searchColumn">
    <DtNav from="索引表管理" name="索引字段配置"></DtNav>

    <TableToolTemp toolTitle="索引字段管理" :toolList="toolList"></TableToolTemp>


    <el-table :data="tableData" stripe class="dt-table">
      <el-table-column prop="columnName" label="索引表名称" align="center" />
      <el-table-column prop="fieldName" label="关联表名" align="center" />
      <el-table-column prop="columnType" label="搜索引擎索引名称" align="center" />
      <el-table-column prop="analyzer" label="搜索引擎索引别名" align="center" />
      <el-table-column prop="columnWeight" label="配置人" align="center" />
      <el-table-column prop="typeLevel" label="维护时间" align="center" />
      <el-table-column prop="updateId" label="维护时间" align="center" />
      <el-table-column prop="updateTime" label="维护时间" align="center" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="primary" text @click="editApp(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination ref="paginationRef" @change="onPageData" :total="total" />

    <!-- 新增编辑索引字段 -->
    <DtPopup :show="addShow" @close="popupClose" @close-by-icon="popupClose" :title="addPopupTitle" :footer="false">
      <div class="formWarp">
        <el-form ref="ruleFormRef" :rules="rules" :model="ruleForm" label-position="left" label-width="120px">
          <el-form-item label="索引字段:" prop="columnName">
            <el-input v-model="ruleForm.columnName" disabled></el-input>
          </el-form-item>
          <el-form-item label="字段类型:" prop="columnType">
            <el-input v-model="ruleForm.columnType" disabled></el-input>
          </el-form-item>
          <el-form-item label="字段名称:" prop="fieldName">
            <el-input v-model="ruleForm.fieldName"></el-input>
          </el-form-item>
          <el-form-item label="是否检索:" prop="isAnalysis">
            <el-radio-group v-model="ruleForm.isAnalysis" :disabled='ruleForm.defaultIsAnalysis == "N"'>
              <el-radio :label="'Y'">检索</el-radio>
              <el-radio :label="'N'">不检索</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="ruleForm.columnType === 'text'" label="分词器:" prop="analyzer">
            <el-select v-model="ruleForm.analyzer" placeholder="分词器" clearable>
              <el-option :label="item.dicItemName" :value="item.dicItemCode" v-for="(item, index) in analyzerList"
                :key="item.dicItemCode + index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="字段权重:" prop="columnWeight">
            <el-input v-model="ruleForm.columnWeight"></el-input>
          </el-form-item>

          <el-form-item label="实体识别等级:" prop="typeLevel">
            <el-select v-model="ruleForm.typeLevel" placeholder="实体识别等级" clearable>
              <el-option :label="item.dicItemName" :value="item.dicItemCode" v-for="(item, index) in recognitionLevelList"
                :key="item.dicItemCode + index"></el-option>
            </el-select>
          </el-form-item>

          <div class="formBtn">
            <el-button class="dt-btn" type="primary" @click="createColumn(ruleFormRef)">保存</el-button>
          </div>
        </el-form>
      </div>
    </DtPopup>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue'
import type { ToolListProps, PageData, PageTableSearch } from '@/components/types'
import { useRouter } from 'vue-router'
import { on } from 'events';
import { searchServiceColumn, editFiled } from './api';
import { getDicItemList } from '@/utils/dict';
import type { FormInstance } from 'element-plus';
const router = useRouter()
const toolList: ToolListProps[] = []
const pageParam: PageTableSearch = {
  pageNum: 1,
  pageSize: 10,
  param: {}
}
const tableData = ref<Record<string, any>[]>([])
const total = ref<number>(0)
const addShow = ref<boolean>(false)
const addPopupTitle = ref<string>('新增索引字段')
const ruleForm = reactive<Record<string, any>>({
  indId: "",
  columnName: "",
  columnType: "",
  fieldName: "",
  isAnalysis: "",
  isReturnField: "",
  analyzer: "",
  columnWeight: "",
  defaultIsAnalysis: '',
  typeLevel: "",
})
const rules = {
  fieldName: [{ required: true, message: "请输入", trigger: "blur" }],
  isAnalysis: [{ required: true, message: "请选择", trigger: "blur" }],
  columnWeight: [{ required: true, message: "请输入", trigger: "blur" }],
}
const ruleFormRef = ref<FormInstance>()
const analyzerList = ref<Record<string, any>[]>([])
const recognitionLevelList = ref<Record<string, any>[]>([])


const onPageData = (page: number) => {
  console.log(page)
}

const editApp = (row: Record<string, any>) => {
  addPopupTitle.value = '编辑索引字段'
  addShow.value = true
  Object.keys(ruleForm).forEach((key) => {
    ruleForm[key] = row[key]
  })
  ruleForm.analyzer = row.analyzer
  ruleForm.columnId = row.columnId
}

const popupClose = () => {
  addShow.value = false
}

const init = async () => {
  await initTableData()
}

const initTableData = async () => {
  const res = await searchServiceColumn(pageParam)
  if (res.status === 0) {
    tableData.value = res.data.list
    total.value = Number(res.data.total)
  }
}

const createColumn = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid) => {
    if (valid) {
      const res = await editFiled(ruleForm)
      if (res.status === 0) {
        ElMessage.success('操作成功')
        popupClose()
        await initTableData()
      }
    }
  })
}

onMounted(() => {
  init()
})
</script>

<style lang="less" scoped>
.formBtn {
  text-align: center;
  margin-bottom: 20px;
}

.breadcrumb {
  padding: 15px 0 15px 20px;
  border-bottom: 1px solid #eee;

  .el-breadcrumb__item {
    .el-breadcrumb__inner {
      color: #666 !important;
    }

    &:last-of-type {
      .el-breadcrumb__inner {
        color: #999 !important;
      }
    }
  }
}
</style>
