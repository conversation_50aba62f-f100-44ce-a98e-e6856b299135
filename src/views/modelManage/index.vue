<template>
    <TableToolTemp :toolTitle="pageTitle" :toolList="toolList" />
    <PageTable :searchFormTemp="searchForm" apiUrl="/web/model/page" ref="modelRef">
      <template #default="{ tableData }">
        <el-table :data="tableData" class="dt-table" stripe>
          <el-table-column prop="modelName" label="模型名称" align="center" width="200px">
            <template  #default="scope">
              <span>{{ scope.row.modelName }}{{scope.row.isCommonModel==1?'（通用）':'' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="platform" label="所属平台" align="center" width="120px">
            <template  #default="scope">
              <span>{{ getDicItemName(scope.row.platform, 'platform.name.type') }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="isCommonModel" label="是否通用模型" align="center" width="120px">
            <template  #default="scope">
              <span>{{ scope.row.isCommonModel==1?'是':'否' }}</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="modelDesc" label="描述" align="center" min-width="100px" />
          <el-table-column prop="updateTime" label="更新时间" align="center" min-width="120px" />
          <el-table-column prop="updateBy" label="更新人" align="center" min-width="80px">
            <template #default="scope">
              {{ getNickName(scope.row.updateBy) }}
            </template>
          </el-table-column>
          <el-table-column prop="code" label="操作" align="center" width="300px">
            <template #default="scope">
              <el-button link type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-button link type="primary" @click="onRemove(scope.row)">删除</el-button>
              <el-button link type="primary" @click="onOpen(scope.row)">设置通用模型</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>
  </template>
  
  <script setup lang="ts">
  import type { ToolListProps, SearchFormTemp } from '@/components/types'
  import useFilters from '@/hooks/useFilters'
  import { useUserStore } from '@/stores/user'
  import useChannel from '@/stores/channel'
  import { useRouter } from 'vue-router'
  import { ref, onActivated } from 'vue'
  import * as api from '@/api/model'
  const modelRef = ref()
  const { setChannel } = useChannel()
  const { getNickName, getDicItemName } = useFilters()
  const { getDic } = useUserStore()
  
  console.log(...getDic('chatbot.common.status'))
  const router = useRouter()
  const pageTitle = '模型列表'
  const toolList: ToolListProps[] = [
    {
      name: '新增',
      icon: 'add',
      btnCode: '',
      action: async () => {
        router.push({ path: '/modelAdd' })
      },
    },
  ]
  
  const searchForm: SearchFormTemp[] = [
   
    {
    label: '所属平台',
    name: 'platform',
    type: 'select',
    placeholder: '请输入所属平台',
    list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('platform.name.type')],
    searchState: true
    },
    {
    label: '模型名称',
    name: 'modelName',
    type: 'input',
    placeholder: '请输入模型名称',
    searchState: true
    },
    // {
    //   label: '类型',
    //   name: 'type',
    //   type: 'select',
    //   placeholder: '请选择类型',
    //   list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('platform.name.type')],
    // },
  ]
  /**
 * @description 删除
 */
const onRemove = async (data: any) => {
  ElMessageBox.confirm('确定删除该模型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.modelDelete(data.id)
    await modelRef.value.onSearchBtn()
  })
}
  const onEdit = (data: any) => {
    setChannel(data)
    router.push({
      path: '/modelAdd',
      query: {
        id: data.id ?? ''
      },
    })
  }
  
  const onOpen = async (data: any) => {
    ElMessageBox.confirm('确定将该模型设置为通用模型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.modelsetCommonModel(data.id)
    await modelRef.value.onSearchBtn()
  })
  }
  
  onActivated(async () => {
    await modelRef.value?.onSearchBtn()
  })
  </script>
  
  <style lang="less">
  
  .awidth {
      width: 100%;
      height: 20px;
      display: block;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      position: relative;
      left: -5px;
      right: -10px;
    }
    /deep/.awidthTooltip{
      white-space: wrap;
      max-width: 100px;
      display: block;
      max-height: 100px;
    }
  </style>
  