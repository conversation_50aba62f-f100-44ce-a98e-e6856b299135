<template>
    <DtNav :from="nav.from" :name="nav.to"  :fromPath="'/modelManage'" />
    <TableToolTemp toolTitle="模型配置" />
    <UpdateFormTemp  class="pt-5" v-if="finished" :form-list="baseList" :rules="rules" show-submit label-position="left" @submit="submit" >
        <template #default="{ form }">
        <el-form-item label="模型名称" prop="modelName">
            <el-input v-model="form.modelName" placeholder="请输入模型名称" clearable />
        </el-form-item>
        <el-form-item label="所属平台" prop="platform">
            <el-select v-model="form.platform" placeholder="请选择类型" clearable>
              <el-option v-for="(item, idx) in getDic('platform.name.type')" :key="idx" :label="item.dicItemName" :value="item.dicItemCode"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="模型描述" prop="modelDesc">
            <el-input v-model="form.modelDesc" placeholder="请输入模型描述" clearable />
        </el-form-item>
        <el-form-item label="参数配置" prop="modelSetting">
            <el-input v-model="form.modelSetting" placeholder="请输入参数配置" clearable />
        </el-form-item>
        </template>
    </UpdateFormTemp>
  </template>
  
  <script setup lang="ts">
  import { reactive, ref, onBeforeMount } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import type { UpdateFormList } from '@/components/types'
  import { useUserStore } from '@/stores/user'
  import type { FormRules } from 'element-plus'
  const route = useRoute()
  import * as api from '@/api/model'
  const { getDic } = useUserStore()
  const { query } = useRoute()
  const router = useRouter()
  const isEdit = ref(false)
  const finished = ref(false)
  const nav = reactive({
    from: '模型列表',
    to: '编辑',
  })
  
  const baseList: UpdateFormList[] = [
    {
      title: '模型名称',
      type: 'input',
      field: 'modelName',
      except: true,
    },
    {
      title: '模型描述',
      type: 'select',
      field: 'platform',
      except: true,
    },
    {
      title: '模型描述',
      type: 'input',
      field: 'modelDesc',
      except: true,
    },
    {
      title: '参数配置',
      type: 'input',
      field: 'modelSetting',
      except: true,
    },
  ]
  
  const rules: FormRules = {
    modelName: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
    platform: [{ required: true, message: '请择所属平台', trigger: 'change' }],
    modelDesc: [{ required: true, message: '请输入模型描述', trigger: 'change' }],
    modelSetting: [{ required: true, message: '请输入参数配置', trigger: 'blur' }],
  }
  
  const submit = async (param: any) => {
    const id = query.id as string
    const data= isEdit.value?{
        id:id,
        ...param
    }:{
       ...param   
    }
    const { status } = await api.modelSave(data)
    if (status != 0) return
    router.back()
  }
  
  async function init() {
    if (!isEdit.value) return
    nav.to = isEdit.value ? '编辑' : '新增'
    const id = query.id as string
    const { status, data } = await api.modelGetById({id})
    if (status != 0) return
    console.log(status,data)
    data.platform = data.platform
    data.modelName = data.modelName
    data.modelDesc = data.modelDesc
    data.modelSetting = data.modelSetting
    baseList.forEach((tmp) => {
        console.log(tmp)
        if (Object.hasOwn(data, tmp.field) && data[tmp.field] !== null) {
        tmp.value = data[tmp.field]
        }
    })
    console.log(baseList)
  }
  
  onBeforeMount(async () => {
    isEdit.value = !!query.id
    await init()
    finished.value = true
})
  </script>
  