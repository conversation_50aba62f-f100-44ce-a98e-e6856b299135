<template>
  <TableToolTemp :toolTitle="pageTitle" :toolList="toolList" />
  <PageTable :searchFormTemp="searchForm"  apiUrl="/web/train/scene/page" ref="machineRef">
    <template #default="{ tableData }">
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column prop="sceneCode" label="场景编码" align="center" width="200px"/>
        <el-table-column prop="sceneName" label="场景名称" align="center" width="200px"/>
        <el-table-column prop="robotName" label="智能体名称" align="center" width="120px"/>
        <el-table-column prop="robotImage" label="智能体头像" align="center" width="120px" >
          <template #default="scope">
            <img :src="scope.row.robotImage" style="width:40px;height:40px;text-align:center;position:relative;left:40%"/>
          </template>
        </el-table-column>
        <el-table-column prop="robotInfo" label="智能体描述" align="center" min-width="180px">
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.robotInfo" />
             <!-- 1111{{ scope.row.robotInfo }} -->
          </template>
        </el-table-column>
        <el-table-column prop="scenePrompt" label="场景提示词" align="center" min-width="180px" >
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.scenePrompt" />
          </template>
        </el-table-column>
        <el-table-column prop="scorePrompt" label="打分提示词" align="center" min-width="180px" >
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.scorePrompt" />
          </template>
        </el-table-column>
        <el-table-column prop="memoryCacheNum" label="对话缓存条数" align="center" min-width="150px" />
        <el-table-column prop="passScore" label="及格分数" align="center" min-width="100px" />
        <el-table-column prop="createBy" label="更新人" align="center" min-width="80px">
          <template #default="scope">
            {{ getNickName(scope.row.createBy) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" align="center" min-width="180px" />
        
        <el-table-column prop="code" label="操作" align="center"  fixed="right" width="200px">
          <template #default="scope">
            <el-button link type="primary" @click="onEdit(scope.row)">编辑</el-button>
            <el-button link type="primary" @click="onRemove(scope.row)">删除</el-button>
            <!-- <el-button link type="primary" @click="onDetails(scope.row)">查看明细</el-button> -->
            <el-button link type="primary" @click="onScore(scope.row)">查看成绩</el-button>
            <!-- <el-button link type="primary" @click="onConversation(scope.row)">查看对话</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>
</template>

<script setup lang="ts">
import type { ToolListProps, SearchFormTemp } from '@/components/types'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import useChannel from '@/stores/channel'
import { useRouter } from 'vue-router'
import { ref, onActivated } from 'vue'
import tooltipWithFn from '@/views/dashboardManage/components/tooltipWithFn.vue'
import * as api from '@/api/machineConfig'
const { setChannel } = useChannel()
const { getNickName, getDicItemName } = useFilters()
const { getDic } = useUserStore()
const router = useRouter()
const machineRef = ref()
const pageTitle = '场景列表'
defineOptions({ name: 'machineManage' })
 function getParams() {
  console.log(machineRef.value)
  if (!machineRef.value) return {}
  const p =  machineRef.value.getSearchQueryexport()
  console.log(p)
  //console.debug(p)
  return p
}
const toolList: ToolListProps[] = [
  {
    name: '新增',
    icon: 'add',
    btnCode: '',
    action: async () => {
      router.push({ path: '/machineAdd' })
    },
  }
]

const searchForm: SearchFormTemp[] = [
  {
  label: '场景编码',
  name: 'sceneCode',
  type: 'input',
  placeholder: '请输入场景编码',
  searchState: false
  },
  {
  label: '场景名称',
  name: 'sceneName',
  type: 'input',
  placeholder: '请输入场景名称',
  searchState: false
  },
  {
  label: '智能体名称',
  name: 'robotName',
  type: 'input',
  placeholder: '请输入智能体名称',
  searchState: false
  },
]


// onActivated(async () => {
//   await machineRef.value?.onSearchBtn()
// })
/**
* @description 删除
*/
const onRemove = async (data: any) => {
ElMessageBox.confirm('确定删除该模型吗？', '提示', {
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  type: 'warning',
}).then(async () => {
  await api.trainDelete(data.id)
  await machineRef.value.onSearchBtn()
})
}
// 编辑
const onEdit = (data: any) => {
  setChannel(data)
  router.push({
    path: '/machineAdd',
    query: {
      id: data.id ?? ''
    },
  })
}
// 查看明细
const onDetails = (data: any) => {
  setChannel(data)
  router.push({
    path: '/machineDetails',
    query: {
      id: data.id ?? ''
    },
  })
}
// 查看成绩
const onScore = (data: any) => {
  setChannel(data)
  router.push({
    path: '/machineScore',
    query: {
      sceneCode: data.sceneCode ?? ''
    },
  })
}
// 查看明细
const onConversation = (data: any) => {
  setChannel(data)
  router.push({
    path: '/viewConversation',
    query: {
      sceneCode: data.sceneCode ?? ''
    },
  })
}
onActivated(async () => {
    await machineRef.value?.onSearchBtn()
  })
</script>

<style lang="less">

.awidth {
    width: 100%;
    height: 20px;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    left: -5px;
    right: -10px;
  }
  /deep/.awidthTooltip{
    white-space: wrap;
    max-width: 100px;
    display: block;
    max-height: 100px;
  }
</style>
