<template>
    <!-- <TableToolTemp :toolTitle="pageTitle" :toolList="toolList" /> -->
    <DtNav :from="nav.from" :fromPath="'/machineManage'"  :name="nav.to" />
    <PageTable :searchFormTemp="searchForm" apiUrl="/web/train/scene/detail"  :param="{ sceneCode }" ref="modelRef">
      <template #default="{ tableData }">
        <el-table :data="tableData" class="dt-table" stripe>
          <el-table-column prop="sceneCode" label="场景编码" align="center" width="200px"/>
        <el-table-column prop="sceneName" label="场景名称" align="center" width="200px"/>
        <el-table-column prop="robotName" label="智能体名称" align="center" width="120px"/>
        <el-table-column prop="robotImage" label="智能体头像" align="center" width="120px" >
          <template #default="scope">
            <img :src="scope.row.robotImage" style="width:40px;height:40px;text-align:center;position:relative;left:40%"/>
          </template>
        </el-table-column>
        <el-table-column prop="scenePrompt" label="场景提示词" align="center" min-width="180px" >
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.scenePrompt" />
          </template>
        </el-table-column>
        <el-table-column prop="scorePrompt" label="打分提示词" align="center" min-width="180px" >
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.scorePrompt" />
          </template>
        </el-table-column>
          <el-table-column prop="code" label="操作" align="center" width="300px">
            <template #default="scope">
              <el-button link type="primary" @click="onEdit(scope.row)">查看对话</el-button>
              <!-- <el-button link type="primary" @click="onOpen(scope.row)">设置通用模型</el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>
  </template>
  
  <script setup lang="ts">
  import type { ToolListProps, SearchFormTemp } from '@/components/types'
  import useFilters from '@/hooks/useFilters'
  import { useUserStore } from '@/stores/user'
  import useChannel from '@/stores/channel'
  import { useRouter,useRoute } from 'vue-router'
  import { ref, onActivated,onBeforeMount,reactive } from 'vue'
  import * as api from '@/api/model'
  const modelRef = ref()
  const { setChannel } = useChannel()
  const { getNickName, getDicItemName } = useFilters()
  const { getDic } = useUserStore()
  const route = useRoute()
  const router = useRouter()
  const sceneCode = ref('')
  const nav = reactive({
    from: '场景列表',
    to: "明细列表",
  })
  
  const pageTitle = '明细列表'
  const toolList: ToolListProps[] = [
    {
      name: '新增',
      icon: 'add',
      btnCode: '',
      action: async () => {
        router.push({ path: '/modelAdd' })
      },
    },
  ]
  
  const searchForm: SearchFormTemp[] = [
   
    // {
    // label: '场景编码',
    // name: 'sceneCode',
    // type: 'input',
    // placeholder: '请输入场景编码',
    // list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('platform.name.type')],
    // searchState: true
    // },
    // {
    // label: '模型名称',
    // name: 'modelName',
    // type: 'input',
    // placeholder: '请输入模型名称',
    // searchState: true
    // },
    // {
    //   label: '类型',
    //   name: 'type',
    //   type: 'select',
    //   placeholder: '请选择类型',
    //   list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('platform.name.type')],
    // },
  ]
  /**
  * @description 删除
  */
  const onRemove = async (data: any) => {
  ElMessageBox.confirm('确定删除该模型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.modelDelete(data.id)
    await modelRef.value.onSearchBtn()
  })
  }
  const onEdit = (data: any) => {
    setChannel(data)
    router.push({
      path: '/machineScore',
      query: {
        sceneCode: data.sceneCode ?? ''
      },
    })
  }
  
  const onOpen = async (data: any) => {
    ElMessageBox.confirm('确定将该模型设置为通用模型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.modelsetCommonModel(data.id)
    await modelRef.value.onSearchBtn()
  })
  }
  
onBeforeMount(async () => {
  sceneCode.value= (route.query.sceneCode as string) ?? ''
})
  onActivated(async () => {
    await modelRef.value?.onSearchBtn()
  })
  </script>
  
  <style lang="less">
  
  .awidth {
      width: 100%;
      height: 20px;
      display: block;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      position: relative;
      left: -5px;
      right: -10px;
    }
    /deep/.awidthTooltip{
      white-space: wrap;
      max-width: 100px;
      display: block;
      max-height: 100px;
    }
  </style>
  