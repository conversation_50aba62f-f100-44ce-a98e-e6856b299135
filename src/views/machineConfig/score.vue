<template>
  <DtNav :from="nav.from" :fromPath="'/machineManage'"  :name="nav.to"/>
  <TableToolTemp :toolTitle="pageTitle" :toolList="toolList" />
    <SearchForm :searchFormTemp="searchFormTemp" @handleSearch="handleSearch" @handleResetQuery="resetSearch" ref="scoreRef">
    </SearchForm>
    <el-table :data="tableData" class="dt-table" stripe>
      <el-table-column prop="sceneCode" label="场景编码" align="center" />
      <el-table-column prop="source" label="来源" align="center" />
      <el-table-column prop="userName" label="用户姓名" align="center" />
      <el-table-column prop="userId" label="userId" align="center" />
      <el-table-column prop="score" label="分数" align="center" width="120px"/>
      <el-table-column prop="createTime" label="创建时间" align="center" width="200px"/>
      <el-table-column prop="code" label="操作" align="center" width="300px">
        <template #default="scope">
          <el-button link type="primary" @click="onEdit(scope.row)">查看对话</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination ref="paginationRef" @change="onPageData" :total="total" />
</template>

<script setup lang="ts">
import type { ToolListProps, PageData, PageTableSearch,SearchFormTemp } from '@/components/types'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import useChannel from '@/stores/channel'
import { useRouter,useRoute } from 'vue-router'
import { ref, onActivated,onBeforeMount,reactive } from 'vue'
import * as api from '@/api/machineConfig'
const scoreRef = ref()
const { setChannel } = useChannel()
const { getNickName, getDicItemName } = useFilters()
const { getDic } = useUserStore()
const route = useRoute()
const router = useRouter()
const sceneCode = ref('')
const total = ref<number>(0)
const pageParam = ref<PageTableSearch>({
  pageNum: 1,
  pageSize: 10,
  param: {
    sceneCode: '',
    userName: '',
    userId: ''
  }
})
const tableData = ref<Record<string, any>[]>([])
defineOptions({ name: 'machineScore' })
const nav = reactive({
  from: '场景列表',
  to: "成绩列表",
})
function getParams() {
  pageParam.value.param.sceneCode = (route.query.sceneCode as string) ?? ''
  return pageParam.value
}
const pageTitle = '成绩列表'
const toolList: ToolListProps[] = [
  {
      name: '导出',
      icon: 'down',
      btnCode: '',
      download: {
        url: '/web/train/score/export',
        params: getParams,
        isPost: true,
        check: () => {
          return true
        },
      },
    },
]

const searchFormTemp: SearchFormTemp[] = [
  {
    label: '用户姓名',
    name: 'userName',
    type: 'input',
    placeholder: '请输入用户姓名',
    searchState: false
  },
  {
    label: '用户ID',
    name: 'userId',
    type: 'input',
    placeholder: '请输入用户ID',
    searchState: false
  },
]
const onEdit = (data: any) => {
  setChannel(data)
  router.push({
    path: '/viewConversation',
    query: {
      sceneCode: data.sceneCode ?? '',
      source: data.source ?? '',
      externalId: data.externalId ?? ''
    },
  })
}
const handleSearch = (data: any) => {
  pageParam.value.param = data
  pageParam.value.param.sceneCode = (route.query.sceneCode as string) ?? ''
  initTableData()
}

const resetSearch = (data: any) => {
  pageParam.value.param = data
  pageParam.value.param.sceneCode = (route.query.sceneCode as string) ?? ''
  initTableData()
}

const onPageData = (data: PageData) => {
  pageParam.value.pageNum = data.pageNum
  pageParam.value.pageSize = data.pageSize
  initTableData()
}
const initTableData = async () => {
  const res = await api.trainPage(pageParam.value)
  if (res.status === 0) {
    tableData.value = res.data.list
    total.value = res.data.total
  }
}

onBeforeMount(async () => {
sceneCode.value= (route.query.sceneCode as string) ?? ''
pageParam.value.param.sceneCode = (route.query.sceneCode as string) ?? ''
})
onActivated(async () => {
  sceneCode.value= (route.query.sceneCode as string) ?? ''
  pageParam.value.param.sceneCode = (route.query.sceneCode as string) ?? ''
  initTableData()
})
</script>

<style lang="less">

.awidth {
    width: 100%;
    height: 20px;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    left: -5px;
    right: -10px;
  }
  /deep/.awidthTooltip{
    white-space: wrap;
    max-width: 100px;
    display: block;
    max-height: 100px;
  }
</style>
