<template>
  <!-- <TableToolTemp :toolTitle="pageTitle" :toolList="toolList" /> -->
  <DtNav :from="nav.from" :fromPath="'/machineManage'" :subFrom="nav.subFrom"  :name="nav.to" />
  <PageTable :searchFormTemp="searchForm" apiUrl="/web/train/scene/record/page"  :param="{ sceneCode,externalId,source }" ref="modelRef">
    <template #default="{ tableData }">
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column prop="question" label="顾问" align="center">
          <template #default="scope">
            <tooltip-with-fn format="md" :content="scope.row.question"  />
          </template>
        </el-table-column>
        <el-table-column prop="answer" label="客户(AI)" align="center">
          <template #default="scope">
          <tooltip-with-fn format="md" :content="scope.row.answer"  />
          </template>
        </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center"/>
      </el-table>
    </template>
  </PageTable>
</template>

<script setup lang="ts">
import type { ToolListProps, SearchFormTemp } from '@/components/types'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import useChannel from '@/stores/channel'
import { useRouter,useRoute } from 'vue-router'
import { ref, onActivated,onBeforeMount,reactive } from 'vue'
import tooltipWithFn from '@/views/dashboardManage/components/tooltipWithFn.vue'
import * as api from '@/api/model'
const modelRef = ref()
const { setChannel } = useChannel()
const { getNickName, getDicItemName } = useFilters()
const { getDic } = useUserStore()
const route = useRoute()
const router = useRouter()
const sceneCode = ref('')
const source = ref('')
const externalId = ref('')
const nav = reactive({
  from: '场景列表',
  subFrom: '成绩列表',
  to: "对话列表",
})
// defineOptions({ name: 'viewConversation' })
const pageTitle = '对话列表'
const toolList: ToolListProps[] = [
  // {
  //   name: '新增',
  //   icon: 'add',
  //   btnCode: '',
  //   action: async () => {
  //     router.push({ path: '/modelAdd' })
  //   },
  // },
]

const searchForm: SearchFormTemp[] = [
//   {
//     label: '场景名称',
//     name: 'sceneName',
//     type: 'input',
//     placeholder: '请输入场景名称',
//     searchState: undefined
// }
  // {
  // label: '模型名称',
  // name: 'modelName',
  // type: 'input',
  // placeholder: '请输入模型名称',
  // searchState: true
  // },
  // {
  //   label: '类型',
  //   name: 'type',
  //   type: 'select',
  //   placeholder: '请选择类型',
  //   list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('platform.name.type')],
  // },
]
/**
* @description 删除
*/
const onRemove = async (data: any) => {
ElMessageBox.confirm('确定删除该模型吗？', '提示', {
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  type: 'warning',
}).then(async () => {
  await api.modelDelete(data.id)
  await modelRef.value.onSearchBtn()
})
}
const onEdit = (data: any) => {
  setChannel(data)
  router.push({
    path: '/modelAdd',
    query: {
      id: data.id ?? ''
    },
  })
}

const onOpen = async (data: any) => {
  ElMessageBox.confirm('确定将该模型设置为通用模型吗？', '提示', {
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  type: 'warning',
}).then(async () => {
  await api.modelsetCommonModel(data.id)
  await modelRef.value.onSearchBtn()
})
}
const formatAnwser = (answer) => {
    try {
      answer = JSON.parse(answer || "")[0].text
      const str = answer.replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      console.log('answer: ', str)
      return str
    } catch (e) {
      return answer || ""
    }
}

onBeforeMount(async () => {
  sceneCode.value= (route.query.sceneCode as string) ?? ''
  source.value= (route.query.source as string) ?? ''
  externalId.value= (route.query.externalId as string) ?? ''
})
onActivated(async () => {
  await modelRef.value?.onSearchBtn()
})
</script>

<style lang="less">

.awidth {
    width: 100%;
    height: 20px;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    left: -5px;
    right: -10px;
  }
  /deep/.awidthTooltip{
    white-space: wrap;
    max-width: 100px;
    display: block;
    max-height: 100px;
  }
</style>
