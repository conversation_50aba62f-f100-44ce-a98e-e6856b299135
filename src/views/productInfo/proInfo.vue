<template>
    <!-- <TableToolTemp :toolTitle="pageTitle" :toolList="toolList" /> -->
    <DtNav :from="nav.from" :fromPath="'/productInfo?index=1'"   :name="nav.to" />
    <TableToolTemp :toolTitle="route.query.productName"  :toolList="toolList" />
    <PageTable :searchFormTemp="searchForm" apiUrl="/web/product/file/page"  :param="{ productId }" ref="proInfoRef">
      <template #default="{ tableData }">
        <el-table :data="tableData" class="dt-table" stripe>
          <el-table-column prop="fileId" label="文件ID" align="center"/>
          <el-table-column prop="fileName" label="文件名称" align="center"/>
          <el-table-column prop="fileType" label="文件类型" align="center">
          <template #default="scope">
            {{ getDicItemName(scope.row.fileType, 'chatbot.product.fileType') }}
          </template>
          </el-table-column>
          <el-table-column prop="source" label="文件来源" align="center">
          <template #default="scope">
            {{ getDicItemName(scope.row.source, 'chatbot.product.source') }}
          </template>
          </el-table-column>
          <el-table-column prop="contentLength" label="字符数" align="center"/>
          <el-table-column prop="syncStatus" label="解析状态" align="center">
            <template #default="scope">
                <span v-if="scope.row.syncStatus=='1'">{{ getDicItemName(scope.row.syncStatus, 'chatbot.product.syncStatus')}}</span>
                <span v-else style="color:red">{{ getDicItemName(scope.row.syncStatus, 'chatbot.product.syncStatus') }}</span>
            </template>
          </el-table-column>
        <el-table-column prop="createTime" label="更新时间" align="center"/>
        <el-table-column label="操作" align="center" fixed="right" width="300px">
          <template #default="scope">
            <el-button type="text" @click="handleDetail(scope.row, 0)">查看切片</el-button>
            <el-button type="text" @click="handleDetail(scope.row, 1)">查看解析原文</el-button>
            <el-button type="text" @click="handleDetail(scope.row, 2)">网页打开</el-button>
            <el-button v-if="scope.row.source=='upload'" type="text" @click="onRemove(scope.row)">删除</el-button>
          </template>
        </el-table-column>
        </el-table>
      </template>
    </PageTable>
    <!-- 知识库iframe -->
    <div class="iframe-dialog" v-if="showKnowledgeIframe">
      <DtIcon icon-name="icondt25" class="popup-icon" @click="closeKnowledgeIframe" />
      <iframe :src="iframeSrc" frameborder="0" height="100%" width="100%"></iframe>
    </div>
     <!--日志报文-->
    <DtPopup :show="showEdit1" @close="showEdit1 = false" width="65%" :title="`解析原文`" :footer="false">
      <!-- <div v-for="(value, key) in jsonData" :key="key">
        <strong>{{ key }}:</strong> {{ value }}
      </div> -->
      <div class="inner">
        <div class="inner__content" v-html="strToHTML(jsonData)"></div>
      </div>
    </DtPopup>
    <DtPopup :show="showAdd" @close="closeDialog" @close-by-icon="closeDialog" :title="`新增产品资料`" :footer="false">
      <el-form label-width="80px" label-position="right" :model="addForm" :rules="addRules" ref="formTempRef">
          <el-form-item label="文件类型" prop="fileType">
              <el-select v-model="addForm.fileType" placeholder="请选择文件类型">
                <el-option
                  v-for="item in initList"
                  :key="item.dicItemCode"
                  :label="item.dicItemName"
                  :value="item.dicItemCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
      </el-form>
      <div class="formBtn">
        <el-button class="dt-btn" @click="closeDialog">取消</el-button>
        <el-button class="dt-btn" type="primary" @click="submit">上传文件并保存</el-button>
      </div>
    </DtPopup>
    <input type="file" @change="handleFileUpload" multiple    name="name" id="fileInput"  style="display: none;"/> 
    <!-- 切片 -->

  <DtPopup :show="showslice" @close="closeslice" @close-by-icon="closeslice" title="切片详情" :footer="false" :isFullscreen="true">
      <!-- <sliceManagement  /> -->
      <sectionDetail v-if="showslice" :fileId="fileId" />
  </DtPopup>
  </template>
  
  <script setup lang="ts">
  import type { ToolListProps, SearchFormTemp } from '@/components/types'
  import useFilters from '@/hooks/useFilters'
  import { useUserStore } from '@/stores/user'
  import useChannel from '@/stores/channel'
  import { useRouter,useRoute } from 'vue-router'
  import sectionDetail from'@/views/productInfo/proScilce.vue'
  import { ref, onActivated,onBeforeMount,reactive } from 'vue'
  import { productOriginal,productFileDel,productFileAdd} from './api'
  import { micromark } from 'micromark'
  import { exportByUrl } from '@/utils'
  // import type { FormRules } from 'element-plus'
import { gfmTable, gfmTableHtml } from 'micromark-extension-gfm-table'
  const proInfoRef = ref()
  const { getNickName, getDicItemName } = useFilters()
  const { getDic } = useUserStore()
  const route = useRoute()
  const router = useRouter()
  const productId = ref('')
  const fileId = ref('')
  const showslice= ref<boolean>(false)
  const source = ref('')
  const showKnowledgeIframe = ref<boolean>(false)
  const iframeSrc = ref<string>('')
  const jsonData = ref(null)
  const showEdit1 = ref(false)
  const showAdd = ref(false)
  const title = ref(null)
  const selectedFile  = ref<any[]>([]);
  const formTempRef = ref(null)
  const initList = ref<Record<string, any>[]>([...getDic('chatbot.product.fileType')])
  const nav = reactive({
    from: '产品资料',
    to: "产品资料列表",
  })
  defineOptions({ name: 'proInfo' })
  const pageTitle = '产品资料列表'
  const toolList: ToolListProps[] = [
    {
      name: '新增',
      icon: 'add',
      btnCode: '',
      action: async () => {
        showAdd.value=true
      },
    },
  ]
  const  addForm= ref<Record<string, any>>({
        fileType: ""
  })
  const searchForm: SearchFormTemp[] = [
    {
    label: '文件名',
    name: 'fileName',
    type: 'input',
    placeholder: '请输入文件名',
    searchState: true
    },
    {
      label: '解析状态',
      name: 'syncStatus',
      type: 'select',
      placeholder: '请选择解析状态',
      list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.product.syncStatus')],
    },
    {
      label: '文件来源',
      name: 'source',
      type: 'select',
      placeholder: '请选择文件来源',
      list: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.product.source')],
    },
  ]
const  addRules = {
  fileType: [{ required: true, message: '请选择文件类型', trigger: 'blur' }],
}
  /**
  * @description 删除
  */
  const onRemove = async (data: any) => {
    if(data.source=='upload'){
      ElMessageBox.confirm('确定删除该产品文件资料吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await productFileDel({fileId:data.fileId})
        await proInfoRef.value.onSearchBtn()
      })
    }else{
        return ElMessage.error('请删除文件来源为上传的文件')
    }
 
  }
  // 切片管理
const closeslice = async () => {
  showslice.value = false
}
  const handleDetail = async (data: any, type: number) => {
    console.log(data,type)
    if (type == 0) {
      // router.push({
      //   path: '/proScilce',
      //   query: {
      //     fileId: data.fileId,
      //   },
      // })
      showslice.value=true
      fileId.value= data.fileId
    } else if(type==1) {
      let res = await productOriginal({ fileId: data.fileId })
      console.log(res)
      if (res.data && res.data.content) {
        showEdit1.value = true
        title.value =data.fileName
        jsonData.value = res.data.content
        // this.total = res.total / 1 || 0;
      } else {
        return ElMessage.error('无原文资料')
      }
    }else if(type==2){
      let ext=data.fileName.split('.').pop()
      if(ext=='docx' || ext=='xlsx'  ||  ext=='pptx'  ||  ext=='txt'  || ext=='xls' ||  ext=='ppt'  ||  ext=='doc'){
        window.open(data.fileUrl);
      }else if (ext=='html'){
        exportByUrl(data.fileUrl,data.fileName)
      }else {
        iframeSrc.value = data.fileUrl
        showKnowledgeIframe.value = true
      }
    
    }

    // this.tableDetailData = data;
  }
const closeKnowledgeIframe = () => {
  showKnowledgeIframe.value = false
}
const closeDialog = async () => {
  showAdd.value = false
  addForm.value.fileType=""
}
const submit = async () => {
  console.log(addForm.value)
  if(!addForm.value.fileType){
   return ElMessage.warning('请选择文件类型！')
  }
  document.getElementById('fileInput').click()
}

const handleFileUpload = async(event:any) => {
  if (event.target.files.length==0) {
      ElMessage.warning('请选择一个文件！')
      return;
   }
  const data=[]
  console.log(event.target.files)
  for(let i=0;i<event.target.files.length;i++){
    if(event.target.files[i].size > 52428800){
      ElMessage.warning('文件大小不能超过50MB')
      return;
    }else{
      console.log(event.target.files[i])
     data.push(event.target.files[i])
    }
  }
   
   // 增加新文件
  selectedFile.value= selectedFile.value.concat(data)
   await submitFile(selectedFile.value)
}
const strToHTML = (str) => {
    str = micromark(str, {
      extensions: [gfmTable()],
      htmlExtensions: [gfmTableHtml()],
    })
  return str
}
/**
 * @description 上传文件
 */
 const submitFile = async (dataItem:any) => {
  console.log(dataItem)
  const formData = new FormData()
  for(let i=0;i<dataItem.length;i++){
    formData.append('files', dataItem[i])
  }
  formData.append('fileType', addForm.value.fileType) 
  formData.append('productId', productId.value)
  const res = await productFileAdd(formData)
    console.log(res)
    if(res && res.status==0){
      ElMessage.success('上传成功')
      selectedFile.value=[]
      showAdd.value = false
      addForm.value.fileType=""
      await proInfoRef.value.onSearchBtn()
    }
  
}

  onBeforeMount(async () => {
    productId.value= (route.query.productId as string) ?? ''
    // source.value= (route.query.source as string) ?? ''
    // externalId.value= (route.query.externalId as string) ?? ''
  })
  onActivated(async () => {
    await proInfoRef.value?.onSearchBtn()
  })
  </script>
  
  <style lang="less">
  
  .awidth {
      width: 100%;
      height: 20px;
      display: block;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      position: relative;
      left: -5px;
      right: -10px;
    }
    /deep/.awidthTooltip{
      white-space: wrap;
      max-width: 100px;
      display: block;
      max-height: 100px;
    }
    .iframe-dialog {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #fff;
      z-index: 99999;

      .popup-icon {
        position: absolute;
        font-size: 65px;
        top: 7px;
        right: -61px;
        color: #b8b8b8;
        cursor: pointer;
      }
    }
    .inner {
  position: relative;

  white-space: wrap;
  display: inline-block;
  // max-width: 700px;
  max-height: 400px;
  overflow-y: auto;

  margin-bottom: 20px;

  &__content {
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 1em;
    }

    th,
    td {
      border: 1px solid #ccc;
      padding: 0.5em;
      text-align: left;
    }

    th {
      background-color: #f0f0f0;
      font-weight: bold;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }
}
.formBtn{
  text-align: right;
}
  </style>
  