<template>
  <!-- <DtNav from="数据看板" name="数据看板"></DtNav> -->
  <el-tabs v-model="versionIndex" type="border-card" class="tabs" @tab-change="versionSelect">
    <el-tab-pane :name="'1'" label="产品资料" v-auth="'chatbot:board:recepTab:show'">
      <TableToolTemp toolTitle="产品资料"/>
      <SearchForm
        ref="recepSearchRef"
        :searchFormTemp="receptionSearchParams"
        @handleSearch="setReceptionParam"
        @handleResetQuery="receptionResetSearch"
      >
      </SearchForm>
      <el-table :data="receptionData" stripe class="dt-table">
        <el-table-column prop="productId" label="产品ID" align="center" > </el-table-column>
        <el-table-column align="center" prop="productName" label="产品名称" ></el-table-column>
        <el-table-column align="center" prop="fileCount" label="文件数量" ></el-table-column>
        <el-table-column align="center" prop="failFileCount" width="200" label="解析失败数量"></el-table-column>
            <el-table-column label="操作" align="center" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="handleDetail(scope.row,1)">Base切片</el-button>
            <el-button type="text" @click="handleDetail(scope.row,2)">产品资料</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination ref="receptionRef" @change="receptionChange" :total="receptionTotal" />
    </el-tab-pane>
    <el-tab-pane :name="'2'" label="保司资料" v-auth="'chatbot:board:analysis:show'">
      <companyFile ref="companyFileRef" />
    </el-tab-pane>
  </el-tabs>

  <!-- 知识库iframe -->
  <div class="iframe-dialog" v-if="showKnowledgeIframe">
    <DtIcon icon-name="icondt25" class="popup-icon" @click="closeKnowledgeIframe" />
    <iframe :src="iframeSrc" frameborder="0" height="100%" width="100%"></iframe>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import type { SearchFormTemp } from '@/components/types'
import { productPage } from './api'
import { onMounted,onActivated } from 'vue'
import useFilters from '@/hooks/useFilters'
import useSystemStore from '@/stores/system'
import companyFile from './components/companyFile.vue'
import sliceManagement from '@/views/section/sliceManagement.vue'
import { httpServe } from '@/utils/httpServe'
import { exportData } from '@/utils'
import { useRouter,useRoute } from 'vue-router'
defineOptions({ name: 'productInfo' })
const router = useRouter()
const route = useRoute()
const companyFileRef = ref()
const recepSearchRef = ref()
const systemStore = useSystemStore()
const { getDicItemName } = useFilters()
const versionIndex = ref<string>('1')
versionIndex.value = route.query.index? route.query.index:'1'
const versionSelect = async (index: any) => {
  versionIndex.value = index
  if (index === '1') {
    initReception()
    // receptionResetSearch()
  } else if (index === '2') {
    companyFileRef.value?.search()
    // historyResetSearch()
  }
}
const receptionData = ref<Record<string, any>[]>([])
const historyData = ref<Record<string, any>[]>([])
const receptionSearchParams: SearchFormTemp[] = [
  {
  label: '产品ID',
  name: 'productId',
  type: 'input',
  placeholder: '请输入',
  searchState: true
  },
    {
  label: '产品名称',
  name: 'productName',
  type: 'input',
  placeholder: '请输入',
  searchState: true
  },
]
const historySearchParams: SearchFormTemp[] = [
  {
    label: '提问内容',
    name: 'content',
    type: 'input',
    placeholder: '请输入内容',
    searchState: true
    },
      {
    label: '会话Id',
    name: 'sessionId',
    type: 'input',
    placeholder: '请输入会话Id',
    searchState: true
    },
]
const receptionTotal = ref<number>(0)
const historyTotal = ref<number>(0)
const receptionParams = ref<Record<string, any>>({
  pageNum: 1,
  pageSize: 10,
  param: {},
})
const historyParams = ref<Record<string, any>>({
  pageNum: 1,
  pageSize: 10,
  param: {},
})

const receptionRef = ref(null)
const historyRef = ref(null)

const iframeSrc = ref<string>('')
const showKnowledgeIframe = ref<boolean>(false)
const handleDetail = async (data: any,type:number) => {
if(type==2){
  router.push({
    path: '/proInfo',
    query: {
      productId: data.productId,
      productName: data.productName,
    },
  })
}else{
  router.push({
    path: '/infoScile',
    query: {
      productId: data.productId,
    },
  })
}
 
}
const closeKnowledgeIframe = () => {
  showKnowledgeIframe.value = false
}

const setReceptionParam = (param: Record<string, any>) => {
  receptionParams.value.param = param
  initReception()
}

const receptionResetSearch = async (data: any) => {
  receptionParams.value.param = data
  await receptionRef.value.normalReset()
  initReception()
}

const receptionChange = (val: Record<string, any>) => {
  receptionParams.value.pageNum = val.pageNum
  receptionParams.value.pageSize = val.pageSize
  initReception()
}

const setHistoryParam = (param: Record<string, any>) => {
  historyParams.value.param = param
  initHistory()
}

const historyChange = (val: Record<string, any>) => {
  historyParams.value.pageNum = val.pageNum
  historyParams.value.pageSize = val.pageSize
  initHistory()
}

const historyResetSearch = async (data: any) => {
  // console.log(data)
  console.log(companyFileRef.value)
  // historyParams.value.param = data
  // await companyFileRef.value.normalReset()
  // initHistory()
}

const initReception = async () => {
  const res = await productPage(receptionParams.value)
  receptionData.value = res.data.list
  receptionTotal.value = res.data.total
  initSearchValue()
}

const initKnowledge = () => {
  console.log('initKnowledge')
}

const initQuestion = () => {
  console.log('initQuestion')
}

const initHistory = async () => {
  // const res = await pageRecord(historyParams.value)
  // historyData.value = res.data.list
  // historyTotal.value = res.data.total
}

const initSearchValue = async () => {
  receptionSearchParams[1]
  // const robotIdList = await searchRobotPage({ pageNum: 1, pageSize: 999999, param: {} })
  // console.log(robotIdList)
  // receptionSearchParams[0].list = robotIdList.data.map((item: any) => {
  //   return {
  //     dicItemName: item.robotName,
  //     dicItemCode: item.projectId,
  //   }
  // })
  // const channelIdList = await searchChannelPage({ pageNum: 1, pageSize: 999999, param: {} })
  // console.log(channelIdList)
  // receptionSearchParams[1].list = channelIdList.data.list.map((item: any) => {
  //   return {
  //     dicItemName: item.channelName,
  //     dicItemCode: item.projectName,
  //   }
  // })
}

const exportTb = async () => {
  const res = await httpServe(
    '/web/databoard/export',
    {
      ...recepSearchRef.value.getSearchQuery(),
    },
    {
    isExport: true,
    method: 'post',
    customPrefix: ''
}
  )
  if (res.status === 0) {
    exportData(res.data, res.fileName)
  }
}

onMounted(async () => {
  initReception()
  // await recepSearchRef.value?.onSearchBtn()
  
})
 onActivated(async () => {
  initReception()
  })
</script>

<style lang="less" scoped>
.tb-header {
  display: flex;
  align-items: center;
  justify-content: center;
  &__icon {
    margin-left: 5px;
  }
}
.tabs {
  border-bottom: none;
}

.iframe-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 99999;

  .popup-icon {
    position: absolute;
    font-size: 65px;
    top: 7px;
    right: -61px;
    color: #b8b8b8;
    cursor: pointer;
  }
}
</style>
