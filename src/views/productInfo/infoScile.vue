<template>
   <DtNav :from="nav.from" :fromPath="'/productInfo'"   :name="nav.to" />
  <TableToolTemp tool-title="切片详情" :toolList="toolList" />
  <PageTable ref="pageRef" :search-form-temp="searchFormTemp" :param="param" list-name="items" apiUrl="/web/product/sliceList">
    <template #default="{ tableData }">
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column align="left" prop="status">
          <template #default="scope">
            <div class="table">
              <div class="content">
                {{ scope.row.text }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>

  <!--编辑弹窗-->
  <DtPopup v-model:show="showFormPop" :title="title" :footer="false" @close-by-icon="closePop">
    <el-form label-width="80px" label-position="right" :model="form" ref="formRef">
      <el-form-item v-if="formTitle" label="标题">
        <el-input type="text" class="full-width-input" :disabled="true" v-model="formTitle"> </el-input>
      </el-form-item>
      <el-form-item label="切片内容" prop="text">
        <el-input type="textarea" :rows="7" placeholder="请输入内容" v-model="form.text"> </el-input>
      </el-form-item>
    </el-form>

    <div class="popup-footer">
      <el-button class="dt-btn" plain @click="closePop(false)">取消</el-button>
      <el-button type="primary" class="dt-btn" @click="onSave">保存</el-button>
    </div>
  </DtPopup>
</template>

<script setup lang="ts">
import type { ToolListProps, SearchFormTemp } from '@/components/types'
import type { FormInstance } from 'element-plus'

import * as api from './api'

import { reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const formRef = ref<FormInstance>()
const pageRef = ref(null)
const baseUrl = import.meta.env.VITE_APP_MASS_API
const showFormPop = ref(false)
const title = ref('')
const formTitle = ref('')

const formType = ref('add') // add | edit
const props = withDefaults(
  defineProps<{
    sliceId?: string
  }>(),
  {
    sliceId: '',
  }
)
const nav = reactive({
    from: '产品资料维护',
    to: "base切片",
  })
const toolList:ToolListProps[] = [
// {
//     name: '返回',
//     icon: 'back',
//     btnCode: '',
//     action: async () => {
//       router.back()
//     },
//   },
]

const searchFormTemp: SearchFormTemp[] = [
  {
    label: '切片内容',
    name: 'keyword',
    placeholder: '请输入切片内容',
    type: 'input',
  },
]

const form = reactive({
  text: '',
  id: '',
})
console.log(props.sliceId)
const param = reactive({
  productId: route.query.productId?route.query.productId:'',
})

/******************* methods ********************/

// 编辑切片
function handleEdit(row: any) {
  showFormPop.value = true
  title.value = '编辑切片'
  formType.value = 'edit'
  form.text = row.text
  form.id = row.id
}

function closePop(fresh?: boolean) {
  //formRef.value && formRef.value.clearValidate()
  formRef.value && formRef.value.resetFields()

  form.text = ""
  form.id = ""

  showFormPop.value = false


  if (fresh) pageRef.value && pageRef.value.onSearchBtn()
}

//编辑弹窗
async function onSave() {
  if (formType.value === 'add') {
    const data = {
     productId: route.query.productId?route.query.productId:'',
      ...form,
    }
    const res = await api.knowledgeSliceAdd(data)
    if (res.status == 0) {
      closePop(true)
    }
    return
  }

  const res = await api.knowledgeSliceUpdate(form)
  if (res.status == 0) {
    closePop(true)
  }
}

function handleDelete(row: any) {
  if (row.slice_type == '1') {
    ElMessage.warning('自动切片，不允许删除!')
    return false
  }

  ElMessageBox.confirm('是否删除切片?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res = await api.sliceDel({
        id: row.id,
        productId: route.query.productId?route.query.productId:'',
      })
      if (res.status == 0) {
        ElMessage.success('删除成功!')

        pageRef.value && pageRef.value.onSearchBtn()
      }
    })
    .catch(() => {})
}

//是否禁用
function handleStatusChange(row: any) {
  const status = row.status
  const prefix = row.status == '0' ? '启用' : '禁用'
  ElMessageBox.confirm(`是否${prefix}?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res = await api.sliceStatusUpdate({
        id: row.id,
        status,
      })
      if (res.status == 0) {
        ElMessage.success(`${prefix}成功!`)
        pageRef.value && pageRef.value.onSearchBtn()
      }
    })
    .catch(() => {
      if (!pageRef.value) return
      const tmp = pageRef.value.tableData.find((item: any) => item.id == row.id)
      if (!tmp) return
      tmp.status = tmp.status == '0' ? '1' : '0'
    })
}
</script>

<style lang="less" scoped>
.log-tool {
  margin-bottom: 10px;
}
.table {
  width: 100%;
  border: 1px solid #e8e9eb;
  background: #fff;
  position: relative;

  .header {
    background: var(--el-color-primary-light-8);
    border-color: var(--el-color-primary-light-8);

    font-weight: 500;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    height: 20px;
    padding: 0 8px;
    border-radius: 0 0 4px;
    font-size: 12px;
    color: var(--el-color-primary);
  }
}
.table:hover {
  border: 1px solid var(--el-color-primary);
  background: #eef3fe;
  position: relative;
  .header {
    color: #fff;
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);

    font-weight: 500;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    height: 20px;
    padding: 0 8px;
    border-radius: 0 0 4px;
    font-size: 12px;
  }
}
.full-width-input {
  width: 100%;
  box-sizing: border-box;
}
.content {
  margin-top: 20px;
  padding: 10px;
}
.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  color: #84868c;
  font-size: 12px;
  line-height: 20px;
  .type {
    font-size: 12px;
  }
}
.detail {
  .el-table__header {
    display: none;
  }
}
.tableScopeSwitch {
  position: relative;
  margin: 0 10px;

  :deep(.el-switch__label) {
    position: absolute;
    color: red;
    z-index: 1;
    display: none;
    margin: 0 !important;

    * {
      line-height: 20px !important;
    }
  }

  :deep(.el-switch__core) {
    min-width: 55px;
  }
  :deep(.is-active) {
    display: inherit;
    color: #fff;
  }
  :deep(.el-switch__label--left) {
    left: 20px;
  }
  :deep(.el-switch__label--right) {
    right: 20px;
  }
}
.popup-footer {
  display: flex;
  justify-content: center;
}
</style>
