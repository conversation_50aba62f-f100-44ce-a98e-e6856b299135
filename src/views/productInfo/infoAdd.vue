<template>
    <DtNav :from="nav.from" :name="nav.to"  :fromPath="'/productInfo'" />
    <TableToolTemp toolTitle="人机对练场景配置" />
    <UpdateFormTemp  class="pt-5" v-if="finished" :form-list="baseList" :rules="rules" show-submit lableWidth="135px" label-position="left" @submit="submit" >
        <template #default="{ form }">
    
        <!-- <el-form-item label="所属平台" prop="platform">
            <el-select v-model="form.platform" placeholder="请选择类型" clearable>
              <el-option v-for="(item, idx) in getDic('platform.name.type')" :key="idx" :label="item.dicItemName" :value="item.dicItemCode"></el-option>
            </el-select>
        </el-form-item> -->
        <el-form-item label="对话模型" prop="modelId">
            <!-- <el-input v-model="form.modelId" placeholder="请选择模型" clearable /> -->
            <el-select v-model="form.modelId" placeholder="请选择对话模型" clearable>
                    <el-option v-for=" (el, idx) in initList" :key="idx"  :label="el.modelName"
                    :value="el.id" />
                </el-select>
        </el-form-item>
        <el-form-item label="打分模型" prop="scoreModelId">
            <!-- <el-input v-model="form.modelId" placeholder="请选择模型" clearable /> -->
            <el-select v-model="form.scoreModelId" placeholder="请选择打分模型" clearable>
                <el-option v-for=" (el, idx) in initList" :key="idx"  :label="el.modelName"
                :value="el.id" />
            </el-select>
        </el-form-item>
        </template>
    </UpdateFormTemp>
  </template>
  
  <script setup lang="ts">
  import { reactive, ref, onBeforeMount } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import type { UpdateFormList } from '@/components/types'
  import { useUserStore } from '@/stores/user'
  import type { FormRules } from 'element-plus'
  const initList = ref<Record<string, any>[]>([])
  const route = useRoute()
  import * as api from '@/api/machineConfig'
  const { getDic } = useUserStore()
  const { query } = useRoute()
  const router = useRouter()
  const isEdit = ref(false)
  const finished = ref(false)
  const nav = reactive({
    from: '场景列表',
    to: isEdit.value ? '新增':'编辑',
  })
  
  const baseList: UpdateFormList[] = [
    {
      title: '场景编码',
      type: 'input',
      field: 'sceneCode',
      disabled:false,
      maxlength:10,
      // except: true,
    },
    {
      title: '场景名称',
      type: 'input',
      field: 'sceneName',
      // except: true,
    },
    {
      title: '智能体名称',
      type: 'input',
      field: 'robotName',
      // except: true,
    },
    {
      title: '智能体头像',
      type: 'uploadimg',
      field: 'robotImage',
      value: [],
      limit: 1,
    },
    {
    title: '智能体描述',
    type: 'textarea',
    field: 'robotInfo',
    maxlength:600,
  },
  // {
  //   title: '输入框引导语',
  //   type: 'textarea',
  //   field: 'inputPlaceHolder',
  // },
  {
    title: '场景提示词',
    type: 'textarea',
    field: 'scenePrompt',
    maxlength:4000,
  },
  {
    title: '打分提示词',
    type: 'textarea',
    field: 'scorePrompt',
    maxlength:4000,
  },
  {
    title: '对话缓存条数',
    type: 'numberInput',
    field: 'memoryCacheNum',
  },
  {
    title: '及格分数线',
    type: 'numberInput',
    field: 'passScore',
  },
  {
    title: '对话模型',
    type: 'select',
    field: 'modelId',
    except: true,
    },
  {
    title: '打分模型',
    type: 'select',
    field: 'scoreModelId',
    except: true,
    },
  {
    title: '是否开启语音录入',
    type: 'switch',
    field: 'voiceStatus',
    labelWidth:"135px"
  },
  {
    title: '是否开启上传图片',
    type: 'switch',
    field: 'uploadFileStatus',
    labelWidth: "135px"
  },
  {
    title: '标准案例图',
    type: 'uploadimg',
    field: 'standardCaseImage',
  },
  ]
  
  const rules: FormRules = {
    sceneCode: [{ required: true, message: '请输入场景编码', trigger: 'blur' }],
    // robotImage: [{ required: true, message: '请上传智能体头像', trigger:'change'}],
    modelId: [{ required: true, message: '请择对话模型类型', trigger: 'change' }],
    robotName: [{ required: true, message: '请输入智能体名称', trigger: 'blur' }],
    robotInfo: [{ required: true, message: '请输入智能体描述', trigger: 'blur' }],
    scorePrompt: [{ required: true, message: '请输入打分提示词', trigger: 'blur' }],
    scenePrompt: [{ required: true, message: '请输入场景提示词', trigger: 'blur' }],
    memoryCacheNum: [{ required: true, message: '请输对话缓存条数', trigger: 'blur' }],
    passScore: [{ required: true, message: '请输入及格分数线', trigger: 'blur' }],
    scoreModelId: [{ required: true, message: '请择打分模型', trigger: 'blur' }],
    voiceStatus: [{ required: true, message: '请择是否开启语音录入', trigger: 'change' }],
    uploadFileStatus: [{ required: true, message: '请择是否开启上传图片', trigger: 'change' }],
  }
  
  const submit = async (param: any) => {
    const id = query.id as string
    param.robotImage =  param.robotImage &&param.robotImage[0]?param.robotImage[0].foreignPath:""
    param.standardCaseImage =param.standardCaseImage&& param.standardCaseImage[0]?param.standardCaseImage[0].foreignPath:""
    const data= !isEdit.value?{
        id:id,
        ...param
    }:{
       ...param   
    }
    if(isEdit.value){//新增
      const { status } = await api.trainSceneCreate(data)
      if (status != 0) return
      router.back()
    }else{//编辑
      const { status } = await api.trainSceneUpdate(data)
      if (status != 0) return
      router.back()
    }
  }
  
  async function init() {
    if (isEdit.value) return
    nav.to = !isEdit.value ? '编辑' : '新增'
    const id = query.id as string
    const { status, data } = await api.sceneDetail({id})
    if (status != 0) return
    console.log(status,data)
    data.sceneCode = data.sceneCode
    data.sceneName = data.sceneName
    data.robotName = data.robotName
    data.robotImage = data.robotImage?[{ name: 'defualt.png', url: data.robotImage }]:[]
    data.standardCaseImage = data.standardCaseImage?[{ name: 'defualt.png', url: data.standardCaseImage }]:[]
    data.robotInfo = data.robotInfo
    data.scenePrompt = data.scenePrompt
    data.scorePrompt = data.scorePrompt
    data.memoryCacheNum = data.memoryCacheNum
    data.modelId = data.modelId
    data.uploadFileStatus =Number(data.uploadFileStatus)
    data.voiceStatus = Number(data.voiceStatus)
    data.passScore = data.passScore
    baseList.forEach((tmp) => {
        if(tmp.field=="sceneCode"){
          !isEdit.value ? tmp.disabled=true:tmp.disabled=false
        }
        if (Object.hasOwn(data, tmp.field) && data[tmp.field] !== null) {
         tmp.value = data[tmp.field]
        }
    })
    console.log(baseList)
  }
  async function InitModelList() {
    const { status, data }= await  api.modelList()
    if (status != 0) return
    initList.value = data
  }
  onBeforeMount(async () => {
    isEdit.value = !query.id
    console.log(isEdit.value)
    await init()
    await InitModelList()
    finished.value = true
})
  </script>
  <style scoped>
  .pt-5{
    margin-left:20px;
  }
  </style>