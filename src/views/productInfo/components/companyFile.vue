<template>
  <TableToolTemp toolTitle="保司资料" :tool-list="toolConfig" />
  <PageTable
    :search-form-temp="temp"
    :hiddenState="true"
    @spread="spread"
    @pack="pack"
    api-url="/web/company/page"
    ref="companyFileRef"
  >
    <template #default="{ tableData }">
      <el-table :data="tableData" class="dt-table" stripe>
        <el-table-column prop="companyId" label="保司ID" align="center"/>
        <el-table-column prop="companyName" label="保司名称" align="center"/>
        <el-table-column prop="fileCount" label="文件资料总数" align="center" width="190px" />
        <el-table-column prop="failFileCount" label="解析失败文件数" align="center" min-width="100px" />
        <el-table-column label="操作" align="center" fixed="right" width="180px">
          <template #default="scope">
            <!--  :disabled="scope.row.answerSource == 1 ? false : true" -->
            <el-button type="text" @click="handleDetail(scope.row, 0)">保司资料</el-button>
            <!-- <el-button type="text" @click="handleDetail(scope.row, 1)">查看解析原文</el-button>
            <el-button type="text" @click="handleDetail(scope.row, 1)">网页打开</el-button>
            <el-button type="text" @click="handleDetail(scope.row, 1)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </template>
  </PageTable>
  <!--知识列表-->
  <DtPopup :show="showEdit" @close="showEdit = false" :title="title" width="73%" :footer="false">
    <el-table :data="tableDetailData" border stripe v-hover style="width: 100%; margin-bottom: 70px" class="dt-table">
      <el-table-column align="center" prop="id" label="切片ID" width="200"></el-table-column>
      <el-table-column align="center" prop="knowledgeId" label="知识ID" width="200"></el-table-column>
      <el-table-column align="center" prop="itemId" width="200" label="知识章节ID"></el-table-column>
      <el-table-column align="center" prop="title" width="250" label="知识章节标题"></el-table-column>
      <el-table-column align="center" prop="slice" label="内容" width="200">
        <template #default="scope">
          <el-tooltip
            v-if="scope.row.slice"
            :content="scope.row.slice"
            effect="dark"
            :width="100"
            :showOverflowTooltip="true"
            class="awidthTooltip"
            placement="top"
          >
            <p class="awidth" v-html="scope.row.slice"></p>
          </el-tooltip>
          <span v-else>""</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="score" width="200" label="相似度"></el-table-column>
    </el-table>
  </DtPopup>
  <!--日志报文-->
  <DtPopup :show="showEdit1" @close="showEdit1 = false" width="65%" :title="title" :footer="false">
    <div v-for="(value, key) in jsonData" :key="key">
      <strong>{{ key }}:</strong> {{ value }}
    </div>
  </DtPopup>
</template>

<script setup lang="ts">
import type { SearchFormTemp, ToolListProps } from '@/components/types'
import PageTable from '@/components/PageTable.vue'
import { ref, reactive } from 'vue'
import useFilters from '@/hooks/useFilters'
import { useUserStore } from '@/stores/user'
import useChannel from '@/stores/channel'
import { qaRecordReferences, qaRecordInfo } from '../api'

import tooltipWithFn from '@/views/dashboardManage/components/tooltipWithFn.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const showEdit = ref(false)
const title = ref(null)
const jsonData = ref(null)
const showEdit1 = ref(false)
const { getDic } = useUserStore()
const { getDicItemName } = useFilters()
const { channels } = useChannel()
const companyFileRef = ref()
const tableDetailData = ref<Record<string, any>[]>([])
const tableDetailData1 = ref<Record<string, any>[]>([])
function getParams() {
  if (!companyFileRef.value) return {}
  const p = companyFileRef.value.getSearchQuery()
  //console.debug(p)
  return p
}

const toolConfig: ToolListProps[] = [
  // {
  //   name: '导出',
  //   icon: 'down',
  //   btnCode: 'chatbot:board:analysis:export',
  //   download: {
  //     url: '/web/databoard/exportConversation',
  //     params: getParams,
  //     isPost: true,
  //     check: () => {
  //       return true
  //     },
  //   },
  // },
]
const dics = {
  userVoteResult: [
    { dicItemName: '全部', dicItemCode: '' },
    { dicItemName: '点踩', dicItemCode: '0' },
    { dicItemName: '点赞', dicItemCode: '1' },
  ],
  answerType: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.answerType')],
  answerSource: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.answerSource')],
  intention: [{ dicItemCode: '', dicItemName: '全部' }, ...getDic('chatbot.intentionType')],
}
const temp: SearchFormTemp[] = reactive([
 
  {
    label: '保司ID',
    name: 'companyId',
    type: 'input',
    placeholder: '请输入内容',
    searchState: true,
  },
  {
    label: '保司名称',
    name: 'companyName',
    type: 'input',
    placeholder: '请输入内容',
    searchState: true,
  },
])

const getCustomDicitemName = (value: any, key: string) => {
  return dics[key]?.find((item: any) => item.dicItemCode === value)?.dicItemName
}
// 展开
const spread = (value: any) => {
  console.log(value)
  for (let i = 0; i < temp.length; i++) {
    if (i > 3) {
      temp[i].searchState = value
    }
  }
  console.log(temp)
  // companyFileRef.value.temp
}
// 收起
const pack = (value: any) => {
  console.log(value)
  for (let i = 0; i < temp.length; i++) {
    if (i > 3) {
      temp[i].searchState = value
    }
  }
  companyFileRef.value
}
const handleDetail = async (data: any, type: number) => {
  console.log(data)
  router.push({
    path: '/companyInfo',
    query: {
      companyId: data.companyId,
      companyName: data.companyName
    },
  })
  // if (type == 0) {
  //   let res = await qaRecordReferences({ recordId: data.answerId })
  //   console.log(res)
  //   if (res.data && res.data.length > 0) {
  //     tableDetailData.value = res.data
  //     // this.total = res.total / 1 || 0;
  //     showEdit.value = true
  //     title.value = '知识列表'
  //   } else {
  //     return ElMessage.warning('列表无数据')
  //   }
  // } else {
  //   let res = await qaRecordInfo({ recordId: data.answerId })
  //   if (res.data && res.data.question) {
  //     showEdit1.value = true
  //     title.value = '日志报文'
  //     jsonData.value = res.data
  //     // this.total = res.total / 1 || 0;
  //   } else {
  //     return ElMessage.warning('无日志报文')
  //   }
  // }

  // this.tableDetailData = data;
}

const formatAnwser = (row) => {
  let { robotAnswerType, answerContent } = row
  if (robotAnswerType == '18') {
    try {
      answerContent = JSON.parse(answerContent || "")[0].text
      const str = answerContent.replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      //console.debug('answerContent: ', str)
      return str
    } catch (e) {
      return answerContent || ""
    }
  }
  return answerContent || ""
}

const formatAnwserImages = (row) => {
  let { robotAnswerType, answerContent } = row
  if (robotAnswerType == '18') {
    try {
      const data = JSON.parse(answerContent)[0]
      if (data && data.urls) {
        return data.urls
      }
      return []
    } catch (e) {
      return []
    }
  }

  return []
}

defineExpose({
  search: () => {
    if (companyFileRef.value) companyFileRef.value.onSearchBtn()
  },
})
</script>
<style lang="less">
.awidth {
  width: 100%;
  display: block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  left: -5px;
  right: -10px;
  cursor: pointer;
  &:hover {
    color: var(--el-color-primary);
  }
}
.awidthTooltip {
  white-space: wrap;
  display: inline-block;

  max-width: 700px;
  max-height: 400px;
  overflow-y: auto;
}

.answer-pop {
  position: relative;
  padding-bottom: 20px;

  &__copy {
    position: absolute;
    z-index: 1;

    right: 10px;
    bottom: 10px;
    cursor: pointer;
    &:active {
      opacity: 0.6;
    }
  }
}
</style>
