import { useAxios } from '@/hooks/http'

/**
 * @description 产品列表
 */
export const productPage = async (data: any) => await useAxios('/web/product/page', data)
/**
 * @description 产品原文
 */
export const productOriginal = async (data: any) => await useAxios('/web/product/file/original', data)

/**
 * @description 保司原文
 */

export const fileOriginal = async ( params: any ) => await useAxios('/web/product/file/original',params)

/**
 * @description 保司产品资料删除
 */
export const companyFileDel = async (data: any) => await useAxios('/web/company/file/del', data)

/**
 * @description 渠道列表
 */
export const productFileDel = async (data: any) => await useAxios('/web/product/file/del', data)


/**
 * @description 产品-添加产品资料
 */
export const productFileAdd = async (data: any) => await useAxios('/web/product/file/batch/add', data)

/**
 * @description 保司-添加产品资料
 */
export const companyFileAdd = async (data: any) => await useAxios('/web/company/file/batch/add', data)