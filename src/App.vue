<template>
  <el-config-provider :locale="zhCn">
    <RouterView v-slot="{ Component }">
      <Transition>
        <KeepAlive :include="routerCaches" :exclude="exceptCaches">
          <Suspense v-if="initEnd && Component">
            <component :is="Component" />
          </Suspense>
        </KeepAlive>
      </Transition>
      <el-backtop :right="100" :bottom="100" />
    </RouterView>
  </el-config-provider>
</template>

<script setup lang="ts">
import { ref, onMounted,onBeforeMount } from 'vue'
import { RouterView } from 'vue-router'
import { useRouterStore } from '@/stores/router'
import useSystemStore from '@/stores/system'
import { useUserStore } from '@/stores/user'
import zhCn from "element-plus/dist/locale/zh-cn.mjs";
// import { openMonitor } from '@dt/dt-monitor'
defineOptions({ name: 'app' })

const { initUser } = useUserStore()
const { routerCaches, exceptCaches } = useRouterStore()
const { initSystem } = useSystemStore()
const initEnd = ref(false)
const init = async () => {
  initSystem()
  await initUser()
  initEnd.value = true
}

onMounted(() => {
  init()
})
onBeforeMount(() => {
  // openMonitor()
})
</script>

<style lang="less">
#app {
  background-color: #ffffff;
  padding-bottom: 20px;
  min-height: 100%;
  box-sizing: border-box;
}
</style>
