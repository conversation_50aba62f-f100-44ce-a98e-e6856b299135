import { createApp,toRaw } from 'vue'
import pinia from '@/stores/index'
import ElementPlus from 'element-plus'
import '@/assets/style/reset.less'
import 'element-plus/dist/index.css'
import './index.css'
import '@/assets/style/dt-custom.less'
import useAuth from '@/hooks/useAuth'
import App from './App.vue'
import router from './router'
// import initMonitor from "@dt/dt-monitor"
// import useSystemStore from '@/stores/system'
const { vAuth } = useAuth()
const app = createApp(App)

app.use(pinia).use(ElementPlus).use(router).directive('v-auth', vAuth).mount('#app')
// app.use(pinia)
// app.use(router)
// app.use(ElementPlus)

// const   { system }   = useSystemStore()
// console.log('system',system)
// const monitorParams = {
//     origin: 'tgj', //快保项目传：kuaibao,童管家传：tgj
//     projectCode: 'chatbot', //项目的应用编码，为每个监控项目的唯一id，不要重复。如：kmApp是知识库的，其它项目对接需找监控对应项目的字典
//     nodeEnv: import.meta.env.VITE_ENV, //本地环境传：dev,测试环境：sta,生产环境传：prod
//     disableDev: false, //可选参数，禁用本地环境监控（不传默认为true禁用），只可控制本地环境（防止本地调试报错时上传错误信息），对测试和生产环境不影响
//     disableAll:false, //可选参数，禁用监控全部功能（不传默认为false不禁用）,主要用途处理单项目多个应用的复杂场景
//     showLog: false, //可选参数（默认为false不显示），是否显示监控中上传数据的console打印显示
//     userId:system.userId,//可选参数，如果不传，会尝试从url的query中或者通过原生方法获取
//     token: system.access_token,//可选参数，如果不传，会尝试从url的query中或者通过原生方法获取
//     unionid: "",//可选参数，如果不传，会尝试从url的query中获取
//     tenantId:system.tenantId,//可选参数，如果不传，会尝试从url的query中获取
//     filterList:[], // Array<string> 可选参数，页面需要过滤掉的上报信息
//     signalValue:"" // 信号强度  调用原生方法获取
//   }
// app.use(initMonitor,monitorParams) //初始化监控
// app.directive('v-auth', vAuth).mount('#app') 



