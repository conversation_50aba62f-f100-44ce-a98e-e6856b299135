import { defineStore } from 'pinia'
import { reactive } from 'vue'

export const useRouterStore = defineStore('router', () => {
  const routerCaches = reactive<string[]>(['channelManage', 'botManage','machineManage','machineScore','productInfo'])
  const exceptCaches = reactive<string[]>([])
  const activeParams = reactive({
    name: '',
    params: Object,
  })

  function addRouterCaches(name: string) {
    const item = routerCaches.find((key: string) => key === name)
    if (!item) routerCaches.push(name)

    const index = exceptCaches.findIndex((key: string) => key === name)
    if (index > -1) exceptCaches.splice(index, 1)
  }

  function delRouterCaches(name: string) {
    const index = routerCaches.findIndex((key: string) => key === name)
    if (index > -1) routerCaches.splice(index, 1)

    const item = exceptCaches.find((key: string) => key === name)
    if (!item) exceptCaches.push(name)
  }

  function updateActiveParams(data: any) {
    activeParams.params = data.params
    activeParams.name = data.name
  }

  return { routerCaches, exceptCaches, addRouterCaches, delRouterCaches, activeParams, updateActiveParams }
})
