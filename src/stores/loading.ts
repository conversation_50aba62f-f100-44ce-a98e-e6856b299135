import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface LoadingConfig {
  fullscreen: boolean
  lock: boolean
  text: string
}

export const useLoading = defineStore(
  'loading',
  (
    config: LoadingConfig = {
      fullscreen: true,
      lock: true,
      text: '加载中...',
    }
  ) => {
    const service = ref<any>()

    function start() {
      if (service.value) return
      service.value = ElLoading.service(config)
    }

    function stop() {
      if (!service.value) return
      service.value.close()
      service.value = null
    }

    return { start, stop, service }
  }
)
