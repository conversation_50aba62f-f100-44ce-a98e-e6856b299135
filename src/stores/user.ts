import { defineStore } from 'pinia'
import type { TenantUser } from '@/api'
import { getTenantUsers, getUsersAuth, getSearchDicItems, getTenantInit } from '@/api'

export const useUserStore = defineStore('chatbot-web-user', {
  state: (): {
    authCode: string[]
    tenantUsers: TenantUser[]
    userEnName: string
    dicMap: Record<string, any>
    tenant: {
      tenantId: string
      tenantInfo: Record<string, any>
    }
  } => {
    return {
      authCode: [],
      tenantUsers: [],
      userEnName: '',
      dicMap: {},
      tenant: {
        tenantId: '',
        tenantInfo: {},
      },
    }
  },
  actions: {
    getAllUser() {
      return this.tenantUsers
    },
    async setTenantUsers() {
      if (this.tenantUsers.length) return
      this.tenantUsers = await getTenantUsers()
    },
    async setAuthCode() {
      const data = await getUsersAuth()
      if (!data.auth.length) {
        return
      }

      this.authCode = data.auth
      this.userEnName = data.userEnName
    },
    async setDics(keys: string[], force?: boolean) {
      const filterKeys = force ? keys : keys.filter((key: string) => !Object.keys(this.dicMap).includes(key))
      if (!filterKeys.length) return
      const data = await getSearchDicItems(filterKeys)
      if (!data) return
      for (const [key, value] of Object.entries(data)) {
        this.dicMap[key] = value
      }
    },
    getDic(key: string) {
      return this.dicMap[key] || []
    },
    async fetchTenant() {
      const { status, data } = await getTenantInit()
      if (status != 0) return
      this.tenant.tenantId = data.tenantId
      this.tenant.tenantInfo = data.tenantInfo
    },
    async initUser() {
      await Promise.all([this.setTenantUsers(), this.setAuthCode(), this.fetchTenant()])
    },
  },
  persist: true,
})
