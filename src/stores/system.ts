import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { getUrlParams } from '@/utils/index'
import { setThemeColor } from '@/utils/themeColor'

const useSystemStore = defineStore(
  'chatbot-web-system',
  () => {
    const system = reactive({
      funcId: '',
      access_token: '',
      token: '',

      tenantId: '',
      tenantName: '',
      userName: '',
      userId: '',
      themeColor: '',
      navTagColor: '',
    })

    function init() {
      system.funcId = getUrlParams('funcId')
      system.access_token = getUrlParams('access_token')
      system.token = getUrlParams('token')
      system.tenantId = getUrlParams('tenantId') ?? 'T0001'
      system.tenantName = getUrlParams('tenantName')
      system.userName = getUrlParams('userName')
      system.userId = getUrlParams('userId')
      system.themeColor = getUrlParams('themeColor') ?? '#D7A256'
      system.navTagColor = getUrlParams('navTagColor') ?? '#FFF6E8'
    }

    function initSystem() {
      if (getUrlParams('access_token') && getUrlParams('access_token') !== system.access_token) {
        init()
      }

      setThemeColor(system.themeColor, system.navTagColor)
      /**
       * 监听父系统 主题切换事件,更新主题颜色
       */
      window.addEventListener('message', (e) => {
        if (e.data && e.data.theme) {
          setThemeColor(e.data.theme.color, e.data.theme.navTagUnselectedColor)
        }
      })

      sessionStorage.setItem('chatbot-web-tenantId', system.tenantId)
    }

    function updateToken(token: string) {
      system.access_token = token
    }

    return { system, initSystem, updateToken }
  },
  { persist: true }
)

export default useSystemStore
