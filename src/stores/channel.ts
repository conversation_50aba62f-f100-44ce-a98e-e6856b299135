import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { searchChannelPage } from '@/views/dashboardManage/api'

interface ActiveChannel {
  channelId: string
  channelCode:string
  robotId: string
  robots: any[]
  proList: any[]
  flowList: any[]
  channelRobotId:string
}

interface Channel {
  dicItemName: string
  dicItemCode: string
}

export const useChannel = defineStore('channel', () => {
  const channelConfig = reactive<ActiveChannel>({
    channelId: '',
    robotId: '',
    robots: [],
    proList: [],
    flowList: [],
    channelCode:'',
    channelRobotId:''
  })

  const setChannel = (data: any) => {
    console.log(data)
    channelConfig.channelId = data.channelId
    channelConfig.channelCode =data.channelCode
    channelConfig.robotId = data.robotId
  }
  const setId = (data: any) => {
    channelConfig.channelRobotId = data.id
  }


  const setRobots = (data: any[]) => {
    channelConfig.robots = data
  }

  const setPro = (data: any[]) => {
    channelConfig.proList = data
  }
  const setFlow = (data: any[]) => {
    channelConfig.flowList = data
  }


  const channels = reactive<Channel[]>([])
  const fetchChannels = async () => {
    console.log('channels',channels)
    if (channels.length) return
    const channelIdList = await searchChannelPage({ pageNum: 1, pageSize: 999999, param: {} })
    const data = channelIdList.data.list.map((item: any) => {
      return {
        dicItemName: item.channelName,
        dicItemCode: item.channelCode,
      }
    })
    channels.length = 0
    channels.push(...data)
  }

  return { channelConfig, setChannel, setRobots, channels, fetchChannels,setPro,setId,setFlow }
})

export default useChannel
