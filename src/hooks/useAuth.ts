import { useUserStore } from '@/stores/user'

export default function () {
  function hasAuth(str: string) {
    const { authCode } = useUserStore()
    const auths = str.split(',')

    return auths.some((auth: string) => {
      return authCode.includes(auth)
    })
  }

  function vAuth(el: HTMLElement, binding: any) {
    if (hasAuth(binding.value)) return
    el.parentNode && el.parentNode.removeChild(el)
  }

  return { hasAuth, vAuth }
}
