import type { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'

// Additional import
import { ElMessage } from 'element-plus'
import useSystemStore from '@/stores/system'
import { useLoading } from '@/stores/loading'

export interface Response<D> {
  status: number | string
  msg: string
  data: D
}

export interface AxiosRequestConfigAtt extends AxiosRequestConfig {
  // Need loading effect or not
  // defaut to true
  loading?: boolean
  // Need show error msg or not
  // default to true
  msg?: boolean
}

const { VITE_APP_API } = import.meta.env
const defaultConfigOptions: AxiosRequestConfig = {
  baseURL: VITE_APP_API,
  timeout: 60000,
  method: 'post',
  headers: {
    'Access-Control-Allow-Origin': '*',
  },
}

async function setHeaderInterceptor(config: InternalAxiosRequestConfig<any>): Promise<InternalAxiosRequestConfig<any>> {
  const { system } = useSystemStore()

  // add additional field
  config.headers.access_token = system.access_token
  config.headers.token = system.token
  config.headers.funcId = system.funcId
  config.headers.tenantId = system.tenantId
  return config
}

async function authInterceptor(res: AxiosResponse) {
  const status = res.data.resp_code
  if ([400, 401, 402].includes(status)) {
    const { system, updateToken } = useSystemStore()
    const parent = window.parent as any
    if (parent && parent.kbcChangeToken) {
      await parent.kbcChangeToken(system.access_token)
      const newAccessToken = sessionStorage.getItem('LoginAccessToken')

      if (newAccessToken && newAccessToken !== system.access_token) {
        updateToken(newAccessToken)
        return await axios(res.config)
      }
    }
  }

  return res
}

function rejectInterceptor(error: any) {
  const { stop } = useLoading()
  stop()

  const msg = error.response.data.resp_msg || error.message
  ElMessage.error(msg)
  return Promise.reject(error)
}

export function makeUseAxios(configOptions: AxiosRequestConfigAtt = {}) {
  const config = {
    ...defaultConfigOptions,
    ...configOptions,
  }
  const ins = axios.create(config)

  ins.interceptors.request.use(setHeaderInterceptor, rejectInterceptor)
  ins.interceptors.response.use(authInterceptor, rejectInterceptor)

  async function useAxios<T = any, P = any>(url: string, data?: T, opt: AxiosRequestConfigAtt = { loading: true, msg: true }): Promise<Response<P>> {
    const { start, stop } = useLoading()
    if (opt.loading) {
      start()
    }

    const res = await ins({ url, data, ...opt })
    const contentType = res.headers['content-type']
    let result: Response<any>

    if (/json/.test(contentType)) {
      result = {
        status: (res.data && (res.data.resp_code || res.data.code)) || 0,
        msg: (res.data && (res.data.resp_msg || res.data.msg)) || '',
        data: (res.data && (res.data.datas || res.data.data)) || null,
      }
    } else {
      // for other response type
      result = { status: 0, msg: '', data: res.data }
    }

    stop()

    if (opt.msg && result.status != 0 && result.msg) {
      if(result.status==401){
        const { system, updateToken } = useSystemStore()
        const parent = window.parent as any
        if (parent && parent.kbcChangeToken) {
          await parent.kbcChangeToken(system.access_token)
          const newAccessToken = sessionStorage.getItem('LoginAccessToken')

          if (newAccessToken && newAccessToken !== system.access_token) {
            updateToken(newAccessToken)
            return await axios(res.config)
          }
        }
      }else{
        ElMessage.error(result.msg)
      }
    }

    return result
  }

  // TODO
  // for download excel or img
  function useDownload() {}

  // TODO for upload file

  return { useAxios, useDownload }
}

const dd = makeUseAxios()
export const useAxios = dd.useAxios
export default dd
