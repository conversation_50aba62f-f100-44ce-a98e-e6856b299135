import { useUserStore } from '@/stores/user'
export default function () {
  const { dicMap, tenantUsers } = useUserStore()

  function getNickName(id: string) {
    return tenantUsers.find((user: any) => user.userId === id)?.nickName ?? id
  }

  function getDicItemName(val: string | number, dicName: string) {
    const list = dicMap[dicName] ?? []
    return list.find((item: any) => item.dicItemCode == val)?.dicItemName ?? val
  }

  function getDic(name: string) {
    return dicMap[name] || []
  }

  return { getNickName, getDicItemName, getDic }
}
