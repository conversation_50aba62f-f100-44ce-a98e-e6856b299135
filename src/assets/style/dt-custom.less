 .el-table thead {
  --el-table-header-bg-color: #f9f9f9;
  --el-table-header-text-color: #333;
}
.dt-table.el-table::before {
  height: 0;
  width: 0;
}
.dt-table.el-table .el-table__row td {
  border-color: transparent;
}

.dt-table.el-table tr th.is-leaf {
  border-color: transparent;
}
.dt-table.el-table tr th .cell {
  text-align: center;
}

#app {
  --el-from-input-width:199px;
  --el-from-input2-width:530px;
}

/* dt-from */
.dt-from {
	.el-input {
		width: var(--el-from-input-width);
	}
  .el-input__wrapper {
    width: var(--el-from-input-width);
  }
	.el-date-editor {
		--el-date-editor-width:  var(--el-from-input-width);
	}
  .el-date-editor--datetimerange {
    --el-date-editor-width:  var(--el-from-input2-width);
  }
  .el-date-editor--daterange {
    --el-date-editor-width:  var(--el-from-input2-width);
  }
  .el-input-number {
    width: var(--el-from-input-width);
  }
  .el-cascader {
    .el-input {
      width: var(--el-from-input-width);
    }
  }
}

.dt-item-center {
  justify-content: center;
	.el-form-item__content {
		justify-content: center;
	}

}


.breadcrumb-dt {
  --el-text-color-regular: var(--el-color-primary);
}

.el-table-required:before {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}
