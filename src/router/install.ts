import { useRouterStore } from '../stores/router'
import type { Router } from 'vue-router'
import NProgress from 'nprogress'
import '@/assets/style/nprogress.css'
import { useUserStore } from '@/stores/user'
import useChannel from '@/stores/channel'

export const install = (router: Router): void => {
  router.beforeEach(async (to, from, next) => {
    const { setDics } = useUserStore()
    const { addRouterCaches } = useRouterStore()
    const { fetchChannels } = useChannel()

    if (to.path !== from.path) NProgress.start()
    if (to.meta.keepAlive && to.name) {
      console.log('to',to.meta)
      addRouterCaches(to.name as string)
    }

    if (to.meta.dics) {
      setDics(to.meta.dics as string[])
    }

    if (to.meta.needChannels) {
      await fetchChannels()
    }

    next()
  })

  router.afterEach(() => {
    NProgress.done()
  })
}
