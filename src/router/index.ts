import { createRouter, createWebHistory } from 'vue-router'
import { install } from './install'

import robots from './robots'
import channels from './channels'
import dashboard from './dashboard'
import project from './project'
import section from './section'
import answerLog from './answerLog'
import modelManage from './modelManage'
import machineManage from './machineManage'
import productInfo from './productInfo'
import flowManage  from './flowManage'
import flowRecordManage from './flowRecordManage'
import mcp from './mcp'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/:pathMatch(.*)*',
      name: 'notFound',
      component: () => import('@/components/NotFound.vue'),
    },
    {
      path: '/',
      name: 'home',
      children: [...robots, ...channels, ...dashboard, ...project, ...section,...answerLog,...modelManage,...machineManage,...productInfo,...flowManage,...flowRecordManage,...mcp],
    },
  ],
})

install(router)
export default router
