export default [
    {
      path: 'modelManage',
      name: 'modelManage',
      component: () => import('@/views/modelManage/index.vue'),
      meta: {
        title: '模型管理',
        dics: ['platform.name.type'],
      },
    },
    {
        path: 'modelAdd',
        name: 'modelAdd',
        component: () => import('@/views/modelManage/modelAdd.vue'),
        meta: {
          dics: ['platform.name.type'],
        },
      },
  ]
  