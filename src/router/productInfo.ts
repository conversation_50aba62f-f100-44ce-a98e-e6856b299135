export default [
    {
      path: 'productInfo',
      name: 'productInfo',
      component: () => import('@/views/productInfo/index.vue'),
      meta: {
        title: '产品知识维护',
        dics: ['chatbot.answerType','chatbot.answerSource','chatbot.product.syncStatus']
      },
    },
    {
      path: 'proInfo',
      name: 'proInfo',
      component: () => import('@/views/productInfo/proInfo.vue'),
      meta: {
        dics: ['chatbot.product.source', 'chatbot.product.fileType', 'chatbot.product.syncStatus']
      },
    },
    {
      path: 'companyInfo',
      name: 'companyInfo',
      component: () => import('@/views/productInfo/companyInfo.vue'),
      meta: {
        dics: ['chatbot.company.source', 'chatbot.company.fileType', 'chatbot.product.syncStatus']
      },
    },
    {
      path: 'infoScile',
      name: 'infoScile',
      component: () => import('@/views/productInfo/infoScile.vue'),
      meta: {
        dics: ['search.elasticsearch.analyzer', 'search.recognition.level'],
      },
    },
    {
      path: 'proScilce',
      name: 'proScilce',
      component: () => import('@/views/productInfo/proScilce.vue'),
      meta: {
        dics: ['search.elasticsearch.analyzer', 'search.recognition.level'],
      },
    },
    {
      path: 'companyScile',
      name: 'companyScile',
      component: () => import('@/views/productInfo/companyScile.vue'),
      meta: {
        dics: ['search.elasticsearch.analyzer', 'search.recognition.level'],
      },
    },
  ]
  