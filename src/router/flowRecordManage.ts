export default [
  {
    path: 'flowRecordManage',
    name: 'flowRecordManage',
    component: () => import('@/views/flowRecordManage/index.vue'),
    meta: {
      title: '工作流记录管理',
      keepAlive: true,
      dics: ['chatbot.answerType','chatbot.answerSource','chatbot.intentionType','chatbot.workflow.type'],
      needChannels: true
    },
  },
  {
    path: 'flowRecordDetail/:workflowRunId',
    name: 'FlowRecordDetail',
    component: () => import('@/views/flowRecordManage/detail.vue'),
    meta: {
      title: '执行记录详情',
    },
  },
] 