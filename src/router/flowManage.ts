export default [
  {
    path: 'flowManage',
    name: 'flowManage',
    component: () => import('@/views/flowManage/index.vue'),
    meta: {
      title: '流程管理',
      dics: ['chatbot.workflow.type'],
    },
  },
//   {
//     path: 'flwoConfig',
//     name: 'flwoConfig',
//     component: () => import('@/views/flowManage/flwoConfig.vue'),
//     meta: {
//       title: '流程配置',
//       dics: ['chatbot.status', 'km.knowledge.type', 'km.knowledge.state', 'km.knowledge.belong.type', 'chatbot.operationType', 'chatbot.syncStatus', 'chatbot.fileType','chatbot.knowledge.syncStatus','chatbot.common.status','chatbot.knowledge.operateStatus'],
//       keepAlive: true
//     },
//   }
]
