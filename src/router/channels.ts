export default [
  {
    path: 'channelManage',
    name: 'channelManage',
    component: () => import('@/views/channels/manage.vue'),
    meta: {
      title: '渠道列表',
      dics: ['chatbot.common.status', 'chatbot.cardType','chatbot.botType'],
    },
  },
  {
    path: 'channelAdd',
    name: 'channelAdd',
    component: () => import('@/views/channels/add.vue'),
    meta: {
      dics: ['chatbot.botType'],
    },
  },
  {
    path: 'channelConfig',
    name: 'channelConfig',
    component: () => import('@/views/channels/config.vue'),
    meta: {
      keepAlive: true,
      dics: ['chatbot.common.status', 'chatbot.cardType', 'chatbot.guessQuestionType','chatbot.promptType','chatbot.channelType'],
    },
  },
   {
    path: 'flowConfig',
    name: 'flowConfig',
    component: () => import('@/views/channels/flowConfig.vue'),
    meta: {
      dics: ['chatbot.common.status', 'chatbot.cardType', 'chatbot.guessQuestionType','chatbot.promptType','chatbot.channelType'],
    },
  },
  {
    path: 'channelCommonAddAndEdit',
    name: 'channelCommonAddAndEdit',
    component: () => import('@/views/channels/components/commonAdd.vue'),
    meta: {
      dics: ['chatbot.common.status', 'chatbot.cardType', 'km.knowledge.type', 'km.knowledge.state', 'km.knowledge.belong.type'],
    },
  },
  {
    path: 'qa',
    name: 'qa',
    component: () => import('@/views/channels/components/qa.vue'),
    meta: {
      dics: ['chatbot.common.status', 'chatbot.cardType', 'chatbot.guessQuestionType'],
    },
  },
  {
    path: 'qaAddAndEdit',
    name: 'qaAddAndEdit',
    component: () => import('@/views/channels/components/qaAddAndEdit.vue'),
    meta: {
      dics: [
        'chatbot.common.status',
        'chatbot.cardType',
        'km.knowledge.type',
        'km.knowledge.state',
        'km.knowledge.belong.type',
        'chatbot.guessQuestionType',
      ],
    },
  },
  {
    path: 'shortWords',
    name: 'shortWords',
    component: () => import('@/views/channels/components/shortWords.vue'),
    meta: {
      dics: ['chatbot.common.status'],
    },
  },
  {
    path: 'shortWordsAddAndEdit',
    name: 'shortWordsAddAndEdit',
    component: () => import('@/views/channels/components/shortWordsAddAndEdit.vue'),
    meta: {
      dics: ['chatbot.common.status', 'chatbot.cardType', 'km.knowledge.type', 'km.knowledge.state', 'km.knowledge.belong.type','chatbot.botType'],
    },
  },
  {
    path: 'IntelligentAdd',
    name: 'IntelligentAdd',
    component: () => import('@/views/channels/components/IntelligentAdd.vue'),
    meta: {
      dics: ['chatbot.common.status', 'chatbot.cardType', 'km.knowledge.type', 'km.knowledge.state', 'km.knowledge.belong.type','chatbot.botType'],
    },
  },
  {
    path: 'channelShortWords',
    name: 'channelShortWords',
    component: () => import('@/views/channels/components/channelShortWords.vue'),
    meta: {
      dics: ['chatbot.common.status', 'chatbot.promptType', 'km.knowledge.type', 'km.knowledge.state', 'km.knowledge.belong.type','chatbot.botType'],
    },
  },
]
