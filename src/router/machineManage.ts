export default [
    {
      path: 'machineManage',
      name: 'machineManage',
      component: () => import('@/views/machineConfig/index.vue'),
      meta: {
        title: '场景列表',
        keepAlive: true,
        dics: ['platform.name.type'],
      },
    },
    {
        path: 'machineAdd',
        name: 'machineAdd',
        component: () => import('@/views/machineConfig/machineAdd.vue'),
        meta: {
          dics: ['platform.name.type'],
        },
    },
    {
        path: 'machineScore',
        name: 'machineScore',
        component: () => import('@/views/machineConfig/score.vue'),
        meta: {
          dics: ['chatbot.status', 'km.knowledge.type', 'km.knowledge.state', 'km.knowledge.belong.type', 'chatbot.operationType', 'chatbot.syncStatus', 'chatbot.fileType'],
          keepAlive: true
        },
      },
      {
        path: 'machineDetails',
        name: 'machineDetails',
        component: () => import('@/views/machineConfig/details.vue'),
        meta: {
          keepAlive: true
        },
      },
      {
        path: 'viewConversation',
        name: 'viewConversation',
        component: () => import('@/views/machineConfig/viewConversation.vue'),
        meta: {
          dics: ['search.elasticsearch.analyzer', 'search.recognition.level'],
        },
      },
  ]
  