export default [
  {
    path: 'appserver',
    name: 'AppServer',
    component: () => import('@/views/mcp/appserver/index.vue'),
    meta: {
      title: '应用服务器管理',
      icon: 'el-icon-s-platform',
      breadcrumb: [
        { title: '应用服务器管理', path: '/appserver' }
      ]
    }
  },
  {
    path: 'appserver/add',
    name: 'AppServerAdd',
    component: () => import('@/views/mcp/appserver/edit.vue'),
    meta: {
      title: '新增应用服务器',
      icon: 'el-icon-plus',
      breadcrumb: [
        { title: '应用服务器管理', path: '/appserver' },
        { title: '新增应用服务器', path: '/appserver/add' }
      ]
    }
  },
  {
    path: 'appserver/edit/:id',
    name: 'AppServerEdit',
    component: () => import('@/views/mcp/appserver/edit.vue'),
    meta: {
      title: '编辑应用服务器',
      icon: 'el-icon-edit',
      breadcrumb: [
        { title: '应用服务器管理', path: '/appserver' },
        { title: '编辑应用服务器' }
      ]
    }
  },
  {
    path: 'appserver/detail/:id',
    name: 'AppServerDetail',
    component: () => import('@/views/mcp/appserver/edit.vue'),
    meta: {
      title: '应用服务器详情',
      icon: 'el-icon-view',
      breadcrumb: [
        { title: '应用服务器管理', path: '/appserver' },
        { title: '应用服务器详情' }
      ]
    }
  }
]