export default [
  {
    path: 'server',
    name: 'Server',
    component: () => import('@/views/mcp/server/index.vue'),
    meta: {
      title: '服务器管理',
      icon: 'el-icon-server',
      breadcrumb: [
        { title: '服务器管理', path: '/server' }
      ]
    }
  },
  {
    path: 'server/add',
    name: 'ServerAdd',
    component: () => import('@/views/mcp/server/edit.vue'),
    meta: {
      title: '新增服务器',
      icon: 'el-icon-plus',
      breadcrumb: [
        { title: '服务器管理', path: '/server' },
        { title: '新增服务器', path: '/server/add' }
      ]
    }
  },
  {
    path: 'server/edit/:id',
    name: 'ServerEdit',
    component: () => import('@/views/mcp/server/edit.vue'),
    meta: {
      title: '编辑服务器',
      icon: 'el-icon-edit',
      breadcrumb: [
        { title: '服务器管理', path: '/server' },
        { title: '编辑服务器' }
      ]
    }
  },
  {
    path: 'server/detail/:id',
    name: 'ServerDetail',
    component: () => import('@/views/mcp/server/edit.vue'),
    meta: {
      title: '服务器详情',
      icon: 'el-icon-view',
      breadcrumb: [
        { title: '服务器管理', path: '/server' },
        { title: '服务器详情' }
      ]
    }
  }
]