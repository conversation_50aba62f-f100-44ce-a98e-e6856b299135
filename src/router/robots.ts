export default [
  {
    path: 'botManage',
    name: 'botManage',
    //component: () => import('@/views/botManage/botManage.vue'),
    component: () => import('@/views/section/robotManagement.vue'),
    meta: {
      title: '智能体列表',
      dics: ['chatbot.status', 'chatbot.botType'],
    },
  },
  {
    path: 'botConfig',
    name: 'botConfig',
    component: () => import('@/views/botManage/botConfig.vue'),
    meta: {
      title: '智能体配置',
      dics: ['chatbot.status', 'km.knowledge.type', 'km.knowledge.state', 'km.knowledge.belong.type', 'chatbot.operationType', 'chatbot.syncStatus', 'chatbot.fileType','chatbot.knowledge.syncStatus','chatbot.common.status','chatbot.knowledge.operateStatus'],
      keepAlive: true
    },
  },
  {
    path: 'searchServiceIndex',
    name: 'searchServiceIndex',
    component: () => import('@/views/searchService/searchServiceIndex.vue'),
    meta: {
      title: '索引表管理',
    },
  },
  {
    path: 'searchServiceColumn',
    name: 'searchServiceColumn',
    component: () => import('@/views/searchService/searchServiceColumn.vue'),
    meta: {
      title: '索引字段配置',
      dics: ['search.elasticsearch.analyzer', 'search.recognition.level'],
    },
  },
  {
    path: 'searchServiceAnalysis',
    name: 'searchServiceAnalysis',
    component: () => import('@/views/searchService/searchServiceAnalysis.vue'),
    meta: {
      title: '查询分析配置',
    },
  },
]
