/// <reference types="vite/client" />
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly NODE_ENV: string // dev pro
  readonly VITE_APP_MODE: string
  // 后台API接口地址
  readonly VITE_APP_API: string
  // Prefix of project
  readonly VITE_APP_ROOT: string
  // Host name of project
  readonly VITE_APP_KBC: string
  // more env variables...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
