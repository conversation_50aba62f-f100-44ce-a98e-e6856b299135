package com.kbao.kbcchatbot.client;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitReqVO;
import com.kbao.kbcchatbot.maas.train.scene.service.TrainSceneService;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeRemoveReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class MaasTrainWebClientController implements MaasTrainWebClient {
    @Autowired
    private TrainSceneService sceneService;

    @Override
    @LogAnnotation(module = "场景管理", action = "查询", desc = "查询对话")
    public Result<PageInfo<JSONObject>> recordPage(PageRequest<TrainSceneInitReqVO> pageRequest){
        PageInfo<JSONObject> pageInfo = sceneService.trainRecordPage(pageRequest, false);
        return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
    }

}
