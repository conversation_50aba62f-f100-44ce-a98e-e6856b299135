package com.kbao.kbcchatbot.client;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.RobotKnowledgeConfigService;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeRemoveReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: 知识库
 * @author: husw
 * @create: 2023-05-19 10:19
 **/
@Slf4j
@RestController
public class RobotKnowledgeWebClientController implements RobotKnowledgeWebClient{

    @Autowired
    private RobotKnowledgeConfigService robotKnowledgeConfigService;

    @Override
    public Result removeDirectory(RobotKnowledgeRemoveReqVO reqVO) {
        robotKnowledgeConfigService.removeDirectory(reqVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
