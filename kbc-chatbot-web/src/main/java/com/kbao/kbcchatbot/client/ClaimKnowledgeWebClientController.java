package com.kbao.kbcchatbot.client;

import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.RobotKnowledgeAsyncService;
import com.kbao.kbcchatbot.robotknowledgeconfig.vo.RobotKnowledgeClaimSyncVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 知识库
 * @author: husw
 * @create: 2023-05-19 10:19
 **/
@Slf4j
@RestController
public class ClaimKnowledgeWebClientController implements ClaimKnowledgeWebClient{

    @Autowired
    private RobotKnowledgeAsyncService robotKnowledgeAsyncService;


    @Override
    public Result syncClaimKnowledge(List<RobotKnowledgeClaimSyncVO> claimSyncVOS) {
        robotKnowledgeAsyncService.embeddingDataFromClaim(claimSyncVOS);
        return Result.succeed("同步成功");
    }
}
