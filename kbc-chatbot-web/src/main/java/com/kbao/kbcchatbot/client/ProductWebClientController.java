package com.kbao.kbcchatbot.client;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.externalapi.model.MaasBdtgDelReq;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.externalapi.model.MaasPolicyExtractReq;
import com.kbao.kbcchatbot.maas.product.bean.ProductLabelVO;
import com.kbao.kbcchatbot.maas.product.service.ProductService;
import com.kbao.tool.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
public class ProductWebClientController implements ProductWebClient {
    @Autowired
    private MaasHttpService maasHttpService;
    @Autowired
    private ProductService productService;

    @Override
    public Result<String> productLabel(ProductLabelVO req) {
        productService.getProductLabel(req);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    public Result<JSONObject> policyExtract(MaasPolicyExtractReq req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONObject result = maasHttpService.postMaasApi(objectMap, "/ex/external/policyExtract");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

    public Result<JSONObject> saveProducts(JSONObject req) {
        JSONObject result = maasHttpService.postMaasApi(req, "/ex/external/bdtg/saveProducts");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

    public Result<JSONObject> delProduct(MaasBdtgDelReq req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONObject result = maasHttpService.postMaasApi(objectMap, "/ex/external/bdtg/delProduct");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }
}
