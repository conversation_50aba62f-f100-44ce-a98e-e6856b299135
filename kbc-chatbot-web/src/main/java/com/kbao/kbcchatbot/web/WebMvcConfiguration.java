package com.kbao.kbcchatbot.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.SimpleDateFormatSerializer;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.kbao.kbcbsc.interceptor.UserAuthInterceptorAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

/**
 * spring mvc配置类
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Configuration
@Slf4j
public class WebMvcConfiguration extends WebMvcConfigurationSupport implements EnvironmentAware {

    /**
     * 需要拦截的请求,Ant表达式
     */
    private static String[] LOGIN_INCLUDE = {"/**"};

    /**
     * 需要排除的请求,Ant表达式
     */
    private static String[] LOGIN_EXCLUDE = {
            "/doc.html",
            "/swagger-ui.html",
            "/swagger-resources/**",
            "/webjars/**",
            "/v2/**",
            "/swagger-ui.html/**",
            "/healthcheck",
            "/api/noauth/**"
    };

    private static String[] WHITELIST_INCLUDE = {
            "/doc.html",
            "/swagger-ui.html"
    };


    @Bean
    public UserAuthInterceptorAdapter userAuthInterceptorAdapter(){
        return new UserAuthInterceptorAdapter();
    }

    /**
     * 拦截器 集成 快保云服基础框架拦截器：要求header中有
     * tenantId 租户id
     * funcId 菜单id
     * access_token 用户token
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(userAuthInterceptorAdapter()).addPathPatterns(LOGIN_INCLUDE).excludePathPatterns(LOGIN_EXCLUDE).order(2);
    }

    /**
     * 静态资源
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html").addResourceLocations(
                "classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations(
                "classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");

    }


    @Override
    public void setEnvironment(Environment environment) {
    }

    /**
     * ResponseBody 消息转换方式
     */
    @Override
    protected void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        super.configureMessageConverters(converters);
//        // 支持字符串格式转换
//        converters.add(new StringHttpMessageConverter(Charset.forName("utf-8")));
//        // 支持json格式转换
//        converters.add(getMappingJackson2HttpMessageConverter());

        // 清除默认 Json 转换器
        converters.removeIf(httpMessageConverter -> httpMessageConverter instanceof MappingJackson2HttpMessageConverter);
        converters.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
        // 支持json格式转换
        converters.add(fastJsonMessageConverter());

        log.debug(" 系统中总共有的转换器有{}个,分别\n", converters.size());
        converters.forEach(a -> System.out.println(a.getClass()));
    }

    @Autowired
    private ObjectMapper objectMapper;


    /**
     * 存在 CDT（中国夏令时问题）,现在不推荐使用了
     *
     * @return
     */
    public MappingJackson2HttpMessageConverter getMappingJackson2HttpMessageConverter() {
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();

        //处理long型数据精度丢失
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(BigInteger.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        simpleModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        simpleModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        simpleModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        simpleModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        simpleModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        simpleModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        objectMapper.registerModule(simpleModule);

        mappingJackson2HttpMessageConverter.setObjectMapper(objectMapper);
        //设置中文编码格式
        List<MediaType> list = new ArrayList<MediaType>();
        list.add(MediaType.APPLICATION_JSON_UTF8);
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(list);
        return mappingJackson2HttpMessageConverter;
    }


    /**
     * json格式转换
     */
    private HttpMessageConverter fastJsonMessageConverter() {
        //配置参数
        FastJsonHttpMessageConverter httpMessageConverter = new FastJsonHttpMessageConverter();
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        JSON.defaultTimeZone = TimeZone.getTimeZone("Asia/Shanghai");
        fastJsonConfig.setSerializerFeatures(SerializerFeature.PrettyFormat,
                SerializerFeature.WriteDateUseDateFormat,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.DisableCircularReferenceDetect);

        //日期转换
        fastJsonConfig.getSerializeConfig().put(Timestamp.class, new SimpleDateFormatSerializer("yyyy-MM-dd HH:mm:ss"));
        fastJsonConfig.getSerializeConfig().put(LocalDate.class, new SimpleDateFormatSerializer("yyyy-MM-dd"));
        fastJsonConfig.getSerializeConfig().put(LocalDateTime.class, new FastJsonLocalDateTimeSerializer());
        //Long转String,防止精度丢失
        fastJsonConfig.getSerializeConfig().put(BigInteger.class, com.alibaba.fastjson.serializer.ToStringSerializer.instance);
        fastJsonConfig.getSerializeConfig().put(BigDecimal.class, com.alibaba.fastjson.serializer.ToStringSerializer.instance);
        httpMessageConverter.setFastJsonConfig(fastJsonConfig);

        //支持数据格式
        List<MediaType> mediaTypes = new ArrayList<>();
        mediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        httpMessageConverter.setSupportedMediaTypes(mediaTypes);
        return httpMessageConverter;
    }
    /**
     * Description: fastjson LocalDateTime序列化方法
     * <AUTHOR>
     * @since V1.0.0
     * @return
     * @date 2022/3/10
     */
    public class FastJsonLocalDateTimeSerializer implements ObjectSerializer {
        @Override
        public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
            if (object != null) {
                LocalDateTime localDateTime = (LocalDateTime) object;
                serializer.write(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } else {
                serializer.write(null);
            }
        }
    }

}
