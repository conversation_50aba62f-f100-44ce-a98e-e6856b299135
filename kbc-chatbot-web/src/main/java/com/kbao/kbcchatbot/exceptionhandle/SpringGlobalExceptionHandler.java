package com.kbao.kbcchatbot.exceptionhandle;

import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.mysql.cj.jdbc.exceptions.MysqlDataTruncation;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.binding.BindingException;
import org.apache.ibatis.exceptions.PersistenceException;
import org.mybatis.spring.MyBatisSystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.bind.BindException;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @className SpringGlobalExceptionHandler
 * @description 全局异常处理
 * @date 2021-03-05 09:40
 * @since 1.0
 **/
@RestControllerAdvice(annotations = {RestController.class, Controller.class})
public class SpringGlobalExceptionHandler {
    private static Logger logger = LoggerFactory.getLogger(SpringGlobalExceptionHandler.class);

    /**
     * @param e
     * @return com.kbao.commons.web.Result
     * @description 请求方式不支持
     * <AUTHOR>
     * @date 2021/3/1 9:36
     */
    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    @ResponseStatus(code = HttpStatus.METHOD_NOT_ALLOWED)
    public Result handle405(HttpRequestMethodNotSupportedException e) {
        logger.error(e.getMessage(), e);
        return Result.failed("不支持' " + e.getMethod() + "'的请求");
    }


    /**
     * 业务异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = {BusinessException.class})
    @ResponseStatus(value = HttpStatus.OK)
    public Result businessException(BusinessException e) {
        logger.error(e.getMessage(), e);
        return Result.failed(e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
    public Result handleException(MethodArgumentNotValidException exception, WebRequest request) {
        StringBuilder errorInfo = new StringBuilder();
        BindingResult bindingResult = exception.getBindingResult();
        for (int i = 0; i < bindingResult.getFieldErrors().size(); i++) {
            if (i > 0) {
                errorInfo.append(",");
            }
            FieldError fieldError = bindingResult.getFieldErrors().get(i);
            errorInfo.append(fieldError.getDefaultMessage());
        }
        return Result.failed("参数校验错误！" + errorInfo.toString());
    }


    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
    public Result handleConstraintViolationException(ConstraintViolationException exception) {
        StringBuilder errorInfo = new StringBuilder();
        String errorMessage;

        Set<ConstraintViolation<?>> violations = exception.getConstraintViolations();
        for (ConstraintViolation<?> item : violations) {
            errorInfo.append(item.getMessage()).append(",");
        }
        errorMessage = errorInfo.toString().substring(0, errorInfo.toString().length() - 1);
        return Result.failed("参数校验错误！" + errorMessage);
    }

    /**
     * 404错误异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = {NoHandlerFoundException.class})
    @ResponseStatus(value = HttpStatus.NOT_FOUND)
    public Result notFound(NoHandlerFoundException e) {
        return Result.failed("不好意思，未找到你请求的内容");
    }

    /**
     * 请求参数类型错误异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = {BindException.class})
    @ResponseStatus(value = HttpStatus.OK)
    public Result badRequest(BindException e) {
        logger.error(e.getMessage(), e);
        return Result.failed("请求参数类型错误");
    }

    /**
     * mybatis 未绑定异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = {BindingException.class})
    @ResponseStatus(value = HttpStatus.OK)
    public Result bindingException(BindingException e) {
        logger.error(e.getMessage(), e);
        return Result.failed("很抱歉，SQL出错了");
    }


    @ExceptionHandler(value = {PersistenceException.class, MyBatisSystemException.class})
    @ResponseStatus(value = HttpStatus.OK)
    public Result ibatisException(Exception e) {
        logger.error(e.getMessage(), e);
        String msg = "";
        if (e.getCause() instanceof PersistenceException) {
            PersistenceException per = (PersistenceException) e.getCause();
            if (per != null && per.getCause() instanceof RuntimeException) {
                RuntimeException runtimeExc = (RuntimeException) per.getCause();
                if (runtimeExc != null && runtimeExc.getCause() instanceof BusinessException) {
                    BusinessException businessException = (BusinessException) runtimeExc.getCause();
                    msg = Optional.ofNullable(businessException).map(BusinessException::getMessage).orElse("");
                } else {
                    msg = Optional.ofNullable(runtimeExc).map(RuntimeException::getMessage).orElse("");
                }
            } else {
                msg = Optional.ofNullable(per).map(PersistenceException::getMessage).orElse("");
            }
        } else {
            msg = e.getMessage();
        }
        return Result.failed(StringUtils.isNotBlank(msg) ? msg : "很抱歉，系统出错了，请联系管理员！");
    }

    /**
     * SQL异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = {SQLException.class, DataAccessException.class})
    @ResponseStatus(value = HttpStatus.OK)
    public Result sqlException(Exception e) {
        logger.error(e.getMessage(), e);
        String msg = "";
        if (e instanceof DataIntegrityViolationException) {
            if (e.getCause() instanceof MysqlDataTruncation) {
                SQLException sqlException = (SQLException) e.getCause();
                msg = Optional.ofNullable(sqlException).map(SQLException::getMessage).orElse("");
            } else {
                msg = Optional.ofNullable(e.getCause()).map(Throwable::getMessage).orElse("");
            }
        } else {
            msg = e.getMessage();
        }
        return Result.failed(StringUtils.isNotBlank(msg) ? msg : "很抱歉，系统出错了，请联系系统管理员！");
    }

    /**
     * 其他异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = {Exception.class})
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    public Result exception(Exception e) {
        logger.error(e.getMessage(), e);
//        return Result.failed("很抱歉，系统出错了，请稍后重试！");
        return Result.failed(e.getMessage());
    }


}
