package com.kbao.kbcchatbot.job;

import com.kbao.job.core.biz.model.ReturnT;
import com.kbao.job.core.handler.IJobHandler;
import com.kbao.job.core.handler.annotation.XxlJob;
import com.kbao.job.core.log.XxlJobLogger;
import com.kbao.kbcchatbot.maas.product.service.ProductSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SyncCompanyFileJob extends IJobHandler {
    @Autowired
    private ProductSyncService productSyncService;

    @Override
    @XxlJob("syncCompanyFileData")
    public ReturnT execute(String param) {
        ReturnT data = ReturnT.SUCCESS;
        try {
            productSyncService.syncCompanyFileData();
        }catch (Exception e) {
            log.error("同步公司文件数据：", e);
            XxlJobLogger.log("同步公司文件数据：", e);
            data = new ReturnT(ReturnT.FAIL_CODE, e.getMessage());
        }
        return data;
    }
}
