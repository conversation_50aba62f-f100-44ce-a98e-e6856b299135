package com.kbao.kbcchatbot.controller.maas.product;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.model.RequestPage;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.product.bean.CompanyListResVo;
import com.kbao.kbcchatbot.maas.product.bean.ProductListResVo;
import com.kbao.kbcchatbot.maas.product.entity.CompanyFile;
import com.kbao.kbcchatbot.maas.product.entity.ProductData;
import com.kbao.kbcchatbot.maas.product.entity.ProductFile;
import com.kbao.kbcchatbot.maas.product.service.CompanyFileService;
import com.kbao.tool.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/web/company")
public class CompanyFileController {
    @Autowired
    private CompanyFileService companyFileService;
    @Autowired
    private MaasHttpService maasHttpService;

    @PostMapping("/page")
    @LogAnnotation(module = "保司资料维护", action = "查询", desc = "查询保司列表")
    public Result<PageInfo<CompanyListResVo>> page(@RequestBody RequestObjectPage<CompanyFile> requestPage){
        PageInfo<CompanyListResVo> productList = companyFileService.getCompanyList(requestPage);
        return Result.succeed(productList, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/page")
    @LogAnnotation(module = "保司资料维护", action = "查询", desc = "查询保司资料列表")
    public Result<PageInfo<CompanyFile>> filePage(@RequestBody RequestPage requestPage){
        PageInfo<CompanyFile> page = companyFileService.page(requestPage);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/sliceList")
    @LogAnnotation(module = "保司资料维护", action = "查询", desc = "查询保司资料切片列表")
    public Result<JSONObject> fileSliceList(@RequestBody RequestPage requestPage){
        requestPage.getParam().put("type", "companyFile");
        JSONObject sliceList = maasHttpService.postMaasApi(MapUtils.objectToMap(requestPage), "/ex/product/sliceList");
        return Result.succeed(sliceList, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/original")
    @LogAnnotation(module = "产品资料维护", action = "查询", desc = "查看原文")
    public Result<JSONObject> getCompanyFileOriginal(@RequestBody Map<String, Object> param){
        JSONObject result = maasHttpService.postMaasApi(param, "/ex/company/file/original");
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/del")
    @LogAnnotation(module = "保司资料维护", action = "查询", desc = "删除")
    public Result<JSONObject> delProductFile(@RequestBody Map<String, Object> param){
        maasHttpService.postMaasApi(param, "/ex/company/file/del");
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/batch/add")
    public Result<JSONObject> addCompanyFiles(@RequestParam("companyId") String companyId, @RequestParam("fileType") String fileType,
                                              @RequestParam("files") MultipartFile[] files){
        companyFileService.addCompanyFiles(companyId, fileType, files);
        return Result.succeed("上传成功，正在解析...");
    }

}
