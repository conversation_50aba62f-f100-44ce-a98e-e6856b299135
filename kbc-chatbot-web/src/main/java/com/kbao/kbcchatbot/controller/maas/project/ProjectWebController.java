package com.kbao.kbcchatbot.controller.maas.project;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.project.bean.ProjectPageReqVO;
import com.kbao.kbcchatbot.maas.project.bean.ProjectSimpleVO;
import com.kbao.kbcchatbot.maas.project.entity.Project;
import com.kbao.kbcchatbot.maas.project.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * maas项目管理web控制层
 * @author: xiaojiayao
 * @time: 2024/12/12 14:52
 */
@RestController
@Slf4j
@RequestMapping("/web/maas/project")
public class ProjectWebController extends BaseController {

    @Autowired
    private ProjectService projectService;

    @PostMapping("/create")
    @LogAnnotation(module = "maas项目管理", action = "新增", desc = "新增项目")
    public Result create(@RequestBody @Validated Project param){
        projectService.save(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "maas项目管理", action = "更新", desc = "更新项目")
    public Result update(@RequestBody @Validated Project param){
        projectService.save(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "maas项目管理", action = "删除", desc = "删除项目")
    public Result delete(@RequestBody @Validated Project param){
        projectService.deleteProject(param.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/updateStatus")
    @LogAnnotation(module = "maas项目管理", action = "更新", desc = "更新项目状态")
    public Result updateStatus(@RequestBody Project param){
        projectService.updateStatus(param.getId(), param.getStatus());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "maas项目管理", action = "查询", desc = "项目分页查询")
    public Result<PageInfo<Project>> page(@RequestBody PageRequest<ProjectPageReqVO> reqVo){
        PageInfo<Project> page = projectService.getProjectPage(reqVo);
        return Result.succeed(page,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/list")
    @LogAnnotation(module = "maas项目管理", action = "查询", desc = "项目列表查询")
    public Result<List<ProjectSimpleVO>> list(){
        List<ProjectSimpleVO> projectSimpleList= projectService.getSimpleList();
        return Result.succeed(projectSimpleList,ResultStatusEnum.SUCCESS.getMsg());
    }
}
