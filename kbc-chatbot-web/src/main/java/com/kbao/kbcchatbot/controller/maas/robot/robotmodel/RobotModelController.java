package com.kbao.kbcchatbot.controller.maas.robot.robotmodel;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcchatbot.maas.model.entity.Model;
import com.kbao.kbcchatbot.maas.robot.robotmodel.bean.RobotModelAddReqVo;
import com.kbao.kbcchatbot.maas.robot.robotmodel.bean.RobotModelResVo;
import com.kbao.kbcchatbot.maas.robot.robotmodel.service.RobotModelService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;

import java.util.List;

/**
 * <AUTHOR> jie
 * @Description 管理
 * @Date 2025-01-09
*/
@RestController
@RequestMapping("/web/robotmodel")
public class RobotModelController extends BaseController {

	@Autowired
	private RobotModelService robotModelService;

	@PostMapping("/list")
	@LogAnnotation(module = "管理", action = "查询", desc = "分页查询列表")
	public Result<List<RobotModelResVo>> list(@RequestBody RobotModelAddReqVo reqVo) {
		List<RobotModelResVo> list = robotModelService.getModelByRobotId(reqVo.getRobotId());
		return Result.succeed(list,ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/save")
	@LogAnnotation(module = "管理", action = "保存", desc = "新增")
	public Result save(@Validated @RequestBody RobotModelAddReqVo reqVo){
		robotModelService.save(reqVo);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}


}