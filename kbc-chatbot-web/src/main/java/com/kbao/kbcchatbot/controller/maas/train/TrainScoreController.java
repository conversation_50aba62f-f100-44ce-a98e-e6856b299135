package com.kbao.kbcchatbot.controller.maas.train;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.export.ExcelUtils;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestPage;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotReceptionExportVO;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotReceptionPageReqVO;
import com.kbao.kbcchatbot.maas.train.scene.entity.TrainScene;
import com.kbao.kbcchatbot.maas.train.score.entity.TrainScore;
import com.kbao.kbcchatbot.maas.train.score.service.TrainScoreService;
import com.kbao.tool.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/web/train/score")
public class TrainScoreController {
    @Autowired
    private TrainScoreService trainScoreService;

    @PostMapping("/page")
    @LogAnnotation(module = "场景管理", action = "查询", desc = "查询成绩")
    public Result<PageInfo<TrainScore>> page(@RequestBody @Validated RequestPage requestPage){
        requestPage.getParam().put("hasRecord", "1");
        PageInfo<TrainScore> page = trainScoreService.page(requestPage);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/export")
    @LogAnnotation(module = "机器人管理", action = "导出", desc = "导出接待概况")
    public void export(@RequestBody @Validated RequestPage requestPage, HttpServletResponse response) throws Exception {
        List<TrainScore> trainScores = trainScoreService.selectByParam(requestPage.getParam());
        ExcelUtils<TrainScore> exportsExcelUtils = new ExcelUtils<>(TrainScore.class);
        exportsExcelUtils.writeExcel(trainScores, "人机对练成绩_" + DateUtils.thisDate(), response);
    }
}
