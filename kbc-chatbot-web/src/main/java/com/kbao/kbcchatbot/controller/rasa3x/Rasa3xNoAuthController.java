package com.kbao.kbcchatbot.controller.rasa3x;

import com.kbao.commons.web.BaseController;
import com.kbao.kbcbsc.adapter.TenantClientAdapter;
import com.kbao.kbcchatbot.rasa3x.service.Rasa3xService;
import com.kbao.kbcchatbot.upload.UploadService;
import com.kbao.kbcufs.adapter.FileClientAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description Rasa3X对话管理（无鉴权）
 * @Date 2023-05-23
 */
@RestController
@RequestMapping("/api/noauth/rasa3x")
public class Rasa3xNoAuthController extends BaseController {

    @Autowired
    private Rasa3xService rasa3xService;

    @Autowired
    private UploadService uploadService;

    @Autowired
    private FileClientAdapter fileClientAdapter;

/*    @Autowired
    private RobotService robotService;*/

    @Autowired
    private TenantClientAdapter tenantClientAdapter;

/*    @GetMapping(value = "/models")
    @LogAnnotation(module = "Rasa3X对话管理（无鉴权）", action = "操作", desc = "训练模型成功回调")
    public void models(@RequestParam(name = "robotId") Long robotId) {
        Robot robot = robotService.selectByPrimaryKey(robotId);
        if(robot == null) {
            logger.error("机器人【{" + robotId + "}】不存在");
            response.setStatus(304);
            return;
        }
        // 从请求头中获取If-None-Match字段的值作为hashCode。
        String hashCode = request.getHeader("If-None-Match");
        // 调用rasa3xService的models方法获取机器人的模型文件信息。
        RobotReleaseRecord releaseRecord = rasa3xService.models(robotId, hashCode);
        if(releaseRecord != null) {
            TenantVo tenantVo = new TenantVo();
            tenantVo.setTenantId(robot.getTenantId());
            // 调用tenantClientAdapter的getTenantConfigInfo方法获取租户配置信息。
            Result<TenantConfigInfoVo> tenantConfigInfoVoResult = tenantClientAdapter.getTenantConfigInfo(tenantVo);
            if(tenantConfigInfoVoResult.getResp_code() == ResultStatusEnum.SUCCESS.getStatus()) {
                // 获取机器人的模型文件。
                FileRequest req = (FileRequest) uploadService.getOssFileChannelWeb(robot.getTenantId(), null, "chatbotWeb", tenantConfigInfoVoResult.getDatas().getTenant().getTenantName(), null);
                req.setParam(releaseRecord.getRasaModelOSSFileId());
                req.setOperator("RASA");
                req.setOnline(true);
                Result<FileDownloadResponse> fileDownloadResponseResult = fileClientAdapter.download(req);
                if(fileDownloadResponseResult.getResp_code() == ResultStatusEnum.SUCCESS.getStatus()) {
                    logger.info("机器人【{" + robotId + "}】获取到新模型【" + fileDownloadResponseResult.getDatas().getFileName() + "】");
                    response.setHeader("ETag", releaseRecord.getId());
                    // 将模型文件写入response中。
                    StreamUtil.write(fileDownloadResponseResult.getDatas(), response, true);
                }else {
                    logger.error("机器人【{" + robotId + "}】获取模型失败，模型文件ID【" + releaseRecord.getRasaModelOSSFileId() + "】");
                    response.setStatus(304);
                }
            }else {
                logger.error("机器人【{" + robotId + "}】获取模型失败，没有找到租户【" + robot.getTenantId() + "】");
                response.setStatus(304);
            }
        }else {
            logger.info("机器人【{" + robotId + "}】没有新的模型");
            response.setStatus(304);
        }
    }*/
}
