package com.kbao.kbcchatbot.controller.maas.train;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestPage;
import com.kbao.kbcchatbot.maas.train.scene.bean.TrainSceneInitReqVO;
import com.kbao.kbcchatbot.maas.train.scene.entity.TrainScene;
import com.kbao.kbcchatbot.maas.train.scene.service.TrainSceneService;
import com.kbao.kbcchatbot.maas.train.score.entity.TrainScore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/web/train/scene")
public class TrainSceneController extends BaseController {
    @Autowired
    private TrainSceneService sceneService;

    @PostMapping("/page")
    @LogAnnotation(module = "场景管理", action = "查询", desc = "分页查询")
    public Result<PageInfo<TrainScene>> page(@RequestBody @Validated RequestPage requestPage){
        PageInfo<TrainScene> page = sceneService.page(requestPage);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/detail")
    @LogAnnotation(module = "场景管理", action = "查询", desc = "明细")
    public Result getById(@RequestBody TrainScene param){
        TrainScene trainScene = sceneService.selectByPrimaryKey(param.getId());
        return Result.succeed(trainScene, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/create")
    @LogAnnotation(module = "场景管理", action = "新增", desc = "新增场景")
    public Result create(@RequestBody TrainScene param){
        sceneService.insert(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "场景管理", action = "更新", desc = "更新场景")
    public Result update(@RequestBody @Validated TrainScene param){
        sceneService.update(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "场景管理", action = "删除", desc = "删除场景")
    public Result delete(@RequestBody @Validated TrainScene param){
        sceneService.delete(param.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/record/page")
    @LogAnnotation(module = "场景管理", action = "查询", desc = "查询对话")
    public Result<PageInfo<JSONObject>> recordPage(@RequestBody PageRequest<TrainSceneInitReqVO> pageRequest){
        PageInfo<JSONObject> pageInfo = sceneService.trainRecordPage(pageRequest, false);
        return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
    }
}