package com.kbao.kbcchatbot.controller.discard.insurancecompanyrule;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageInfo;
import org.springframework.validation.BindingResult;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.discard.insurancecompanyrule.service.InsuranceCompanyRuleService;
import com.kbao.kbcchatbot.discard.insurancecompanyrule.entity.InsuranceCompanyRule;

/**
 * <AUTHOR>
 * @Description 保险公司回访规则管理
 * @Date 2023-11-07
*/
@RestController
@RequestMapping("/web/insurancecompanyrule")
public class InsuranceCompanyRuleController extends BaseController {

	@Autowired
	private InsuranceCompanyRuleService insuranceCompanyRuleService;

	@PostMapping("/page")
	@LogAnnotation(module = "保险公司回访规则管理", action = "查询", desc = "分页查询列表")
	public Result<PageInfo<InsuranceCompanyRule>> page(@RequestBody RequestObjectPage<InsuranceCompanyRule> page) {
		PageInfo<InsuranceCompanyRule> insuranceCompanyRulePage = insuranceCompanyRuleService.page(page);
		Result<PageInfo<InsuranceCompanyRule>> result = Result.succeed(insuranceCompanyRulePage,ResultStatusEnum.SUCCESS.getMsg());
		return result;
	}

	@PostMapping("/add")
	@LogAnnotation(module = "保险公司回访规则管理", action = "新增", desc = "新增保险公司回访规则")
	public Result add(@Validated @RequestBody InsuranceCompanyRule insuranceCompanyRule, BindingResult result){
		//校验确认
		checkValidator(result);
		Result res;
		int num = insuranceCompanyRuleService.insert(insuranceCompanyRule);
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;
	}

	@PostMapping("/update")
	@LogAnnotation(module = "保险公司回访规则管理", action = "修改", desc = "修改保险公司回访规则")
	public Result update(@Validated @RequestBody InsuranceCompanyRule insuranceCompanyRule, BindingResult result){
		//校验确认
		checkValidator(result);
		Result res;
		int num = insuranceCompanyRuleService.update(insuranceCompanyRule);
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;

	}

	@PostMapping("/delete")
	@LogAnnotation(module = "保险公司回访规则管理", action = "删除", desc = "删除保险公司回访规则")
	public Result delete(@RequestBody InsuranceCompanyRule insuranceCompanyRule) {
		Result res;
	    int num = insuranceCompanyRuleService.delete(insuranceCompanyRule.getId());
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;
	}

}