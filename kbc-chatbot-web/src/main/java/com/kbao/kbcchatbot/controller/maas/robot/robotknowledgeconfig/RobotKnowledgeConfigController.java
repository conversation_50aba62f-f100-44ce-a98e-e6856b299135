package com.kbao.kbcchatbot.controller.maas.robot.robotknowledgeconfig;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.enums.YesNoEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.param.entity.Param;
import com.kbao.kbcchatbot.common.mqtt.service.MqttClientService;
import com.kbao.kbcchatbot.elasticsearch.search.bean.SearchQuery;
import com.kbao.kbcchatbot.discard.robot.enums.EnvTypeEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.entity.RobotKnowledgeConfig;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.enums.KnowledgeOperationEnum;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.vo.*;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeconfig.service.RobotKnowledgeConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @program: kbc-chatbot
 * @description: 知识配置
 * @author: husw
 * @create: 2023-05-17 11:15
 **/
@RestController
@RequestMapping("/web/knowledgeconfig")
public class RobotKnowledgeConfigController {

    @Autowired
    private RobotKnowledgeConfigService robotKnowledgeConfigService;

    @Autowired
    private MqttClientService mqttClientService;

    @PostMapping("/list")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "知识配置目录查询")
    public Result<List<RobotKnowledgeConfigDirectVO>> list(@RequestBody @Validated RobotKnowledgeConfigIdReqVO param){
        List<RobotKnowledgeConfigDirectVO> list = robotKnowledgeConfigService.list(param);
        return Result.succeed(list,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/refresh")
    @LogAnnotation(module = "机器人管理", action = "更新", desc = "刷新知识")
    public Result refresh(@RequestBody @Validated RobotKnowledgeConfigRefreshReqVO param){
        robotKnowledgeConfigService.refresh(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "机器人管理", action = "更新", desc = "单个知识更新")
    public Result update(@RequestBody @Validated RobotKnowledgeUpdateReqVO param){
        robotKnowledgeConfigService.update(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/bind")
    @LogAnnotation(module = "机器人管理", action = "更新", desc = "绑定")
    public Result bind(@RequestBody @Validated RobotKnowledgeConfigBindReqVO param){
        robotKnowledgeConfigService.bind(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/unbind")
    @LogAnnotation(module = "机器人管理", action = "更新", desc = "解绑")
    public Result unbind(@RequestBody @Validated RobotKnowledgeConfigBindReqVO param){
        robotKnowledgeConfigService.unbind(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/bindList")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "已绑定目录查询")
    public Result<PageInfo<RobotKnowledgeConfig>> bindList(@RequestBody @Validated RequestObjectPage<RobotKnowledgeConfigIdReqVO> param){
        if (YesNoEnum.NO.getValue().equals(param.getParam().getType())){
            param.getParam().setOperationNoEq(KnowledgeOperationEnum.UNBIND.getCode());
        }
        param.getParam().setType(EnvTypeEnum.PROD_ENV.getCode());
        param.setSort("update_time desc");
        PageInfo<RobotKnowledgeConfig> page = robotKnowledgeConfigService.page(param);
        return Result.succeed(page,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/directoryList")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "查询已绑定目录")
    public Result<List<RobotKnowledgeDirectRespVO>> directoryList(@RequestBody @Validated RobotKnowledgeDirectoryReqVO param){
        if (YesNoEnum.NO.getValue().equals(param.getType())){
            param.setOperationNoEq(KnowledgeOperationEnum.UNBIND.getCode());
        }
        param.setType(EnvTypeEnum.PROD_ENV.getCode());
        List<RobotKnowledgeDirectRespVO> list = robotKnowledgeConfigService.directoryList(param);
        return Result.succeed(list,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getFuncId")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "查询知识库菜单Id")
    public Result<Param> getFuncId(){
        return  Result.succeed(robotKnowledgeConfigService.getParam(), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/search")
    @LogAnnotation(module = "知识包管理", action = "查询", desc = "查询知识")
    public Result search(@RequestBody SearchQuery searchQuery){
        return  Result.succeed(robotKnowledgeConfigService.search(searchQuery), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/pageAll")
    @LogAnnotation(module = "知识包管理", action = "查询", desc = "查询知识")
    public Result pageAll(@RequestBody RequestObjectPage<RobotKnowledgeConfigIdReqVO> pageRequest){
        return  Result.succeed(robotKnowledgeConfigService.pageAll(pageRequest), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/pageSearch")
    @LogAnnotation(module = "知识包管理", action = "查询", desc = "分页查询知识明细")
    public Result<PageInfo<RobotKnowledgeSubDetailVO>> pageSearch(@RequestBody @Validated RequestObjectPage<RobotKnowledgeSearchReqVO> pageRequest){
        return  Result.succeed(robotKnowledgeConfigService.pageSearch(pageRequest), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getDetail")
    @LogAnnotation(module = "知识包管理", action = "查询", desc = "查询知识详情")
    public Result<RobotKnowledgeDetailRespVO> getDetail(@RequestBody @Validated RobotKnowledgeDetailReqVO reqVO){
        return  Result.succeed(robotKnowledgeConfigService.getDetail(reqVO.getRobotId(),reqVO.getKnowledgeId()), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/deleteEs")
    @LogAnnotation(module = "知识包管理", action = "查询", desc = "清理ES数据")
    public Result deleteEs(@RequestBody @Validated RobotKnowledgeConfigIdReqVO reqVO){
        robotKnowledgeConfigService.deleteByRobot(reqVO.getRobotId(),reqVO.getType());
        return  Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/refreshAll")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "批量刷新知识")
    public Result refreshAll(@RequestBody @Validated RobotKnowledgeConfigRefreshAllReqVO param){
        robotKnowledgeConfigService.refreshAll(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/updateDirectoryCode")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "批量知识目录")
    public Result updateDirectoryCode(@RequestBody @Validated RobotKnowledgeConfigRefreshAllReqVO param){
        robotKnowledgeConfigService.updateDirectoryCode(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/subscribe")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "批量刷新知识")
    public Result refreshAll(){
        mqttClientService.subscribe();
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

}
