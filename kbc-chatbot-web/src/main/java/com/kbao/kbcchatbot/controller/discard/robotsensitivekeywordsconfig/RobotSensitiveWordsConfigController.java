package com.kbao.kbcchatbot.controller.discard.robotsensitivekeywordsconfig;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.entity.RobotSensitiveWords;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.service.RobotSensitiveWordsConfigService;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.vo.RobotSensitiveWordsIdVO;
import com.kbao.kbcchatbot.discard.robotsensitivewordsconfig.vo.RobotSensitiveWordsSaveVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: 敏感词配置
 * @author: husw
 * @create: 2023-07-03 17:55
 **/
@RestController
@RequestMapping("/web/sensitivewords")
public class RobotSensitiveWordsConfigController {

    @Autowired
    private RobotSensitiveWordsConfigService robotSensitiveWordsConfigService;

    @PostMapping("/save")
    @LogAnnotation(module = "机器人管理", action = "新增", desc = "保存敏感词配置")
    public Result save(@RequestBody RobotSensitiveWordsSaveVO param){
        robotSensitiveWordsConfigService.save(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }


    @PostMapping("/page")
    @LogAnnotation(module = "机器人管理", action = "新增", desc = "敏感词配置分页查询")
    public Result<PageInfo<RobotSensitiveWords>> page(@RequestBody RequestObjectPage<RobotSensitiveWordsSaveVO> pageRequest){
        pageRequest.setSort("create_time desc");
        PageInfo<RobotSensitiveWords> page = robotSensitiveWordsConfigService.page(pageRequest);
        return Result.succeed(page,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "机器人管理", action = "新增", desc = "删除敏感词配置")
    public Result delete(@RequestBody RobotSensitiveWordsIdVO param){
        robotSensitiveWordsConfigService.delete(param.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
