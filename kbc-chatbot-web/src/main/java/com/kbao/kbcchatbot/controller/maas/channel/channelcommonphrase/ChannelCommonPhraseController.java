package com.kbao.kbcchatbot.controller.maas.channel.channelcommonphrase;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.bean.ChannelCommonPhraseVO;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.entity.ChannelCommonPhrase;
import com.kbao.kbcchatbot.maas.channel.channelcommonphrase.service.ChannelCommonPhraseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 渠道常用短语管理
 * @Date 2023-05-26
*/
@RestController
@RequestMapping("/web/channelcommonphrase")
@Validated
public class ChannelCommonPhraseController extends BaseController {

	@Autowired
	private ChannelCommonPhraseService channelCommonPhraseService;

	@PostMapping("/page")
	@LogAnnotation(module = "渠道常用短语管理", action = "查询", desc = "分页查询列表")
	public Result<PageInfo<ChannelCommonPhrase>> page(@RequestBody RequestObjectPage<ChannelCommonPhrase> page) {
		page.setSort("sort asc");
		PageInfo<ChannelCommonPhrase> channelCommonPhrasePage = channelCommonPhraseService.page(page);
		Result<PageInfo<ChannelCommonPhrase>> result = Result.succeed(channelCommonPhrasePage,ResultStatusEnum.SUCCESS.getMsg());
		return result;
	}

	@PostMapping("/add")
	@LogAnnotation(module = "渠道常用短语管理", action = "新增", desc = "新增渠道常用短语")
	public Result add(@Validated @RequestBody ChannelCommonPhraseVO channelCommonPhraseVO){
		return Result.succeed(channelCommonPhraseService.addChannelCommonPhrase(channelCommonPhraseVO),ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/update")
	@LogAnnotation(module = "渠道常用短语管理", action = "修改", desc = "修改渠道常用短语")
	public Result update(@Validated @RequestBody ChannelCommonPhraseVO channelCommonPhraseVO, BindingResult result){
		//校验确认
		checkValidator(result);
		Result res;
		int num = channelCommonPhraseService.update(channelCommonPhraseVO);
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;

	}

	@PostMapping("/deleteChannelCommonPhrase")
	@LogAnnotation(module = "渠道常用短语管理", action = "删除", desc = "删除渠道常用短语")
	public Result delete(@RequestBody ChannelCommonPhrase channelCommonPhrase) {
	    channelCommonPhraseService.deleteChannelCommonPhrase(channelCommonPhrase);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());

	}

	@PostMapping("/getChannelCommonPhraseById")
	@LogAnnotation(module = "渠道常用短语管理", action = "查询", desc = "根据id查询渠道常用短语")
	public Result<ChannelCommonPhraseVO> getById(@RequestBody ChannelCommonPhrase channelCommonPhrase) {
		return Result.succeed(channelCommonPhraseService.getChannelCommonPhraseById(channelCommonPhrase.getId()),ResultStatusEnum.SUCCESS.getMsg());
	}

}