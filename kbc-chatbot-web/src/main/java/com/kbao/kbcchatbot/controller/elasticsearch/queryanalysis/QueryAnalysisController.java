package com.kbao.kbcchatbot.controller.elasticsearch.queryanalysis;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.constants.ElasticsearchConstants;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.bean.QueryAnalysisReqVO;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.bean.QueryAnalysisResVO;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.entity.QueryAnalysis;
import com.kbao.kbcchatbot.elasticsearch.queryanalysis.service.QueryAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2020/11/16 16:37
 */
@RestController
@RequestMapping("/web/es/queryAnalysis")
public class QueryAnalysisController extends BaseController {

    @Autowired
    private QueryAnalysisService queryanalysisService;
    
    @PostMapping("/insert")
    @LogAnnotation(module = "查询词分析配置", action = "新增", fieldPath = "I$.queryColumn", desc = "新增字段%s分析配置")
    public Result insert(@RequestBody QueryAnalysisReqVO queryAnalysisReqVO) {
        queryanalysisService.insert(queryAnalysisReqVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "查询词分析配置", action = "修改", fieldPath = "I$.queryColumn", desc = "修改字段%s分析配置")
    public Result update(@RequestBody QueryAnalysisReqVO queryAnalysisReqVO) {
        queryanalysisService.update(queryAnalysisReqVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/enable")
    @LogAnnotation(module = "查询词分析配置", action = "操作", fieldPath = "I$.queryColumn", desc = "启用字段%s分析配置")
    public Result enable(@RequestBody QueryAnalysisReqVO queryAnalysisReqVO) {
        queryAnalysisReqVO.setAnalysisStatus(ElasticsearchConstants.QUERY_ANALYSIS_STATUS_NORMAL);
        queryanalysisService.update(queryAnalysisReqVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/disable")
    @LogAnnotation(module = "查询词分析配置", action = "操作", fieldPath = "I$.queryColumn", desc = "停用字段%s分析配置")
    public Result disable(@RequestBody QueryAnalysisReqVO queryAnalysisReqVO) {
        queryAnalysisReqVO.setAnalysisStatus(ElasticsearchConstants.QUERY_ANALYSIS_STATUS_INVALID);
        queryanalysisService.update(queryAnalysisReqVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "查询词分析配置", action = "删除", fieldPath = "I$.queryColumn", desc = "删除字段%s分析配置")
    public Result delete(@RequestBody QueryAnalysisReqVO queryAnalysisReqVO) {
        queryanalysisService.delete(queryAnalysisReqVO.getAnalyId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "查询词分析配置", action = "查询", desc = "分页查询分析配置列表")
    public Result page(@RequestBody RequestObjectPage<QueryAnalysis> p) {
        p.getParam().setIsDeleted(ElasticsearchConstants.STATUS_NOT_DELETE);
        PageInfo<QueryAnalysisResVO> page = queryanalysisService.pageByCondition(p);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

}
