package com.kbao.kbcchatbot.controller.discard.robotpackagerel;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageInfo;
import org.springframework.validation.BindingResult;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.discard.robotpackagerel.service.RobotPackageRelService;
import com.kbao.kbcchatbot.discard.robotpackagerel.entity.RobotPackageRel;

/**
 * <AUTHOR>
 * @Description 机器人包关联管理
 * @Date 2023-06-02
*/
@RestController
@RequestMapping("/web/robotpackagerel")
public class RobotPackageRelController extends BaseController {

	@Autowired
	private RobotPackageRelService robotPackageRelService;

	@PostMapping("/page")
	@LogAnnotation(module = "机器人包关联管理", action = "查询", desc = "分页查询列表")
	public Result<PageInfo<RobotPackageRel>> page(@RequestBody RequestObjectPage<RobotPackageRel> page) {
		PageInfo<RobotPackageRel> robotPackageRelPage = robotPackageRelService.page(page);
		Result<PageInfo<RobotPackageRel>> result = Result.succeed(robotPackageRelPage,ResultStatusEnum.SUCCESS.getMsg());
		return result;
	}

	@PostMapping("/add")
	@LogAnnotation(module = "机器人包关联管理", action = "新增", desc = "新增机器人包关联")
	public Result add(@Validated @RequestBody RobotPackageRel robotPackageRel, BindingResult result){
		//校验确认
		checkValidator(result);
		Result res;
		int num = robotPackageRelService.insert(robotPackageRel);
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;
	}

	@PostMapping("/update")
	@LogAnnotation(module = "机器人包关联管理", action = "修改", desc = "修改机器人包关联")
	public Result update(@Validated @RequestBody RobotPackageRel robotPackageRel, BindingResult result){
		//校验确认
		checkValidator(result);
		Result res;
		int num = robotPackageRelService.update(robotPackageRel);
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;

	}

	@PostMapping("/delete")
	@LogAnnotation(module = "机器人包关联管理", action = "删除", desc = "删除机器人包关联")
	public Result delete(@RequestBody RobotPackageRel robotPackageRel) {
		Result res;
	    int num = robotPackageRelService.delete(robotPackageRel.getId());
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;
	}

}