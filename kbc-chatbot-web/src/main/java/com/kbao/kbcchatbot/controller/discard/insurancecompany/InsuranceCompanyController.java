package com.kbao.kbcchatbot.controller.discard.insurancecompany;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageInfo;
import org.springframework.validation.BindingResult;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.discard.insurancecompany.service.InsuranceCompanyService;
import com.kbao.kbcchatbot.discard.insurancecompany.entity.InsuranceCompany;

/**
 * <AUTHOR>
 * @Description 保险公司管理
 * @Date 2023-11-07
*/
@RestController
@RequestMapping("/web/insurancecompany")
public class InsuranceCompanyController extends BaseController {

	@Autowired
	private InsuranceCompanyService insuranceCompanyService;

	@PostMapping("/page")
	@LogAnnotation(module = "保险公司管理", action = "查询", desc = "分页查询列表")
	public Result<PageInfo<InsuranceCompany>> page(@RequestBody RequestObjectPage<InsuranceCompany> page) {
		PageInfo<InsuranceCompany> insuranceCompanyPage = insuranceCompanyService.page(page);
		Result<PageInfo<InsuranceCompany>> result = Result.succeed(insuranceCompanyPage,ResultStatusEnum.SUCCESS.getMsg());
		return result;
	}

	@PostMapping("/add")
	@LogAnnotation(module = "保险公司管理", action = "新增", desc = "新增保险公司")
	public Result add(@Validated @RequestBody InsuranceCompany insuranceCompany, BindingResult result){
		//校验确认
		checkValidator(result);
		Result res;
		int num = insuranceCompanyService.insert(insuranceCompany);
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;
	}

	@PostMapping("/update")
	@LogAnnotation(module = "保险公司管理", action = "修改", desc = "修改保险公司")
	public Result update(@Validated @RequestBody InsuranceCompany insuranceCompany, BindingResult result){
		//校验确认
		checkValidator(result);
		Result res;
		int num = insuranceCompanyService.update(insuranceCompany);
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;

	}

	@PostMapping("/delete")
	@LogAnnotation(module = "保险公司管理", action = "删除", desc = "删除保险公司")
	public Result delete(@RequestBody InsuranceCompany insuranceCompany) {
		Result res;
	    int num = insuranceCompanyService.delete(insuranceCompany.getId());
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;
	}

}