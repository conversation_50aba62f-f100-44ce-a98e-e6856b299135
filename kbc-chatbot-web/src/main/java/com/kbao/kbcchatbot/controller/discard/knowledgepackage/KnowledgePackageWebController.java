package com.kbao.kbcchatbot.controller.discard.knowledgepackage;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcchatbot.elasticsearch.search.bean.SearchQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageInfo;
import org.springframework.validation.BindingResult;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.discard.knowledgepackage.service.KnowledgePackageService;
import com.kbao.kbcchatbot.discard.knowledgepackage.entity.KnowledgePackage;
import org.springframework.web.multipart.MultipartFile;

import static com.kbao.kbcchatbot.constants.CacheConstant.SYNC_DEFAULT_ROBOT_CODE;

/**
 * <AUTHOR>
 * @Description 知识包管理
 * @Date 2023-05-23
*/
@RestController
@RequestMapping("/web/knowledgepackage")
public class KnowledgePackageWebController extends BaseController {

	@Autowired
	private KnowledgePackageService knowledgePackageService;

	@Autowired
	private RedisUtil redisUtil;


	@PostMapping("/page")
	@LogAnnotation(module = "知识包管理", action = "查询", desc = "分页查询列表")
	public Result<PageInfo<KnowledgePackage>> page(@RequestBody RequestObjectPage<KnowledgePackage> page) {
		PageInfo<KnowledgePackage> knowledgePackagePage = knowledgePackageService.page(page);
		Result<PageInfo<KnowledgePackage>> result = Result.succeed(knowledgePackagePage,ResultStatusEnum.SUCCESS.getMsg());
		return result;
	}

	@PostMapping("/add")
	@LogAnnotation(module = "知识包管理", action = "新增", desc = "新增知识包")
	public Result add(@Validated @RequestBody KnowledgePackage knowledgePackage, BindingResult result){
		//校验确认
		checkValidator(result);
		Result res;
		int num = knowledgePackageService.insert(knowledgePackage);
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;
	}

	@PostMapping("/update")
	@LogAnnotation(module = "知识包管理", action = "修改", desc = "修改知识包")
	public Result update(@Validated @RequestBody KnowledgePackage knowledgePackage, BindingResult result){
		//校验确认
		checkValidator(result);
		Result res;
		int num = knowledgePackageService.update(knowledgePackage);
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;

	}

	@PostMapping("/delete")
	@LogAnnotation(module = "知识包管理", action = "删除", desc = "删除知识包")
	public Result delete(@RequestBody KnowledgePackage knowledgePackage) {
		Result res;
	    int num = knowledgePackageService.delete(knowledgePackage.getId());
		if(num == 0) {
			res = Result.failed(ResultStatusEnum.FAIL.getMsg());
		}else {
			res = Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
		}
		return res;
	}

	@PostMapping("/uploadPackage")
	@LogAnnotation(module = "知识包管理", action = "导入", desc = "导入知识包")
	public Result upload(@RequestParam(name = "file",required = false) MultipartFile file, @RequestParam(value = "packageDTOJson") String packageDTOJson){
		knowledgePackageService.uploadPackage(file, packageDTOJson);
		return  Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/search")
	@LogAnnotation(module = "知识包管理", action = "导入", desc = "导入知识包")
	public Result search(@RequestBody SearchQuery searchQuery){
		return  Result.succeed(knowledgePackageService.search(searchQuery), ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/updateRobotCode")
	@LogAnnotation(module = "知识包管理", action = "更新", desc = "更新默认智能体")
	public Result updateRobotCode(@RequestParam String robotCode){
		String s = redisUtil.generateKey(SYNC_DEFAULT_ROBOT_CODE);
		redisUtil.set(s,robotCode);
		return  Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

}