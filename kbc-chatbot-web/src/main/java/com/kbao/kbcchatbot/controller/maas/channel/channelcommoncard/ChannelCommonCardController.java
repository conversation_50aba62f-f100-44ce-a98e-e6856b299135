package com.kbao.kbcchatbot.controller.maas.channel.channelcommoncard;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.bean.ChannelCommonCardVO;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.entity.ChannelCommonCard;
import com.kbao.kbcchatbot.maas.channel.channelcommoncard.service.ChannelCommonCardService;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 渠道常用卡片管理
 * @Date 2023-05-22
 */
@RestController
@RequestMapping("/web/channelcommoncard")
public class ChannelCommonCardController extends BaseController {

    @Autowired
    private ChannelCommonCardService channelCommonCardService;

    @Autowired
    private ChatSessionRecordService chatSessionRecordService;

    @PostMapping("/page")
    @LogAnnotation(module = "渠道常用卡片管理", action = "查询", desc = "分页查询列表")
    public Result<PageInfo<ChannelCommonCard>> page(@RequestBody RequestObjectPage<ChannelCommonCard> page) {
        PageInfo<ChannelCommonCard> channelCommonCardPage = channelCommonCardService.page(page);
        return Result.succeed(channelCommonCardPage, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/add")
    @LogAnnotation(module = "渠道常用卡片管理", action = "新增", desc = "新增渠道常用卡片")
    public Result add(@Validated @RequestBody ChannelCommonCardVO channelCommonCardVO) {
        return Result.succeed(channelCommonCardService.addChannelCommonCard(channelCommonCardVO), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "渠道常用卡片管理", action = "修改", desc = "修改渠道常用卡片")
    public Result update(@Validated @RequestBody ChannelCommonCardVO channelCommonCardVO) {
        return Result.succeed(channelCommonCardService.update(channelCommonCardVO), ResultStatusEnum.SUCCESS.getMsg());


    }

    @PostMapping("/deleteChannelCommonCard")
    @LogAnnotation(module = "渠道常用卡片管理", action = "删除", desc = "删除渠道常用卡片")
    public Result delete(@RequestBody ChannelCommonCard channelCommonCard) {
        channelCommonCardService.deleteChannelCommonCard(channelCommonCard);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getChannelCommonCardById")
    @LogAnnotation(module = "渠道常用卡片管理", action = "查询", desc = "根据id查询渠道常用卡片")
    public Result<ChannelCommonCardVO> getById(@RequestBody ChannelCommonCardVO channelCommonCardVO) {
        return Result.succeed(channelCommonCardService.getChannelCommonCardById(channelCommonCardVO.getId()), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/convertJSONData")
    @LogAnnotation(module = "聊天会话管理", action = "查询", desc = "结束时间查询聊天记录")
    public Result convertJSONData() {
        chatSessionRecordService.convertJSONData();
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

}