//package com.kbao.kbcchatbot.controller.knowledgegraph;
//
//import com.github.pagehelper.PageInfo;
//import com.kbao.commons.enums.ResultStatusEnum;
//import com.kbao.commons.web.Result;
//import com.kbao.kbcbsc.log.annotation.LogAnnotation;
//import com.kbao.kbcbsc.model.RequestObjectPage;
//import com.kbao.kbcchatbot.knowledgegraph.cypher.entity.InsuranceProductNode;
//import com.kbao.kbcchatbot.knowledgegraph.cypher.service.KnowledgeGraphService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("/web/knowledgegraph")
//public class KnowledgeGraphController {
//
//    @Autowired
//    private KnowledgeGraphService knowledgeGraphService;
//
////    @Autowired
////    private InsuranceProductGraphService insuranceProductGraphService;
//
//    @PostMapping("/syncInsuranceProduct")
//    @LogAnnotation(module = "知识图谱管理", action = "操作", desc = "同步产品知识图谱")
//    public Result<List<String>> syncInsuranceProduct() {
//        knowledgeGraphService.syncInsuranceProduct();
//        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PostMapping("/pageInsuranceProduct")
//    @LogAnnotation(module = "知识图谱管理", action = "操作", desc = "同步产品知识图谱")
//    public Result<PageInfo<InsuranceProductNode>> pageInsuranceProduct(@RequestBody RequestObjectPage<InsuranceProductNode> req) {
//        PageInfo<InsuranceProductNode> pageInfo = knowledgeGraphService.pageInsuranceProduct(req);
//        return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//}
