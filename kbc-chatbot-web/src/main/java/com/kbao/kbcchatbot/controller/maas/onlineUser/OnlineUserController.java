package com.kbao.kbcchatbot.controller.maas.onlineUser;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.model.entity.Model;
import com.kbao.kbcchatbot.maas.onlineUser.service.OnlineUserStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;

@RestController
@Slf4j
@RequestMapping("/web/onlineUser")
public class OnlineUserController {
    @Autowired
    private OnlineUserStatService onlineUserStatService;

    @PostMapping("/count")
    @LogAnnotation(module = "在线用户统计", action = "查询在线人数")
    public Result<Long> count(@RequestBody JSONObject params){
        Integer duration = params.getInteger("duration");
        Long count = onlineUserStatService.count(Duration.ofMinutes(duration));
        return Result.succeed(count, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/clear")
    @LogAnnotation(module = "在线用户统计", action = "清除在线人数")
    public Result clear(@RequestBody JSONObject params){
        Integer duration = params.getInteger("duration");
        onlineUserStatService.clear(Duration.ofHours(duration));
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
