package com.kbao.kbcchatbot.controller.elasticsearch.column;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.constants.ElasticsearchConstants;
import com.kbao.kbcchatbot.elasticsearch.column.bean.ColumnReqVO;
import com.kbao.kbcchatbot.elasticsearch.column.bean.ColumnResVO;
import com.kbao.kbcchatbot.elasticsearch.column.entity.Column;
import com.kbao.kbcchatbot.elasticsearch.column.service.ColumnService;
import com.kbao.kbcchatbot.elasticsearch.index.bean.IndexReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2020/11/16 17:02
 */
@RestController
@RequestMapping("/web/es/column")
public class ColumnController extends BaseController {

    @Autowired
    private ColumnService columnService;

    @PostMapping("/insert")
    @LogAnnotation(module = "索引字段管理", action = "新增", fieldPath = "I$.columnName", desc = "新增%s索引字段")
    public Result insert(@RequestBody ColumnReqVO req) {
        columnService.insert(req);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "索引字段管理", action = "修改", fieldPath = "I$.columnName", desc = "修改%s索引字段")
    public Result update(@RequestBody ColumnReqVO req) {
        columnService.update(req);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
    
    @PostMapping("/setAnalyzerStatus")
    @LogAnnotation(module = "索引字段管理", action = "操作", fieldPath = "I$.columnName", desc = "设置%s索引字段检索状态")
    public Result setAnalyzerStatus(@RequestBody Column req) {
        columnService.setAnalyzerStatus(req);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
    
    @PostMapping("/delete")
    @LogAnnotation(module = "索引字段管理", action = "删除", fieldPath = "I$.columnName", desc = "删除%s索引字段")
    public Result delete(@RequestBody ColumnReqVO req){
        columnService.delete(req.getColumnId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "索引字段管理", action = "查询", desc = "分页查询索引列表")
    public Result page(@RequestBody RequestObjectPage<Column> req) {
        req.setSort("create_time");
        PageInfo<ColumnResVO> page = columnService.pageByCondition(req);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }
    
    @PostMapping("/generateColumn")
    @LogAnnotation(module = "索引字段管理", action = "操作", desc = "自动创建索引字段")
    public Result generateColumn(@RequestBody IndexReqVO req) {
        columnService.generateColumn(req);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/list")
    @LogAnnotation(module = "索引字段管理", action = "查询", fieldPath = "I$.indId", desc = "获取索引%s下的所有字段")
    public Result list(@RequestBody Column column) {
        Map<String, Object> params = new HashMap<>();
        params.put("isDeleted", ElasticsearchConstants.STATUS_NOT_DELETE);
        params.put("indId", column.getIndId());
        params.put("isAnalyzer", ElasticsearchConstants.DICITEM_YORN_YES);
        List<Column> columns = columnService.list(params);
        return Result.succeed(columns, ResultStatusEnum.SUCCESS.getMsg());
    }
    
}