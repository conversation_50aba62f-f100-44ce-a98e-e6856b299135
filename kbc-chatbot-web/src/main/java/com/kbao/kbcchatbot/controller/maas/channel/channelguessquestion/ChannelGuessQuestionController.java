package com.kbao.kbcchatbot.controller.maas.channel.channelguessquestion;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.bean.ChannelGuessQuestionVO;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.entity.ChannelGuessQuestion;
import com.kbao.kbcchatbot.maas.channel.channelguessquestion.service.ChannelGuessQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 渠道猜你想问管理
 * @Date 2023-05-24
 */
@RestController
@RequestMapping("/web/channelguessquestion")
public class ChannelGuessQuestionController extends BaseController {

    @Autowired
    private ChannelGuessQuestionService channelGuessQuestionService;

    @PostMapping("/page")
    @LogAnnotation(module = "渠道猜你想问管理", action = "查询", desc = "分页查询列表")
    public Result<PageInfo<ChannelGuessQuestion>> page(@RequestBody RequestObjectPage<ChannelGuessQuestion> pageReq) {
        pageReq.setSort("updateTime desc");
        return Result.succeed(channelGuessQuestionService.page(pageReq), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/add")
    @LogAnnotation(module = "渠道猜你想问管理", action = "新增", desc = "新增渠道猜你想问")
    public Result add(@Validated @RequestBody ChannelGuessQuestionVO channelGuessQuestionVO) {
        return Result.succeed(channelGuessQuestionService.createChannelGuessQuestion(channelGuessQuestionVO),ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "渠道猜你想问管理", action = "修改", desc = "修改渠道猜你想问")
    public Result update(@Validated @RequestBody ChannelGuessQuestionVO channelGuessQuestionVO) {
        return Result.succeed(channelGuessQuestionService.update(channelGuessQuestionVO), ResultStatusEnum.SUCCESS.getMsg());


    }

    @PostMapping("/deleteChannelGuessQuestion")
    @LogAnnotation(module = "渠道猜你想问管理", action = "删除", desc = "删除渠道猜你想问")
    public Result delete(@RequestBody ChannelGuessQuestion channelGuessQuestion) {
        channelGuessQuestionService.deleteChannelGuessQuestion(channelGuessQuestion);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());

    }
    @PostMapping("/getChannelGuessQuestionById")
    @LogAnnotation(module = "渠道猜你想问管理", action = "查询", desc = "根据id查询渠道猜你想问")
    public Result<ChannelGuessQuestionVO> getById(@RequestBody ChannelGuessQuestionVO channelGuessQuestionVO) {
        return Result.succeed(channelGuessQuestionService.getChannelGuessQuestionById(channelGuessQuestionVO.getId()), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/updateSourceType")
    @LogAnnotation(module = "渠道猜你想问管理", action = "修改", desc = "修改渠道猜你想问")
    public Result updateSourceType() {
        channelGuessQuestionService.getMapper().updateSourceType();
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());

    }

}