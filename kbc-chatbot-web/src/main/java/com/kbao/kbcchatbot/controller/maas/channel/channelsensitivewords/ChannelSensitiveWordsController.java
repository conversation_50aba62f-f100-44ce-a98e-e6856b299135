package com.kbao.kbcchatbot.controller.maas.channel.channelsensitivewords;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.maas.channel.channelsensitivewords.bean.ChannelSensitiveWordsIdVO;
import com.kbao.kbcchatbot.maas.channel.channelsensitivewords.bean.ChannelSensitiveWordsSaveVO;
import com.kbao.kbcchatbot.maas.channel.channelsensitivewords.entity.ChannelSensitiveWords;
import com.kbao.kbcchatbot.maas.channel.channelsensitivewords.service.ChannelSensitiveWordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: maas渠道敏感词配置
 * @author: xiaojy
 * @create: 2024年12月25日09:49:50
 **/
@RestController
@RequestMapping("/web/maas/channelsensitivewords")
public class ChannelSensitiveWordsController {

    @Autowired
    private ChannelSensitiveWordsService channelSensitiveWordsService;

    @PostMapping("/save")
    @LogAnnotation(module = "maas渠道敏感词管理", action = "新增", desc = "保存敏感词配置")
    public Result save(@RequestBody ChannelSensitiveWordsSaveVO param){
        channelSensitiveWordsService.save(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }


    @PostMapping("/page")
    @LogAnnotation(module = "maas渠道敏感词管理", action = "新增", desc = "敏感词配置分页查询")
    public Result<PageInfo<ChannelSensitiveWords>> page(@RequestBody RequestObjectPage<ChannelSensitiveWordsSaveVO> pageRequest){
        pageRequest.setSort("create_time desc");
        PageInfo<ChannelSensitiveWords> page = channelSensitiveWordsService.page(pageRequest);
        return Result.succeed(page,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "maas渠道敏感词管理", action = "删除", desc = "删除敏感词配置")
    public Result delete(@RequestBody ChannelSensitiveWordsIdVO param){
        channelSensitiveWordsService.delete(param.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
