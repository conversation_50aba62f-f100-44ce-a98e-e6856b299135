package com.kbao.kbcchatbot.controller.maas.robot.robotknowledgerecord;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.maas.robot.robotknowledgeRecord.service.RobotKnowledgeRecordService;
import com.kbao.kbcchatbot.maas.robot.robotknowledgerecord.entity.RobotKnowledgeRecord;
import com.kbao.kbcchatbot.maas.robot.robotknowledgerecord.vo.RobotKnowledgeRecordIdReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: 历史记录
 * @author: husw
 * @create: 2023-05-23 11:20
 **/
@RestController
@RequestMapping("/web/knowledgerecord")
public class RobotKnowledgeRecordController {

    @Autowired
    private RobotKnowledgeRecordService robotKnowledgeRecordService;

    @PostMapping("/page")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "查询操作记录")
    public Result<PageInfo<RobotKnowledgeRecord>> page(@RequestBody @Validated RequestObjectPage<RobotKnowledgeRecordIdReqVO> pageRequest){
        PageInfo<RobotKnowledgeRecord> page = robotKnowledgeRecordService.page(pageRequest);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }
}
