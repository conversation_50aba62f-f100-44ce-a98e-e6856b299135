package com.kbao.kbcchatbot.controller.discard.robotreleaserecord;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.discard.robotreleaserecord.entity.RobotReleaseRecord;
import com.kbao.kbcchatbot.discard.robotreleaserecord.service.RobotReleaseRecordService;
import com.kbao.kbcchatbot.discard.robotreleaserecord.vo.RobotReleaseRecordIdReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: 发布记录
 * @author: husw
 * @create: 2023-05-30 17:41
 **/
@RestController
@RequestMapping("/web/releaserecord")
public class RobotReleaseRecordController {

    @Autowired
    private RobotReleaseRecordService robotReleaseRecordService;

    @PostMapping("/page")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "查询操作记录")
    public Result<PageInfo<RobotReleaseRecord>> page(@RequestBody @Validated RequestObjectPage<RobotReleaseRecordIdReqVO> pageRequest){
        PageInfo<RobotReleaseRecord> page = robotReleaseRecordService.page(pageRequest);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }
}
