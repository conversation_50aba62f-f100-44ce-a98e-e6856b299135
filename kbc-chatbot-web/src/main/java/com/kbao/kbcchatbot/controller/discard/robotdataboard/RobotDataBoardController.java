package com.kbao.kbcchatbot.controller.discard.robotdataboard;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.externalapi.model.*;
import com.kbao.kbcchatbot.maas.chatsession.service.ChatSessionService;
import com.kbao.kbcchatbot.maas.chatsessionrecord.ChatSessionRecord;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.ChatSessionConvRecord;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.ChatSessionConvRecordReqVO;
import com.kbao.kbcchatbot.maas.chatsessionrecord.service.ChatSessionRecordService;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotChatSessionRecordReqVO;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotReceptionPageReqVO;
import com.kbao.kbcchatbot.discard.robotdataboard.vo.RobotReceptionRespVO;
import com.kbao.tool.util.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description: 数据看板
 * @author: husw
 * @create: 2023-06-29 15:11
 **/
@RestController
@RequestMapping("/web/databoard")
public class RobotDataBoardController {

    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private ChatSessionRecordService chatSessionRecordService;

    @Autowired
    private MaasHttpService maasHttpService;


    @PostMapping("/page")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "查询接待概况")
    public Result<PageInfo<RobotReceptionRespVO>> page(@RequestBody @Validated RequestObjectPage<RobotReceptionPageReqVO> pageRequest){
        PageInfo<RobotReceptionRespVO> result = chatSessionService.pageInfo(pageRequest);
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/export")
    @LogAnnotation(module = "机器人管理", action = "导出", desc = "导出接待概况")
    public void export(@RequestBody @Validated RobotReceptionPageReqVO reqVO, HttpServletResponse response) throws Exception {
        chatSessionService.export(reqVO, response);
    }

    @PostMapping("/pageRecord")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "查询会话历史")
    public Result<PageInfo<ChatSessionRecord>> pageRecord(@RequestBody @Validated RequestObjectPage<RobotChatSessionRecordReqVO> pageRequest){
        PageInfo<ChatSessionRecord> result = chatSessionRecordService.pageInfo(pageRequest);
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/pageConversation")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "查询对话记录")
    public Result<PageInfo<ChatSessionConvRecord>> pageConversation(@RequestBody @Validated RequestObjectPage<ChatSessionConvRecordReqVO> pageRequest){
        PageInfo<ChatSessionConvRecord> result = chatSessionRecordService.pageConversationNew(pageRequest);
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/exportConversation")
    @LogAnnotation(module = "机器人管理", action = "导出", desc = "导出对话记录")
    public void exportConversation(@RequestBody ChatSessionConvRecordReqVO reqVO, HttpServletResponse response) throws Exception {
        chatSessionRecordService.exportConversationNew(reqVO, response);
    }

    @PostMapping("/fixRecordUserName")
    @LogAnnotation(module = "机器人管理", action = "操作", desc = "修复对话记录用户名")
    public void fixRecordUserName(){
        chatSessionRecordService.fixRecordUserName();
    }

    @PostMapping("/fixRecordTenantId")
    @LogAnnotation(module = "机器人管理", action = "操作", desc = "修复对话记录租户ID")
    public void fixRecordTenantId(){
        chatSessionRecordService.fixRecordTenantId();
    }

//    @PostMapping("/fixChatSession")
//    @LogAnnotation(module = "机器人管理", action = "操作", desc = "修复会话记录")
//    public void fixChatSession(@RequestParam String date){
//        chatSessionService.fixChatSession(date);
//    }

    @PostMapping("/syncSessionRecord")
    @LogAnnotation(module = "机器人管理", action = "操作", desc = "修复会话记录(接口调用处理数据)")
    public void syncSessionRecord(@RequestBody @Validated LargeModelSyncVO syncVO){
        chatSessionRecordService.syncSessionRecord(syncVO);
    }

    @PostMapping("/syncVoteAndCopy")
    @LogAnnotation(module = "机器人管理", action = "操作", desc = "同步历史数据")
    public void syncVoteAndCopy(@RequestParam String startTime,@RequestParam String endTime){
        chatSessionRecordService.syncVoteAndCopy(startTime,endTime);
    }

    @PostMapping("/pageFlowRecord")
    @LogAnnotation(module = "执行记录", action = "查询", desc = "查询执行记录")
    public Result<JSONObject> pageFlowRecord(@RequestBody RequestObjectPage<MaasFlowDetailReq> req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONObject result = maasHttpService.postMaasApi(objectMap, "ex/flow/getFlowRecord");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getFlowDetail")
    @LogAnnotation(module = "执行记录", action = "查询", desc = "查询执行记录详情")
    public Result<JSONArray> getFlowDetail(@RequestBody MaasFlowRecordReq req) {
        Map<String, Object> objectMap = MapUtils.objectToMap(req);
        JSONArray result = maasHttpService.postMaasArrayApi(objectMap, "ex/flow/getFlowDetail");
        return Result.succeed(result,ResultStatusEnum.SUCCESS.getMsg());
    }

}
