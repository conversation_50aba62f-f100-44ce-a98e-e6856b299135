package com.kbao.kbcchatbot.controller.elasticsearch.index;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.elasticsearch.index.bean.IndexAnalyzerVO;
import com.kbao.kbcchatbot.elasticsearch.index.bean.IndexReqVO;
import com.kbao.kbcchatbot.elasticsearch.index.bean.IndexTaskVO;
import com.kbao.kbcchatbot.elasticsearch.index.entity.Index;
import com.kbao.kbcchatbot.elasticsearch.index.service.IndexService;
import io.swagger.annotations.ApiOperation;
import org.elasticsearch.client.tasks.GetTaskResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2020/11/16 13:56
 */
@RestController
@RequestMapping("/web/es/index")
@RefreshScope
public class IndexController extends BaseController {

    @Value("${reindex.settingPath}")
    private String settingPath;

    @Autowired
    private IndexService indexService;

    @ApiOperation(value = "索引列表分页查询", notes = "索引列表分页查询")
    @LogAnnotation(module = "应用管理", action = "查询", desc = "分页查询索引列表")
    @RequestMapping("/pageIndex")
    public Result<Object> pageIndex(@RequestBody RequestObjectPage<Index> req){
        PageInfo<Index> page = indexService.page(req);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/insert")
    @LogAnnotation(module = "索引", action = "新增", fieldPath = "I$.name", desc = "新增索引%s")
    public Result insert(@RequestBody IndexReqVO indexReqVO) {
        indexService.insert(indexReqVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "索引", action = "修改", fieldPath = "I$.name", desc = "修改索引%s")
    public Result update(@RequestBody IndexReqVO indexReqVO) {
        indexService.update(indexReqVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "索引", action = "删除", fieldPath = "I$.name", desc = "删除索引%s")
    public Result delete(@RequestBody IndexReqVO indexReqVO) {
        indexService.delete(indexReqVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "索引管理", action = "查询", desc = "分页查询索引字段列表")
    public Result page(@RequestBody RequestObjectPage<Index> req) {
        PageInfo page = indexService.page(req);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/testAnalyzer")
    @LogAnnotation(module = "索引管理", action = "查询", fieldPath = "I$.text", desc = "查询%s分词结果")
    public Result testAnalyzer(@RequestBody IndexAnalyzerVO req) {
        List ikTokenList = indexService.getIkAnalysisList(req.getText(), req.getAnalyzer());
        return Result.succeed(ikTokenList, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/reindex")
    @LogAnnotation(module = "索引管理", action = "操作", fieldPath = "I$.name", desc = "重建索引%s")
    public Result reindex(@RequestBody IndexReqVO indexReqVO) throws IOException {
        //获取通用es_setting
        InputStream resourceAsStream = IndexController.class.getResourceAsStream(settingPath);
        int n = 0;
        StringBuilder settingJson = new StringBuilder();
        while (true) {
            try {
                if ((n = resourceAsStream.read()) == -1) break;
            } catch (IOException e) {
                e.printStackTrace();
            }
            settingJson.append((char) n);
        }
        try {
            resourceAsStream.close();
        } catch (IOException e) {
            throw new BusinessException("索引配置不存在");
        }
        indexService.reindex(indexReqVO.getIndId(), settingJson.toString());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/reindexAll")
    @LogAnnotation(module = "索引管理", action = "操作", desc = "重建全部索引")
    public Result reindexAll() {
        //获取通用es_setting
        InputStream resourceAsStream = IndexController.class.getResourceAsStream(settingPath);
        int n = 0;
        StringBuilder settingJson = new StringBuilder();
        while (true) {
            try {
                if ((n = resourceAsStream.read()) == -1) break;
            } catch (IOException e) {
                e.printStackTrace();
            }
            settingJson.append((char) n);
        }
        try {
            resourceAsStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        indexService.reindexAll(settingJson.toString());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/queryTaskInfo")
    @LogAnnotation(module = "索引管理", action = "查询", fieldPath = "I$.task", desc = "查询任务%s详细信息")
    public Result queryTaskInfo(@RequestBody IndexTaskVO indexReqVO) {
        GetTaskResponse taskStatus = indexService.getTaskStatus(indexReqVO.getTask());
        return Result.succeed(taskStatus, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getMapping")
    @LogAnnotation(module = "索引管理", action = "查询", fieldPath = "I$.indId", desc = "查询索引%s字段映射")
    public Result getIndexMapping(@RequestBody IndexReqVO indexReqVO) {
        Map indexMapping = indexService.getMapping(indexReqVO);
        return Result.succeed(indexMapping, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/deleteAllData")
    @LogAnnotation(module = "索引管理", action = "删除", fieldPath = "I$.indId", desc = "删除索引%s下所有数据")
    public Result deleteAllData(@RequestBody IndexReqVO indexReqVO) {
        indexService.deleteAllData(indexReqVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

}
