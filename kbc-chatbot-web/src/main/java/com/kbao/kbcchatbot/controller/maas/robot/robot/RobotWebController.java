package com.kbao.kbcchatbot.controller.maas.robot.robot;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.robot.robot.bean.RobotAddReqVO;
import com.kbao.kbcchatbot.maas.robot.robot.bean.RobotPageReqVO;
import com.kbao.kbcchatbot.maas.robot.robot.bean.RobotSimpleVO;
import com.kbao.kbcchatbot.maas.robot.robot.entity.Robot;
import com.kbao.kbcchatbot.maas.robot.robot.service.RobotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * maas机器人web控制层
 * @author: xiaojiayao
 * @time: 2024/12/12 14:52
 */
@RestController
@Slf4j
@RequestMapping("/web/maas/robot")
public class RobotWebController extends BaseController {

    @Autowired
    private RobotService robotService;

    @PostMapping("/create")
    @LogAnnotation(module = "maas机器人管理", action = "新增", desc = "新增机器人")
    public Result create(@RequestBody @Validated RobotAddReqVO param){
        robotService.insert(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "maas机器人管理", action = "更新", desc = "更新机器人")
    public Result update(@RequestBody @Validated RobotAddReqVO param){
        robotService.update(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "maas机器人管理", action = "查询", desc = "机器人分页查询")
    public Result<PageInfo<Robot>> page(@RequestBody PageRequest<RobotPageReqVO> reqVo){
        PageInfo<Robot> page = robotService.getRobotPage(reqVo);
        return Result.succeed(page,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/list")
    @LogAnnotation(module = "maas机器人管理", action = "查询", desc = "机器人列表查询")
    public Result<List<RobotSimpleVO>> list(@RequestBody RobotPageReqVO robotPageReqVO){
        List<RobotSimpleVO> robotList = robotService.getRobotList(robotPageReqVO);
        return Result.succeed(robotList,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/find")
    @LogAnnotation(module = "maas机器人管理", action = "查询", desc = "机器人列表查询")
    public Result<Robot> getRobot(@RequestBody RobotPageReqVO robotPageReqVO){
        Robot robot = robotService.getRobot(robotPageReqVO);
        return Result.succeed(robot,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "maas机器人管理", action = "查询", desc = "机器人列表查询")
    public Result<Robot> delete(@RequestBody RobotSimpleVO vo){
        robotService.delete(vo.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
