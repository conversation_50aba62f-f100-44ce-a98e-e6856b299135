package com.kbao.kbcchatbot.controller.bsc;

import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.dicitems.entity.DicItems;
import com.kbao.kbcbsc.tenant.bean.TenantConfigInfoVo;
import com.kbao.kbcbsc.user.model.UserIdReq;
import com.kbao.kbcbsc.util.BscApiContext;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.kbc.bsc.DicsReqParam;
import com.kbao.kbcchatbot.kbc.bsc.KbcBscService;
import com.kbao.kbcucs.bsc.WebUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: kbc-chatbot
 * @description: BSC
 * @author: husw
 * @create: 2023-05-22 15:16
 **/
@RestController
@RequestMapping("/web/bsc")
public class KbcBscController {

    @Autowired
    private KbcBscService kbcBscService;

    @PostMapping("/dic/getDicItems")
    public Result<Map<String,List<DicItems>>> getDicItems(@Validated @RequestBody DicsReqParam dicsReqParam) {
        Map<String,List<DicItems>> map = new HashMap<>(dicsReqParam.getDicCodeList().size());
        for (String s : dicsReqParam.getDicCodeList()) {
            List<DicItems> dicItems = kbcBscService.getDicItems(s);
            map.put(s,dicItems);
        }
        return Result.succeed(map,"查询成功");
    }

    @PostMapping("/getWebUserInfo")
    public Result getWebUserInfo() {
        WebUser webUser = new WebUser();
        webUser.setIsAdmin(BscUserUtils.getUser().getUser().getIsAdmin());
        webUser.setFuncAuthDTO(BscUserUtils.getUser().getFunction().getFuncAuths());
        webUser.setAppTenantListVo(kbcBscService.getWebUserTenants().getDatas());
        return Result.succeed(webUser, "获取登录用户信息成功");
    }

    @PostMapping("/tenant/getTenantUsers")
    public Result<List<UserIdReq>> getTenantUsers() {
        return kbcBscService.getTenantUsers();
    }

    @PostMapping("/tenant/init")
    public Result<TenantConfigInfoVo> getTenantInfo() {
        return Result.succeed(kbcBscService.getTenantConfigInfo(BscApiContext.TenantId.get()), "查询成功");
    }
}
