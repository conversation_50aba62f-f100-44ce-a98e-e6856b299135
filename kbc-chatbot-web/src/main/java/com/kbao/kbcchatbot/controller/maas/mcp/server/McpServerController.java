package com.kbao.kbcchatbot.controller.maas.mcp.server;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.mcp.server.entity.McpServer;
import com.kbao.kbcchatbot.maas.mcp.server.service.McpServerService;
import com.kbao.tool.util.MapUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * MCP服务器管理控制器
 *
 * <AUTHOR>
 * @Since 2025-09-02
 */
@Api(tags = "MCP服务器管理")
@RestController
@RequestMapping("/api/mcp/server")
public class McpServerController extends BaseController {

    @Autowired
    private McpServerService mcpServerService;

    /**
     * 新增服务器
     */
    @ApiOperation("新增服务器")
    @PostMapping("/add")
    public Result<String> add(@RequestBody McpServer mcpServer) {
        try {
            mcpServerService.add(mcpServer);
            return Result.succeed("新增成功");
        } catch (Exception e) {
            return Result.failed("新增失败：" + e.getMessage());
        }
    }

    /**
     * 更新服务器
     */
    @ApiOperation("更新服务器")
    @PostMapping("/update")
    public Result<String> update(@RequestBody McpServer mcpServer) {
        try {
            mcpServerService.update(mcpServer);
            return Result.succeed("更新成功");
        } catch (Exception e) {
            return Result.failed("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除服务器
     */
    @ApiOperation("删除服务器")
    @PostMapping("/delete")
    public Result<String> delete(@RequestParam Long serverId) {
        try {
            mcpServerService.delete(serverId);
            return Result.succeed("删除成功");
        } catch (Exception e) {
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询服务器
     */
    @ApiOperation("根据ID查询服务器")
    @GetMapping("/get")
    public Result<McpServer> get(@RequestParam Long serverId) {
        try {
            McpServer mcpServer = mcpServerService.selectByPrimaryKey(serverId);
            return Result.succeed(mcpServer, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据编码查询服务器
     */
    @ApiOperation("根据编码查询服务器")
    @GetMapping("/getByCode")
    public Result<McpServer> getByCode(@RequestParam String serverCode) {
        try {
            McpServer queryServer = new McpServer();
            queryServer.setServerCode(serverCode);
            List<McpServer> list = mcpServerService.selectByParam(MapUtils.objectToMap(queryServer));
            if (list != null && !list.isEmpty()) {
                return Result.succeed(list.get(0), ResultStatusEnum.SUCCESS.getMsg());
            } else {
                return Result.failed("未找到对应的服务器");
            }
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询服务器列表
     */
    @ApiOperation("分页查询服务器列表")
    @PostMapping("/list")
    public Result<PageInfo<McpServer>> list(@RequestBody RequestObjectPage<McpServer> reqVO) {
        try {
            // 自动添加当前用户的租户ID过滤条件
            if (reqVO.getParam() == null) {
                reqVO.setParam(new McpServer());
            }
            if (reqVO.getParam().getTenantId() == null) {
                reqVO.getParam().setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            }
            PageInfo<McpServer> pageInfo = mcpServerService.page(reqVO);
            return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有服务器列表
     */
    @ApiOperation("查询所有服务器列表")
    @PostMapping("/listAll")
    public Result<List<McpServer>> listAll(@RequestBody McpServer mcpServer) {
        try {
            // 自动添加当前用户的租户ID过滤条件
            if (mcpServer.getTenantId() == null) {
                mcpServer.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            }
            List<McpServer> list = mcpServerService.selectByParam(MapUtils.objectToMap(mcpServer));
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用服务器列表
     */
    @ApiOperation("获取可用服务器列表")
    @GetMapping("/available")
    public Result<List<McpServer>> getAvailableServers() {
        try {
            McpServer queryServer = new McpServer();
            queryServer.setServerStatus(1);
            // 自动添加当前用户的租户ID过滤条件
            queryServer.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            List<McpServer> list = mcpServerService.selectByParam(MapUtils.objectToMap(queryServer));
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

}