//package com.kbao.kbcchatbot.controller.channel;
//
//import com.github.pagehelper.PageInfo;
//import com.kbao.commons.enums.ResultStatusEnum;
//import com.kbao.commons.web.BaseController;
//import com.kbao.commons.web.Result;
//import com.kbao.kbcbsc.log.annotation.LogAnnotation;
//import com.kbao.kbcbsc.model.RequestObjectPage;
//import com.kbao.kbcchatbot.channel.bean.ChannelFullVO;
//import com.kbao.kbcchatbot.channel.entity.Channel;
////import com.kbao.kbcchatbot.channel.service.ChannelService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * <AUTHOR>
// * @Description 渠道表管理
// * @Date 2023-05-18
// */
////@RestController
////@RequestMapping("/web/channel")
//public class ChannelController extends BaseController {
//
///*    @Autowired
//    private ChannelService channelService;*/
///*
//    @PostMapping("/page")
//    @LogAnnotation(module = "渠道表管理", action = "查询", desc = "分页查询列表")
//    public Result<PageInfo<Channel>> page(@RequestBody RequestObjectPage<Channel> page) {
//        page.setSort("updateTime desc");
//        return Result.succeed(channelService.getChannelPage(page), ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PostMapping("/add")
//    @LogAnnotation(module = "渠道表管理", action = "新增", desc = "新增渠道表")
//    public Result add(@RequestBody Channel channel) {
//        return Result.succeed(channelService.insert(channel), ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PostMapping("/update")
//    @LogAnnotation(module = "渠道表管理", action = "修改", desc = "修改渠道表")
//    public Result update(@RequestBody Channel channel) {
//        return Result.succeed(channelService.update(channel), ResultStatusEnum.SUCCESS.getMsg());
//
//
//    }
//
//    @PostMapping("/delete")
//    @LogAnnotation(module = "渠道表管理", action = "删除", desc = "删除渠道表")
//    public Result delete(@RequestBody Channel channel) {
//        return Result.succeed(channelService.delete(channel.getId()), ResultStatusEnum.SUCCESS.getMsg());
//
//    }
//
//    @PostMapping("/getChannelFullData")
//    @LogAnnotation(module = "渠道表管理", action = "查询", desc = "查询渠道所有数据")
//    public Result<ChannelFullVO> getChannelFullData(@RequestBody Channel channel) {
//        return Result.succeed(channelService.getChannelFullData(channel.getId()), ResultStatusEnum.SUCCESS.getMsg());
//    }*/
//}