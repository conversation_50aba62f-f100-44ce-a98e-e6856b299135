package com.kbao.kbcchatbot.controller.maas.flow;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestPage;
import com.kbao.kbcchatbot.maas.flow.entity.Flow;
import com.kbao.kbcchatbot.maas.flow.service.FlowService;
import com.kbao.kbcchatbot.maas.model.entity.Model;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/web/flow")
public class FlowController {
    @Autowired
    private FlowService flowService;

    @PostMapping("/save")
    @LogAnnotation(module = "流程管理", action = "保存", desc = "新增流程配置")
    public Result save(@RequestBody Flow flow){
        flowService.save(flow);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "流程管理", action = "保存", desc = "修改流程配置")
    public Result update(@RequestBody Flow flow){
        flowService.update(flow);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "流程管理", action = "查询", desc = "分页查询")
    public Result<PageInfo<Flow>> getPageInfo(@RequestBody RequestPage reqVo){
        reqVo.setSort("update_time desc");
        PageInfo<Flow> page = flowService.page(reqVo);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/list")
    @LogAnnotation(module = "流程管理", action = "保存", desc = "查询流程列表")
    public Result<List<Flow>> list(@RequestBody Flow flow){
        List<Flow> list = flowService.selectList(flow);
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "流程管理", action = "删除", desc = "删除流程")
    public Result<PageInfo<Model>> delete(@RequestBody Flow flow){
        flowService.delete(flow.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
