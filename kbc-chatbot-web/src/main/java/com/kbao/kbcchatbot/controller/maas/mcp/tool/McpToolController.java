package com.kbao.kbcchatbot.controller.maas.mcp.tool;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.mcp.tool.entity.McpTool;
import com.kbao.kbcchatbot.maas.mcp.tool.service.McpToolService;
import com.kbao.tool.util.MapUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * MCP工具管理控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Api(tags = "MCP工具管理")
@RestController
@RequestMapping("/api/mcp/tool")
public class McpToolController extends BaseController {

    @Autowired
    private McpToolService mcpToolService;

    /**
     * 新增工具
     */
    @ApiOperation("新增工具")
    @PostMapping("/add")
    public Result<String> add(@RequestBody McpTool mcpTool) {
        try {
            mcpToolService.add(mcpTool);
            return Result.succeed("新增成功");
        } catch (Exception e) {
            return Result.failed("新增失败：" + e.getMessage());
        }
    }

    /**
     * 更新工具
     */
    @ApiOperation("更新工具")
    @PostMapping("/update")
    public Result<String> update(@RequestBody McpTool mcpTool) {
        try {
            mcpToolService.update(mcpTool);
            return Result.succeed("更新成功");
        } catch (Exception e) {
            return Result.failed("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除工具
     */
    @ApiOperation("删除工具")
    @PostMapping("/delete")
    public Result<String> delete(@RequestParam Long toolId) {
        try {
            mcpToolService.delete(toolId);
            return Result.succeed("删除成功");
        } catch (Exception e) {
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询工具
     */
    @ApiOperation("根据ID查询工具")
    @GetMapping("/get")
    public Result<McpTool> get(@RequestParam Long toolId) {
        try {
            McpTool mcpTool = mcpToolService.selectByPrimaryKey(toolId);
            return Result.succeed(mcpTool, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据编码查询工具
     */
    @ApiOperation("根据编码查询工具")
    @GetMapping("/getByCode")
    public Result<McpTool> getByCode(@RequestParam String toolCode) {
        try {
            McpTool queryTool = new McpTool();
            queryTool.setToolCode(toolCode);
            List<McpTool> list = mcpToolService.selectByParam(MapUtils.objectToMap(queryTool));
            if (list != null && !list.isEmpty()) {
                return Result.succeed(list.get(0), ResultStatusEnum.SUCCESS.getMsg());
            } else {
                return Result.failed("未找到对应的工具");
            }
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询工具列表
     */
    @ApiOperation("分页查询工具列表")
    @PostMapping("/list")
    public Result<PageInfo<McpTool>> list(@RequestBody RequestObjectPage<McpTool> reqVO) {
        try {
            // 自动添加当前用户的租户ID过滤条件
            if (reqVO.getParam() == null) {
                reqVO.setParam(new McpTool());
            }
            if (reqVO.getParam().getTenantId() == null) {
                reqVO.getParam().setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            }
            PageInfo<McpTool> pageResult = mcpToolService.page(reqVO);
            return Result.succeed(pageResult, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有工具列表
     */
    @ApiOperation("查询所有工具列表")
    @PostMapping("/listAll")
    public Result<List<McpTool>> listAll(@RequestBody McpTool mcpTool) {
        try {
            // 自动添加当前用户的租户ID过滤条件
            if (mcpTool.getTenantId() == null) {
                mcpTool.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            }
            List<McpTool> list = mcpToolService.selectByParam(MapUtils.objectToMap(mcpTool));
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据服务器ID查询工具列表
     */
    @ApiOperation("根据服务器ID查询工具列表")
    @GetMapping("/listByServerId")
    public Result<List<McpTool>> listByServerId(@RequestParam Long serverId) {
        try {
            McpTool queryTool = new McpTool();
            queryTool.setServerId(serverId);
            // 自动添加当前用户的租户ID过滤条件
            queryTool.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            List<McpTool> list = mcpToolService.selectByParam(MapUtils.objectToMap(queryTool));
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用工具列表
     */
    @ApiOperation("获取可用工具列表")
    @GetMapping("/available")
    public Result<List<McpTool>> getAvailableTools() {
        try {
            McpTool queryTool = new McpTool();
            queryTool.setToolStatus(1);
            // 自动添加当前用户的租户ID过滤条件
            queryTool.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            List<McpTool> list = mcpToolService.selectByParam(MapUtils.objectToMap(queryTool));
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据服务器ID获取可用工具列表
     */
    @ApiOperation("根据服务器ID获取可用工具列表")
    @GetMapping("/availableByServerId")
    public Result<List<McpTool>> getAvailableToolsByServerId(@RequestParam Long serverId) {
        try {
            McpTool queryTool = new McpTool();
            queryTool.setServerId(serverId);
            queryTool.setToolStatus(1);
            // 自动添加当前用户的租户ID过滤条件
            queryTool.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            List<McpTool> list = mcpToolService.selectByParam(MapUtils.objectToMap(queryTool));
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 切换工具状态
     */
    @ApiOperation("切换工具状态")
    @PostMapping("/toggleStatus")
    public Result<String> toggleStatus(@RequestParam Long toolId, @RequestParam Integer status) {
        try {
            McpTool mcpTool = new McpTool();
            mcpTool.setToolId(toolId);
            mcpTool.setToolStatus(status);
            mcpToolService.update(mcpTool);
            return Result.succeed("状态切换成功");
        } catch (Exception e) {
            return Result.failed("状态切换失败：" + e.getMessage());
        }
    }

}