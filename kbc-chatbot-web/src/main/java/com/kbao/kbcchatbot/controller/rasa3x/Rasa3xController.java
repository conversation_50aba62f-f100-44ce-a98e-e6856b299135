package com.kbao.kbcchatbot.controller.rasa3x;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.rasa3x.service.Rasa3xService;
import com.kbao.kbcchatbot.upload.UploadService;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Description Rasa3X对话管理
 * @Date 2023-05-23
 */
@RestController
@RequestMapping("/web/rasa3x")
public class Rasa3xController extends BaseController {


    @Autowired
    private Rasa3xService rasa3xService;

    @Autowired
    private UploadService uploadService;

//    @PostMapping("/trainModel")
//    @LogAnnotation(module = "Rasa3X对话管理", action = "操作", desc = "训练模型")
//    public Result trainModel(@RequestBody TrainModelReqVO reqVO) {
//        String accessToken = TokenUtil.getToken(request);
//        reqVO.setAccessToken(accessToken);
//        String funcId = request.getHeader("funcId");
//        reqVO.setFuncId(funcId);
//        rasa3xService.tranModel(reqVO);
//        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PostMapping("/parse")
//    @LogAnnotation(module = "Rasa3X对话管理", action = "操作", desc = "训练模型")
//    public Result parse(@RequestBody String msg) {
//        ParseResult parseResult = rasa3xService.parse(msg);
//        return Result.succeed(parseResult, ResultStatusEnum.SUCCESS.getMsg());
//    }



}
