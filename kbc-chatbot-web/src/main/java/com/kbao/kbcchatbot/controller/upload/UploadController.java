package com.kbao.kbcchatbot.controller.upload;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.upload.UploadService;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> qiuzb
 * @Description: 上传文件
 * @create 2023/5/23 14:24
 */
@RestController
@RequestMapping(value = "/web/upload")
public class UploadController extends BaseController {

    @Autowired
    private UploadService uploadService;

    @PostMapping(value = "/uploadFile")
    @LogAnnotation(module = "文件上传", action = "常用卡片", desc = "常用卡片文件上传")
    public Result uploadFile(@ApiParam(value = "上传文件", required = true) @RequestParam("file") MultipartFile file,
                                     @ApiParam(value = "业务类型", required = true) @RequestParam("fileType") String fileType) {
        return uploadService.uploadFileWeb(file, fileType);
    }

    @PostMapping(value = "/uploadImages")
    public Result uploadImages(@RequestParam("images") MultipartFile[] images,@RequestParam("bizModule") String bizModule) {
        List<FileUploadResponse> fileUploadResponses = uploadService.uploadImages(images, bizModule);
        return Result.succeed(fileUploadResponses,"上传成功");
    }

    @PostMapping(value = "/deleteFile")
    public Result deleteFile(@RequestParam("bizModule") String bizModule) {
        List<FileUploadResponse> fileUploadResponses = uploadService.deleteFile(bizModule);
        return Result.succeed(fileUploadResponses,"上传成功");
    }
}
