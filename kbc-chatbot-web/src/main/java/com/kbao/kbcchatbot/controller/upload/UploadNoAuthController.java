package com.kbao.kbcchatbot.controller.upload;

import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.upload.UploadService;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: mass文件上传
 * @date 2025/4/29 13:58
 */
@RestController
@RequestMapping(value = "/api/noauth/upload")
public class UploadNoAuthController {

    @Autowired
    private UploadService uploadService;

    @PostMapping(value = "/uploadFile")
    @LogAnnotation(module = "文件上传", action = "语音上传", desc = "常用卡片文件上传")
    public Result uploadFile(@ApiParam(value = "上传文件", required = true) @RequestParam("file") MultipartFile file,
                             @ApiParam(value = "文件类型", required = true) @RequestParam("fileType") String fileType,
                             @ApiParam(value = "用户id", required = true) @RequestParam("userId") String userId) {
        FileUploadResponse fileUploadResponse = uploadService.uploadFile(file, fileType, userId);
        return Result.succeed(fileUploadResponse,"请求成功");
    }
}
