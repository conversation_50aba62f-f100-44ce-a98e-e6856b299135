package com.kbao.kbcchatbot.controller.maas.record;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.product.service.ProductSyncService;
import com.kbao.kbcchatbot.maas.record.bean.RecordInfoResVo;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.service.RobotKnowledgeHttpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/web/maas/qaRecord")
public class RecordController extends BaseController {
    @Autowired
    private RobotKnowledgeHttpService robotKnowledgeHttpService;
    @Autowired
    private ProductSyncService productSyncService;

    @PostMapping("/info")
    @LogAnnotation(module = "maas问答记录", action = "查询", desc = "查询日志")
    public Result getQueryQaRecordInfo(@RequestBody @Validated RecordInfoResVo reqVo){
        JSONObject recordPage = robotKnowledgeHttpService.getQueryQaRecordInfo(reqVo);
        return Result.succeed(recordPage, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/references")
    @LogAnnotation(module = "maas问答记录", action = "查询", desc = "查询引用知识")
    public Result getQueryQaRecordReferences(@RequestBody @Validated RecordInfoResVo reqVo){
        JSONArray recordPage = robotKnowledgeHttpService.getQueryQaRecordReferences(reqVo);
        return Result.succeed(recordPage, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/syncProductData")
    @LogAnnotation(module = "同步产品", action = "查询", desc = "同步产品信息")
    public Result syncProductData(){
        productSyncService.syncProducts();
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
