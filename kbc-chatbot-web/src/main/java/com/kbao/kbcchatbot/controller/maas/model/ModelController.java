package com.kbao.kbcchatbot.controller.maas.model;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestPage;
import com.kbao.kbcchatbot.constants.SettingConstant;
import com.kbao.kbcchatbot.maas.model.bean.ModelPageResVo;
import com.kbao.kbcchatbot.maas.model.entity.Model;
import com.kbao.kbcchatbot.maas.model.service.ModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/web/model")
public class ModelController {
    @Autowired
    private ModelService modelService;

    @PostMapping("/save")
    @LogAnnotation(module = "模型管理", action = "保存", desc = "新增/修改模型配置")
    public Result save(@RequestBody Model model){
        modelService.save(model);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "模型管理", action = "查询", desc = "分页查询")
    public Result<PageInfo<ModelPageResVo>> getPageInfo(@RequestBody RequestPage reqVo){
        List<ModelPageResVo> modelPage = modelService.getModelPage(reqVo);
        return Result.succeed(PageInfo.of(modelPage), ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getById")
    @LogAnnotation(module = "模型管理", action = "查询", desc = "查询明细")
    public Result<Model> getById(@RequestBody Model vo) {
        Model model = modelService.selectByPrimaryKey(vo.getId());
        return Result.succeed(model, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/list")
    @LogAnnotation(module = "模型管理", action = "查询", desc = "列表查询")
    public Result<List<Model>> getModelList(){
        List<Model> list = modelService.getModelList();
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/setCommonModel")
    @LogAnnotation(module = "模型管理", action = "更新", desc = "设置通用模型")
    public Result<PageInfo<Model>> setCommonModel(@RequestBody Model model){
        modelService.setCommonModel(model.getId(), SettingConstant.DEFAULT_COMMON_LLM);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "模型管理", action = "删除", desc = "删除模型")
    public Result<PageInfo<Model>> delete(@RequestBody Model model){
        modelService.delete(model.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
