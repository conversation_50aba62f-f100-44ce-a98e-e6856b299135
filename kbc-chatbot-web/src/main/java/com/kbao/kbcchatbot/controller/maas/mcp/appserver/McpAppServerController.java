package com.kbao.kbcchatbot.controller.maas.mcp.appserver;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.mcp.appserver.entity.McpAppServer;
import com.kbao.kbcchatbot.maas.mcp.appserver.service.McpAppServerService;
import com.kbao.tool.util.MapUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * MCP应用服务器管理控制器
 *
 * <AUTHOR>
 * @Since 2025-01-24
 */
@Api(tags = "MCP应用服务器管理")
@RestController
@RequestMapping("/api/mcp/appserver")
public class McpAppServerController extends BaseController {

    @Autowired
    private McpAppServerService mcpAppServerService;

    /**
     * 新增应用服务器
     */
    @ApiOperation("新增应用服务器")
    @PostMapping("/add")
    public Result<String> add(@RequestBody McpAppServer mcpAppServer) {
        try {
            mcpAppServerService.add(mcpAppServer);
            return Result.succeed("新增成功");
        } catch (Exception e) {
            return Result.failed("新增失败：" + e.getMessage());
        }
    }

    /**
     * 更新应用服务器
     */
    @ApiOperation("更新应用服务器")
    @PostMapping("/update")
    public Result<String> update(@RequestBody McpAppServer mcpAppServer) {
        try {
            mcpAppServerService.update(mcpAppServer);
            return Result.succeed("更新成功");
        } catch (Exception e) {
            return Result.failed("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除应用服务器
     */
    @ApiOperation("删除应用服务器")
    @PostMapping("/delete")
    public Result<String> delete(@RequestParam Long appServerId) {
        try {
            mcpAppServerService.delete(appServerId);
            return Result.succeed("删除成功");
        } catch (Exception e) {
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询应用服务器
     */
    @ApiOperation("根据ID查询应用服务器")
    @GetMapping("/get")
    public Result<McpAppServer> get(@RequestParam Long appServerId) {
        try {
            McpAppServer mcpAppServer = mcpAppServerService.selectByPrimaryKey(appServerId);
            return Result.succeed(mcpAppServer, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据编码查询应用服务器
     */
    @ApiOperation("根据编码查询应用服务器")
    @GetMapping("/getByCode")
    public Result<McpAppServer> getByCode(@RequestParam String appServerCode) {
        try {
            McpAppServer queryAppServer = new McpAppServer();
            queryAppServer.setAppServerCode(appServerCode);
            List<McpAppServer> list = mcpAppServerService.selectByParam(MapUtils.objectToMap(queryAppServer));
            if (list != null && !list.isEmpty()) {
                return Result.succeed(list.get(0), ResultStatusEnum.SUCCESS.getMsg());
            } else {
                return Result.failed("未找到对应的应用服务器");
            }
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询应用服务器列表
     */
    @ApiOperation("分页查询应用服务器列表")
    @PostMapping("/list")
    public Result<PageInfo<McpAppServer>> list(@RequestBody RequestObjectPage<McpAppServer> reqVO) {
        try {
            // 自动添加当前用户的租户ID过滤条件
            if (reqVO.getParam() == null) {
                reqVO.setParam(new McpAppServer());
            }
            if (reqVO.getParam().getTenantId() == null) {
                reqVO.getParam().setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            }
            PageInfo<McpAppServer> pageInfo = mcpAppServerService.page(reqVO);
            return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有应用服务器列表
     */
    @ApiOperation("查询所有应用服务器列表")
    @PostMapping("/listAll")
    public Result<List<McpAppServer>> listAll(@RequestBody McpAppServer mcpAppServer) {
        try {
            // 自动添加当前用户的租户ID过滤条件
            if (mcpAppServer.getTenantId() == null) {
                mcpAppServer.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            }
            List<McpAppServer> list = mcpAppServerService.selectByParam(MapUtils.objectToMap(mcpAppServer));
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用的应用服务器列表
     */
    @ApiOperation("获取可用的应用服务器列表")
    @GetMapping("/available")
    public Result<List<McpAppServer>> getAvailableAppServers() {
        try {
            McpAppServer queryAppServer = new McpAppServer();
            queryAppServer.setAppServerStatus(1); // 1表示启用状态
            queryAppServer.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            List<McpAppServer> list = mcpAppServerService.selectByParam(MapUtils.objectToMap(queryAppServer));
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }



}