package com.kbao.kbcchatbot.controller.maas.product;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.model.RequestPage;
import com.kbao.kbcchatbot.externalapi.model.MaasHttpService;
import com.kbao.kbcchatbot.maas.product.bean.ProductListResVo;
import com.kbao.kbcchatbot.maas.product.entity.ProductData;
import com.kbao.kbcchatbot.maas.product.entity.ProductFile;
import com.kbao.kbcchatbot.maas.product.service.ProductDataService;
import com.kbao.kbcchatbot.maas.product.service.ProductFileService;
import com.kbao.kbcchatbot.maas.train.scene.entity.TrainScene;
import com.kbao.tool.util.MapUtils;
import com.netflix.discovery.converters.Auto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/web/product")
public class ProductDataController {
    @Autowired
    private ProductDataService productDataService;
    @Autowired
    private MaasHttpService maasHttpService;
    @Autowired
    private ProductFileService productFileService;

    @PostMapping("/page")
    @LogAnnotation(module = "产品资料维护", action = "查询", desc = "查询产品列表")
    public Result<PageInfo<ProductListResVo>> page(@RequestBody RequestObjectPage<ProductData> requestPage){
        PageInfo<ProductListResVo> productList = productDataService.getProductList(requestPage);
        return Result.succeed(productList, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/sliceList")
    @LogAnnotation(module = "产品资料维护", action = "查询", desc = "查询切片列表")
    public Result<JSONObject> sliceList(@RequestBody RequestPage requestPage){
        requestPage.getParam().put("type", "product");
        JSONObject sliceList = maasHttpService.postMaasApi(MapUtils.objectToMap(requestPage), "/ex/product/sliceList");
        return Result.succeed(sliceList, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/page")
    @LogAnnotation(module = "产品资料维护", action = "查询", desc = "查询产品资料列表")
    public Result<PageInfo<ProductFile>> filePage(@RequestBody RequestPage requestPage){
        PageInfo<ProductFile> page = productFileService.page(requestPage);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/sliceList")
    @LogAnnotation(module = "产品资料维护", action = "查询", desc = "查询产品资料切片列表")
    public Result<JSONObject> fileSliceList(@RequestBody RequestPage requestPage){
        requestPage.getParam().put("type", "productFile");
        JSONObject sliceList = maasHttpService.postMaasApi(MapUtils.objectToMap(requestPage), "/ex/product/sliceList");
        return Result.succeed(sliceList, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/original")
    @LogAnnotation(module = "产品资料维护", action = "查询", desc = "查看原文")
    public Result<JSONObject> getProductFileOriginal(@RequestBody Map<String, Object> param){
        JSONObject result = maasHttpService.postMaasApi(param, "/ex/product/file/original");
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/del")
    @LogAnnotation(module = "产品资料维护", action = "查询", desc = "查询产品资料切片列表")
    public Result<JSONObject> delProductFile(@RequestBody Map<String, Object> param){
        maasHttpService.postMaasApi(param, "/ex/product/file/del");
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/file/batch/add")
    public Result<JSONObject> addProductFiles(@RequestParam("productId") String productId, @RequestParam("fileType") String fileType,
                                              @RequestParam("files") MultipartFile[] files){
        productFileService.addProductFiles(productId, fileType, files);
        return Result.succeed("上传成功，正在解析...");
    }

}
