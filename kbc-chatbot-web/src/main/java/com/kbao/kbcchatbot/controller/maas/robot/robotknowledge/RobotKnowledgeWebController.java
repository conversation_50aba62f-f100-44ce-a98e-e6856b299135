package com.kbao.kbcchatbot.controller.maas.robot.robotknowledge;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.bean.*;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.service.RobotKnowledgeDataService;
import com.kbao.kbcchatbot.maas.robot.robotknowledge.service.RobotKnowledgeHttpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * maas知识web控制层
 * @author: xiaojiayao
 * @time: 2024/12/13 10:01
 */
@RestController
@Slf4j
@RequestMapping("/web/maas/knowledge")
public class RobotKnowledgeWebController extends BaseController {

    @Autowired
    private RobotKnowledgeDataService robotKnowledgeDataService;
    @Autowired
    private RobotKnowledgeHttpService robotKnowledgeHttpService;

    @PostMapping("/data/page")
    @LogAnnotation(module = "maas知识库管理", action = "查询", desc = "知识库分页查询")
    public Result<PageInfo<RobotKnowledgeDataPageRespVo>> page(@RequestBody PageRequest<RobotKnowledgeDataPageReqVo> reqVo){
        PageInfo<RobotKnowledgeDataPageRespVo> page = robotKnowledgeDataService.getKnowledgeDataPage(reqVo);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/chapter/page")
    @LogAnnotation(module = "maas知识库管理", action = "查询", desc = "知识库分页查询")
    public Result<PageInfo<RobotKnowledgeDataPageRespVo>> getChapterDataList(@RequestBody PageRequest<RobotKnowledgeDataPageReqVo> reqVo){
        PageInfo<RobotKnowledgeDataPageRespVo> page = robotKnowledgeDataService.getChapterDataList(reqVo);
        return Result.succeed(page, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/slice/list")
    @LogAnnotation(module = "maas知识库管理", action = "查询", desc = "查询知识库切片列表")
    public Result<RobotKnowledgeSlicePageRespVo> getSliceList(@RequestBody PageRequest<RobotKnowledgeSlicePageReqVo> reqVo){
        RobotKnowledgeSlicePageRespVo sliceList = robotKnowledgeHttpService.getSliceList(reqVo);
        return Result.succeed(sliceList, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/data/status/update")
    @LogAnnotation(module = "maas知识库管理", action = "查询", desc = "更新知识数据状态")
    public Result getSliceList(@RequestBody RobotKnowledgeDataUpdateVo reqVo){
        robotKnowledgeHttpService.knowledgeDataStatusUpdate(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
    @PostMapping("/data/del")
    @LogAnnotation(module = "maas知识库管理", action = "查询", desc = "删除知识数据状态")
    public Result knowledgeDataDel(@RequestBody RobotKnowledgeDataUpdateVo reqVo){
        robotKnowledgeHttpService.knowledgeDataDel(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
    @PostMapping("/slice/add")
    @LogAnnotation(module = "maas知识库管理", action = "查询", desc = "添加知识库切片")
    public Result knowledgeSliceAdd(@RequestBody RobotKnowledgeSliceAddVo reqVo){
        robotKnowledgeHttpService.knowledgeSliceAdd(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
    @PostMapping("/slice/del")
    @LogAnnotation(module = "maas知识库管理", action = "查询", desc = "删除知识库切片")
    public Result knowledgeSliceDel(@RequestBody RobotKnowledgeSliceIdVo reqVo){
        robotKnowledgeHttpService.knowledgeSliceDel(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
    @PostMapping("/slice/content/update")
    @LogAnnotation(module = "maas知识库管理", action = "查询", desc = "更新知识库切片内容")
    public Result knowledgeSliceContentUpdate(@RequestBody RobotKnowledgeSliceUpdateVo reqVo){
        robotKnowledgeHttpService.knowledgeSliceContentUpdate(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
    @PostMapping("/slice/status/update")
    @LogAnnotation(module = "maas知识库管理", action = "查询", desc = "更新知识库切片状态")
    public Result knowledgeSliceStatusUpdate(@RequestBody RobotKnowledgeSliceStatusVo reqVo){
        robotKnowledgeHttpService.knowledgeSliceStatusUpdate(reqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
