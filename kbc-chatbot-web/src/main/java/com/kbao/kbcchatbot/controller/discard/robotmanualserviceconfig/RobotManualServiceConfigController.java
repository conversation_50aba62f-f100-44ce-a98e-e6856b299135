package com.kbao.kbcchatbot.controller.discard.robotmanualserviceconfig;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.service.RobotManualServiceConfigService;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.vo.RobotManualServiceConfigDetailVO;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.vo.RobotManualServiceConfigGetReqVO;
import com.kbao.kbcchatbot.discard.robotmanualserviceconfig.vo.RobotManualServiceConfigSaveReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: kbc-chatbot
 * @description: 转人工配置
 * @author: husw
 * @create: 2023-05-17 14:02
 **/
@RestController
@RequestMapping("/web/manualserviceconfig")
public class RobotManualServiceConfigController {

    @Autowired
    private RobotManualServiceConfigService robotManualServiceConfigService;

    @PostMapping("/save")
    @LogAnnotation(module = "机器人管理", action = "新增", desc = "保存转人工配置")
    public Result save(@RequestBody RobotManualServiceConfigSaveReqVO param){
        robotManualServiceConfigService.save(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/get")
    @LogAnnotation(module = "机器人管理", action = "查询", desc = "查询转人工配置")
    public Result<RobotManualServiceConfigDetailVO> get(@RequestBody RobotManualServiceConfigGetReqVO param){
        RobotManualServiceConfigDetailVO vo = robotManualServiceConfigService.get(param);
        return Result.succeed(vo,ResultStatusEnum.SUCCESS.getMsg());
    }

}
