package com.kbao.kbcchatbot.controller.maas.mcp.log;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcchatbot.maas.mcp.log.model.McpCallLogMo;
import com.kbao.kbcchatbot.maas.mcp.log.service.McpCallLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * MCP调用日志Web控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Api(tags = "MCP调用日志管理")
@RestController
@RequestMapping("/api/mcp/log")
public class McpCallLogController {

    @Autowired
    private McpCallLogService mcpCallLogService;

    /**
     * 根据ID查询调用日志
     */
    @ApiOperation("根据ID查询调用日志")
    @GetMapping("/get")
    public Result<McpCallLogMo> get(@RequestParam("logId") String logId) {
        try {
            McpCallLogMo mcpCallLog = mcpCallLogService.findById(logId);
            return Result.succeed(mcpCallLog, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询调用日志列表
     */
    @ApiOperation("分页查询调用日志列表")
    @PostMapping("/list")
    public Result<PageInfo<McpCallLogMo>> list(@RequestBody RequestObjectPage<McpCallLogMo> reqVO) {
        try {
            // 自动添加当前用户的租户ID过滤条件
            if (reqVO.getParam() == null) {
                reqVO.setParam(new McpCallLogMo());
            }
            if (reqVO.getParam().getTenantId() == null) {
                reqVO.getParam().setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            }
            PageInfo<McpCallLogMo> pageResult = mcpCallLogService.page(reqVO);
            return Result.succeed(pageResult, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有调用日志列表
     */
    @ApiOperation("查询所有调用日志列表")
    @PostMapping("/listAll")
    public Result<List<McpCallLogMo>> listAll(@RequestBody McpCallLogMo mcpCallLog) {
        try {
            // 自动添加当前用户的租户ID过滤条件
            if (mcpCallLog.getTenantId() == null) {
                mcpCallLog.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
            }
            List<McpCallLogMo> list = mcpCallLogService.selectAll(mcpCallLog);
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据服务器ID查询调用日志列表
     */
    @ApiOperation("根据服务器ID查询调用日志列表")
    @GetMapping("/listByServerId")
    public Result<List<McpCallLogMo>> listByServerId(@RequestParam("serverId") String serverId) {
        try {
            List<McpCallLogMo> list = mcpCallLogService.findByServerId(serverId);
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据工具ID查询调用日志列表
     */
    @ApiOperation("根据工具ID查询调用日志列表")
    @GetMapping("/listByToolId")
    public Result<List<McpCallLogMo>> listByToolId(@RequestParam("toolId") String toolId) {
        try {
            List<McpCallLogMo> list = mcpCallLogService.findByToolId(toolId);
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户ID查询调用日志列表
     */
    @ApiOperation("根据用户ID查询调用日志列表")
    @GetMapping("/listByUserId")
    public Result<List<McpCallLogMo>> listByUserId(@RequestParam("userId") String userId) {
        try {
            List<McpCallLogMo> list = mcpCallLogService.findByUserId(userId);
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据租户ID查询调用日志列表
     */
    @ApiOperation("根据租户ID查询调用日志列表")
    @GetMapping("/listByTenantId")
    public Result<List<McpCallLogMo>> listByTenantId(@RequestParam("tenantId") String tenantId) {
        try {
            List<McpCallLogMo> list = mcpCallLogService.findByTenantId(tenantId);
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据调用状态查询调用日志列表
     */
    @ApiOperation("根据调用状态查询调用日志列表")
    @GetMapping("/listByCallStatus")
    public Result<List<McpCallLogMo>> listByCallStatus(@RequestParam("callStatus") Integer callStatus) {
        try {
            List<McpCallLogMo> list = mcpCallLogService.findByCallStatus(callStatus);
            return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 删除调用日志
     */
    @ApiOperation("删除调用日志")
    @PostMapping("/delete")
    public Result<String> delete(@RequestParam("logId") String logId) {
        try {
            mcpCallLogService.remove(logId);
            return Result.succeed("删除成功", ResultStatusEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
}