package com.kbao.kbcchatbot.controller.maas.channel.channelbasicconfig;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.bean.ChannelBasicManualServiceVO;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.entity.ChannelBasicConfig;
import com.kbao.kbcchatbot.maas.channel.channelbasicconfig.service.ChannelBasicConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 渠道基础配置管理
 * @Date 2023-05-19
 */
@RestController
@RequestMapping("/web/channelbasicconfig")
public class ChannelBasicConfigController extends BaseController {

    @Autowired
    private ChannelBasicConfigService channelBasicConfigService;

    @PostMapping("/page")
    @LogAnnotation(module = "渠道基础配置管理", action = "查询", desc = "分页查询列表")
    public Result<PageInfo<ChannelBasicConfig>> page(@RequestBody RequestObjectPage<ChannelBasicConfig> page) {
        PageInfo<ChannelBasicConfig> channelBasicConfigPage = channelBasicConfigService.page(page);
        Result<PageInfo<ChannelBasicConfig>> result = Result.succeed(channelBasicConfigPage, ResultStatusEnum.SUCCESS.getMsg());
        return result;
    }

    @PostMapping("/add")
    @LogAnnotation(module = "渠道基础配置管理", action = "新增", desc = "新增渠道基础配置")
    public Result add(@Validated @RequestBody ChannelBasicConfig channelBasicConfig) {
		return Result.succeed(channelBasicConfigService.addChannelBasicConfig(channelBasicConfig),ResultStatusEnum.SUCCESS.getMsg());

    }

    @PostMapping("/update")
    @LogAnnotation(module = "渠道基础配置管理", action = "修改", desc = "修改渠道基础配置")
    public Result update(@Validated @RequestBody ChannelBasicConfig channelBasicConfig) {
        channelBasicConfigService.updateChannelBasicConfig(channelBasicConfig);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());


    }

    @PostMapping("/delete")
    @LogAnnotation(module = "渠道基础配置管理", action = "删除", desc = "删除渠道基础配置")
    public Result delete(@RequestBody ChannelBasicConfig channelBasicConfig) {
		return Result.succeed(channelBasicConfigService.delete(channelBasicConfig.getId()),ResultStatusEnum.SUCCESS.getMsg());
    }


//    @PostMapping("/getChannelBasicConfigByChannelId")
//    @LogAnnotation(module = "渠道基础配置管理", action = "查询", desc = "根据渠道id查询渠道基础配置")
//    public Result<ChannelVO> getChannelBasicConfigByChannelId(@RequestBody ChannelBasicConfig channelBasicConfig) {
//        return Result.succeed(channelBasicConfigService.getChannelBasicConfigByChannelId(channelBasicConfig.getChannelId()), ResultStatusEnum.SUCCESS.getMsg());
//    }


    @PostMapping("/manual/service/update")
    @LogAnnotation(module = "渠道基础配置管理", action = "修改", desc = "转人工配置保存")
    public Result manualServiceUpdate(@Validated @RequestBody ChannelBasicConfig channelBasicConfig) {
        channelBasicConfigService.manualServiceUpdate(channelBasicConfig);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/manual/service/find")
    @LogAnnotation(module = "渠道基础配置管理", action = "修改", desc = "转人工配置保存")
    public Result<ChannelBasicManualServiceVO> manualServiceFind(@RequestBody ChannelBasicConfig channelBasicConfig) {
        ChannelBasicManualServiceVO channelBasicManualServiceVO = channelBasicConfigService.manualServiceFind(channelBasicConfig.getChannelCode());
        return Result.succeed(channelBasicManualServiceVO, ResultStatusEnum.SUCCESS.getMsg());
    }
}