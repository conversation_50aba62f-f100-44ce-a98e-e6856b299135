package com.kbao.kbcchatbot.controller.maas.channel.channelrobot;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotAddReqVo;
import com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotListVo;
import com.kbao.kbcchatbot.maas.channel.channelrobot.bean.ChannelRobotPageReqVo;
import com.kbao.kbcchatbot.maas.channel.channelrobot.entity.ChannelRobot;
import com.kbao.kbcchatbot.maas.channel.channelrobot.service.ChannelRobotService;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.bean.ChannelRobotPromptQueryVo;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.bean.ChannelRobotPromptUpdateVo;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.entity.ChannelRobotPrompt;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.service.ChannelRobotPromptService;
import com.kbao.kbcchatbot.maas.robot.robot.entity.Robot;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * maas机器人web控制层
 * @author: xiaojiayao
 * @time: 2024/12/12 14:52
 */
@RestController
@Slf4j
@RequestMapping("/web/maas/channelrobot")
public class ChannelRobotWebController extends BaseController {

    @Autowired
    private ChannelRobotService channelRobotService;
    @Autowired
    private ChannelRobotPromptService channelRobotPromptService;

    @PostMapping("/add")
    @LogAnnotation(module = "maas渠道机器人配置", action = "新增", desc = "新增渠道机器人")
    public Result add(@RequestBody @Validated ChannelRobotAddReqVo channelRobotAddReqVo){
        channelRobotService.insert(channelRobotAddReqVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "maas渠道机器人配置", action = "查询", desc = "查询渠道机器人列表")
    public Result<PageInfo<ChannelRobotListVo>> getChannelRobotList(@RequestBody RequestObjectPage<ChannelRobotPageReqVo> pageReq){
        PageHelper.startPage(pageReq.getPageNum(), pageReq.getPageSize(), "t.create_time desc");
        List<ChannelRobotListVo> channelRobotList = channelRobotService.getChannelRobotPage(pageReq.getParam());
        return Result.succeed(new PageInfo<>(channelRobotList),ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/updateStatus")
    @LogAnnotation(module = "maas渠道机器人配置", action = "更新", desc = "更新渠道机器人状态")
    public Result<List<Robot>> updateStatus(@RequestBody ChannelRobot channelRobot){
        channelRobotService.update(channelRobot);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "maas渠道机器人配置", action = "删除", desc = "删除渠道机器人")
    public Result delete(@RequestBody ChannelRobot vo){
        channelRobotService.delChannelRobot(vo.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/prompt/create")
    @LogAnnotation(module = "maas渠道机器人配置", action = "新增", desc = "新增渠道机器人提示词")
    public Result<List<Robot>> promptCreate(@RequestBody ChannelRobotPromptUpdateVo channelRobotPromptUpdateVo){
        channelRobotPromptService.save(channelRobotPromptUpdateVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/prompt/update")
    @LogAnnotation(module = "maas渠道机器人配置", action = "更新", desc = "更新渠道机器人提示词")
    public Result<List<Robot>> promptUpdate(@RequestBody ChannelRobotPromptUpdateVo channelRobotPromptUpdateVo){
        channelRobotPromptService.save(channelRobotPromptUpdateVo);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/prompt/page")
    @LogAnnotation(module = "maas渠道机器人配置", action = "查询", desc = "查询渠道机器人提示词列表")
    public Result<PageInfo<ChannelRobotPrompt>> promptPage(@RequestBody RequestObjectPage<ChannelRobotPromptQueryVo> pageReq){
        PageHelper.startPage(pageReq.getPageNum(), pageReq.getPageSize(), "t.prompt_type asc");
        List<ChannelRobotPrompt> promptList = channelRobotPromptService.getChannelRobotPromptList(pageReq.getParam());
        return Result.succeed(new PageInfo<>(promptList),ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/prompt/delete")
    @LogAnnotation(module = "maas渠道机器人配置", action = "删除", desc = "删除渠道机器人提示词")
    public Result deletePrompt(@RequestBody ChannelRobotPromptQueryVo vo){
        channelRobotPromptService.delete(vo.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
