//package com.kbao.kbcchatbot.controller.robot;
//
//import com.github.pagehelper.PageInfo;
//import com.kbao.commons.enums.ResultStatusEnum;
//import com.kbao.commons.exception.BusinessException;
//import com.kbao.commons.web.BaseController;
//import com.kbao.commons.web.Result;
//import com.kbao.kbcbsc.log.annotation.LogAnnotation;
//import com.kbao.kbcbsc.model.RequestObjectPage;
//import com.kbao.kbcbsc.util.BscApiContext;
//import com.kbao.kbcchatbot.robot.bean.TrainRasaModelFailureReqVO;
//import com.kbao.kbcchatbot.robot.enums.RobotModelTypeEnum;
//import com.kbao.kbcchatbot.robot.enums.RobotReleaseTypeEnum;
//import com.kbao.kbcchatbot.robot.enums.RobotStatusEnum;
//import com.kbao.kbcchatbot.robot.entity.Robot;
//import com.kbao.kbcchatbot.robot.vo.RobotAddReqVO;
//import com.kbao.kbcchatbot.robot.vo.RobotPageReqVO;
//import com.kbao.kbcchatbot.robot.vo.RobotUpdateStatusVO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
///**
// * @program: kbc-chatbot
// * @description: 机器人controller
// * @author: husw
// * @create: 2023-05-16 11:42
// **/
//@RestController
//@Slf4j
//@RequestMapping("/web/robot")
//public class RobotWebController extends BaseController {
//
///*    @Autowired
//    private RobotService robotService;*/
//
///*
//    @PostMapping("/add")
//    @LogAnnotation(module = "机器人管理", action = "新增", desc = "新增机器人")
//    public Result add(@RequestBody @Validated RobotAddReqVO param){
//        robotService.add(param);
//        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PostMapping("/update")
//    @LogAnnotation(module = "机器人管理", action = "更新", desc = "更新机器人")
//    public Result update(@RequestBody @Validated RobotAddReqVO param){
//        robotService.update(param);
//        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PostMapping("/page")
//    @LogAnnotation(module = "机器人管理", action = "查询", desc = "机器人分页查询")
//    public Result<PageInfo<Robot>> page(@RequestBody @Validated RequestObjectPage<RobotPageReqVO> pageRequest){
//        pageRequest.getParam().setTenantId(BscApiContext.TenantId.get());
//        pageRequest.setSort("update_time desc,publish_time desc");
//        PageInfo<Robot> page = robotService.page(pageRequest);
//        return Result.succeed(page,ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PostMapping("/release")
//    @LogAnnotation(module = "机器人管理", action = "更新", desc = "发布")
//    public Result release(@RequestBody @Validated RobotUpdateStatusVO param){
//        throw new BusinessException("接口因暂停测试环境而停用");
////        param.setStatus(RobotStatusEnum.PUBLISHING.getCode());
////        robotService.release(param);
////        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PostMapping("/up")
//    @LogAnnotation(module = "机器人管理", action = "更新", desc = "上线")
//    public Result up(@RequestBody @Validated RobotUpdateStatusVO param){
//        param.setStatus(RobotStatusEnum.PUBLISHED.getCode());
//        robotService.up(param);
//        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
//    }
//
////    @PostMapping("/down")
////    @LogAnnotation(module = "机器人管理", action = "更新", desc = "下线")
////    public Result down(@RequestBody @Validated RobotUpdateStatusVO param){
////        robotService.down(param);
////        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
////    }
//
//    @PostMapping(value = "/trainRasaModelCallback")
//    @LogAnnotation(module = "机器人管理", action = "操作", desc = "训练RASA模型成功回调")
//    public Result trainRasaModelCallback(@RequestParam MultipartFile file, @RequestParam(name = "robotId") Long robotId, @RequestParam(name = "releaseRecordId") String releaseRecordId) {
//        robotService.trainModelCallback(file, robotId, releaseRecordId);
//        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
//    }
//
//    @PutMapping(value = "/trainRasaModelCallback")
//    @LogAnnotation(module = "机器人管理", action = "操作", desc = "训练RASA模型失败回调")
//    public Result trainRasaModelCallback(@RequestBody TrainRasaModelFailureReqVO reqVO, @RequestParam(name = "robotId") Long robotId, @RequestParam(name = "releaseRecordId") String releaseRecordId) {
//        robotService.trainModelCallbackFailure(reqVO, robotId, releaseRecordId);
//        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
//    }*/
//
////    @PostMapping(value = "/replaceRasaModel")
////    @LogAnnotation(module = "机器人管理", action = "操作", desc = "替换RASA模型")
////    public Result replaceRasaModel(@RequestBody @Validated ReplaceRasaModelReqVO reqVO, BindingResult bindingResult) {
////        checkValidator(bindingResult);
////        //robotService.replaceRasaModel(reqVO);
////        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
////    }
//
//}
