package com.kbao.kbcchatbot.controller.discard.robotbasicconfig;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.discard.robotbasicconfig.service.RobotBasicConfigService;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.RobotBasicConfigDetailVO;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.RobotBasicConfigGetVO;
import com.kbao.kbcchatbot.discard.robotbasicconfig.vo.RobotBasicConfigSaveVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @program: kbc-chatbot
 * @description: 机器人基础配置controller
 * @author: husw
 * @create: 2023-05-16 15:22
 **/
@RestController
@RequestMapping("/web/robotbasicconfig")
public class RobotBasicConfigWebController extends BaseController {

    @Autowired
    private RobotBasicConfigService robotBasicConfigService;

    @PostMapping("/save")
    @LogAnnotation(module = "机器人管理", action = "新增", desc = "保存机器人基础配置")
    public Result save(@RequestBody @Validated RobotBasicConfigSaveVO param){
        robotBasicConfigService.save(param);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/get")
    @LogAnnotation(module = "机器人管理", action = "新增", desc = "查询机器人基础配置")
    public Result<RobotBasicConfigDetailVO> get(@RequestBody @Validated RobotBasicConfigGetVO param){
        RobotBasicConfigDetailVO detail = robotBasicConfigService.get(param);
        return Result.succeed(detail,ResultStatusEnum.SUCCESS.getMsg());
    }
}
