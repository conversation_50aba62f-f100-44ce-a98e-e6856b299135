package com.kbao.kbcchatbot.controller.maas.channel.channel;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelPageReqVO;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelPageRespVO;
import com.kbao.kbcchatbot.maas.channel.channel.bean.ChannelUpdateVO;
import com.kbao.kbcchatbot.maas.channel.channel.entity.Channel;
import com.kbao.kbcchatbot.maas.channel.channel.service.ChannelService;
import com.kbao.kbcchatbot.maas.channel.channelrobotprompt.bean.ChannelRobotPromptQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * maas机器人web控制层
 * @author: xiaojiayao
 * @time: 2024/12/12 14:52
 */
@RestController
@Slf4j
@RequestMapping("/web/maas/channel")
public class ChannelWebController extends BaseController {

    @Autowired
    private ChannelService channelService;

    @PostMapping("/create")
    @LogAnnotation(module = "maas渠道管理", action = "新增", desc = "新增渠道")
    public Result create(@RequestBody @Validated ChannelUpdateVO channelUpdateVO){
        channelService.insert(channelUpdateVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/update")
    @LogAnnotation(module = "maas渠道管理", action = "更新", desc = "更新渠道")
    public Result update(@RequestBody @Validated ChannelUpdateVO channelUpdateVO){
        channelService.update(channelUpdateVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/updateChannel")
    @LogAnnotation(module = "maas渠道管理", action = "更新", desc = "更新渠道")
    public Result updateChannel(@RequestBody @Validated Channel channel){
        channelService.updateChannel(channel);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/page")
    @LogAnnotation(module = "maas渠道管理", action = "查询", desc = "渠道分页查询")
    public Result<PageInfo<ChannelPageRespVO>> page(@RequestBody PageRequest<ChannelPageReqVO> reqVo){
        PageInfo<ChannelPageRespVO> page = channelService.getChannelPage(reqVo);
        return Result.succeed(page,ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/find")
    @LogAnnotation(module = "maas渠道管理", action = "查询", desc = "渠道配置查询")
    public Result<ChannelUpdateVO> find(@RequestBody ChannelPageReqVO reqVo){
        ChannelUpdateVO channelUpdateVO = channelService.getChannel(reqVo.getChannelCode());
        return Result.succeed(channelUpdateVO,ResultStatusEnum.SUCCESS.getMsg());
    }


    @PostMapping("/status/update")
    @LogAnnotation(module = "maas渠道管理", action = "更新", desc = "更新渠道状态")
    public Result statusUpdate(@RequestBody @Validated ChannelUpdateVO channelUpdateVO){
        channelService.updateStatus(channelUpdateVO);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/delete")
    @LogAnnotation(module = "maas渠道管理", action = "删除", desc = "删除渠道")
    public Result delete(@RequestBody ChannelUpdateVO vo){
        channelService.deleteChannel(vo.getChannelId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
