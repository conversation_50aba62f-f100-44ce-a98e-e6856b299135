package com.kbao.kbcchatbot.config;

import lombok.Data;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.RestClients;
import org.springframework.data.elasticsearch.config.AbstractElasticsearchConfiguration;

import java.time.Duration;

@Data
@Configuration
@PropertySource(value = "classpath:/application.yml")
@ConfigurationProperties(prefix = "spring.elasticsearch.rest")
public class EsRestClientConfig extends AbstractElasticsearchConfiguration {

    private String uris;

    private String username;

    private String password;

    @Override
    public RestHighLevelClient elasticsearchClient() {
        String[] urisSplit = uris.split(",");
        ClientConfiguration configuration = ClientConfiguration.builder().connectedTo(urisSplit)
                .withConnectTimeout(Duration.ofSeconds(120))
                .withSocketTimeout(Duration.ofSeconds(60))
                .withBasicAuth(username, password)
                .build();
        return RestClients.create(configuration).rest();
    }

}
