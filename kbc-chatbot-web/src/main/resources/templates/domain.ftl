session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: false
intents:
- begin_personal_invoice
- inform_order_code
- affirm
- deny
- thanks
- greet
- query_insurance_base
- inform_invoice_type
- faq_qa
- faq_cloud
- begin_order_claim_progress
- begin_order_sms_code
- begin_order_commission
- select_apply_type
- begin_renewal_deduction
- begin_order_apply_claim
- begin_return_visit_status
- begin_how_return_visit
- begin_which_return_visit
- query_why_return_visit
- select_order_code
- begin_open_white_list
- select_question_type
- send_order_info
- begin_unbind_trusteeship
- send_claim_work_info
- send_image
- info_customer_name
- info_card_id
- info_phone
- yes
- no
entities:
- order_code
- card_id
- invoice_type
- question_type
- apply_type
- cus_id
- customer_name
- phone
slots:
  scene_id:
    type: text
    influence_conversation: false
    mappings:
    - type: custom
  token:
    type: text
    influence_conversation: false
    mappings:
    - type: custom
  tenant_id:
    type: text
    influence_conversation: false
    mappings:
    - type: custom
  user_id:
    type: text
    influence_conversation: false
    mappings:
    - type: custom
  is_evaluate:
    type: bool
    influence_conversation: false
    mappings:
    - type: custom
  source_type:
    type: text
    influence_conversation: false
    mappings:
    - type: custom
  order_code:
    type: text
    influence_conversation: false
    mappings:
    - type: custom
  product_name:
    type: text
    influence_conversation: false
    mappings:
    - type: custom
  agent_card_id:
    type: text
    influence_conversation: false
    mappings:
      - type: from_entity
        entity: card_id
        intent: info_card_id
        conditions:
        - active_loop: unbind_service_form
          requested_slot: agent_card_id
      - type: from_entity
        entity: phone
        intent: info_phone
        conditions:
        - active_loop: unbind_service_form
          requested_slot: agent_card_id
  cus_id:
    type: text
    influence_conversation: false
    mappings:
      - type: from_entity
        entity: card_id
        conditions:
        - active_loop: unbind_service_form
          requested_slot: cus_id
  customer_name:
    type: text
    influence_conversation: false
    mappings:
      - type: from_entity
        entity: customer_name
        conditions:
        - active_loop: unbind_service_form
          requested_slot: customer_name
  invoice_type:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: invoice_type
  requested_slot:
    type: any
    influence_conversation: false
    mappings:
    - type: custom
  apply_type:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: apply_type
  card_id_bool:
    type: bool
    influence_conversation: true
    mappings:
    - type: custom
  check_relation_bool:
    type: bool
    influence_conversation: true
    mappings:
    - type: custom
  is_over_time:
    type: bool
    influence_conversation: true
    mappings:
    - type: custom
  is_product_dhj:
    type: bool
    influence_conversation: true
    mappings:
    - type: custom
  identity:
    type: text
    influence_conversation: true
    mappings:
    - type: custom
  question_type:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: question_type
      intent: select_question_type
  slots_map:
    type: text
    influence_conversation: false
    mappings:
    - type: custom


responses:
  utter_confirm_then_no:
  - text: 好的！
  utter_confirm_then_yes:
  - text: 信息已确认。
  utter_api_succeed_true:
  - text: 购票成功。
  utter_api_succeed_false:
  - text: 购票失败，请稍后重试！
  - text: 系统出错了，请稍后重试！·
  utter_default:
  - text: 对不起，我没有理解您的话。
  utter_ask_continue:
  - text: 请问您是要停止当前流程吗？
  utter_ask_rephrase:
  - text: 很抱歉，没有能明白您的话，辛苦您重新描述一下问题哦。
  utter_personal_invoice_order_ineffective:
  - text: 开票失败，失败原因：订单未生效。
  utter_ask_personal_invoice_form_order_code:
  - text: 麻烦您提供一下订单号。
  utter_ask_personal_invoice_form_invoice_type:
  - buttons:
    - payload: /inform_invoice_type{{"invoice_type":"personal"}}
      title: 个人电子普票
    - payload: /inform_invoice_type{{"invoice_type":"public"}}
      title: 对公电子普票
    text: 请选择您要开的发票类型：
  utter_apply_invoice:
  - buttons:
    - payload: skip_target_1,{invoiceAddress}
      title: 发票申请
    text: 请点击申请发票
  utter_order_not_effective_answer:
  - text: 您好，此订单的状态为未生效，需要订单生效后才可以申请发票。
  utter_order_code_not_exist:
  - text: 没有查询到订单，请检查订单号后重新输入。
  utter_ask_apply_type:
  - buttons:
    - payload: /select_apply_type{{"apply_type":"self"}}
      title: 自助理赔
    - payload: /select_apply_type{{"apply_type":"agent"}}
      title: 好赔代办
    - payload: /select_apply_type{{"apply_type":"company"}}
      title: 保司线上报案/线下邮寄
    text: 请问您之前是通过什么方式申请报案的呢？
  utter_provider_dhj_claim_info:
  - buttons:
    - payload: claim_service_order
      title: 去填写
    text: '{"title":"您好，为了更高效的解决您的问题，系统将为您创建工单处理，预计2-3个工作日内进行反馈，请提供：","content":["事故出险日期","理赔递交资料日期","理赔递交资料方式","本次理赔联系人电话","本次理赔人邮箱"],"submissionType":["保险公司线上渠道","快递","自助理赔"]}'
  utter_provider_other_claim_info:
  - buttons:
    - payload: claim_service_order
      title: 去填写
    text: '{"title":"您好，为了更高效的解决您的问题，系统将为您创建工单处理，预计2-3个工作日内进行反馈，请提供：","content":["事故出险日期","理赔递交资料日期","理赔递交资料方式","本次理赔联系人电话","本次理赔人邮箱"],"submissionType":["保司线上报案","线下邮寄"]}'
  utter_not_over_time:
  - text: 您好，理赔正常处理时效为理赔资料递交后15个工作日内，您耐心等待保司结论即可。
  utter_renewal_success_answer:
  - text: 您好，订单已经在{date}扣款成功。
  utter_renewal_fail_answer:
  - text: 您好，订单已经在{date}扣款失败，失败原因为：客户银行卡余额不足、请及时进行充值。
  utter_renewal_exist_answer:
  - text: 您好，客户今年投保的产品为：{productName}，订单已经续保成功
  utter_renewal_not_exist_answer:
  - text: 您好，通过客户的身份证号码没有查询到今年通过大童投保的订单。
  utter_rb_answer:
  - text: 该产品需要通过快保自助理赔或好赔代办申请理赔，申请路径：快保APP--好赔--理赔申请--自助理赔或好赔代办。
  utter_support_answer:
  - text: 您好，有三种方式可以申请理赔，①可申请好赔代办全程一对一协助，申请好赔代办方式：快保APP--好赔--理赔申请--好赔代办②通过快保自助理赔申请：申请自助理赔方式：快保APP--好赔--理赔申请--自助理赔③联系保险公司客服热线{tel}，从保司渠道进行理赔申请。
  utter_nonsupport_answer:
  - text: 您好，本产品不支持自助理赔服务，若您要申请理赔，可申请好赔代办全程一对一协助（申请方式：快保APP--好赔--理赔申请--好赔代办），或联系保险公司客服热线{tel}，从保司渠道进行理赔申请。
  utter_visited_answer:
  - text: 您好，该订单已回访成功
  utter_life_not_visit_answer:
  - text: 您好，此订单未回访，烦请通过我司机构运营或者拨打保司客服热线{tel}联系保司进行回访。
  utter_short_answer:
  - text: 您好，短险订单不需要进行回访。
  utter_nmi_pingan_answer:
  - text: 关注平安健康保险生活公众号--点击我的--我的保单--对应保单--保单服务--电子回访
  utter_nmi_tpy_answer:
  - text: 投保人关注保司公众号完成在线回访
  utter_nmi_htrs_answer:
  - text: 关注保司和泰官微回访路径：在公众号直接输入“预约回访”，会弹出一个指引链接，点开输入投保人姓名和保单号即可预约
  utter_how_visit_answer:
  - text: 您好，您可以通过{channel}方式进行回访，具体回访操作路径为：{address}
  utter_nmi_visit_answer:
  - text: 您好，这款产品属于期缴产品，期缴产品均需要完成回访。
  utter_nmi_why_visit_answer:
  - text: 您好，根据监管要求，保单承保后保险公司会对投保人进行回访。其次目前系统发放宣传费或佣金条件是订单过犹豫期且回访完成。故为保障您和客户的权益，请提醒客户配合完成保险公司的回访。具体佣金规则以产品实际规则为准。
  utter_short_visit_answer:
  - text: 您好，该产品需要完成回访。是否回访成功不影响佣金发放。建议您提醒客户及时完成回访。
  utter_short_not_visit_answer:
  - text: 您好，该产品不需要回访。
  utter_white_result_answer:
  - text: 已收到您的开白信息，以上资料默认为被保险人开白所需资料，若投保人被风控，我们会告知您补充资料，请您注意及时查看邮件回复并接听快保热线提醒；保司会在工作日12点和18点两个时间段集中受理，预计1~2个工作日内处理完毕，请知晓。
  utter_send_email_fail_answer:
  - text: 发送邮件失败，您可以联系人工客服处理。
  utter_open_white_type:
  - buttons:
    - payload: /select_question_type{{"question_type":"occupation"}}
      title: 职业类别问题
    - payload: /select_question_type{{"question_type":"actual_name"}}
      title: 实名认证问题
    text: 请根据下面的提示选择报错类型：
  utter_open_white_type_tip:
  - text: "报错提示“根据您填写的信息暂时不能投保本产品，如有疑问请联系在线客服（21006）”——为职业类别问题。<br/>报错提示“您输入的手机号、姓名、身份证无法通过实名信息验证，请更换为在运营商实名登记的手机号后重新提交。如有疑问请联系在线客服。”——为实名认证问题。"
  utter_provider_occupation_type:
  - text: "请提供职业证明资料照片。示例："
    image: "https://www-sta.kbao123.com/gateway/oss2/native_upload/T0001/B00100/1661202178780893185/static/2023/11/14/image/5a9f3e3b8738417b85e8ce9da05c5605.png?Expires=**********&OSSAccessKeyId=LTAI5tGAnFWo1w2Trz8ojEcJ&Signature=nlugpeucR%2Bd4akkv0VVeezeVzyY%3D"
  utter_provider_actual_name:
  - text: "请提供手机号实名认证截图。示例："
    image: "https://www-sta.kbao123.com/gateway/oss2/native_upload/T0001/B00100/1661202178780893185/static/2023/11/14/image/b6c3d866f036490ca0c61ba4263b241a.png?Expires=**********&OSSAccessKeyId=LTAI5tGAnFWo1w2Trz8ojEcJ&Signature=8oXGWN6xcKb5qvTWGXJyYqtPOPs%3D"
  utter_provide_query_order_answer:
  - buttons:
    - payload: show_order_list
      title: 选择订单
    text: 请发送您要查询的订单？
  utter_provide_white_order_answer:
  - buttons:
    - payload: show_order_list
      title: 选择订单
    text: 请发送您要开白的订单。
  utter_provide_invoice_order_answer:
  - buttons:
    - payload: show_order_list
      title: 选择订单
    text: 请发送您要开发票的订单。
  utter_ask_unbind_service_form_agent_card_id:
  - text: 请提供一下您的身份证号码或者手机号。
  utter_ask_unbind_service_form_customer_name:
  - text: 请提供您要解绑客户的姓名。
  utter_ask_unbind_service_form_cus_id:
  - text: 请提供您要解绑客户的身份证号码。
  utter_provide_card_answer:
  - text: 请提供一下您的身份证号码或者手机号。
  utter_provide_card_tip:
  - text: 未识别到身份证号和姓名，请重新输入。示例：姓名是张三，身份证号是******************
  utter_over_time:
  - text: 您好，已为您创建工单，会在2-3个工作日内进行反馈，您可在快保里查看处理进度。
  utter_self_claim:
  - text: 您好，通过自助理赔申请的保单，可通过快保进行查询，查询路径：快保APP首页--好赔--进度查询--处理中。对于同一状态停留超过5个工作日的案件，可在快保中进行“催一催”，催促路径：快保APP首页--好赔--进度查询--处理中--“点击所需催促的保单”--点击”催一催”。
  utter_agent_claim:
  - text: 您好，申请了好赔代办的保单由好赔专员全权协助您跟进该案件的理赔，专业的事由专业的人去做，建议联系下好赔专员协助。
  utter_actual_name_deny:
  - text: 那您这边是遇到了什么问题呢？
  utter_provider_card_id:
  - text: 麻烦您提供一下认证号童管家的身份证号码，这边为您查询一下。
  utter_no_auth_answer:
  - text: 您是否可以正常登录童管家APP呢？如果您可以正常登录童管家APP，系统可告知您自助注销童管家账号的路径，您可按照路径自行操作。
  utter_is_auth_answer:
  - text: 系统查询到您没有用此身份证号认证童管家，您是否要更换提供的身份证号呢。
  utter_has_relieve_tip:
  - text: 具体操作步骤如下：第一步，服务顾问可以通过快保APP-保单托管-客户详情，解除托管关系；第二步，客户通过童管家APP-我的-设置-账号与安全-注销账号；第三步，如果顾问需要重新托管客户，可以重新发送托管邀请，客户按照正确信息认证童管家即可；您是否要解除服务/托管关系？
  utter_no_relieve_tip:
  - text: 您可以通过童管家APP-我的-设置-账号与安全-注销账号，注销后重新按照正确信息认证童管家。
  utter_verify_answer:
  - text: 您是否要解除服务/托管关系。
  utter_provider_card_photo:
  - text: 您好，麻烦您提供一下客户的手持身份证照片。
  utter_create_service_order:
  - text: 您好，已为您创建工单，会在2-3个工作日内进行反馈，您可在快保里查看处理进度。
  utter_create_service_verify:
  - buttons:
    - payload: /yes
      title: 是
    - payload: /no
      title: 否
    text: 您好，您当前咨询的订单{orderId}{question}问题可以创建工单为您反馈，是否要为您创建工单。
  utter_life_sms_answer:
  - text: 如果您获取短信验证码已超过60秒，麻烦您进行语音验证。如果您仍收不到验证码，请转人工。
  utter_life_phone_answer:
  - text: 您好，请您用投保手机号或出单顾问手机号进行咨询
  utter_unbind_card_id:
  - text: 您要和哪个客户解除服务/托管关系呢？请提供您要解绑客户的身份证姓名和号码。
  utter_info_not_equal_answer:
  - text: 核实到您的信息和系统存储的信息不一致，麻烦您再核实一下。如果原手机号不再使用请通过人工客服咨询。
  utter_comfirm_unbind_relation:
  - buttons:
    - payload: /yes
      title: 是
    - payload: /no
      title: 否
    text: 您是否要和姓名：{customerName}，身份证号：{cusCardId}客户解除托管/服务关系？
  utter_unbind_fail_answer:
  - text: 解除托管/服务关系失败，您可以联系人工客服处理。
  utter_not_relation:
  - text: 未查询到您与此客户存在托管/服务关系，请核实下客户信息。
  utter_unbind_relation_answer:
  - text: 已为您解除与姓名：{customerName}，身份证号：{cusCardId}客户的托管/服务关系。
  utter_commission_no_data_answer:
  - buttons:
    - payload: skip_target_1,{commissionAddress}
      title: 订单佣金详情
    text: 您好，暂未查询到此单佣金流水，您可以点击下方链接查询订单佣金情况，如仍有问题请转人工客服处理。
  utter_commission_party_no_issue_answer:
  - buttons:
    - payload: skip_target_1,{commissionAddress}
      title: 订单佣金详情
    text: 您好，您可以点击下方链接查询订单佣金流水情况，如仍有问题请转人工客服处理。
  utter_commission_party_issue_answer:
  - buttons:
    - payload: skip_target_1,{commissionAddress}
      title: 订单佣金详情
    text: 您好，您可以点击下方链接查询订单佣金情况，如仍有问题请转人工客服处理。
  utter_commission_all_issue_answer:
  - text: 您好，经后台核实此单佣金已经发至您尾号{bankNo}的银行卡上了。
  utter_commission_month_end_answer:
  - text: 您好，您的佣金预计在本月底进行发放。
  utter_commission_next_month_end_answer:
  - text: 您的佣金仍在审核中，预计在次月月底进行发放，请耐心等待下。
  utter_commission_not_review_answer:
  - text: 您好，您的佣金目前暂未完成后台审核，请耐心等待。
  utter_commission_prohibit_answer:
  - text: 您好，2020年12月14日银保监会已经正式颁布《互联网保险业务监管办法》，保险机构在互联网保险销售或经纪活动中，不得向未在本机构进行执业登记的人员支付或变相支付佣金及劳动报酬此保单，由于您是司外用户出单，根据规则，不予发放佣金。
  utter_commission_not_allow_answer:
  - text: 您好，此订单由于您未签署互联网协议所以还未发放，麻烦您联系机构签署互联网协议。
  utter_commission_not_effect_answer:
  - text: 您好，此保单由于未生效因此没有发放，麻烦您辛苦耐心等待保单生效。
  utter_commission_not_effect_allow_answer:
  - text: 您好，此订单由于您未签署互联网协议所以还未发放，麻烦您联系机构签署互联网协议，辛苦耐心等待保单生效。
  utter_commission_not_hesitation_answer:
  - text: 您好，因为订单未过犹豫期所以还未发放，麻烦您耐心等待。
  utter_commission_not_visit_answer:
  - text: 您好此订单因为未回访或者回访失败所以还未发放，麻烦您联系一下保司重新预约回访。
  utter_commission_all_not_answer:
  - text: 您好，此订单由于您未签署互联网协议、保单未过犹豫期、未回访或者回访失败的原因所以还未发放，麻烦您联系机构签署互联网协议、联系一下保司重新预约回访、并耐心等待保单过犹豫期。
  utter_commission_not_hesitation_visit_answer:
  - text: 您好，此订单由于保单未过犹豫期、未回访或者回访失败的原因所以还未发放，麻烦您联系一下保司重新预约回访、并耐心等待保单过犹豫期。
  utter_commission_not_allow_visit_answer:
  - text: 您好，此订单由于您未签署互联网协议、未回访或者回访失败的原因所以还未发放，麻烦您联系机构签署互联网协议、联系一下保司重新预约回访。
  utter_commission_not_allow_hesitation_answer:
  - text: 您好，此订单由于您未签署互联网协议、保单未过犹豫期的原因所以还未发放，麻烦您联系机构签署互联网协议、并耐心等待保单过犹豫期。
  utter_end_tip:
  - text: 如果您有任何其他问题，随时向我提问。我很乐意为您提供帮助！
  utter_order_inquire_tip:
  - text: 请问您这边需要咨询什么呢？
  utter_transfer_labor:
  - buttons:
    - payload: transfer_labor
      title: 转人工
    text: 请点击按钮转接人工服务咨询。
  utter_not_life_transfer_labor:
  - buttons:
    - payload: transfer_labor
      title: 转人工
    text: 很抱歉，非寿险订单建议您直接咨询人工客服。
  utter_visit_transfer_labor:
  - buttons:
    - payload: transfer_labor
      title: 转人工
    text: 未查询到回访方式，您可以联系人工客服咨询。
  utter_not_claim_labor:
  - buttons:
    - payload: transfer_labor
      title: 转人工
    text: 未查询到理赔中保单信息，如有疑问，您可以联系人工客服咨询。
  utter_not_user_labor:
  - buttons:
    - payload: transfer_labor
      title: 转人工
    text: 未查询到您的信息，您可以联系人工客服咨询。
  utter_custom_labor:
  - buttons:
    - payload: transfer_labor
      title: 转人工
    text: 您好，{message}
  utter_thanks_answer:
  - text: 不客气！
  utter_yes_answer:
  - text: 我一直都在，有疑问可以随时咨询我哦~
  - text: 请问有什么业务问题可以帮您吗，辛苦详细描述一下哦。
  - text: 有疑问可以随时咨询我哦。
  utter_greet_answer:
  - text: 您好，请问有什么可以帮您！
  utter_file_answer:
  - text: 建议您将您的问题通过文字描述后进行发送，便于我们理解~
  utter_repeat_answer:
  - text: 请问还有什么可以帮助您的呢？
  utter_no_answer:
  - text: no_answer


  #faq responses
<#list faqList as faq>
  utter_faq_<#if faq.knowledgeType == "1">cloud<#else>qa</#if>/${faq.knowledgeId}:
  - text: get from java service
<#--  <#if faq.chapters?has_content>-->
<#--  - text: "<#list faq.chapters as chapter><#if chapter_index gt 0>\n</#if><#if chapter.chapterContent?has_content>${chapter.chapterContent?replace("<.*?>","","r")?replace("\r","\\r","r")?replace("\n","\\n","r")}<#else>FAQ知识没有配置答案，请联系管理员处理</#if></#list>"-->
<#--  <#else>-->
<#--  - text: "FAQ知识没有配置答案，请联系管理员处理"-->
<#--  </#if>-->
</#list>
actions:
  - action_invoice_form_submit
  - action_personal_invoice_order
  - action_query_insurance
  - validate_unbind_service_form
  - action_slot_reset
  - utter_faq_qa
  - utter_faq_cloud
  - action_default_fallback
  - action_query_order_list
  - action_query_sms
  - action_query_commission
  - action_relieve_relation
  - utter_is_auth_answer
  - utter_no_auth_answer
  - utter_has_relieve_tip
  - utter_no_relieve_tip
  - utter_order_inquire_tip
  - utter_greet_answer
  - utter_ask_unbind_service_form_agent_card_id
  - utter_ask_unbind_service_form_customer_name
  - utter_ask_unbind_service_form_cus_id
  - action_query_claim_progress
  - action_query_claim
  - action_query_claim_answer
  - action_apply_type_answer
  - action_query_renewal_deduction
  - action_query_return_visit_status
  - action_query_how_return_visit
  - action_query_which_return_visit
  - action_query_why_return_visit
  - action_check_card
  - action_question_type
  - action_open_white_type
  - action_send_example_image
  - action_open_white_result
  - action_check_order
  - action_public_invoice_order
  - action_query_apply_claim
  - action_unbind_relation
  - action_submit_work
  - action_comfirm_unbind_relation
  - action_create_work_order
  - action_unbind_service_submit
  - action_order_answer
forms:
  unbind_service_form:
    required_slots:
    - agent_card_id

