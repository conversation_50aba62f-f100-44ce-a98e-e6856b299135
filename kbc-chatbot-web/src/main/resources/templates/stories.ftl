# 故事    约定story有且仅会话开始时以begin_开头，标识为新会话开始。便于记录跟踪每个完整会话流水。
stories:
# 故事：查询催促理赔进度流程
- story: query claim progress
  steps:
  - intent: begin_order_claim_progress
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_claim
  - slot_was_set:
    - is_product_dhj: true
  - slot_was_set:
    - is_over_time: true
  - intent: send_claim_work_info
  - action: action_submit_work
  - action: action_slot_reset

- story: query claim progress
  steps:
  - intent: begin_order_claim_progress
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_claim
  - intent: select_apply_type
  - slot_was_set:
    - is_product_dhj: false
  - action: action_apply_type_answer
  - slot_was_set:
    - apply_type: company
  - intent: send_claim_work_info
  - action: action_submit_work
  - action: action_slot_reset

# 故事：询问订单续费扣费是否成功
- story: query order renewal deduction
  steps:
  - intent: begin_renewal_deduction
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_renewal_deduction
  - intent: yes
  - action: action_create_work_order
  - action: action_slot_reset

- story: query order renewal deduction
  steps:
  - intent: begin_renewal_deduction
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_renewal_deduction
  - intent: no
  - action: utter_repeat_answer
  - action: action_slot_reset


# 故事：询问是否回访成功
- story: query order return visit status
  steps:
  - intent: begin_return_visit_status
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_return_visit_status
  - intent: yes
  - action: action_create_work_order
  - action: action_slot_reset

- story: query order return visit status
  steps:
  - intent: begin_return_visit_status
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_return_visit_status
  - intent: no
  - action: utter_repeat_answer
  - action: action_slot_reset


# 故事：询问如何回访
- story: query how return visit
  steps:
  - intent: begin_how_return_visit
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_how_return_visit
  - action: action_slot_reset

# 故事：询问什么产品需要回访
- story: query which return visit
  steps:
  - intent: begin_which_return_visit
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_which_return_visit
  - action: action_slot_reset
  - intent: query_why_return_visit
  - action: action_query_why_return_visit
  - action: action_slot_reset

# 故事：解除托管关系
- story: unbind trusteeship relation
  steps:
  - intent: begin_unbind_trusteeship
  - action: unbind_service_form
  - active_loop: unbind_service_form
  - active_loop: null
  - action: action_unbind_service_submit
  - intent: yes
  - action: action_unbind_relation
  - action: action_deactivate_loop
  - active_loop: null
  - action: action_slot_reset

- story: unbind trusteeship relation
  steps:
  - intent: begin_unbind_trusteeship
  - action: unbind_service_form
  - active_loop: unbind_service_form
  - active_loop: null
  - action: action_unbind_service_submit
  - intent: no
  - action: utter_transfer_labor
  - action: action_deactivate_loop
  - active_loop: null
  - action: action_slot_reset

- story: unbind trusteeship relation
  steps:
  - intent: begin_unbind_trusteeship
  - action: unbind_service_form
  - active_loop: unbind_service_form
  - intent: deny
  - action: utter_repeat_answer
  - action: action_deactivate_loop
  - active_loop: null
  - action: action_slot_reset

# 故事：大护甲开白流程
- story: open white list
  steps:
  - intent: begin_open_white_list
  - action: utter_provide_white_order_answer
  - intent: send_order_info
  - action: action_open_white_type
  - intent: select_question_type
  - action: action_send_example_image
  - intent: send_image
  - action: action_open_white_result
  - action: action_slot_reset

# 故事：开票流程-其他问题
- story: invoice order
  steps:
  - intent: begin_personal_invoice
  - action: utter_provide_invoice_order_answer
  - intent: send_order_info
  - action: action_check_order
  - action: action_slot_reset

# 故事：互联网产品申请理赔流程
- story: query order apply claim
  steps:
  - intent: begin_order_apply_claim
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_apply_claim
  - action: action_slot_reset

# 故事：寿险订单收不到短信验证码
- story: query order sms code
  steps:
  - intent: begin_order_sms_code
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_sms
  - intent: yes
  - action: action_create_work_order
  - action: action_slot_reset

- story: query order sms code
  steps:
  - intent: begin_order_sms_code
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_sms
  - intent: no
  - action: utter_repeat_answer
  - action: action_slot_reset

# 故事：查询佣金未收到
- story: query commission not received
  steps:
  - intent: begin_order_commission
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_commission
  - intent: yes
  - action: action_create_work_order
  - action: action_slot_reset

- story: query commission not received
  steps:
  - intent: begin_order_commission
  - action: utter_provide_query_order_answer
  - intent: send_order_info
  - action: action_query_commission
  - intent: no
  - action: utter_repeat_answer
  - action: action_slot_reset


- story: send image
  steps:
  - intent: send_image
  - action: utter_file_answer
  - action: action_slot_reset

- story: send customer name
  steps:
  - intent: info_customer_name
  - action: utter_no_answer
  - action: action_slot_reset

- story: send order
  steps:
  - intent: send_order_info
  - action: action_order_answer
  - action: action_slot_reset

- story: greet
  steps:
  - intent: greet
  - action: utter_greet_answer

- story: affirm
  steps:
  - intent: affirm
  - action: utter_yes_answer