rules:
#激活发开票规则
#- rule: Activate invoice form
#  steps:
#  - intent: personal_invoice
#  - action: personal_invoice_form
#  - active_loop: personal_invoice_form

#提交发开票规则
#- rule: Submit invoice form
# condition:
 # 表单是否激活的条件
# - active_loop: personal_invoice_form
#  steps:
# - action: personal_invoice_form
# - active_loop: null
# - slot_was_set:
#   - requested_slot: null
# - action: action_invoice_form_submit
# wait_for_user_input: true

# 激活解除托管服务信息规则
- rule: activate unbind form
  steps:
  - intent: begin_unbind_trusteeship   # intent that triggers form activation
  - action: unbind_service_form      # run the form
  - active_loop: unbind_service_form

# 提交解除托管服务信息规则
- rule: Submit unbind form
  condition:
    # 表单是否激活的条件
  - active_loop: unbind_service_form
  steps:
  - action: unbind_service_form
  - active_loop: null
  - slot_was_set:
    - requested_slot: null
  - action: action_unbind_service_submit

- rule: respond to FAQ QA
  steps:
  - intent: faq_qa
  - action: utter_faq_qa

- rule: respond to FAQ Cloud
  steps:
  - intent: faq_cloud
  - action: utter_faq_cloud

- rule: respond to Thanks
  steps:
  - intent: thanks
  - action: utter_thanks_answer

  #处理nlu理解失败的规则
- rule: process nlu fallback
  steps:
  - intent: nlu_fallback
  - action: action_default_fallback
