recipe: default.v1
language: zh
pipeline:
- name: SpacyNLP                   # 预训练词向量
  model: "zh_core_web_md"
- name: SpacyTokenizer          # 文本分词器
- name: SpacyFeaturizer         #特征提取器 将一句话变成一个向量
  pooling: max
- name: RegexFeaturizer
  case_sensitive: True
  use_word_boundaries: True
- name: RegexEntityExtractor
  case_sensitive: False
  use_lookup_tables: True
  use_regexes: True
  use_word_boundaries: True
- name: DIETClassifier
  epochs: 200
  constrain_similarities: True
  tensorboard_log_directory: ./tensorboard_log
- name: ResponseSelector
  epochs: 200
  constrain_similarities: True
  retrieval_intent: faq_qa
- name: ResponseSelector
  epochs: 200
  constrain_similarities: True
  retrieval_intent: faq_cloud
- name: EntitySynonymMapper
- name: FallbackClassifier
  threshold: 0.6
  ambiguity_threshold: 0.1
policies:
- name: MemoizationPolicy
  max_history: 50
- name: TEDPolicy
  max_history: 50
  epochs: 200
  constrain_similarities: True
- name: RulePolicy
  core_fallback_threshold: 0.3
  core_fallback_action_name: action_default_fallback
  enable_fallback_prediction: True
  restrict_rules: True
  check_for_contradictions: True
