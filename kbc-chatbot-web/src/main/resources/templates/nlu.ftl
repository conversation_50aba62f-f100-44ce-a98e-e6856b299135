nlu:
- intent: affirm
  examples: |
    - 是的
    - 是
    - 好的
    - 嗯
    - 可以
    - 没问题
    - OK
    - Yes
    - 行
    - 对
    - 对的
    - 是滴
    - 确认
    - 确定
    - 好勒
    - 可行
    - 好呀
    - ok的哦
    - 可以滴
    - 行的呢
    - 好滴
- intent: deny
  examples: |
    - 不
    - 不要
    - 不行
    - 不用
    - 不需要
    - No
    - 别
    - 不对
    - 不是
    - 不是的
    - 不是滴
    - 取消
    - 否
    - 没必要
- intent: yes
  examples: |
    - 是
- intent: no
  examples: |
    - 否
- intent: thanks
  examples: |
    - 谢谢
    - 感谢
    - 鸣谢
    - 感激
    - 感谢您
    - 谢谢您的帮助
    - 深表感谢
- intent: greet
  examples: |
    - 你好
    - hello
    - 您好
    - 在吗
    - 亲
    - hi
    - 在么
- intent: begin_personal_invoice
  examples: |
    - 开发票
    - 我要开发票
    - 我要开票
    - 可以开具个人发票吗
    - 如何申请发票
    - 我想给订单[2023070103003027028](order_code)开发票
    - 我想开发票
    - 怎么开发票
    - 怎么申请发票
    - 发票怎么开
    - 需要开发票
- intent: send_order_info
  examples: |
    - [天安人寿健康源2019增强版终身重大疾病保险](product_name)订单号:[2023070103003027028](order_code)订单时间：[2023-08-0110:00:00]保单号：[823A87Y98439]投保人：[张三]
- intent: begin_order_claim_progress
  examples: |
    - 理赔进度查询
    - 申请了理赔,怎么催促一下
    - 申请了理赔的进度
    - 理赔进度一直没有更新
    - 我需要查询一下我的理赔进度
    - 怎么查询理赔进度
    - 我的理赔进行到哪一步了？
    - 查询理赔进行到哪一步
    - 理赔太慢了
    - 什么时候理赔完成
    - 理赔催促
    - 催促理赔速度
    - 催促一下理赔进度
    - 怎么催促理赔
- intent: send_claim_work_info
  examples: |
    - 事故出险日期:[2023-10-17]理赔递交资料日期：[2023-10-17]理赔递交资料方式：[邮寄]本次理赔联系人电话：[13419598063]
    - 事故出险日期:[2023-10-17]
    - 理赔递交资料日期：[2023-10-17]
    - 理赔递交资料方式：[邮寄]
    - 本次理赔联系人电话：[13419598063]
- intent: begin_renewal_deduction
  examples: |
    - 订单[2023070103003027028](order_code)今年续购成功了吗
    - [2023070103003027028](order_code)订单续期扣费成功了吗
    - 咨询订单续期扣费问题
    - 我的订单续期扣费成功了吗
    - 咨询订单续期是否成功
    - 产品续期扣费是否成功
    - 产品续期是否成功扣费
    - 产品续期扣费成功了么
    - 这个订单续购失败了是怎么回事
    - 这个订单为啥续购失败了
    - 这个订单为什么续购失败了
    - 帮我查下这个订单续购失败的原因
    - 帮我查询下这个订单续购失败的原因
    - 这个订单续购不成功的原因是什么
    - 这个订单为什么续购不成功
    - 帮我查下订单续期扣费不成功的原因
    - 订单续期扣费是否成功
    - 订单续期扣费成功了嘛
    - 保单续上了吗
    - 订单续上了嘛
- intent: begin_order_apply_claim
  examples: |
    - 互联网产品如何申请理赔
    - 我要申请理赔
    - 怎么申请理赔？
    - 如何申请理赔？
    - 客户需要申请互联网产品理赔。怎么报案
    - 理赔报案渠道都有哪些？
    - 这个产品支持自助理赔吗？
    - [北京人寿大黄蜂9号少儿重疾险]产品支持自助理赔吗？
    - 客户需要理赔网销产品，怎么报案
    - 网销产品如何申请理赔
    - 我想要理赔网销产品
    - 互联网订单怎么理赔
    - 怎么理赔互联网产品
    - 怎么理赔网销产品
- intent: begin_return_visit_status
  examples: |
    - [2023070103003027028](order_code)订单快保显示没有回访成功
    - 订单[2023070103003027028](order_code)没有回访成功
    - 咨询订单是否回访成功
    - 订单是回访成功还是回访失败了
    - 订单回访失败了吗？
    - 订单没有回访成功是怎么回事
    - 我的订单回访失败了吗？
    - 查询订单回访状态
    - 订单显示没有回访成功是怎么回事
    - 咨询订单是否回访成功
    - 订单是回访成功还是回访失败了
    - 订单回访失败了吗?
    - 订单没有回访成功是怎么回事
    - 我的订单回访失败了吗?
    - 订单回访是否已经完成？
    - 订单回访是否已经成功？
    - 订单回访是否已经结束？
    - 产品为什么没有回访成功
- intent: begin_how_return_visit
  examples: |
    - [北京人寿大黄蜂9号少儿重疾险](product_name)产品可以通过什么方式回访
    - [北京人寿大黄蜂9号少儿重疾险](product_name)产品如何回访
    - 产品可以通过什么方式回访
    - 怎么回访
    - 如何回访
    - 我需要怎么操作回访
    - 产品怎么回访啊
    - 帮我查询下这个订单怎么进行回访
    - 订单怎么进行回访
    - 产品的回访途径有哪些
    - 这个产品可以通过什么途径进行回访
    - 产品回访的方法有哪些？
    - 产品回访可以采用哪些方式？
    - 有哪些途径可以进行产品回访？
    - 如何进行产品回访？
    - 产品回访可以通过哪些渠道进行？
    - 产品回访的方式有哪些选择？
    - 产品回访可以选择哪些方法？
    - 有哪些方式可以进行产品的回访？
    - 进行产品回访的方式有哪些选择？
    - 产品该怎么回访呢
    - 怎么回访产品呢
    - 产品该怎么回访呢
- intent: begin_which_return_visit
  examples: |
    - [北京人寿大黄蜂9号少儿重疾险](product_name)产品需要回访吗
    - [北京人寿大黄蜂9号少儿重疾险](product_name)需要回访吗
    - 产品需要回访吗
    - 需要回访吗
    - 什么产品需要回访
    - 订单需要回访不
    - 哪种订单需要回访
    - 哪些订单需要回访呢
    - 这个产品需要回访不
    - 这个产品可以不进行回访吗
    - 这个产品需要进行回访吗？
    - 产品是否需要回访
    - 产品需要回访嘛
- intent: query_why_return_visit
  examples: |
    - 期缴产品为什么要回访
    - 不完成回访有什么影响吗
    - 不完成回访会怎么样呢
    - 不完成回访会影响佣金发放吗？
    - 产品为什么要进行回访
    - 如果产品回访失败了会怎么样呢
    - 回访的目的是什么？
    - 为什么需要进行产品回访？
    - 为什么要安排回访服务？
    - 回访对于产品销售有何好处？
- intent: begin_unbind_trusteeship
  examples: |
    - 我要解除托管/服务关系
    - 怎么解除托管关系
    - 怎么解除服务关系
    - 怎么取消托管/服务关系
    - 如何取消托管关系
    - 如何取消服务关系
    - 如何解除和客户的托管/服务关系?
    - 怎么解除和顾问的托管/服务关系?
- intent: begin_open_white_list
  examples: |
    - 大护甲产品如何开白
    - 如何开通大护甲产品白名单
    - 大护甲产品需要开白
    - 大护甲产品报错了需要开白
    - 大护甲开通白名单
    - 怎么开通大护甲白名单
    - 我要开通大护甲白名单
- intent: begin_order_sms_code
  examples: |
    - 订单[2023070103003027028](order_code)收不到验证码
    - 寿险电投产品投保时收不到短信验证码
    - 我投保时没有收到短信验证码
    - 订单[2023070103003027028](order_code)回执时收不到验证码
    - 投保寿险订单收不到验证码
    - 回执无法收到验证码
    - 给客户回执没法收到验证码
    - 寿险订单投保手机收不到验证码
    - 投保寿险没有收到验证码
    - 回执没有收到验证码
- intent: begin_order_commission
  examples: |
    - [2023070103003027028]订单佣金未收到
    - 佣金未发放
    - 没有收到佣金
    - 我的订单[2023070103003027028](order_code)为什么没有收到佣金
    - 为什么没有发佣金
    - 佣金没有发是什么情况
    - 佣金没有收到
    - 未发放佣金
    - 佣金没有发给我
    - 佣金怎么没有发
- intent: info_card_id
  examples: |
    - [******************](card_id)
    - [350525199111083319](card_id)
    - [510704192705151013](card_id)
    - [******************](card_id)
    - [******************](card_id)
    - [654225199504179088](card_id)
    - [320412200703089848](card_id)
    - [320412193207265942](card_id)
    - [35052520101129336X](card_id)
    - [511503193802209537](card_id)
    - 身份证号[******************](card_id)
    - 号码是[150785199004229044](card_id)
- intent: info_customer_name
  examples: |
    - [杜敏](customer_name)
    - [晴明](customer_name)
    - [秦时明月](customer_name)
    - [张君宝](customer_name)
    - 姓名是[张俊明](customer_name)
    - 名字是[矢野浩二](customer_name)
    - 叫做[顾金璐](customer_name)
    - 叫[李龙](customer_name)
    - [汤明](customer_name)
    - 好像叫[李斯](customer_name)
    - 名字是[李维斯基](customer_name)
- intent: info_phone
  examples: |
    - [13419598063](phone)
    - 手机号是[13987865656](phone)
    - 号码是[13387324245](phone)
    - 号码是[14389764526](phone)
- intent: send_image
  examples: |
    - 用户发送图片
    - 发送示例图片
- intent: inform_order_code
  examples: |
    - 订单号是[2023070103003048661](order_code)
    - 订单号[2023070103003028053](order_code)
    - 我的订单号[2023070102001205115](order_code)
    - 我的订单号是[2023070103003027028](order_code)
    - [2023070103003026732](order_code)这张订单
    - [2023070103003027028](order_code)订单号
- regex: phone
  examples: |
    - 1[3456789]\d{9}
- regex: card_id
  examples: |
    - \d{17}(\d|[xX])
- regex: customer_name
  examples: |
    - [\u4e00-\u9fa5]{1,5}

  #faq intent
<#list faqList as faq>
- intent: faq_<#if faq.knowledgeType == "1">cloud<#else>qa</#if>/${faq.knowledgeId}
  examples: |
    - <#if faq.title?has_content>${faq.title?replace("<.*?>","","r")?replace("\r","\\r","r")?replace("\n","\\n","r")}</#if>
  <#if faq.similarQuestions?has_content>
    <#list faq.similarQuestions as similarQuestion>
      <#if faq.chapters?has_content>
    - ${similarQuestion?replace("<.*?>","","r")?replace("\r","\\r","r")?replace("\n","\\n","r")}
      </#if>
    </#list>
  </#if>
</#list>