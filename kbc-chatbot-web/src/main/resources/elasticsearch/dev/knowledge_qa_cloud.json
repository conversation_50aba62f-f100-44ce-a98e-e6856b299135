{"mappings": {"properties": {"id": {"type": "keyword", "ignore_above": 30}, "knowledgeId": {"type": "keyword", "ignore_above": 30}, "robotId": {"type": "keyword", "ignore_above": 30}, "environment": {"type": "integer", "ignore_above": 30}, "title": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 1024}}, "analyzer": "ik-index-synonym", "similarity": "my_bm25"}, "type": {"type": "keyword", "ignore_above": 5}, "projectId": {"type": "keyword", "ignore_above": 10}, "projectName": {"type": "text", "ignore_above": 50}, "folderId": {"type": "keyword", "ignore_above": 50}, "folderName": {"type": "text", "ignore_above": 50}, "firstDirect": {"type": "keyword", "ignore_above": 50}, "firstDirectName": {"type": "text", "ignore_above": 50}, "secondDirect": {"type": "keyword", "ignore_above": 50}, "secondDirectName": {"type": "text", "ignore_above": 50}, "faqCategoryId": {"type": "long", "ignore_above": 20}, "belongType": {"type": "keyword", "ignore_above": 20}, "belongName": {"type": "text", "ignore_above": 50}, "companyId": {"type": "keyword", "ignore_above": 20}, "companyName": {"type": "text", "ignore_above": 50}, "productId": {"type": "keyword", "ignore_above": 20}, "productName": {"type": "text", "ignore_above": 100}, "keyword": {"type": "text", "ignore_above": 100}, "keywordNum": {"type": "integer", "ignore_above": 20}, "mutexArticle": {"type": "text", "ignore_above": 500}, "mutexArticleName": {"type": "text", "ignore_above": 500}, "similarQuestion": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 1024}}, "analyzer": "ik-index-synonym"}, "relatedArticles": {"type": "text", "ignore_above": 500}, "subContent": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 5120}}, "analyzer": "ik-index-synonym"}, "cover": {"type": "keyword", "ignore_above": 100}, "state": {"type": "keyword", "ignore_above": 100}, "readingVolume": {"type": "integer", "ignore_above": 10}, "praiseVolume": {"type": "integer", "ignore_above": 10}, "commentVolume": {"type": "integer", "ignore_above": 10}, "praiseState": {"type": "integer", "ignore_above": 10}, "top": {"type": "integer", "ignore_above": 10}, "topTime": {"type": "date", "ignore_above": 20}, "editable": {"type": "integer", "ignore_above": 5}, "shareable": {"type": "integer", "ignore_above": 5}, "isShare": {"type": "integer", "ignore_above": 5}, "sourceFolder": {"type": "keyword", "ignore_above": 5}, "faqKnowledgeId": {"type": "long", "ignore_above": 5}, "createId": {"type": "keyword", "ignore_above": 5}, "createTime": {"type": "date", "ignore_above": 20}, "updateId": {"type": "keyword", "ignore_above": 5}, "updateName": {"type": "keyword", "ignore_above": 20}, "updateTime": {"type": "date", "ignore_above": 20}, "isDeleted": {"type": "integer", "ignore_above": 2}, "tenantId": {"type": "keyword", "ignore_above": 10}}}, "settings": {"number_of_shards": 1, "number_of_replicas": 1, "similarity": {"my_bm25": {"type": "BM25", "b": 0.2, "k1": 1.2}}, "analysis": {"filter": {"local_synonym": {"type": "dynamic_synonym", "synonyms_path": "https://kbc-sta.kbao123.com/gateway/kbc-search/api/noauth/synonymword/analyzer/getSynonymWord"}}, "analyzer": {"ik-index-synonym": {"type": "custom", "tokenizer": "ik_max_word", "filter": ["local_synonym"]}, "ik-index": {"type": "custom", "tokenizer": "ik_max_word"}, "ik-smart-synonym": {"type": "custom", "tokenizer": "ik_smart", "filter": ["local_synonym"]}, "ik-smart": {"type": "custom", "tokenizer": "ik_smart"}, "semicolon": {"type": "pattern", "pattern": ";"}}}}, "aliases": {"sta-kbcs-search_knowledge_qa_cloud_latest": {}}}