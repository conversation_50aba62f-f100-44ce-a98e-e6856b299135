spring:
  datasource:
    # JDBC 配置(驱动类自动从url的mysql识别,数据源类型自动识别)
    url: **************************************************************************************************************************************************************
    username: ENC(G19XT8pqmqQA7KVLX8r5TC6FoiVDHn+PA5fibNVUVXzUV+78E2ATnJMDZjyTNevA)
    password: ENC(+pgUs2Hd/FJ05Df+EIHGR8IR2PYv6NUjxNhWt/H/11k1TjstkgyslX0EB0SV63fGunfCSjcoPvQglVJ4DupIsg==)
    driver-class-name:  com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    #连接池配置(通常来说，只需要修改initialSize、minIdle、maxActive
    # 配置获取连接等待超时的时间
    druid:
      max-active: 20
      min-idle: 5
      initial-size: 5
      max-wait: 10000

  redis:
    ################### redis 单机版 start ##########################
    host: redis-kbcs-uat-lan.kbao123.com
    port: 6379
    password: ENC(b1fvuhLsHUW6Bs4LhIHY3DO0haiGH2q0p7ZYyYkeXkGf96coHw+LwFRb+ki+XLqyTNfy5tEGXP47tzKLghNb3Q==)
    timeout: 6000
    database: 1
    lettuce:
      pool:
        max-active: 30 # 连接池最大连接数（使用负值表示没有限制）,如果赋值为-1，则表示不限制；如果pool已经分配了maxActive个jedis实例，则此时pool的状态为exhausted(耗尽)
        max-idle: 8   # 连接池中的最大空闲连接 ，默认值也是8
        max-wait: 100 # # 等待可用连接的最大时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
        min-idle: 2    # 连接池中的最小空闲连接 ，默认值也是0
      shutdown-timeout: 100ms
    redisson:
      enable: true

  ################### redis 单机版 end ##########################

  ################## mongodb 配置 #################
  data:
    mongodb:
      uri: mongodb://${spring.data.mongodb.dbusername}:${spring.data.mongodb.dbpassword}@mongo-kbcs-test-lan.kbao123.com:27017/chatbot_sta?authsource=chatbot_sta
      dbusername: ENC(MrV9lumsqXy9KB0JRkhkOiw0vHrtpr/Bc7Oql3KwMaPndONKWinPtsyUPnjvV9MM)
      dbpassword: ENC(QjQ1/7Nj5niwQgjBTY8sd7GD52uw0p8CypevegiZjKX6XkBHv17T96XNkVO1mDiA7yU3Ge6BJip/7SHxW6/rvQ==)

  elasticsearch:
    rest:
      uris: elk-kbcs01-test-lan.kbao123.com:9200,elk-kbcs02-test-lan.kbao123.com:9200,elk-kbcs03-test-lan.kbao123.com:9200
      username: ENC(uJws8pyhIdu+a4zed4UhD1eyG4gC7miJvPPopomPg8QaAT0GTrayFcD4BDQm4ZVfs8C2KkFNM3RFgFx4W/xCWg==)
      password: ENC(mGm/uT2JjppUE9/gq3f6flVtjwz9Zhe/2fr9DauNC4G8wBfPtrMxKD3aQ0ihVD1N8mz3nIcpNTLGp/CI8oNxtb74M5EkUL0IVcpxAI8PkYM=)

#  neo4j:
#    uri: bolt://************:7687
#    authentication:
#      username: neo4j
#      password: Dt_Neo4j230811

################## eureka 注册中心配置 #################
eureka:
  client:
    enabled: true # 是否开启向注册中心进行注册
    serviceUrl:
      defaultZone: https://kbc:<EMAIL>/eureka/
    registry-fetch-interval-seconds: 5
    instance-info-replication-interval-seconds: 10
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${spring.application.instance_id:${server.port}}
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 30  # 续约时间有效期， 主动下线检测心跳， 服务器默认90秒
    status-page-url: http://${spring.cloud.client.ip-address}:${server.port}/swagger-ui.html

ribbon:
  ServerListRefreshInterval: 5000    #刷新服务列表源的间隔时间

logging:
  level:
    com.kbao: debug
    org.hibernate: info
    org.springframework: info
    org.hibernate.type.descriptor.sql.BasicBinder: trace
    org.hibernate.type.descriptor.sql.BasicExtractor: trace
    org.springframework.data.mongodb.core.MongoTemplate: debug

# 调度平台
xxl:
  job:
    admin:
      addresses: https://taskjob-sta.kbao123.com/job-api
    accessToken: 8cb51079ee4f4951a74d378fa61f45aa
    executor:
      appName: kbc-chatbot-sta
    client:
      enable: true

##feign参数优化
feign:
  client:
    config:
      default:
        # 连接其它项目服务超时时间
        connectTimeout: 5000
        # 读取其它项目服务超时时间
        readTimeout: 10000
        ## 配合logging.level=trace debug用于开发调式日志
        loggerLevel: full
  httpclient:
    enabled: true
    # 最大连接数
    max-connections: 400
    # 单个路由最大链接数
    max-connections-per-route: 100


systemLog:
  elasticsearch:
    indexName: logstash-sta-kbcs-bsc-rest-api-system

es:
  indexName:
    knowledgePackageQA: sta-kbcs-search_knowledge_qa_extra_latest
    entityRecognition: sta-kbcs-search_entity_recognition_latest
    knowledgePackageCloud: sta-kbcs-search_knowledge_cloud_latest

reindex:
  settingPath: /elasticsearch/sta/settings.json

rasa:
  api:
    restUrl: https://kbc-sta.kbao123.com/rasa
    jwtSecretKey: Qwer@1234
    modelPath: /opt/project/models/
    trainModelCallbackUrl: https://kbc-sta.kbao123.com/gateway/kbc-chatbot/web/robot/trainRasaModelCallback

external:
  url:
    createWorkOrderUrl: http://kefu.kbao123.com:9025/api/v1/business/startFow
    skillTenantConfigUrl: https://kefu.kbao123.com/yongLe/business/getSkillTenantConfig
    dtUserInfoUrl: https://kefu.kbao123.com/yongLe/business/getDtUserInfo
    skillConfigUrl: https://kefu.kbao123.com/yongLe/business/getSkillConfig
    initUrl: https://kefu.kbao123.com/imcloud/api/apiChannel
    #大模型平台
    maasBaseUrl: https://kbc-sta.kbao123.com/maas/
    embeddingUrl: https://kbc-sta.kbao123.com/maas/ex/llm/dataEmbedding
    embeddingDelUrl: https://kbc-sta.kbao123.com/maas/ex/llm/delData
    qaSearch: https://kbc-sta.kbao123.com/maas/ex/llm/qaSearch
    queryToken: https://kbc-sta.kbao123.com/maas/noauth/getToken
    queryMqttToken: https://kbc-sta.kbao123.com/maas/ex/getMToken
    queryChatRecord: https://kbc-sta.kbao123.com/maas/ex/qa/record/list
    queryQaRecordPage: https://kbc-sta.kbao123.com/maas/ex/qa/record/page
    queryQaRecordInfo: https://kbc-sta.kbao123.com/maas/ex/qa/record/recordInfo
    queryQaRecordReferences: https://kbc-sta.kbao123.com/maas/ex/qa/record/references
    getSliceList: https://kbc-sta.kbao123.com/maas/ex/knowledge/slice/list
    knowledgeDataStatusUpdate: https://kbc-sta.kbao123.com/maas/ex/knowledge/data/status/update
    knowledgeDataDel: https://kbc-sta.kbao123.com/maas/ex/knowledge/data/del
    knowledgeSliceAdd: https://kbc-sta.kbao123.com/maas/ex/knowledge/slice/add
    knowledgeSliceContentUpdate: https://kbc-sta.kbao123.com/maas/ex/knowledge/slice/content/update
    knowledgeSliceDel: https://kbc-sta.kbao123.com/maas/ex/knowledge/slice/del
    knowledgeSliceStatusUpdate: https://kbc-sta.kbao123.com/maas/ex/knowledge/slice/status/update
    syncProductData: https://kbc-sta.kbao123.com/maas/ex/product/syncData
    syncProductFile: https://kbc-sta.kbao123.com/maas/ex/product/syncProductFile
    syncCompanyFile: https://kbc-sta.kbao123.com/maas/ex/product/syncCompanyFile
    productLabel: https://kbc-sta.kbao123.com/maas/ex/product/productLabel
    trainRecordList: https://kbc-sta.kbao123.com/maas/ex/train/record/list
    updateChatRecord: ex/qa/record/update

#大模型配置
largemodel:
  secretKey: 0b38d04d0f3d4b55afbbd02d27952102
  code: ZL
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDnAC0GH0r6QPxYWPppYpIiDfMgO91w80ejnkb54H3+/dpUznEiiDKe8DcAWmJs6eQuw3VsDUdLHqpaAHzCWywngT4/Y/Gv8uoAgq2k+pD/njSEo0woCDw5GC/M7yplSXUqWeToXovuju2OCUM3hVsoQtejQZP7LactM1bxwyeAtQIDAQAB


# 快保云服配置
kbcbsc:
  appId: APP000220
  appName: 智聊机器人
  appCode: chatbotWeb

# mqtt配置
mqtt:
  mass:
    instanceId: mqtt-cn-i7m2qp31404
    topic: Topic-STA-KBC-MAAS-MESSAGE
    groupId: GID-STA-KBC-MAAS-MESSAGE
    accessKey: LTAI5tRhsUhaeLMCTaa4whRr
    endPoint: tcp://mqtt-cn-i7m2qp31404-internal.mqtt.aliyuncs.com
    enabled: true