spring:
  datasource:
    # JDBC 配置(驱动类自动从url的mysql识别,数据源类型自动识别)
    url: ***********************************************************************************************************************************************************
    username: chatbot_prod_rw02
    password: ENC(HAlkz3ekF+7q1yBkg3AITrRNJ8S08pImfYqRElDGShek+4zwp7k9wBG8VCsxcAfJzVz93iwFLrP/wdsJ8pHexg==)
    driver-class-name:  com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    #连接池配置(通常来说，只需要修改initialSize、minIdle、maxActive
    # 配置获取连接等待超时的时间
    druid:
      max-active: 20
      min-idle: 5
      initial-size: 5
      max-wait: 10000

  redis:
    ################### redis 单机版 start ##########################
    host: r-pz5wu8dxhv1ip0xtpk.redis.rds.aliyuncs.com
    port: 6379
    password: ENC(L76cE09sXTIY+TrrnBHd5palJ5W4Rq0tZseu7KZqRckfMD6x0Wcf1Ktx1Mwt35VPvezlgn5YttpPqBFT+7I1DRuwyGuHC3BENH39rzRlxZQ=)
    timeout: 6000
    database: 1
    lettuce:
      pool:
        max-active: 30 # 连接池最大连接数（使用负值表示没有限制）,如果赋值为-1，则表示不限制；如果pool已经分配了maxActive个jedis实例，则此时pool的状态为exhausted(耗尽)
        max-idle: 8   # 连接池中的最大空闲连接 ，默认值也是8
        max-wait: -1  # 等待可用连接的最大时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
        min-idle: 2    # 连接池中的最小空闲连接 ，默认值也是0
        time-between-eviction-runs: 10000ms # 这个设置是，每隔多少毫秒，空闲线程驱逐器关闭多余的空闲连接，且保持最少空闲连接可用，这个值最好设置大一点，否者影响性能。同时 genericObjectPoolConfig.setMinIdle(minIdle); 中minldle值要大于0。
        #lettuce连接池属性timeBetweenEvictionRunsMillis如果不设置 默认是 -1，当该属性值为负值时，lettuce连接池要维护的最小空闲连接数的目标minIdle就不会生效 。源码中的解释如下：
      shutdown-timeout: 100ms
    redisson:
      enable: true

  ################### redis 单机版 end ##########################

  ################## mongodb 配置 #################
  data:
    mongodb:
      uri: mongodb://${spring.data.mongodb.dbusername}:${spring.data.mongodb.dbpassword}@mongo-zh01.kbao123.com:27017,mongo-zh02.kbao123.com:27017,mongo-zh03.kbao123.com:27017/chatbot_prod?authsource=chatbot_prod
      dbusername: chatbot_prod_rw02
      dbpassword: ENC(d/tmg7Gqav+ZWLQqGQV7C/zIaBEJXYO5Ny4xPFWE6SN2aBWX90hK4UUEly4mjrVVtOoepv+egPdHx21gjf5wjg==)

  elasticsearch:
    rest:
      uris: kbcs-es-search01.kbao123.com:9200,kbcs-es-search02.kbao123.com:9200,kbcs-es-search03.kbao123.com:9200
      username: <EMAIL>
      password: EmwscnaGuxY5AJWUFov4ZT723MXgk1

################## eureka 注册中心配置 #################
eureka:
  client:
    enabled: true # 是否开启向注册中心进行注册
    serviceUrl:
      defaultZone: http://pro-kbcs-bsc-eureka-jar-cluster.kbao-cs-bsc-pro:8000/eureka/
    registry-fetch-interval-seconds: 5
    instance-info-replication-interval-seconds: 10
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${spring.application.instance_id:${server.port}}
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 15  # 续约时间5， 主动下线检测心跳， 服务器默认90秒
    status-page-url: http://${spring.cloud.client.ip-address}:${server.port}/swagger-ui.html

ribbon:
  ServerListRefreshInterval: 5000    #刷新服务列表源的间隔时间

logging:
  level:
    com.kbao: info
    org.hibernate: info
    org.springframework: info
    org.hibernate.type.descriptor.sql.BasicBinder: trace
    org.hibernate.type.descriptor.sql.BasicExtractor: trace
    org.springframework.data.mongodb.core.MongoTemplate: info

# 调度平台
xxl:
  job:
    admin:
      addresses: https://taskjob.dtinsure.com/job-api
    access-token: ca9fd0ce058c4424a58248e53681d99b
    executor:
      app-name: kbc-chatbot-prod
    client:
      enable: true

##feign参数优化
feign:
  client:
    config:
      default:
        # 连接其它项目服务超时时间
        connectTimeout: 5000
        # 读取其它项目服务超时时间
        readTimeout: 10000
        ## 配合logging.level=trace debug用于开发调式日志
        loggerLevel: full
  httpclient:
    enabled: true
    # 最大连接数
    max-connections: 400
    # 单个路由最大链接数
    max-connections-per-route: 100

es:
  indexName:
    knowledgePackageQA: pro-kbcs-search_knowledge_qa_extra_latest
    entityRecognition: pro-kbcs-search_entity_recognition_latest
    knowledgePackageCloud: pro-kbcs-search_knowledge_cloud_latest

reindex:
  settingPath: /elasticsearch/prod/settings.json

rasa:
  api:
    restUrl: https://kbc.dtinsure.com/rasa
    jwtSecretKey: BDZQgmuP8NlswoO3#
    modelPath: /opt/project/models/
    trainModelCallbackUrl: https://kbc.dtinsure.com/gateway/kbc-chatbot/web/robot/trainRasaModelCallback

external:
  url:
    createWorkOrderUrl: http://kefu.kbao123.com:9025/api/v1/business/startFow
    skillTenantConfigUrl: https://kefu.kbao123.com/yongLe/business/getSkillTenantConfig
    dtUserInfoUrl: https://kefu.kbao123.com/yongLe/business/getDtUserInfo
    skillConfigUrl: https://kefu.kbao123.com/yongLe/business/getSkillConfig
    initUrl: https://kefu.kbao123.com/imcloud/api/apiChannel
    #大模型平台
    maasBaseUrl: https://kbc.dtinsure.com/maas/
    embeddingUrl: https://kbc.dtinsure.com/maas/ex/llm/dataEmbedding
    embeddingDelUrl: https://kbc.dtinsure.com/maas/ex/llm/delData
    qaSearch: https://kbc.dtinsure.com/maas/ex/llm/qaSearchStream
    queryToken: https://kbc.dtinsure.com/maas/noauth/getToken
    queryMqttToken: https://kbc.dtinsure.com/maas/ex/getMToken
    queryChatRecord: https://kbc.dtinsure.com/maas/ex/qa/record/list
    queryQaRecordPage: https://kbc.dtinsure.com/maas/ex/qa/record/page
    queryQaRecordInfo: https://kbc.dtinsure.com/maas/ex/qa/record/recordInfo
    queryQaRecordReferences: https://kbc.dtinsure.com/maas/ex/qa/record/references
    getSliceList: https://kbc.dtinsure.com/maas/ex/knowledge/slice/list
    knowledgeDataStatusUpdate: https://kbc.dtinsure.com/maas/ex/knowledge/data/status/update
    knowledgeDataDel: https://kbc.dtinsure.com/maas/ex/knowledge/data/del
    knowledgeSliceAdd: https://kbc.dtinsure.com/maas/ex/knowledge/slice/add
    knowledgeSliceContentUpdate: https://kbc.dtinsure.com/maas/ex/knowledge/slice/content/update
    knowledgeSliceDel: https://kbc.dtinsure.com/maas/ex/knowledge/slice/del
    knowledgeSliceStatusUpdate: https://kbc.dtinsure.com/maas/ex/knowledge/slice/status/update
    syncProductData: https://kbc.dtinsure.com/maas/ex/product/syncData
    syncProductFile: https://kbc.dtinsure.com/maas/ex/product/syncProductFile
    syncCompanyFile: https://kbc.dtinsure.com/maas/ex/product/syncCompanyFile
    productLabel: https://kbc.dtinsure.com/maas/ex/product/productLabel
    trainRecordList: https://kbc.dtinsure.com/maas/ex/train/record/list
    updateChatRecord: ex/qa/record/update


# 快保云服配置
kbcbsc:
  appId: APP000203
  appName: 智聊机器人
  appCode: chatbotWeb

#大模型配置
largemodel:
  secretKey: ********************************
  code: ZL
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDnAC0GH0r6QPxYWPppYpIiDfMgO91w80ejnkb54H3+/dpUznEiiDKe8DcAWmJs6eQuw3VsDUdLHqpaAHzCWywngT4/Y/Gv8uoAgq2k+pD/njSEo0woCDw5GC/M7yplSXUqWeToXovuju2OCUM3hVsoQtejQZP7LactM1bxwyeAtQIDAQAB

# mqtt配置
mqtt:
  mass:
    instanceId: mqtt-cn-j4g3wk60j04
    topic: Topic-PROD-KBC-MAAS-MESSAGE
    groupId: GID-PROD-KBC-MAAS-MESSAGE
    accessKey: LTAI5tAFjV1seKXvS4Fcp5ww
    endPoint: tcp://mqtt-cn-j4g3wk60j04-internal.mqtt.aliyuncs.com
    enabled: true