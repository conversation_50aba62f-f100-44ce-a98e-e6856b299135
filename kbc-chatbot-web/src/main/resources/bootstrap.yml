#端口配置
server:
  port: 7003   #固定端口
#  port: ${randomServerPort.value[7000,7005]}  #随机端口

#服务名称
spring:
  application:
    name: kbc-chatbot-web
  cloud:
    nacos:
      config:
        # 配置中心地址
        server-addr: https://config-lan.kbao123.com
        # 账号
        username: kbc-icsc-chatbot-r
        # 密码
        password: WkcynwXzCrUupatQPQz5%WE!LEYxQ%bc
        # 自动刷新配置
        #refresh-enabled: true
        # 配置文件格式
        file-extension: yml
        # 指定group 默认 DEFAULT_GROUP
        group: group-kbc-icsc-chatbot
        # 指定namespace id 默认public
        namespace: ${spring.profiles.active}
        # 自定义dataId，默认spring.application.name
        prefix: kbc-chatbot-web

redis:
  application:
    name: kbc-chatbot
