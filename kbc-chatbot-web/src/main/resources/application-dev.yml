spring:
  datasource:
    # JDBC 配置(驱动类自动从url的mysql识别,数据源类型自动识别)
    url: *******************************************************************************************************************************************
    username: chatbot_sta_rw
    password: ChatBot_sta230516
    driver-class-name:  com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    #连接池配置(通常来说，只需要修改initialSize、minIdle、maxActive
    # 配置获取连接等待超时的时间
    druid:
      max-active: 20
      min-idle: 5
      initial-size: 5
      max-wait: 10000

  redis:
    ################### redis 单机版 start ##########################
    host: redis-kbcs-uat-lan.kbao123.com
    port: 6379
    password: ewOIJ*f7gUT^&63tiu3flk3o
    timeout: 6000
    database: 1
    lettuce:
      pool:
        max-active: 30 # 连接池最大连接数（使用负值表示没有限制）,如果赋值为-1，则表示不限制；如果pool已经分配了maxActive个jedis实例，则此时pool的状态为exhausted(耗尽)
        max-idle: 8   # 连接池中的最大空闲连接 ，默认值也是8
        max-wait: 100 # # 等待可用连接的最大时间，单位毫秒，默认值为-1，表示永不超时。如果超过等待时间，则直接抛出JedisConnectionException
        min-idle: 2    # 连接池中的最小空闲连接 ，默认值也是0
      shutdown-timeout: 100ms
    redisson:
      enable: true

  ################### redis 单机版 end ##########################

  ################## mongodb 配置 #################
  data:
    mongodb:
      uri: mongodb://chatbot_sta_rw:<EMAIL>:27017/chatbot_sta?authsource=chatbot_sta


  elasticsearch:
    rest:
      uris: elk-kbcs01-test-lan.kbao123.com:9200,elk-kbcs02-test-lan.kbao123.com:9200,elk-kbcs03-test-lan.kbao123.com:9200
      username: <EMAIL>
      password: 5YUH7BFjgSy0IVs3hdwtlc9uGr1JWAK4

#  neo4j:
#    uri: bolt://************:7687
#    authentication:
#      username: neo4j
#      password: Dt_Neo4j230811

################## eureka 注册中心配置 #################
eureka:
  client:
    enabled: true # 是否开启向注册中心进行注册
    serviceUrl:
      defaultZone: https://kbc:<EMAIL>/eureka/
    registry-fetch-interval-seconds: 5
    instance-info-replication-interval-seconds: 10
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${spring.application.instance_id:${server.port}}
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 30  # 续约时间有效期， 主动下线检测心跳， 服务器默认90秒
    status-page-url: http://${spring.cloud.client.ip-address}:${server.port}/swagger-ui.html

ribbon:
  ServerListRefreshInterval: 5000    #刷新服务列表源的间隔时间

logging:
  level:
    com.kbao: debug
    org.hibernate: info
    org.springframework: info
    org.hibernate.type.descriptor.sql.BasicBinder: trace
    org.hibernate.type.descriptor.sql.BasicExtractor: trace
    org.springframework.data.mongodb.core.MongoTemplate: debug
#    org.springframework.data.neo4j: debug
    com.alibaba.nacos.client: off

spring.cloud.nacos.config.enabled: false

##feign参数优化
feign:
  client:
    config:
      default:
        # 连接其它项目服务超时时间
        connectTimeout: 5000
        # 读取其它项目服务超时时间
        readTimeout: 10000
        ## 配合logging.level=trace debug用于开发调式日志
        loggerLevel: full
  httpclient:
    enabled: true
    # 最大连接数
    max-connections: 400
    # 单个路由最大链接数
    max-connections-per-route: 100


systemLog:
  elasticsearch:
    indexName: logstash-sta-kbcs-bsc-rest-api-system

es:
  indexName:
    knowledgePackageQA: sta-kbcs-search_knowledge_qa_extra_latest
    entityRecognition: sta-kbcs-search_entity_recognition_latest
    knowledgePackageCloud: sta-kbcs-search_knowledge_cloud_latest
    unifiedProduct: sta-kbcs-search_unifiedproduct_latest

reindex:
  settingPath: /elasticsearch/dev/settings.json

#rasa:
#  api:
#    restUrl: http://localhost:5005
#    jwtSecretKey: Qwer@1234
#    modelPath: D:\\workspace_python\\kbc-chatbot-rasa\\models\\
#    trainModelCallbackUrl: http://localhost:7003/web/robot/trainRasaModelCallback

rasa:
  api:
    restUrl: https://kbc-sta.kbao123.com/rasa
    jwtSecretKey: Qwer@1234
    modelPath: /opt/project/models/
    trainModelCallbackUrl: https://kbc-sta.kbao123.com/gateway/kbc-chatbot/web/robot/trainRasaModelCallback

external:
  url:
    createWorkOrderUrl: http://kefu.kbao123.com:9025/api/v1/business/startFow
    skillTenantConfigUrl: https://kefu.kbao123.com/yongLe/business/getSkillTenantConfig
    dtUserInfoUrl: https://kefu.kbao123.com/yongLe/business/getDtUserInfo
    skillConfigUrl: https://kefu.kbao123.com/yongLe/business/getSkillConfig
    initUrl: https://kefu.kbao123.com/imcloud/api/apiChannel
    #大模型平台
    maasBaseUrl: http://127.0.0.1:8000/
    embeddingUrl: http://127.0.0.1:8000/ex/llm/dataEmbedding
    embeddingDelUrl: http://127.0.0.1:8000/ex/llm/delData
    qaSearch: http://127.0.0.1:8000/ex/llm/qaSearch
    queryToken: http://127.0.0.1:8000/noauth/getToken
    queryMqttToken: http://127.0.0.1:8000/ex/getMToken
    queryChatRecord: http://127.0.0.1:8000/ex/qa/record/list
    queryQaRecordPage: http://127.0.0.1:8000/ex/qa/record/page
    queryQaRecordInfo: http://127.0.0.1:8000/ex/qa/record/recordInfo
    queryQaRecordReferences: http://127.0.0.1:8000/ex/qa/record/references
    getSliceList: http://127.0.0.1:8000/ex/knowledge/slice/list
    knowledgeDataStatusUpdate: http://127.0.0.1:8000/ex/knowledge/data/status/update
    knowledgeDataDel: http://127.0.0.1:8000/ex/knowledge/data/del
    knowledgeSliceAdd: http://127.0.0.1:8000/ex/knowledge/slice/add
    knowledgeSliceContentUpdate: http://127.0.0.1:8000/ex/knowledge/slice/content/update
    knowledgeSliceDel: http://127.0.0.1:8000/ex/knowledge/slice/del
    knowledgeSliceStatusUpdate: http://127.0.0.1:8000/ex/knowledge/slice/status/update
    syncProductData: http://127.0.0.1:8000/ex/product/syncData
    syncProductFile: http://127.0.0.1:8000/ex/product/syncProductFile
    syncCompanyFile: http://127.0.0.1:8000/ex/product/syncCompanyFile
    productLabel: http://127.0.0.1:8000/ex/product/productLabel
    trainRecordList: http://127.0.0.1:8000/ex/train/record/list
    updateChatRecord: ex/qa/record/update


#大模型配置
largemodel:
  secretKey: 0b38d04d0f3d4b55afbbd02d27952102
  code: ZL
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDnAC0GH0r6QPxYWPppYpIiDfMgO91w80ejnkb54H3+/dpUznEiiDKe8DcAWmJs6eQuw3VsDUdLHqpaAHzCWywngT4/Y/Gv8uoAgq2k+pD/njSEo0woCDw5GC/M7yplSXUqWeToXovuju2OCUM3hVsoQtejQZP7LactM1bxwyeAtQIDAQAB

# 快保云服配置
kbcbsc:
  appId: APP000220
  appName: 智聊机器人
  appCode: chatbotWeb
# mqtt配置
mqtt:
  mass:
    instanceId: mqtt-cn-i7m2qp31404
    topic: Topic-STA-KBC-MAAS-MESSAGE
    groupId: GID-STA-KBC-MAAS-MESSAGE
    accessKey: LTAI5tRhsUhaeLMCTaa4whRr
    endPoint: tcp://mqtt-cn-i7m2qp31404.mqtt.aliyuncs.com
    enabled: false