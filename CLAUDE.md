#永远用中文回答我

#项目通用结构：
chatbot - 项目总目录
    kbc-chatbot -后端项目
		kbc-chatbot-entity -实体目录
			{busi}/entity -存放当前业务mysql实体和Mapper.xml文件
			{busi}/model -存放当前业务mongo实体
			    mongo实体示例：
			        @Document(collection = "驼峰")
                    public class 实体名 implements Serializable {
                        必须要有id字段
                        字段不需要添加@Field，部分字段需要@Index
                    }
			{busi}/bean	-存放当前业务相关VO
		kbc-chatbot-service -service目录
			{busi}/dao -存放Mapper接口目录
				Mybatis Mapper接口格式：public interface {busi}Mapper  extends BaseMapper<实体名, Integer>{
				Mongo dao接口格式：public class {busi}Dao extends BaseMongoDaoImpl<实体名, String> {
			{busi}/service - 存放{busi}Service.java
				Mybatis Service类格式：public class {busi}Service extends BaseSQLServiceImpl<实体名, Integer, Mapper接口名> {
				Mongo service类格式：public class {busi}Service extends BaseMongoServiceImpl<实体名, String, Mongo Dao接口名> {
		kbc-chatbot-web -controller目录
			{busi}/{busi}Controller -controller文件
				格式：public class {busi}Controller extends BaseController {
				    仅添加当前需求的接口，没有让添加的接口不要添加
				}
			
	kbc-chatbot-web -前端项目
	
	##项目结构变量说明：
		busi：业务名称，随当前变化对应不同的目录


#后端代码规范说明：
	-禁止创建任何形式的文档，除非我明确要求让你写
	-不要自己去添加一些额外的操作和接口，如果需要我会告诉你，代码尽量简洁
	-尽量使用entity作为接口传参，非必要不要新增VO
	-接口逻辑尽量放到service层去实现
	-service会继承com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl或者com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl
		代码我复制到了rule_common目录下，在编写增删改查方法时，你需要去该目录查看父类拥有的方法，并直接引用父类方法，不要自己写增删改查
	-分页接口通用格式：public Result<PageInfo<实体类>> page(@RequestBody PageRequest<实体类> page) {
			没有特殊逻辑的情况下，你可以直接使用super.page方法
	
#前端项目规范说明：
	前端项目尽量使用组件，不要添加太多过于繁琐的样式
	我没让你添加的页面，不要自己去添加
	后端项目接口目录和前端项目文件名相同，都是kbc-chatbot-web，但是后端项目位于kbc-chatbot文件夹中，注意区分

#通用代码规范说明：
	项目文件必须放在对应的文件目录下，严格遵守项目结构规范
	修改代码前，尽可能的查看相关的上下文，代码尽量简洁完整
	禁止自行启动服务进行验证，我会自己去验证
	如果你有不清楚的地方，请向我确认，不要自己瞎猜
	